<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DepartmentSupervisorAssistant extends Model
{
    use HasFactory;

    protected $fillable = [
        'department_id',
        'user_id',
        'role_type',
        'supervisor_id',
        'assigned_at',
        'assigned_by',
        'is_active',
    ];

    protected $casts = [
        'assigned_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    // Role type constants
    const ROLE_SUPERVISOR = 'supervisor';
    const ROLE_ASSISTANT = 'assistant';

    /**
     * Get the department this assignment belongs to.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the user assigned to this role.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the supervisor (if this is an assistant role).
     */
    public function supervisor()
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    /**
     * Get the user who made this assignment.
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Scope to get active assignments.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get supervisors.
     */
    public function scopeSupervisors($query)
    {
        return $query->where('role_type', self::ROLE_SUPERVISOR);
    }

    /**
     * Scope to get assistants.
     */
    public function scopeAssistants($query)
    {
        return $query->where('role_type', self::ROLE_ASSISTANT);
    }

    /**
     * Scope to get assignments for a specific department.
     */
    public function scopeForDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * Scope to get assignments for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get assistants under a specific supervisor in a department.
     */
    public static function getAssistantsForSupervisor($supervisorId, $departmentId = null)
    {
        $query = static::active()
            ->assistants()
            ->where('supervisor_id', $supervisorId)
            ->with(['user', 'department']);

        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        return $query->get();
    }

    /**
     * Get departments where a user is a supervisor.
     */
    public static function getDepartmentsWhereSupervisor($userId)
    {
        return static::active()
            ->supervisors()
            ->where('user_id', $userId)
            ->with('department')
            ->get()
            ->pluck('department');
    }

    /**
     * Get departments where a user is an assistant.
     */
    public static function getDepartmentsWhereAssistant($userId)
    {
        return static::active()
            ->assistants()
            ->where('user_id', $userId)
            ->with(['department', 'supervisor'])
            ->get();
    }

    /**
     * Check if a user is a supervisor in any department.
     */
    public static function isSupervisorInAnyDepartment($userId)
    {
        return static::active()
            ->supervisors()
            ->where('user_id', $userId)
            ->exists();
    }

    /**
     * Check if a user is an assistant in any department.
     */
    public static function isAssistantInAnyDepartment($userId)
    {
        return static::active()
            ->assistants()
            ->where('user_id', $userId)
            ->exists();
    }
}
