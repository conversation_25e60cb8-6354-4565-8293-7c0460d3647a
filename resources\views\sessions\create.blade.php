<x-layout bodyClass="bg-gradient-to-br from-indigo-50 via-purple-50 to-pink-50">

        <div class="container position-sticky z-index-sticky top-0">
            <div class="row">
                <div class="col-12">
                    <!-- Navbar -->
                    {{-- <x-navbars.navs.guest signin='login' signup='register'></x-navbars.navs.guest> --}}
                    <!-- End Navbar -->
                </div>
            </div>
        </div>
        <main class="main-content mt-0">
            <div class="page-header align-items-center min-vh-100 position-relative"
                style="background: linear-gradient(135deg, 
                    rgba(139, 69, 255, 0.1) 0%, 
                    rgba(79, 70, 229, 0.1) 25%, 
                    rgba(59, 130, 246, 0.1) 50%, 
                    rgba(139, 69, 255, 0.1) 100%), 
                    url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'); 
                    background-size: cover; 
                    background-position: center; 
                    background-attachment: fixed;">
                <div class="position-absolute w-100 h-100" style="background: linear-gradient(135deg, rgba(79, 70, 229, 0.8), rgba(139, 69, 255, 0.8))"></div>
                <div class="container position-relative z-index-1">
                    <div class="row justify-content-center">
                        <div class="col-lg-5 col-md-8 col-12">
                            <div class="card shadow-lg border-0" style="backdrop-filter: blur(20px); background: rgba(255, 255, 255, 0.95); border-radius: 20px;">
                                <div class="card-header bg-transparent text-center pt-4 pb-3">
                                    <div class="d-flex justify-content-center mb-3">
                                        <div class="avatar avatar-xl bg-gradient-primary rounded-circle shadow-lg">
                                            <i class="fas fa-user-lock text-white fs-3"></i>
                                        </div>
                                    </div>
                                    <h3 class="text-dark font-weight-bold mb-1">Welcome Back</h3>
                                    <p class="text-muted mb-0">Please sign in to your account</p>
                                        {{-- <div class="row mt-3">
                                            <h6 class='text-white text-center'>
                                                <span class="font-weight-normal">Email:</span> <EMAIL>
                                                <br>
                                                <span class="font-weight-normal">Password:</span> secret</h6>
                                            <div class="col-2 text-center ms-auto">
                                                <a class="btn btn-link px-3" href="javascript:;">
                                                    <i class="fa fa-facebook text-white text-lg"></i>
                                                </a>
                                            </div>
                                            <div class="col-2 text-center px-1">
                                                <a class="btn btn-link px-3" href="javascript:;">
                                                    <i class="fa fa-github text-white text-lg"></i>
                                                </a>
                                            </div>
                                            <div class="col-2 text-center me-auto">
                                                <a class="btn btn-link px-3" href="javascript:;">
                                                    <i class="fa fa-google text-white text-lg"></i>
                                                </a>
                                            </div>
                                        </div> --}}
                                    </div>
                                </div>
                                <div class="card-body">
                                    <form role="form" method="POST" action="{{ route('login') }}" class="text-start">
                                        @csrf
                                        @if (Session::has('status'))
                                        <div class="alert alert-success alert-dismissible text-white" role="alert">
                                            <span class="text-sm">{{ Session::get('status') }}</span>
                                            <button type="button" class="btn-close text-lg py-3 opacity-10"
                                                data-bs-dismiss="alert" aria-label="Close">
                                                <span aria-hidden="true">&times;</span>
                                            </button>
                                        </div>
                                        @endif
                                        <div class="mb-3">
                                            <label for="name" class="form-label text-dark fw-bold">User Name</label>
                                            <input type="text" id="name" class="form-control bg-light border-2 rounded-pill px-3 py-2" name="name" value="{{ old('name') }}" required autofocus autocomplete="username" style="border-color: #e9ecef !important;" placeholder="Enter your username">
                                        </div>
                                        @error('name')
                                        <p class='text-danger inputerror'>{{ $message }} </p>
                                        @enderror
                                        <div class="mb-3">
                                            <label for="password" class="form-label text-dark fw-bold">Password</label>
                                            <input type="password" id="password" class="form-control bg-light border-2 rounded-pill px-3 py-2" name="password" required autocomplete="current-password" style="border-color: #e9ecef !important;" placeholder="Enter your password">
                                        </div>
                                        @error('password')
                                        <p class='text-danger inputerror'>{{ $message }} </p>
                                        @enderror
                                        <div class="form-check form-switch d-flex align-items-center my-3">
                                            <input class="form-check-input" type="checkbox" id="rememberMe">
                                            <label class="form-check-label mb-0 ms-2" for="rememberMe">Remember
                                                me</label>
                                        </div>
                                        <div class="mb-3">
                                            <label for="captcha" class="form-label text-dark fw-bold">Captcha: <span class="badge bg-info text-dark fs-6 px-3 py-2 rounded-pill">{{ $captcha }}</span></label>
                                            <input type="text" id="captcha" class="form-control bg-light border-2 rounded-pill px-3 py-2" name="captcha" required style="border-color: #e9ecef !important;" placeholder="Enter the captcha above">
                                        </div>
                                        @error('captcha')
                                            <p class='text-danger inputerror'>{{ $message }} </p>
                                        @enderror
                                        <div class="text-center">
                                            <button type="submit" class="btn w-100 my-4 mb-2 rounded-pill py-3 fw-bold login-btn" style="background: linear-gradient(135deg, #4f46e5, #8b5cf6); color: white; border: none; transition: all 0.3s ease; box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);">Log In</button>
                                        </div>
                                        
                                        <style>
                                        .login-btn:hover {
                                            transform: translateY(-2px);
                                            box-shadow: 0 12px 40px rgba(139, 92, 246, 0.4) !important;
                                            background: linear-gradient(135deg, #6366f1, #a855f7) !important;
                                        }
                                        
                                        .form-control:focus {
                                            border-color: #8b5cf6 !important;
                                            box-shadow: 0 0 0 0.2rem rgba(139, 92, 246, 0.25) !important;
                                        }
                                        
                                        .card {
                                            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
                                        }
                                        </style>
                                        {{-- <p class="mt-4 text-sm text-center">
                                            Don't have an account?
                                            <a href="{{ route('register') }}"
                                                class="text-primary text-gradient font-weight-bold">Sign up</a>
                                        </p> --}}
                                        <!-- <p class="text-sm text-center">
                                            Forgot your password? Reset your password
                                            <a href="{{ route('verify') }}"
                                                class="text-primary text-gradient font-weight-bold">here</a>
                                        </p> -->
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <x-footers.guest></x-footers.guest>
            </div>
        </main>
        @push('js')
<script src="{{ asset('assets') }}/js/jquery.min.js"></script>
<script>
    $(function() {

    var text_val = $(".input-group input").val();
    if (text_val === "") {
      $(".input-group").removeClass('is-filled');
    } else {
      $(".input-group").addClass('is-filled');
    }
});
</script>
@endpush
</x-layout>
