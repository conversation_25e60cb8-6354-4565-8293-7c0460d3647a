@php
    $Admin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
@endphp

<!-- Navigation Buttons (Admin Only) -->
@if ($Admin)
    <div class="modern-card mb-4">
        <div class="modern-card-body">
            <h6 class="mb-3">
                <i class="fas fa-sitemap me-2"></i>
                Quick Navigation
            </h6>
            <div class="d-flex flex-wrap gap-2">
                @php
                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                    $isAllIftaActive = request()->route('darulifta') == null;
                @endphp
                
                <a href="{{ route('remaining-fatawa', [
                    'selectedmufti' => $this->selectedmufti,
                    'selectedTimeFrame' => $this->selectedTimeFrame,
                    'startDate' => $tempStartDate,
                    'endDate' => $tempEndDate,
                    'showDetail' => $showDetail ? '1' : '0',
                    'showQue' => $showQue ? '1' : '0',
                    'showChat' => $showChat ? '1' : '0',
                    'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                ]) }}" 
                   class="btn-modern {{ $isAllIftaActive ? 'btn-primary-modern' : 'btn-outline-modern' }}">
                    <i class="fas fa-globe me-2"></i>
                    All Ifta
                </a>

                @foreach($daruliftalist as $daruliftalistn)
                    @php
                        $isActive = request()->route('darulifta') == $daruliftalistn;
                    @endphp
                    <a href="{{ route('remaining-fatawa', [
                        'darulifta' => $daruliftalistn,
                        'selectedmufti' => $this->selectedmufti,
                        'selectedTimeFrame' => $this->selectedTimeFrame,
                        'startDate' => $tempStartDate,
                        'endDate' => $tempEndDate,
                        'showDetail' => $showDetail ? '1' : '0',
                        'showQue' => $showQue ? '1' : '0',
                        'showChat' => $showChat ? '1' : '0',
                        'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                    ]) }}" 
                       class="btn-modern {{ $isActive ? 'btn-primary-modern' : 'btn-outline-modern' }}">
                        <i class="fas fa-building me-2"></i>
                        {{ $daruliftalistn }}
                    </a>
                @endforeach
            </div>
        </div>
    </div>
@endif
