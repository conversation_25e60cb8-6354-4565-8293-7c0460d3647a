
<div>
            @if(!$viralLink)
                <!-- Show input for viral link if viralLink is not available -->
                <input type="text" wire:model.lazy="viralLink" placeholder="Insert viral link">
                <div wire:loading wire:target="viralLink">Saving viral link...</div>
            @else
                <!-- Show view and edit buttons for viral link -->
                <a href="{{ $viralLink }}" target="_blank" class="btn btn-info btn-sm">View viral link</a>
                <button wire:click="enableViralLinkEditing" class="btn btn-secondary btn-sm">Edit</button>

                @if($isEditingViralLink)
                    <!-- Show input for editing viral link -->
                    <input type="text" wire:model.lazy="viralLink" placeholder="Edit viral link">
                    <button wire:click="saveViralLink" class="btn btn-success btn-sm">Save</button>
                    <button wire:click="cancelViralLinkEditing" class="btn btn-secondary btn-sm">Cancel</button>
                @endif
            @endif
             <!-- Display the web_date if it exists -->
    @if($viralDate)
        <br>
        <span>Viral: {{ $viralDate }}</span>
    @elseif($ViralDays)
        <!-- Display days not uploaded on web if web_date is empty -->
        <br>
        <span style="color: {{ $ViralDays > 7 ? 'red' : 'black' }};">
            {{ $ViralDays }} days not Viral
        </span>
    @endif
</div>


