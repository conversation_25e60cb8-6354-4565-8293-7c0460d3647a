<!-- Modern Styles for <PERSON><PERSON> -->
<style>
    /* Modern Card Styles */
    .modern-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .modern-card-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-bottom: none;
    }

    .modern-card-body {
        background: white;
        padding: 2rem;
    }

    /* Filter Section Styles */
    .filter-section {
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: white;
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group-modern {
        display: flex;
        flex-direction: column;
    }

    .form-group-modern label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: white;
        font-size: 0.9rem;
    }

    .form-control-modern {
        padding: 0.75rem 1rem;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        color: white;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .form-control-modern:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.2);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    }

    .form-control-modern option {
        background: #333;
        color: white;
    }

    /* Button Styles */
    .btn-modern {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .btn-outline-modern {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-outline-modern:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .btn-success-modern {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        color: white;
    }

    .btn-danger-modern {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
    }

    .btn-info-modern {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    /* Table Styles */
    .table-modern {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem;
        font-weight: 600;
        text-align: left;
        border: none;
        font-size: 0.9rem;
    }

    .table-modern tbody td {
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
        vertical-align: middle;
        font-size: 0.9rem;
    }

    .table-modern tbody tr:hover {
        background-color: #f8f9fa;
    }

    .table-modern tbody tr:last-child td {
        border-bottom: none;
    }

    /* Toggle Sections */
    .toggle-section {
        margin-bottom: 1.5rem;
    }

    .toggle-header-modern {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 10px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .toggle-header-modern:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .toggle-content-modern {
        padding: 0 1rem;
    }

    /* Folder Entries */
    .folder-entries-modern {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .folder-entry-modern {
        background: linear-gradient(135deg, #34495e, #2c3e50);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .folder-entry-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .folder-date-modern {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .folder-count-modern {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    /* Action Icons */
    .action-icon {
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .action-view:hover {
        color: #007bff !important;
        transform: scale(1.2);
    }

    .action-download:hover {
        color: #28a745 !important;
        transform: scale(1.2);
    }

    .action-delete:hover {
        color: #dc3545 !important;
        transform: scale(1.2);
    }

    .action-details:hover {
        color: #17a2b8 !important;
        transform: scale(1.2);
    }

    /* Display Options */
    .display-options {
        background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
    }

    .checkbox-group {
        display: flex;
        gap: 2rem;
        flex-wrap: wrap;
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .checkbox-item input[type="checkbox"] {
        width: 1.2rem;
        height: 1.2rem;
        accent-color: #667eea;
    }

    .checkbox-item label {
        font-weight: 600;
        color: #333;
        cursor: pointer;
    }

    /* Navigation Buttons */
    .navigation-section {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
    }

    .navigation-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .nav-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .nav-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        color: white;
        text-decoration: none;
    }

    .nav-button.active {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .filter-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .folder-entries-modern {
            grid-template-columns: 1fr;
        }

        .checkbox-group {
            flex-direction: column;
            gap: 1rem;
        }

        .navigation-buttons {
            flex-direction: column;
        }
    }

    /* Urdu Text Support */
    .urdu-text {
        font-family: 'Jameel Noori Nastaleeq', 'Noto Nastaliq Urdu', serif;
        direction: rtl;
        text-align: right;
    }

    .question-text {
        font-family: 'Jameel Noori Nastaleeq', 'Noto Nastaliq Urdu', serif;
        direction: rtl;
        text-align: right;
        white-space: normal;
        word-wrap: break-word;
        max-width: 100%;
        color: #333;
        line-height: 1.6;
    }

    /* Chat Styles */
    .chat-container {
        max-height: 400px;
        overflow-y: auto;
    }

    .chat-message {
        margin-bottom: 1rem;
    }

    .chat-message.own {
        text-align: right;
    }

    .chat-message.other {
        text-align: left;
    }

    /* Loading Animation */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Utility Classes */
    .cursor-pointer {
        cursor: pointer;
    }

    .opacity-75 {
        opacity: 0.75;
    }

    .text-center {
        text-align: center;
    }

    .mb-0 {
        margin-bottom: 0;
    }

    .me-2 {
        margin-right: 0.5rem;
    }

    .ms-auto {
        margin-left: auto;
    }

    .d-flex {
        display: flex;
    }

    .align-items-center {
        align-items: center;
    }

    .justify-content-between {
        justify-content: space-between;
    }

    .gap-2 {
        gap: 0.5rem;
    }

    .gap-3 {
        gap: 1rem;
    }

    .fw-bold {
        font-weight: bold;
    }

    .fw-medium {
        font-weight: 500;
    }

    .text-primary {
        color: #667eea !important;
    }

    .text-success {
        color: #28a745 !important;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .text-warning {
        color: #ffc107 !important;
    }

    .text-info {
        color: #17a2b8 !important;
    }

    .text-muted {
        color: #6c757d !important;
    }

    .badge {
        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.375rem;
    }

    .bg-primary {
        background-color: #667eea !important;
    }

    .bg-success {
        background-color: #28a745 !important;
    }

    .bg-danger {
        background-color: #dc3545 !important;
    }

    .bg-warning {
        background-color: #ffc107 !important;
        color: #333 !important;
    }

    .bg-info {
        background-color: #17a2b8 !important;
    }

    .bg-secondary {
        background-color: #6c757d !important;
    }

    .bg-white {
        background-color: white !important;
    }

    .text-white {
        color: white !important;
    }

    /* Professional Statistics Summary */
    .stats-summary-professional {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        border: 1px solid #e9ecef;
    }

    .stat-item-professional {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.25rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .stat-item-professional:last-child {
        border-bottom: none;
    }

    .stat-label-professional {
        font-weight: 500;
        color: #495057;
        font-size: 0.9rem;
    }

    .stat-value-professional {
        font-weight: 600;
        color: #212529;
        font-size: 0.9rem;
    }

    .stat-value-professional.text-success {
        color: #28a745 !important;
    }

    .stat-value-professional.text-warning {
        color: #ffc107 !important;
    }

    .stat-value-professional.text-info {
        color: #17a2b8 !important;
    }

    .stat-value-professional.text-danger {
        color: #dc3545 !important;
    }

    /* Dark theme support for professional stats */
    .dark-theme .stats-summary-professional {
        background: #34495e;
        border-color: #2c3e50;
    }

    .dark-theme .stat-item-professional {
        border-bottom-color: #2c3e50;
    }

    .dark-theme .stat-label-professional {
        color: #bdc3c7;
    }

    .dark-theme .stat-value-professional {
        color: #ecf0f1;
    }

    /* Professional Stat Card for Additional Statistics */
    .professional-stat-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        transition: all 0.3s ease;
    }

    .professional-stat-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    /* Dark theme support for professional stat card */
    .dark-theme .professional-stat-card {
        background: #34495e;
        border-color: #2c3e50;
        color: #ecf0f1;
    }

    .dark-theme .professional-stat-card:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
</style>
