<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ViralLink extends Component
{
    use WithFileUploads;

    public $fileCode;
    public $viralDate;  // Add this at the top
    public $title;  // Initialize title if it exists
    public $viralUpload;
    public $daruliftaNames;
    public $shobaViralDate;
    public $ViralDays;
    public $toViralDate;
    public $fileErrorMessage;
    public $uploadedFile;
    public $fileName;
    public $webLink;
    public $viralLink;  // Add viralLink property
    public $isEditingWebLink = false;
    public $isEditingViralLink = false;  // Add isEditingViralLink property
    public $isEditingTitle = false;  // Add isEditingViralLink property

    public function mount($title, $fileName, $fileCode, $viralUpload, $webLink = null, $viralLink = null)  // Add viralLink parameter
    {
        $this->fileCode = $fileCode;
        $this->fileName = $fileName;
        $this->viralUpload = $viralUpload;
        $this->webLink = $webLink;
        $this->viralLink = $viralLink;  // Initialize viralLink if it exists
        $this->title = $title;  // Initialize title if it exists


        $fileRecord = DB::table('uploaded_files')
        ->where('checked_file_name', $this->fileName)
        ->where('checked_folder', 'ok')
        ->where('viral', '!=', 0)
        ->first();

    $this->shobaViralDate = $fileRecord ? $fileRecord->shoba_viral : null;
    $this->daruliftaNames = DB::table('daruliftas')
    ->pluck('darul_name')
    ->toArray();
    $this->viralDate = $fileRecord ? $fileRecord->viral_date : null;
    $this->toViralDate = $fileRecord ? $fileRecord->shoba_viral : null;
if (is_null($this->viralDate) && $this->toViralDate) {
        $this->ViralDays = Carbon::parse($this->toViralDate)->diffInDays(Carbon::now('Asia/Karachi'));
    } else {
        $this->ViralDays = null;
    }

    }



    // Handle viralLink updates
    public function updatedViralLink()
    {
        $currentDatePakistan = Carbon::now('Asia/Karachi')->toDateString();
        if (empty($this->viralLink)) {
        DB::table('uploaded_files')
            ->where('file_code', $this->fileCode)
            ->update([
                'viral_link' => null,
                'viral_date' => null
            ]);
            $this->viralDate = null;
        } else {
            DB::table('uploaded_files')
                ->where('file_code', $this->fileCode)
                ->update([
                    'viral_link' => $this->viralLink,
                    'viral_date' => $currentDatePakistan
                ]);
            $this->viralDate = $currentDatePakistan;
        }

        $this->dispatch('viralLinkUpdated');
    }


    // Methods for viral link editing
    public function enableViralLinkEditing()
    {
        $this->isEditingViralLink = true;
    }

    public function saveViralLink()
    {
        $this->updatedViralLink();
        $this->isEditingViralLink = false;
    }

    public function cancelViralLinkEditing()
    {
        $this->isEditingViralLink = false;
    }



    public function render()
    {
        return view('livewire.viral-link');
    }
}
