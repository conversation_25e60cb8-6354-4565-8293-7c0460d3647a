<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\PerformanceHoliday;
use Carbon\Carbon;

class PerformanceHolidayManagement extends Component
{
    use WithPagination;

    protected $layout = 'layouts.user_type.auth';

    public $showAddForm = false;
    public $editingId = null;
    
    // Form fields
    public $holiday_date = '';
    public $name = '';
    public $description = '';
    public $type = 'custom';
    
    // Filters
    public $filterType = '';
    public $filterYear = '';
    
    protected $rules = [
        'holiday_date' => 'required|date|after_or_equal:today',
        'name' => 'required|string|max:255',
        'description' => 'nullable|string|max:1000',
        'type' => 'required|in:religious,national,custom',
    ];

    protected $messages = [
        'holiday_date.required' => 'Holiday date is required.',
        'holiday_date.after_or_equal' => 'Holiday date must be today or in the future.',
        'name.required' => 'Holiday name is required.',
        'type.required' => 'Holiday type is required.',
    ];

    public function mount()
    {
        $this->filterYear = Carbon::now()->year;
    }

    public function render()
    {
        $query = PerformanceHoliday::with('createdBy')
            ->orderBy('holiday_date', 'desc');

        // Apply filters
        if ($this->filterType) {
            $query->where('type', $this->filterType);
        }

        if ($this->filterYear) {
            $query->whereYear('holiday_date', $this->filterYear);
        }

        $holidays = $query->paginate(10);

        return view('livewire.performance-holiday-management', [
            'holidays' => $holidays,
            'types' => [
                'religious' => 'Religious',
                'national' => 'National',
                'custom' => 'Custom'
            ]
        ]);
    }

    public function showAddForm()
    {
        $this->resetForm();
        $this->showAddForm = true;
    }

    public function hideAddForm()
    {
        $this->showAddForm = false;
        $this->editingId = null;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->holiday_date = '';
        $this->name = '';
        $this->description = '';
        $this->type = 'custom';
        $this->resetValidation();
    }

    public function save()
    {
        $this->validate();

        try {
            if ($this->editingId) {
                // Update existing holiday
                $holiday = PerformanceHoliday::findOrFail($this->editingId);
                $holiday->update([
                    'holiday_date' => $this->holiday_date,
                    'name' => $this->name,
                    'description' => $this->description,
                    'type' => $this->type,
                ]);
                
                session()->flash('success', 'Holiday updated successfully!');
            } else {
                // Create new holiday
                PerformanceHoliday::create([
                    'holiday_date' => $this->holiday_date,
                    'name' => $this->name,
                    'description' => $this->description,
                    'type' => $this->type,
                    'created_by' => auth()->id(),
                ]);
                
                session()->flash('success', 'Holiday added successfully!');
            }

            $this->hideAddForm();
        } catch (\Exception $e) {
            if (str_contains($e->getMessage(), 'Duplicate entry')) {
                session()->flash('error', 'A holiday already exists for this date.');
            } else {
                session()->flash('error', 'An error occurred while saving the holiday.');
            }
        }
    }

    public function edit($id)
    {
        $holiday = PerformanceHoliday::findOrFail($id);
        
        $this->editingId = $id;
        $this->holiday_date = $holiday->holiday_date->format('Y-m-d');
        $this->name = $holiday->name;
        $this->description = $holiday->description;
        $this->type = $holiday->type;
        
        $this->showAddForm = true;
    }

    public function toggleStatus($id)
    {
        $holiday = PerformanceHoliday::findOrFail($id);
        $holiday->update(['is_active' => !$holiday->is_active]);
        
        $status = $holiday->is_active ? 'activated' : 'deactivated';
        session()->flash('success', "Holiday {$status} successfully!");
    }

    public function delete($id)
    {
        try {
            PerformanceHoliday::findOrFail($id)->delete();
            session()->flash('success', 'Holiday deleted successfully!');
        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while deleting the holiday.');
        }
    }

    public function updatedFilterType()
    {
        $this->resetPage();
    }

    public function updatedFilterYear()
    {
        $this->resetPage();
    }
}
