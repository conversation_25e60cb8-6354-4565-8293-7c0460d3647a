<?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => '']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => '']); ?>

    <div>
        <div class="container position-sticky z-index-sticky top-0">
            <div class="row">
                <div class="col-12">
                    <!-- Navbar -->
                    <?php if (isset($component)) { $__componentOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.navs.guest','data' => ['signin' => 'static-sign-in','signup' => 'static-sign-up']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.navs.guest'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['signin' => 'static-sign-in','signup' => 'static-sign-up']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f)): ?>
<?php $attributes = $__attributesOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f; ?>
<?php unset($__attributesOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f)): ?>
<?php $component = $__componentOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f; ?>
<?php unset($__componentOriginal2fab3bdf6ae00eb5bf79e90e2eebce5f); ?>
<?php endif; ?>
                    <!-- End Navbar -->
                </div>
            </div>
        </div>
        <main class="main-content  mt-0">
            <section>
                <div class="page-header min-vh-100">
                    <div class="container">
                        <div class="row">
                            <div
                                class="col-6 d-lg-flex d-none h-100 my-auto pe-0 position-absolute top-0 start-0 text-center justify-content-center flex-column">
                                <div class="position-relative bg-gradient-primary h-100 m-3 px-7 border-radius-lg d-flex flex-column justify-content-center"
                                    style="background-image: url('<?php echo e(asset('assets')); ?>/img/illustrations/illustration-signup.jpg'); background-size: cover;">
                                </div>
                            </div>
                            <div
                                class="col-xl-4 col-lg-5 col-md-7 d-flex flex-column ms-auto me-auto ms-lg-auto me-lg-5">
                                <div class="card card-plain">
                                    <div class="card-header">
                                        <h4 class="font-weight-bolder">Show User</h4>
                                        
                                    </div>
                                    <div class="card-body">
                                        
                                            <label class="form-label">Name</label>
                                            <div class="input-group input-group-outline mb-3">
                                                
                                                <p><?php echo e($user->name); ?></p>
                                                
                                            </div>
                                            <label class="form-label">Email</label>
                                            <div class="input-group input-group-outline mb-3">
                                                
                                                <p><?php echo e($user->email); ?></p>
                                                

                                            </div>
                                            
                                            <label class="form-label">Roles</label>
                                            <div class="input-group input-group-outline mb-3">
                                                
                                                <p>
                                                    <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php echo e($role->name); ?> <?php echo e(!$loop->last ? ', ' : ''); ?>

                                                        
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </p>
                                                
                                            </div>
                                            
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/users/show.blade.php ENDPATH**/ ?>