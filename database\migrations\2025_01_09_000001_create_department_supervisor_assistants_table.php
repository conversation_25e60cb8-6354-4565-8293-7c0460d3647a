<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('department_supervisor_assistants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('role_type', ['supervisor', 'assistant']); // supervisor or assistant in this department
            $table->foreignId('supervisor_id')->nullable()->constrained('users')->onDelete('cascade'); // if assistant, who is their supervisor
            $table->timestamp('assigned_at')->useCurrent();
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            // Ensure a user can have only one role per department
            $table->unique(['department_id', 'user_id', 'role_type'], 'dept_user_role_unique');
            
            // Index for efficient lookups
            $table->index(['department_id', 'role_type', 'is_active'], 'dept_role_active_idx');
            $table->index(['user_id', 'role_type', 'is_active'], 'user_role_active_idx');
            $table->index(['supervisor_id', 'is_active'], 'supervisor_active_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('department_supervisor_assistants');
    }
};
