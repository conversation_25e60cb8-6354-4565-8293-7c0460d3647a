<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Locked - Fatawa Checking System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container-fluid vh-100 d-flex align-items-center justify-content-center"
     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="row justify-content-center w-100">
        <div class="col-lg-4 col-md-6 col-sm-8">
            <div class="card shadow-lg border-0" style="backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.95);">
                <div class="card-header bg-danger text-white text-center py-4">
                    <i class="fas fa-lock fa-3x mb-3"></i>
                    <h3 class="mb-0">Account Locked</h3>
                    <p class="mb-0 opacity-75">Performance submission required</p>
                </div>
                <div class="card-body p-4">
<div class="page-header align-items-start min-vh-100" 
     style="background-image: url('https://images.unsplash.com/photo-*************-9365093b7331?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1950&q=80'); background-position: center;">
    <span class="mask bg-gradient-dark opacity-6"></span>
    <div class="container my-auto">
        <div class="row">
            <div class="col-lg-4 col-md-8 col-12 mx-auto">
                <div class="card z-index-0 fadeIn3 fadeInBottom" style="backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.95);">
                    <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                        <div class="bg-gradient-danger shadow-danger border-radius-lg py-3 pe-1">
                            <div class="text-center">
                                <i class="fas fa-lock fa-3x text-white mb-2"></i>
                                <h4 class="font-weight-bolder text-white mt-1">Account Locked</h4>
                                <p class="mb-0 text-white text-sm">Performance submission required</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                    <div class="text-center mb-4">
                        <h5 class="fw-bold text-dark">{{ $user->name }}</h5>
                        <p class="text-muted">Your account has been temporarily locked due to missed daily performance submissions.</p>
                    </div>

                    @if($reason === 'performance_not_submitted')
                    <div class="alert alert-warning" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <div>
                                <strong>Missing Performance Reports</strong><br>
                                <small>You have {{ count($missedDays) }} missed working day(s) of performance submissions.</small>
                            </div>
                        </div>
                    </div>

                    <!-- Missed Days List -->
                    @if(!empty($missedDays))
                    <div class="mb-4">
                        <h6 class="fw-bold text-dark mb-2">Missed Days:</h6>
                        <div class="row">
                            @foreach(array_slice($missedDays, 0, 5) as $missedDay)
                            <div class="col-12 mb-1">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-calendar-times text-danger me-2"></i>
                                    <span class="small">{{ \Carbon\Carbon::parse($missedDay)->format('M d, Y (l)') }}</span>
                                </div>
                            </div>
                            @endforeach
                            @if(count($missedDays) > 5)
                            <div class="col-12">
                                <small class="text-muted">... and {{ count($missedDays) - 5 }} more day(s)</small>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif
                    @endif

                    <!-- Action Buttons -->
                    <div class="text-center">
                        <a href="{{ route('daily-performance.create') }}" class="btn btn-primary w-100 mb-3">
                            <i class="fas fa-clipboard-check me-2"></i>
                            Submit Performance Report
                        </a>

                        <a href="{{ route('dashboard') }}" class="btn btn-outline-secondary w-100 mb-2">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Go to Dashboard
                        </a>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <div class="text-center">
                            <i class="fas fa-info-circle text-info mb-2"></i>
                            <h6 class="fw-bold text-dark mb-1">Need Help?</h6>
                            <p class="small text-secondary mb-2">
                                If you believe this is an error or need assistance, please contact your Nazim or Admin.
                            </p>
                            <div class="d-flex justify-content-center">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Account locked on {{ now()->format('M d, Y \a\t g:i A') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Logout Option -->
                    <div class="text-center mt-3">
                        <form method="POST" action="{{ route('logout') }}" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-link text-secondary">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                Sign Out
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<style>
/* Animation for the lock icon */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.fa-lock {
    animation: shake 2s infinite;
}

/* Pulse effect for missed days */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.fa-calendar-times {
    animation: pulse 2s infinite;
}

/* Card hover effect */
.card {
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Button hover effects */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Auto-refresh every 30 seconds to check if account is unlocked
setTimeout(function() {
    window.location.reload();
}, 30000);

// Show loading state when clicking submit button
document.addEventListener('DOMContentLoaded', function() {
    const submitBtn = document.querySelector('a[href*="daily-performance"]');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            this.classList.add('disabled');
        });
    }
});
</script>
</body>
</html>
