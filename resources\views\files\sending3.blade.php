<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Recived Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- Create Appointment Entry Form -->
        <style>
                        .year {
                background-color: #7971ec; /* Year section color */
            }

            .month {
                background-color: #96c47d; /* Month section color */
            }

            .date {
                background-color: #c8d053; /* Date section color */
            }

            .file {
                background-color: #c6d2f4; /* Date section color */
            }
            .download-button {
                display: inline-block;
                padding: 10px 20px; /* Adjust the padding as needed */
                background-color: #007bff; /* Button background color */
                color: #fff; /* Text color */
                text-decoration: none;
                border: none;
                border-radius: 5px; /* Rounded corners */
                cursor: pointer;
            }

            .download-button:hover {
                background-color: #0056b3; /* Change color on hover */
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
        </style>
        <meta name="csrf-token" content="your-csrf-token-value">
        
        {{-- <div id="app" data-route="{{ route('store.selected.values') }}" data-csrf="{{ csrf_token() }}"></div> --}}

        <a href="{{ route('summary') }}" class="btn btn-primary" style="background-color: lightgray;">Summary</a>
        <a href="{{ route('recived') }}" class="btn btn-success" style="background-color: lightgray;">Recived Fatawa</a>
        <a href="{{ route('sending') }}" class="btn btn-success" style="background-color: rgb(115, 150, 110);">Sending Fatwaw</a>


        <h1>Recived Fatwa Form Darulifta</h1>
        
        {{-- <form action="/get-data" method="get">
            <select name="darulifta_name">
                <option value="">Select Darulifta Name</option>
                @foreach($daruliftaNames as $name)
                    <option value="{{ $name }}">{{ $name }}</option>
                @endforeach
            </select> --}}
        
        
        <form action="{{ route('update') }}" method="post">
            @csrf
            <!-- Display filtered data -->
            <table id="table1" border="1">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>File Name</th>
                        <th>Sender</th>
                        <th>Darulifta</th>
                        <th>Mail Folder Date</th>
                        <th>Checked File Name</th>
                        <th>Checked Folder Name</th>
                        <th>Checked Grade</th>
                        <th>Checked Tasurat</th>
                        <th>Checked Instructions</th>
                        <th>Selected</th>
                    </tr>
                </thead>
                <tbody>
                    @if (!empty($fatwaData))
                    @foreach ($fatwaData as $row)
                    <tr>
                        <td>{{ $row->id }}</td>
                        <td>{{ $row->file_name }}</td>
                        <td>{{ $row->sender }}</td>
                        <td>{{ $row->darulifta_name }}</td>
                        <td>{{ $row->mail_folder_date }}</td>
                        <!-- Add other table columns as needed -->
                        <td><input type="text" name="checked_file_name[]" data-id="{{ $row->id }}"></td>
                        <td><input type="text" name="checked_folder[]" data-id="{{ $row->id }}"></td>
                        <td>
                            <select name="checked_grade[]" data-id="{{ $row->id }}">
                                <option value="Munasib">Munasib</option>
                                <option value="Bhetar">Bhetar</option>
                                <option value="Mumtaz">Mumtaz</option>
                            </select>
                        </td>
                        <td><input type="text" name="checked_tasurat[]" data-id="{{ $row->id }}"></td>
                        <td><textarea name="checked_instructions[]" data-id="{{ $row->id }}"></textarea></td>
                        <td>
                            <input type="checkbox" name="selected[]" data-id="{{ $row->id }}" onchange="updateCheckboxValue(this)">
                            <input type="hidden" name="row_id[]" value="{{ $row->id }}">
                        </td>
                    </tr>
                    @endforeach
                @else
                    <tr>
                        <td colspan="11">
                            @if(isset($message))
                            <p>{{ $message }}</p>
                        @endif
                        </td>
                    </tr>
                @endif
            </tbody>
        </table>
            <!-- Single "Update" button -->
            <button type="submit">Update All</button>
        </form>
    </form>
        <!-- Add a new table to display file details -->
        <table id="table2" border="1">
            <thead>
                <tr>
                    
                    <th>Checked File Name</th>
                    <th>Checked Folder Name</th>
                    <th>Selected</th>
                    <th>ID</th>
                </tr>
            </thead>
            <tbody id="file-details">
                <!-- File details will be added here dynamically -->
            </tbody>
        </table>
        
<!-- File input elements -->
{{-- <input type="file" class="form-control" id="fileInput" multiple data-label="File Label" required> --}}

    
        <!-- JavaScript for filtering data without a button click -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        {{-- <script src="path/to/your/javascript-file.js"></script> --}}

        <script>
       
       // Function to update folder date options
//        function updatefolderdateOptions() {
//   const selectedDarul = document.getElementById('darulifta_name').value;
//   const folderSelect = document.getElementById('folder_select');
//   const selectedFolder = folderSelect.value; // Get the currently selected folder before clearing the options

//   // Clear existing options
//   folderSelect.innerHTML = '<option value="0">Select Folder</option>';

//   if (selectedDarul !== '0') {
//     fetch(`/get-folder/${selectedDarul}`)
//       .then(response => {
//         if (!response.ok) {
//           throw new Error('Network response was not ok');
//         }
//         return response.json();
//       })
//       .then(data => {
//         // Use a Set to store unique mail_folder_date values
//         const uniqueMailFolderDates = new Set();

//         // Iterate through the data and add mail_folder_date values to the Set
//         data.forEach(folder => {
//           uniqueMailFolderDates.add(folder.mail_folder_date);
//         });

//         // Convert the Set back to an array
//         const uniqueMailFolderDatesArray = [...uniqueMailFolderDates];

//         // Sort the unique values if needed
//         uniqueMailFolderDatesArray.sort();

//         // Populate the dropdown with unique values
//         uniqueMailFolderDatesArray.forEach(mailFolderDate => {
//           const option = document.createElement('option');
//           option.value = mailFolderDate;
//           option.textContent = mailFolderDate;
//           folderSelect.appendChild(option);
//         });

//         // Set the selectedFolder value back to the previously selected value
//         folderSelect.value = selectedFolder;

//         // Check if a folder is selected before sending the data to the server
//         if (selectedFolder !== '0') {
//   // Construct the route URL with selected data and query parameters
//   const route = `/store-selected-values/${selectedDarul}/${selectedFolder}`;

//   // Get the CSRF token from a meta tag in your HTML (make sure it exists in your HTML)
//   const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

//   // Create headers with the CSRF token
//   const headers = new Headers({
//     'Content-Type': 'application/json',
//     'X-CSRF-TOKEN': csrfToken, // Include the CSRF token in the request headers
//   });

//   // Send the selected values to the server using a GET request with query parameters
//   fetch(route, {
//     method: 'GET', // Use GET method
//     headers: headers, // Set the headers with CSRF token
//     // Include any data as query parameters
//     // Example: `/store-selected-values/${selectedDarul}/${selectedFolder}?param1=value1&param2=value2`
//   })
//             .then(response => {
//               if (!response.ok) {
//                 throw new Error('Server response was not ok');
//               }
//               // Handle success if needed
//               console.log('Selected values sent successfully');
//             })
//             .catch(error => {
//               // Handle any errors that occurred during the fetch.
//               console.error('Fetch error:', error);
//             });
//         }
//       })
//       .catch(error => {
//         // Handle any errors that occurred during the fetch.
//         console.error('Fetch error:', error);
//       });
//   }
// }

// // Add an event listener for the folder_select element
// document.getElementById('folder_select').addEventListener('change', function () {
//   // Call the updatefolderdateOptions() function when folder_select value changes
//   updatefolderdateOptions();

//   // You can also add any other code that should execute when the value changes
// });
// Add an event listener to the folder select element


        </script>

        <script>
            // $(document).ready(function() {
            //     $('form').on('submit', function(e) {
            //         e.preventDefault();
            //         $.ajax({
            //             type: 'GET',
            //             url: '/get-data',
            //             data: $(this).serialize(),
            //             success: function(response) {
            //                 $('#table1').html(response);
            //             }
            //         });
            //     });
            // });
        </script>
        <script>
//             document.getElementById('folder_select').addEventListener('change', function () {
//     const selectedFolder = this.value; // Get the selected folder value
//     const downloadLink = document.getElementById('download-link'); // Get the download link element

//     // Check if a folder is selected
//     if (selectedFolder !== '0') {
//         // Set the href attribute of the download link using the selected folder value
//         downloadLink.href = `/year-month/download-all/${selectedFolder}`; // Replace with your download route
//     } else {
//         // If no folder is selected, disable the download link or handle it as needed
//         downloadLink.href = '#'; // Set the href to '#' or an appropriate URL
//     }
// });
         
//   // Function to filter table data based on darulifta_name and mail_folder_date
//   function filterTableData() {
//         var daruliftaName = document.getElementById('darulifta_name').value;
//         var mailFolderDate = document.getElementById('folder_select').value;

//         rows.forEach(function (row) {
//     var daruliftaElement = row.querySelector('td:nth-child(4)');
//     var mailFolderDateElement = row.querySelector('td:nth-child(5)');

//     // Check if the elements exist
//     if (daruliftaElement && mailFolderDateElement) {
//         var rowDaruliftaName = daruliftaElement.textContent;
//         var rowMailFolderDate = mailFolderDateElement.textContent;

//             if (
//                 (mailFolderDate !== '' && rowMailFolderDate === mailFolderDate) && // Check mail_folder_date filter
//                 (daruliftaName === '' || rowDaruliftaName === daruliftaName) // Check darulifta_name filter
//             ) {
//                 row.style.display = 'table-row';
//             } else {
//                 row.style.display = 'none';
//             }
//   }});
//     }

//     // Listen for changes in the dropdowns and trigger the data fetch
//     var daruliftaName = document.getElementById('darulifta_name').value;
//     // Get the selected value from the mail-folder-date filter dropdown
// var mailFolderDate = document.getElementById('folder_select').value;


// document.getElementById('darulifta_name').addEventListener('change', filterTableData);
// document.getElementById('folder_select').addEventListener('change', filterTableData);

//     // Hide all rows initially
//     var rows = document.querySelectorAll('#table1 tbody tr');
//     rows.forEach(function (row) {
//         row.style.display = 'none';
//     });
//     function updateCheckboxValue(checkbox) {
//     var hiddenInput = checkbox.parentElement.querySelector('input[type="hidden"]');
//     hiddenInput.value = checkbox.checked ? "1" : "0";
// }

document.addEventListener('DOMContentLoaded', function () {
    // Function to add file details to the table
    function addFileDetails(file, label) {
        var tr = document.createElement('tr');

        // Display the file name in the "Checked File Name" column
        var tdCheckedFileName = document.createElement('td');
        tdCheckedFileName.textContent = file.name;
        tr.appendChild(tdCheckedFileName);

        // Display the label in the "Checked Folder Name" column
        var tdCheckedFolderName = document.createElement('td');
        tdCheckedFolderName.textContent = label;
        tr.appendChild(tdCheckedFolderName);

        // Find the corresponding ID from the first table
        var tdID = document.createElement('td');
        var correspondingID = findCorrespondingID(file.name);
        tdID.textContent = correspondingID;
        tr.appendChild(tdID);

        // Append the row to the table
        document.getElementById('file-details').appendChild(tr);

        // Now that files are in table2, trigger the function to populate table1
        populateTable1FromTable2();
    }

    // Function to find the corresponding ID in the first table
    function findCorrespondingID(fileName) {
        // Loop through the rows in the first table
        var firstTableRows = document.querySelectorAll('#table1 tbody tr');
        for (var i = 0; i < firstTableRows.length; i++) {
            var firstTableFileName = firstTableRows[i].querySelector('td:nth-child(2)').textContent;
            var firstTableID = firstTableRows[i].querySelector('td:nth-child(1)').textContent;

            // Check if the file name matches
            if (firstTableFileName === fileName) {
                return firstTableID;
            }
        }
        return 'Not Found'; // If no match is found
    }

    // Get all file input elements
    var fileInputs = document.querySelectorAll('input[type="file"]');

    // Listen for changes in the file inputs
    fileInputs.forEach(function (input) {
        input.addEventListener('change', function (e) {
            var files = e.target.files;
            var label = e.target.getAttribute('data-label');

            // Loop through selected files and add them to table2
            for (var i = 0; i < files.length; i++) {
                addFileDetails(files[i], label);
            }
        });
    });

    // Function to populate table1 from table2
    function populateTable1FromTable2() {
        // Get rows from both tables
        var table1Rows = document.querySelectorAll('#table1 tbody tr');
        var table2Rows = document.querySelectorAll('#table2 tbody tr');

        // Loop through table1 and table2 rows to populate data
        table1Rows.forEach(function (table1Row) {
            var table1FileName = table1Row.querySelector('td:nth-child(2)').textContent;

            table2Rows.forEach(function (table2Row) {
                var table2FileName = table2Row.querySelector('td:nth-child(1)').textContent;

                if (table1FileName === table2FileName) {
                    // Populate the corresponding columns in table1
                    table1Row.querySelector('input[name="checked_file_name[]"]').value = table2Row.querySelector('td:nth-child(1)').textContent;
                    table1Row.querySelector('input[name="checked_folder[]"]').value = table2Row.querySelector('td:nth-child(2)').textContent;
                    // You can populate other fields in a similar manner if needed
                    var checkbox = table1Row.querySelector('input[name="selected[]"]');
                    checkbox.checked = true;
                }
            });
        });
    }
});

        </script>
        
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
