<!-- Detailed Fatawa Section -->
@if($showDetail)
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <h5 class="mb-0">
                <i class="fas fa-list-alt me-2"></i>
                Reception Fatawa Details
            </h5>
        </div>

        <div class="modern-card-body">
            <div x-data="{ allOpen: false }" class="folder-toggle">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <button @click="allOpen = !allOpen" 
                            :aria-expanded="allOpen.toString()" 
                            class="btn btn-outline-secondary modern-btn">
                        <i class="fas fa-eye me-1"></i>
                        <span x-text="allOpen ? 'Hide All Details' : 'Show All Details'"></span>
                        <i :class="allOpen ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" class="ms-1"></i>
                    </button>
                </div>

                @foreach($daruliftaNames as $daruliftaName)
                    @if(isset($reciptionFatawa[$daruliftaName]))
                        <div x-data="{ open: true }" class="darulifta-section mb-4">
                            <div class="darulifta-header" @click="open = !open">
                                <h6 class="mb-0 cursor-pointer d-flex align-items-center">
                                    <i :class="open ? 'fas fa-chevron-down' : 'fas fa-chevron-right'" class="me-2"></i>
                                    <i class="fas fa-building me-2"></i>
                                    {{ $daruliftaName }}
                                </h6>
                            </div>
                            
                            <div x-show="open" class="darulifta-content mt-3">
                                @php
                                    $serialNumber_fl = 1;
                                    $mailfolderDateCount = 0;
                                    $colors = ['#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd'];
                                @endphp
                                
                                @foreach($recdate as $mailfolderDates)
                                    @if(isset($reciptionFatawa[$daruliftaName][$mailfolderDates]))
                                        @php
                                            $mailfolderDateCount = count($reciptionFatawa[$daruliftaName][$mailfolderDates]);
                                        @endphp

                                        <div class="folder-section mb-3" style="background-color: {{ $colors[$loop->index % count($colors)] }};">
                                            <div x-data="{ innerOpen: true }" x-init="$watch('allOpen', value => innerOpen = value)">
                                                <div class="folder-header p-3" @click="innerOpen = !innerOpen">
                                                    <h6 class="mb-0 cursor-pointer d-flex align-items-center justify-content-between">
                                                        <span>
                                                            <i :class="innerOpen ? 'fas fa-chevron-down' : 'fas fa-chevron-right'" class="me-2"></i>
                                                            <i class="fas fa-calendar-alt me-2"></i>
                                                            ({{ $serialNumber_fl++ }}) {{ $mailfolderDates }}
                                                        </span>
                                                        <span class="badge bg-primary">
                                                            <i class="fas fa-file-alt me-1"></i>
                                                            {{ $mailfolderDateCount }} Fatawa
                                                        </span>
                                                    </h6>
                                                </div>

                                                <div x-show="innerOpen" class="folder-content">
                                                    <div class="table-responsive">
                                                        <table class="table modern-table table-hover mb-0">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th class="text-center" style="width: 5%;">
                                                                        <i class="fas fa-hashtag me-1"></i>
                                                                        S.No
                                                                    </th>
                                                                    <th style="width: 10%;">
                                                                        <i class="fas fa-barcode me-1"></i>
                                                                        Fatwa No & Type
                                                                    </th>
                                                                    <th style="width: 15%;">
                                                                        <i class="fas fa-user me-1"></i>
                                                                        Sayel Info
                                                                    </th>
                                                                    <th style="width: 15%;">
                                                                        <i class="fas fa-tag me-1"></i>
                                                                        Category & Title
                                                                    </th>
                                                                    <th class="text-center" style="width: 10%;">
                                                                        <i class="fas fa-calendar me-1"></i>
                                                                        Rec Date
                                                                    </th>
                                                                    <th style="width: 10%;">
                                                                        <i class="fas fa-user-tie me-1"></i>
                                                                        Mujeeb
                                                                    </th>
                                                                    <th style="width: 10%;">
                                                                        <i class="fas fa-folder me-1"></i>
                                                                        M.Folder
                                                                    </th>
                                                                    <th style="width: 10%;">
                                                                        <i class="fas fa-info-circle me-1"></i>
                                                                        Status
                                                                    </th>
                                                                    <th style="width: 10%;">
                                                                        <i class="fas fa-clock me-1"></i>
                                                                        Expected Date
                                                                    </th>
                                                                    @if(Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray()))
                                                                        <th style="width: 5%;">
                                                                            <i class="fas fa-eye me-1"></i>
                                                                            Actions
                                                                        </th>
                                                                    @endif
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @php $serialNumber_re = 1; @endphp
                                                                
                                                                @foreach($reciptionFatawa[$daruliftaName][$mailfolderDates] as $file)
                                                                    <tr class="table-row-hover">
                                                                        <!-- Serial Number -->
                                                                        <td class="text-center">
                                                                            <span class="badge bg-secondary">{{ $serialNumber_re++ }}</span>
                                                                        </td>
                                                                        
                                                                        <!-- Fatwa Code & Type -->
                                                                        <td>
                                                                            <div class="d-flex flex-column">
                                                                                <span class="text-primary fw-bold">{{ $file->ifta_code }}</span>
                                                                                <span class="badge bg-{{ $file->question_type == 'Email' ? 'info' : 'success' }} badge-sm">
                                                                                    {{ $file->question_type }}
                                                                                </span>
                                                                            </div>
                                                                        </td>
                                                                        
                                                                        <!-- Sayel Info -->
                                                                        <td>
                                                                            <div class="sayel-info">
                                                                                <div class="fw-bold text-dark">{{ $file->sayel }}</div>
                                                                                <div class="text-muted small">
                                                                                    <i class="fas fa-envelope me-1"></i>{{ $file->email }}
                                                                                </div>
                                                                                <div class="text-muted small">
                                                                                    <i class="fas fa-map-marker-alt me-1"></i>{{ $file->address }}
                                                                                </div>
                                                                                <div class="text-muted small">
                                                                                    <i class="fas fa-phone me-1"></i>{{ $file->phone }}
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                        
                                                                        <!-- Category & Title -->
                                                                        <td>
                                                                            <div class="category-info">
                                                                                <div class="text-primary fw-bold">موضوع: {{ $file->issue }}</div>
                                                                                <div class="text-success">عنوان: {{ $file->question_title }}</div>
                                                                            </div>
                                                                        </td>
                                                                        
                                                                        <!-- Reception Date -->
                                                                        <td class="text-center">
                                                                            @php
                                                                                $recDate = new \DateTime($file->rec_date);
                                                                                $currentDate = now();
                                                                                $daysDifference = $currentDate->diff($recDate)->days;
                                                                            @endphp
                                                                            <div class="date-info">
                                                                                <div class="text-primary fw-bold">{{ $file->rec_date }}</div>
                                                                                <div class="text-muted small">{{ $daysDifference }} days ago</div>
                                                                            </div>
                                                                        </td>
                                                                        
                                                                        <!-- Mujeeb -->
                                                                        <td>
                                                                            <span class="badge bg-{{ $file->assign_id === null ? 'danger' : 'success' }}">
                                                                                {{ $file->assign_id === null ? 'Not Assigned' : $file->assign_id }}
                                                                            </span>
                                                                        </td>
                                                                        
                                                                        <!-- Mail Folder -->
                                                                        <td>
                                                                            @foreach ($mahlenazar_null as $mahle)
                                                                                @if ($file->ifta_code == $mahle->file_code)
                                                                                    <span class="text-info">{{ $mahle->mail_folder_date }}</span>
                                                                                @endif
                                                                            @endforeach
                                                                        </td>
                                                                        
                                                                        <!-- Status -->
                                                                        <td>
                                                                            @foreach ($notsentiftacode as $notsent)
                                                                                @if ($file->ifta_code != $notsent->ifta_code)
                                                                                    @php $statusFound = false; @endphp
                                                                                    
                                                                                    @foreach ($resultstatus as $status)
                                                                                        @if (Str::lower($status->file_code) == Str::lower($file->ifta_code))
                                                                                            @if ($status->checked_folder != null)
                                                                                                <span class="badge bg-success">{{ $status->checked_folder }}</span>
                                                                                            @else
                                                                                                <span class="badge bg-warning">Sent for Checking</span>
                                                                                                <div class="text-muted small">{{ $status->mail_folder_date }}</div>
                                                                                            @endif
                                                                                            @php $statusFound = true; break; @endphp
                                                                                        @endif
                                                                                    @endforeach
                                                                                    
                                                                                    @if (!$statusFound)
                                                                                        <span class="badge bg-danger">Not Sent for Checking</span>
                                                                                    @endif
                                                                                    @break
                                                                                @endif
                                                                            @endforeach
                                                                        </td>
                                                                        
                                                                        <!-- Expected Date -->
                                                                        <td class="text-center">
                                                                            @php
                                                                                $expectedDate = \Carbon\Carbon::parse($file->expected_date);
                                                                                $currentDate = \Carbon\Carbon::now();
                                                                                $isOverdue = $currentDate->greaterThan($expectedDate);
                                                                            @endphp
                                                                            <span class="badge bg-{{ $isOverdue ? 'danger' : 'success' }}">
                                                                                {{ $file->expected_date }}
                                                                            </span>
                                                                        </td>
                                                                        
                                                                        <!-- Actions (Admin Only) -->
                                                                        @if(Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray()))
                                                                            <td class="text-center">
                                                                                <a href="{{ route('fatwa-detail', ['fatwa' => $file->ifta_code]) }}" 
                                                                                   target="_blank" 
                                                                                   class="btn btn-sm btn-outline-primary">
                                                                                    <i class="fas fa-eye"></i>
                                                                                </a>
                                                                            </td>
                                                                        @endif
                                                                    </tr>
                                                                    
                                                                    <!-- Question Row -->
                                                                    <tr x-data="{ openQuestion: false }">
                                                                        <td colspan="1" 
                                                                            @click="openQuestion = !openQuestion" 
                                                                            class="text-center cursor-pointer bg-light">
                                                                            <span class="fw-bold">
                                                                                <i class="fas fa-question-circle me-1"></i>
                                                                                Question
                                                                                <i :class="openQuestion ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" class="ms-1"></i>
                                                                            </span>
                                                                        </td>
                                                                        <td x-show="openQuestion" 
                                                                            colspan="{{ Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ? '9' : '8' }}" 
                                                                            class="question-cell">
                                                                            <div class="question-text p-3">
                                                                                <strong>سوال:</strong> {{ $file->question }}
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                    
                                                                    <!-- Chat Row (if enabled) -->
                                                                    @if ($showChat)
                                                                        <tr x-data="{ openChat: false }">
                                                                            <td colspan="1" 
                                                                                @click="openChat = !openChat" 
                                                                                class="text-center cursor-pointer bg-light">
                                                                                <span class="fw-bold">
                                                                                    <i class="fas fa-comments me-1"></i>
                                                                                    Chat
                                                                                    <i :class="openChat ? 'fas fa-chevron-up' : 'fas fa-chevron-down'" class="ms-1"></i>
                                                                                </span>
                                                                            </td>
                                                                            <td x-show="openChat" 
                                                                                colspan="{{ Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ? '9' : '8' }}" 
                                                                                class="chat-cell">
                                                                                <div class="chat-container p-3">
                                                                                    @include('livewire.partials.reciption-fatawa-chat', ['file' => $file])
                                                                                </div>
                                                                            </td>
                                                                        </tr>
                                                                    @endif
                                                                @endforeach
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
@endif
