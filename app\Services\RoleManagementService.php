<?php

namespace App\Services;

use App\Models\User;
use App\Models\Role;
use App\Models\SupervisorAssistant;
use App\Models\UserRestriction;
use Illuminate\Support\Facades\DB;

class RoleManagementService
{
    /**
     * Assign Superior role to a user and manage their assistants.
     */
    public function assignSuperiorRole(User $user, array $assistantIds, User $assignedBy): bool
    {
        DB::beginTransaction();
        
        try {
            // Add Superior role
            $superiorRole = Role::where('name', 'Superior')->first();
            if ($superiorRole && !$user->roles->contains($superiorRole)) {
                $user->roles()->attach($superiorRole);
            }

            // Assign assistants
            $this->assignAssistants($user, $assistantIds, $assignedBy);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Assign assistants to a supervisor.
     */
    public function assignAssistants(User $supervisor, array $assistantIds, User $assignedBy): void
    {
        // Deactivate existing assignments
        SupervisorAssistant::where('supervisor_id', $supervisor->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        // Create new assignments
        foreach ($assistantIds as $assistantId) {
            SupervisorAssistant::create([
                'supervisor_id' => $supervisor->id,
                'assistant_id' => $assistantId,
                'assigned_by' => $assignedBy->id,
                'is_active' => true,
            ]);
        }
    }

    /**
     * Remove Superior role and deactivate assistant relationships.
     */
    public function removeSuperiorRole(User $user): bool
    {
        DB::beginTransaction();
        
        try {
            // Remove Superior role
            $superiorRole = Role::where('name', 'Superior')->first();
            if ($superiorRole) {
                $user->roles()->detach($superiorRole);
            }

            // Deactivate all assistant relationships
            SupervisorAssistant::where('supervisor_id', $user->id)
                ->where('is_active', true)
                ->update(['is_active' => false]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Get all users eligible to be Superiors (Mujeeb users).
     */
    public function getEligibleSuperiors(): \Illuminate\Database\Eloquent\Collection
    {
        return User::whereHas('roles', function ($query) {
            $query->where('name', 'mujeeb');
        })->get();
    }

    /**
     * Get all users eligible to be assistants (Mujeeb users not already Superior).
     */
    public function getEligibleAssistants(): \Illuminate\Database\Eloquent\Collection
    {
        return User::whereHas('roles', function ($query) {
            $query->where('name', 'mujeeb');
        })->whereDoesntHave('roles', function ($query) {
            $query->where('name', 'Superior');
        })->get();
    }

    /**
     * Get supervisor-assistant mappings.
     */
    public function getSupervisorAssistantMappings(): \Illuminate\Database\Eloquent\Collection
    {
        return SupervisorAssistant::with(['supervisor', 'assistant', 'assignedBy'])
            ->where('is_active', true)
            ->get()
            ->groupBy('supervisor_id');
    }

    /**
     * Lock a user account.
     */
    public function lockUser(User $user, string $reason, User $restrictedBy): void
    {
        UserRestriction::create([
            'user_id' => $user->id,
            'restriction_type' => 'manual_lock',
            'reason' => $reason,
            'restricted_by' => $restrictedBy->id,
            'is_active' => true,
        ]);
    }

    /**
     * Unlock a user account.
     */
    public function unlockUser(User $user, string $reason, User $liftedBy): void
    {
        UserRestriction::where('user_id', $user->id)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $liftedBy->id,
                'lifted_at' => now(),
                'lift_reason' => $reason,
            ]);
    }

    /**
     * Check if a user can be assigned as Superior.
     */
    public function canBeAssignedAsSuperior(User $user): bool
    {
        // Must be a Mujeeb and not already a Superior
        return $user->hasRole('mujeeb') && !$user->hasRole('Superior');
    }

    /**
     * Check if a user can be assigned as Assistant.
     */
    public function canBeAssignedAsAssistant(User $user): bool
    {
        // Must be a Mujeeb and not already assigned to another Superior
        if (!$user->hasRole('mujeeb')) {
            return false;
        }

        // Check if already assigned as assistant
        return !SupervisorAssistant::where('assistant_id', $user->id)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Get users by role.
     */
    public function getUsersByRole(string $roleName): \Illuminate\Database\Eloquent\Collection
    {
        return User::whereHas('roles', function ($query) use ($roleName) {
            $query->where('name', $roleName);
        })->get();
    }

    /**
     * Assign multiple roles to a user.
     */
    public function assignRoles(User $user, array $roleNames): void
    {
        // Get role IDs from names
        $roleIds = Role::whereIn('name', $roleNames)->pluck('id')->toArray();

        // Sync roles (this will remove old roles and add new ones)
        $user->roles()->sync($roleIds);
    }

    /**
     * Remove a specific role from a user.
     */
    public function removeRole(User $user, string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();

        if ($role) {
            $user->roles()->detach($role->id);
        }
    }

    /**
     * Add a role to a user without removing existing roles.
     */
    public function addRole(User $user, string $roleName): void
    {
        $role = Role::where('name', $roleName)->first();

        if ($role && !$user->roles->contains($role->id)) {
            $user->roles()->attach($role->id);
        }
    }
}
