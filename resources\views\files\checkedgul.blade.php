<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="checkedgul"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Checked Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- Create Appointment Entry Form -->
        <style>
                        .year {
                background-color: #7971ec; /* Year section color */
            }

            .month {
                background-color: #96c47d; /* Month section color */
            }

            .date {
                background-color: #c8d053; /* Date section color */
            }

            .file {
                background-color: #c6d2f4; /* Date section color */
            }
            .download-button {
                display: inline-block;
                padding: 10px 20px; /* Adjust the padding as needed */
                background-color: #007bff; /* Button background color */
                color: #fff; /* Text color */
                text-decoration: none;
                border: none;
                border-radius: 5px; /* Rounded corners */
                cursor: pointer;
            }

            .download-button:hover {
                background-color: #0056b3; /* Change color on hover */
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
        </style>
        @if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) || in_array('Mufti', Auth::user()->roles->pluck('name')->toArray()))
        {{-- <a href="{{ route('summary') }}" class="btn btn-primary" style="background-color: lightgray;">Summary</a> --}}
        <a href="{{ route('recivedNor') }}" class="btn btn-success" style="background-color: lightgray;">Recived Fatawa</a>
        <a href="{{ route('store.selected.values') }}" class="btn btn-success" style="background-color: lightgray;">Sending Fatwaw</a>
        <a href="{{ route('checkedNor') }}" class="btn btn-success" style="background-color: rgb(115, 150, 110);">Checked Fatawa</a>

        <h1>Checked Fatawa</h1>
        <a href="{{ route('checkedNor') }}" class="btn btn-primary" style="background-color: lightgray;">Noorulirfan</a>
        <a href="{{ route('checkedFaj') }}" class="btn btn-success" style="background-color: lightgray;">Faizan-e-Ajmair</a>
        <a href="{{ route('checkedGul') }}" class="btn btn-success" style="background-color: rgb(115, 150, 110);">Gulzar-e-Taiba</a>
        <a href="{{ route('checkedIec') }}" class="btn btn-success" style="background-color: lightgray;">Markaz-ul-Iqtisad</a>
        
        @else
        
        <h1>Checked Fatawa From Mufti Sahib</h1>
            
        @endif
        <div class="mb-3 float-end">
            <label for="searchInput" class="form-label">Search:</label>
            <input type="text" id="searchInput" placeholder="Enter For Search">
        </div>
        {{-- @if(count(Auth::user()->roles) > 1)
        <label for="select-role">Select Role:</label>
        <select id="select-role" name="selected_role">
            @foreach(Auth::user()->roles as $role)
                @if(in_array($role->name, ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']))
                    <option value="{{ $role->name }}">{{ $role->name }}</option>
                @endif
            @endforeach
        </select>
    @else
        <p id="single-role">{{ Auth::user()->roles->first()->name }}</p>
    @endif --}}
        <table class="table table-bordered" id="yearMonthTable">
            <tbody>
                
                @foreach ($data as $year => $months)
                <tr class="year">
                    <td>
                        <a href="#" class="toggle-row" data-target=".year-{{ $year }}">
                            <span class="toggle-arrow">▼</span> {{ $year }}
                        </a>
                    </td>
                </tr>
                @foreach ($months as $month => $dates)
                <tr class="month year-{{ $year }}">
                    <td>
                        <a href="#" class="toggle-row" data-target=".month-{{ $year }}-{{ $month }}">
                            <span class="toggle-arrow">▼</span> {{ $month }}
                        </a>
                    </td>
                </tr>
                @foreach ($dates as $date => $files)
    <tr class="date year-{{ $year }} month-{{ $year }}-{{ $month }}">
        <td>
            <div class="d-flex justify-content-between align-items-center">
                <a href="#" class="toggle-row" data-target=".date-{{ $year }}-{{ $month }}-{{ $date }}">
                    <span class="toggle-arrow">▼</span> {{ $date }}
                </a>

                <div class="d-flex">
                    <a href="{{ route('downloadAll', ['date' => $date . $datesInfo[$loop->index]['darulifta_name']]).'Checked' }}">
                        <button type="download" style="background-color: #007bff" class="btn btn-primary  mx-1">Download Folder</button>
                    </a>
                    @if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) || in_array('Mufti', Auth::user()->roles->pluck('name')->toArray()))
                    <form method="POST" action="{{ route('checkfolder.delete', ['path' => $date]) }}">
                        @csrf
                        @method('DELETE')
                        <input type="hidden" name="darulifta_name" value="{{ $datesInfo[$loop->index]['darulifta_name']}}">
                        <button type="submit" class="btn btn-danger mx-1">Delete Folder</button>
                    </form>
                    @endif
                </div>
            </div>
        </td>
    </tr>
    @foreach ($files as $file)
                {{-- @if(in_array($file['darulifta_name'], $userRoles)) --}}
                <tr class="file date-{{ $year }}-{{ $month }}-{{ $date }}">
                    <td class="d-flex flex-wrap align-items-center">
                        <div class="mb-2">
                        <a href="{{ route('downloadCheck',
                         ['date' => $date . $file['darulifta_name'] .'Checked',
                          'folder' => $file['checked_folder'],
                           'filename' => $file['file_name']]) }}">
                                      {{ $file['file_name'] }}
                        </a>
                    </div>
                    <div class="d-flex justify-content-center mb-2">
                                    | {{ $file['file_code'] }}
                                    | {{ $file['sender'] }}
                                    | {{ $file['darulifta_name'] }}
                                    | {{ $file['checked_folder'] }}
                                    | {{ $file['checked_grade'] }}
                                    | {{ $file['checked_tasurat'] }}
                                    | {{ $file['checked_Instructions'] }}
                                </div>
                                @if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) || in_array('Mufti', Auth::user()->roles->pluck('name')->toArray()))
                                <div class="d-flex justify-content-center mb-2">
                                    
                                    <form method="POST" action="{{ route('checkfile.delete',
                                     ['path' => $date . $file['darulifta_name'] .'Checked']) }}">
                                        @csrf
                                        @method('DELETE')
                                        <input type="hidden" name="id" value="{{ $file['id'] }}">
                                        <input type="hidden" name="file_name" value="{{ $file['file_name'] }}">
                                        <input type="hidden" name="folder_name" value="{{ $file['checked_folder'] }}">
                                        {{-- <button type="submit" class="btn btn-danger ml-2 mb-2">Delete File</button> --}}

                                        <button type="submit" style="background-color: tomato" class="highlight-text ml-2 mb-2">Delete File</button>
                                    </form>
                                </div>
                                @endif
                                </td>
                            </tr>
                        {{-- @endif --}}
                    @endforeach
                @endforeach
            @endforeach
        @endforeach
    </tbody>
</table>  
<script>
    jQuery(document).ready(function () {
        // Initially hide month, date, and file sections
        $('#yearMonthTable tbody tr.file').hide();

        // Show year sections initially
        $('#yearMonthTable tbody tr.year').show();

        // Add click event handler for toggling rows
        $('.toggle-row').click(function () {
            var target = $(this).data('target');
            $(target).toggle();
            $(this)
                .find('.toggle-arrow')
                .text(function (_, text) {
                    return text === '▼' ? '▲' : '▼';
                });
        });

        // Add input event handler for search input
        $('#searchInput').on('input', function () {
            var searchTerm = $(this).val().toLowerCase();

            // Hide all rows initially
            $('#yearMonthTable tbody tr').hide();

            // Show rows that match the search term
            $('#yearMonthTable tbody tr.year, #yearMonthTable tbody tr.month, #yearMonthTable tbody tr.date, #yearMonthTable tbody tr:contains("' + searchTerm + '")').show();

            // If the search input is empty, show the initial rows
            if (searchTerm === '') {
                $('#yearMonthTable tbody tr.file').hide();
            }
        });
    });

    // Extend jQuery to support case-insensitive :contains
    jQuery.expr[':'].contains = function(a, i, m) {
        return jQuery(a).text().toLowerCase().indexOf(m[3].toLowerCase()) >= 0;
    };
</script>
        
        
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
