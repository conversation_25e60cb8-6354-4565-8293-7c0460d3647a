<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Department Management</h5>
                            <p class="text-sm mb-0">Manage departments and assign users</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <div class="btn-group me-2" role="group">
                                    <a href="{{ route('supervisor-assistant-mapping') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-users"></i>&nbsp;&nbsp;Team Mapping
                                    </a>
                                    <a href="{{ route('workflow-tasks.index') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-tasks"></i>&nbsp;&nbsp;Tasks
                                    </a>
                                </div>
                                <button wire:click="openCreateModal" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-plus"></i>&nbsp;&nbsp;Add Department
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search departments...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select wire:model.live="filterActive" class="form-select">
                                    <option value="all">All Departments</option>
                                    <option value="active">Active Only</option>
                                    <option value="inactive">Inactive Only</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Departments Table -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Department</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Description</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Team</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($departments as $department)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $department->name }}</h6>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs text-secondary mb-0">
                                            {{ Str::limit($department->description, 50) }}
                                        </p>
                                    </td>
                                    <td class="align-middle text-center text-sm">
                                        <div class="d-flex flex-column align-items-center">
                                            @if($department->users_count > 0)
                                                <span class="badge badge-sm bg-gradient-primary mb-1">
                                                    <i class="fas fa-user me-1"></i>{{ $department->users_count }} User{{ $department->users_count > 1 ? 's' : '' }}
                                                </span>
                                            @endif
                                            @if($department->supervisors_count > 0)
                                                <span class="badge badge-sm bg-gradient-success mb-1">
                                                    <i class="fas fa-crown me-1"></i>{{ $department->supervisors_count }} Supervisor{{ $department->supervisors_count > 1 ? 's' : '' }}
                                                </span>
                                            @endif
                                            @if($department->assistants_count > 0)
                                                <span class="badge badge-sm bg-gradient-info">
                                                    <i class="fas fa-users me-1"></i>{{ $department->assistants_count }} Assistant{{ $department->assistants_count > 1 ? 's' : '' }}
                                                </span>
                                            @endif
                                            @if($department->users_count == 0 && $department->supervisors_count == 0 && $department->assistants_count == 0)
                                                <span class="badge badge-sm bg-gradient-secondary">No team assigned</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $department->is_active ? 'success' : 'secondary' }}">
                                            {{ $department->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-2">
                                            <button wire:click="openEditModal({{ $department->id }})" 
                                                    class="btn btn-sm btn-outline-primary mb-0">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button wire:click="openAssignModal({{ $department->id }})" 
                                                    class="btn btn-sm btn-outline-info mb-0">
                                                <i class="fas fa-users"></i>
                                            </button>
                                            <button wire:click="toggleStatus({{ $department->id }})" 
                                                    class="btn btn-sm btn-outline-{{ $department->is_active ? 'warning' : 'success' }} mb-0">
                                                <i class="fas fa-{{ $department->is_active ? 'pause' : 'play' }}"></i>
                                            </button>
                                            @if($department->users_count == 0 && $department->team_members_count == 0)
                                            <button wire:click="deleteDepartment({{ $department->id }})"
                                                    wire:confirm="Are you sure you want to delete this department?"
                                                    class="btn btn-sm btn-outline-danger mb-0">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <p class="text-secondary mb-0">No departments found.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="px-4">
                        {{ $departments->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Create Department Modal -->
    @if($showCreateModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create Department</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="createDepartment">
                        <div class="mb-3">
                            <label class="form-label">Department Name *</label>
                            <input type="text" wire:model="name" class="form-control @error('name') is-invalid @enderror">
                            @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control @error('description') is-invalid @enderror" rows="3"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" wire:model="is_active" class="form-check-input" id="createActive">
                                <label class="form-check-label" for="createActive">Active</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="createDepartment">Create Department</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Edit Department Modal -->
    @if($showEditModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Department</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="updateDepartment">
                        <div class="mb-3">
                            <label class="form-label">Department Name *</label>
                            <input type="text" wire:model="name" class="form-control @error('name') is-invalid @enderror">
                            @error('name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control @error('description') is-invalid @enderror" rows="3"></textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" wire:model="is_active" class="form-check-input" id="editActive">
                                <label class="form-check-label" for="editActive">Active</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="updateDepartment">Update Department</button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Assign Users Modal -->
    @if($showAssignModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Assign Users to Department</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <label class="form-label">Select Users:</label>
                            <div class="row">
                                @foreach($availableUsers as $user)
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input type="checkbox"
                                               wire:model="selectedUsers"
                                               value="{{ $user->id }}"
                                               class="form-check-input"
                                               id="user{{ $user->id }}">
                                        <label class="form-check-label" for="user{{ $user->id }}">
                                            {{ $user->name }} ({{ $user->email }})
                                        </label>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="assignUsers">Assign Users</button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
