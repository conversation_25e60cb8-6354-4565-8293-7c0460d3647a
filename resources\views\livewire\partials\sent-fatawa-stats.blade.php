<!-- Statistics Section -->
@php
    $totalCounts = 0;
    $overallFolderCount = 0;
    $totalOkCount = 0;
    $totalMahlENazarCount = 0;
    $totalDuplicateFileCodes = 0;
    $totalUniqueFileCodes = 0;
    $seenFileCodes = [];

    // Calculate statistics
    foreach ($sendingFatawa as $daruliftaName => $checkers) {
        foreach ($checkers as $checked => $mailfolderGroups) {
            foreach ($mailfolderGroups as $mailfolderDates => $fatawaData) {
                $overallFolderCount++;
                foreach ($fatawaData as $file) {
                    $totalCounts++;

                    // Count by checked status
                    if ($file->checked_folder == 'ok') {
                        $totalOkCount++;
                    } elseif ($file->checked_folder == 'Mahl-e-Nazar') {
                        $totalMahlENazarCount++;
                    }

                    // Count duplicates
                    $fileCode = $file->file_code;
                    if (isset($seenFileCodes[$fileCode])) {
                        if ($seenFileCodes[$fileCode] == 1) {
                            $totalDuplicateFileCodes++;
                            $seenFileCodes[$fileCode]++;
                        }
                    } else {
                        $seenFileCodes[$fileCode] = 1;
                        $totalUniqueFileCodes++;
                    }
                }
            }
        }
    }

    $totalRepeatedFatawa = $totalCounts - ($totalUniqueFileCodes - $totalDuplicateFileCodes);
    $totalActualUnique = $totalUniqueFileCodes - $totalDuplicateFileCodes;
@endphp

<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            Sent Fatawa Statistics
        </h5>
        <p class="mb-0 opacity-75">Overview of all sent fatawa data and metrics</p>
    </div>
    <div class="modern-card-body">
        <div class="stats-grid">
            <!-- Total Fatawa -->
            <div class="stat-card">
                <div class="stat-number">{{ number_format($totalCounts) }}</div>
                <div class="stat-label">
                    <i class="fas fa-file-alt me-1"></i>
                    Total Fatawa
                </div>
            </div>

            <!-- Total Folders -->
            <div class="stat-card">
                <div class="stat-number">{{ number_format($overallFolderCount) }}</div>
                <div class="stat-label">
                    <i class="fas fa-folder me-1"></i>
                    Total Folders
                </div>
            </div>

            <!-- OK Fatawa -->
            <div class="stat-card">
                <div class="stat-number text-success">{{ number_format($totalOkCount) }}</div>
                <div class="stat-label">
                    <i class="fas fa-check-circle me-1"></i>
                    OK Fatawa
                </div>
            </div>

            <!-- Mahl-e-Nazar -->
            <div class="stat-card">
                <div class="stat-number text-warning">{{ number_format($totalMahlENazarCount) }}</div>
                <div class="stat-label">
                    <i class="fas fa-eye me-1"></i>
                    Mahl-e-Nazar
                </div>
            </div>

            <!-- Unique Fatawa -->
            <div class="stat-card">
                <div class="stat-number text-info">{{ number_format($totalActualUnique) }}</div>
                <div class="stat-label">
                    <i class="fas fa-star me-1"></i>
                    Unique Fatawa
                </div>
            </div>

            <!-- Repeated Fatawa -->
            <div class="stat-card">
                <div class="stat-number text-danger">{{ number_format($totalRepeatedFatawa) }}</div>
                <div class="stat-label">
                    <i class="fas fa-copy me-1"></i>
                    Total Repeated
                </div>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="professional-stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-percentage me-2 text-success"></i>Success Rate (OK)</span>
                        <strong class="text-success">{{ $totalCounts > 0 ? number_format(($totalOkCount / $totalCounts) * 100, 1) : 0 }}%</strong>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="professional-stat-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-chart-pie me-2 text-warning"></i>Review Rate (Mahl-e-Nazar)</span>
                        <strong class="text-warning">{{ $totalCounts > 0 ? number_format(($totalMahlENazarCount / $totalCounts) * 100, 1) : 0 }}%</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
