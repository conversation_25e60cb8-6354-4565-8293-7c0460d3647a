<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class TransferredFatawa extends Component
{
    use WithPagination;

    public $search = '';
    public $editFatwaId = null;
    public $newSender = '';
    public $mujeebs = [];
    public $confirmingDelete = false;
    public $deleteFatwaId = null;
    public $successMessage = '';
    public $errorMessage = '';

    protected $listeners = ['refresh' => '$refresh'];

    protected $queryString = [
        'search' => ['except' => '']
    ];

    public function mount()
    {
        // Check if user is admin
        if (!Auth::check() || !in_array('Admin', Auth::user()->roles->pluck('name')->toArray())) {
            return redirect()->route('dashboard');
        }

        $this->loadMujeebs();
    }

    private function loadMujeebs()
    {
        // Get all distinct senders from uploaded_files table
        $this->mujeebs = DB::table('uploaded_files')
            ->select('sender')
            ->distinct()
            ->whereNotNull('sender')
            ->where('sender', '!=', '')
            ->orderBy('sender')
            ->pluck('sender')
            ->toArray();
    }

    public function startEdit($fatwaId)
    {
        $this->editFatwaId = $fatwaId;
        $fatwa = DB::table('uploaded_files')->where('id', $fatwaId)->first();
        if ($fatwa) {
            $this->newSender = $fatwa->sender;
        }
    }

    public function cancelEdit()
    {
        $this->editFatwaId = null;
        $this->newSender = '';
        $this->resetValidation();
    }

    public function updateFatwa()
    {
        $this->validate([
            'newSender' => 'required|string',
        ], [
            'newSender.required' => 'Please select a sender.'
        ]);

        try {
            // Get the fatwa record first
            $fatwa = DB::table('uploaded_files')->where('id', $this->editFatwaId)->first();
            
            if (!$fatwa) {
                $this->errorMessage = 'Fatwa not found.';
                return;
            }
            
            // Make sure file_created_date is set if not already available
            $fileCreatedDate = $fatwa->file_created_date ?? now()->toDateString();
            
            DB::table('uploaded_files')
                ->where('id', $this->editFatwaId)
                ->update([
                    'sender' => $this->newSender,
                    'file_created_date' => $fileCreatedDate,
                    'updated_at' => now()
                ]);

            $this->successMessage = 'Fatwa updated successfully.';
            $this->editFatwaId = null;
            $this->newSender = '';
        } catch (\Exception $e) {
            $this->errorMessage = 'Error updating fatwa: ' . $e->getMessage();
        }
    }

    public function confirmDelete($fatwaId)
    {
        $this->confirmingDelete = true;
        $this->deleteFatwaId = $fatwaId;
    }

    public function cancelDelete()
    {
        $this->confirmingDelete = false;
        $this->deleteFatwaId = null;
    }

    public function deleteFatwa()
    {
        try {
            DB::table('uploaded_files')
                ->where('id', $this->deleteFatwaId)
                ->delete();

            $this->successMessage = 'Fatwa deleted successfully.';
            $this->confirmingDelete = false;
            $this->deleteFatwaId = null;
        } catch (\Exception $e) {
            $this->errorMessage = 'Error deleting fatwa: ' . $e->getMessage();
        }
    }

    public function render()
    {
        $query = DB::table('uploaded_files')
            ->where('checked_folder', 'Assigned')
            ->when($this->search, function ($query) {
                return $query->where(function ($query) {
                    $query->where('file_code', 'like', '%' . $this->search . '%')
                        ->orWhere('sender', 'like', '%' . $this->search . '%')
                        ->orWhere('darulifta_name', 'like', '%' . $this->search . '%')
                        ->orWhere('category', 'like', '%' . $this->search . '%');
                });
            })
            ->select('uploaded_files.*')
            ->orderBy('updated_at', 'desc');

        $transferredFatawa = $query->paginate(20);
        
        // Get mahlenazar_null data for Mail Folder Dates trail
        $mahlenazar_null = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select('file_code', 'mail_folder_date', 'sender', 'file_name', 'checker', 'by_mufti', 'darulifta_name')
            ->get();

        return view('livewire.transferred-fatawa', [
            'transferredFatawa' => $transferredFatawa,
            'mahlenazar_null' => $mahlenazar_null
        ])->layout('layouts.app');
    }
}
