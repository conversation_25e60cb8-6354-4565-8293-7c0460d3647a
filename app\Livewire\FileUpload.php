<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;

class FileUpload extends Component
{
    use WithFileUploads;

    public $ok = [];
    public $mahlENazar = [];
    public $tahqiqi = [];
    public $other = [];

    public $selectedDarul;
    public $selectedFolder;
    public $checker;

    public $fileList = [
        'ok' => [],
        'Mahl-e-Nazar' => [],
        'Tahqiqi' => [],
        'Other' => [],
    ];

    public function mount($selectedDarul, $selectedFolder, $checker)
    {
        $this->selectedDarul = $selectedDarul;
        $this->selectedFolder = $selectedFolder;
        $this->checker = $checker;
    }

    public function updatedOk()
    {
        $this->uploadFiles($this->ok, 'ok');
    }

    public function updatedMahlENazar()
    {
        $this->uploadFiles($this->mahlENazar, 'Mahl-e-Nazar');
    }

    public function updatedTahqiqi()
    {
        $this->uploadFiles($this->tahqiqi, 'Tahqiqi');
    }

    public function updatedOther()
    {
        $this->uploadFiles($this->other, 'Other');
    }

    private function uploadFiles($files, $type)
    {
        $directory = "{$this->selectedFolder}{$this->selectedDarul}Checked_by_{$this->checker}_to_mujeeb/{$type}";

        foreach ($files as $file) {
            $filePath = $file->storeAs($directory, $file->getClientOriginalName(), 'public');
            $this->fileList[$type][] = [
                'name' => $file->getClientOriginalName(),
                'path' => $filePath,
                'folder' => $directory,
            ];

            // Dispatch JavaScript function to add the new file to the table
            $this->dispatch('add-new-file', [
                'name' => $file->getClientOriginalName(),
                'folder' => $directory,
                'type' => $type,
            ]);
        }

        session()->flash('message', 'Files uploaded successfully to ' . $directory);
    }

    public function removeFile($type, $index)
    {
        $file = $this->fileList[$type][$index];

        // Remove file from storage
        Storage::disk('public')->delete($file['path']);

        // Remove file from the list
        unset($this->fileList[$type][$index]);
        $this->fileList[$type] = array_values($this->fileList[$type]);

        session()->flash('message', 'File removed successfully.');
    }

    public function render()
    {
        return view('livewire.file-upload');
    }
}
