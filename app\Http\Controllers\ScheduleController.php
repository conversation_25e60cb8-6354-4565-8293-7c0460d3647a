<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
class ScheduleController extends Controller
{
    public function ApptStore(Request $request)
{
    $dates = $request->input('date');
    $days = $request->input('day');
    $times = $request->input('time');
    $schedulers = $request->input('event');
    $contacts = $request->input('location');
    $descriptions = $request->input('description');

    // Assuming you have a 'schedules' table
    foreach ($dates as $key => $date) {
        DB::table('schedules')->insert([
            'schedule_date' => $date,
            'schedule_day' => $days[$key],
            'schedule_time' => $times[$key],
            'event_name' => $schedulers[$key],
            'location' => $contacts[$key],
            'description' => $descriptions[$key],
        ]);
    }

    return redirect()->back()->with('success', 'Schedules added successfully.');
}
public function index()
{
    $schedules = DB::table('schedules')->get(); // Fetch all appointments from the 'appt' table

    return view('pages.schedule', compact('schedules'));

}
public function edit($id)
    {
        $schedules = DB::table('schedules')->where('id', $id)->first();

        if (!$schedules) {
            return redirect()->route('schedules')->with('error', 'schedules not found.');
        }

        return view('pages.edit-schedule', compact('schedules'));
    }

    public function update(Request $request, $id)
    {
        // Validation and update logic here

        DB::table('schedules')
            ->where('id', $id)
            ->update([
                'schedule_date' => $request->input('date'),
                'schedule_day' => $request->input('day'),
                'schedule_time' => $request->input('time'),
                'event_name' => $request->input('event'),
                'location' => $request->input('location'),
                'description' => $request->input('description'),
            ]);

        return redirect()->route('schedule')->with('success', 'Schedules updated successfully');
    }
    public function destroy($id)
    {
        DB::table('schedules')->where('id', $id)->delete();

        return redirect()->route('schedule')->with('success', 'schedules deleted successfully');
    }

}
