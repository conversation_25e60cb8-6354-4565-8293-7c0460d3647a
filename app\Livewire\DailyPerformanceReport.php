<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DailyPerformance;
use App\Models\Task;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class DailyPerformanceReport extends Component
{
    use AuthorizesRequests;

    public $performance_date;
    public $selected_task_id = null;
    public $tasks_completed = '';
    public $challenges_faced = '';
    public $next_day_plan = '';
    public $hours_worked = '';
    public $overall_rating = 'good';
    public $additional_notes = '';

    public $isSubmitted = false;
    public $existingRecord = null;
    public $hasAssignedTasks = false;
    public $assignedTasks = [];
    public $userDepartments = [];
    public $selectedTaskDepartment = null;

    protected $rules = [
        'performance_date' => 'required|date',
        'selected_task_id' => 'required|exists:workflow_tasks,id',
        'tasks_completed' => 'required|string',
        'challenges_faced' => 'nullable|string',
        'next_day_plan' => 'nullable|string',
        'hours_worked' => 'required|numeric|min:0|max:24',
        'overall_rating' => 'nullable|in:poor,fair,good,excellent',
        'additional_notes' => 'nullable|string',
    ];

    public function mount()
    {
        $this->authorize('submit-performance');
        $this->performance_date = Carbon::today()->format('Y-m-d');
        $this->loadAssignedTasks();
        $this->loadUserDepartments();
        $this->checkIfUserHasTasks();
        $this->loadExistingRecord();
    }

    public function render()
    {
        return view('livewire.daily-performance-report');
    }

    public function checkIfUserHasTasks()
    {
        $this->hasAssignedTasks = Task::where('assigned_to', auth()->id())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->exists();
    }

    public function loadAssignedTasks()
    {
        $this->assignedTasks = Task::where('assigned_to', auth()->id())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['department:id,name', 'assignedBy:id,name'])
            ->orderBy('due_date', 'asc')
            ->get();
    }

    public function updatedSelectedTaskId()
    {
        if ($this->selected_task_id) {
            $task = Task::with('department')->find($this->selected_task_id);
            $this->selectedTaskDepartment = $task ? $task->department : null;
            $this->loadExistingRecord();
        }
    }

    public function loadExistingRecord()
    {
        if (!$this->selected_task_id) {
            return;
        }

        $this->existingRecord = DailyPerformance::where('user_id', auth()->id())
            ->where('task_id', $this->selected_task_id)
            ->where('performance_date', $this->performance_date)
            ->first();

        if ($this->existingRecord) {
            $this->tasks_completed = $this->existingRecord->tasks_completed ?? '';
            $this->challenges_faced = $this->existingRecord->challenges_faced ?? '';
            $this->next_day_plan = $this->existingRecord->next_day_plan ?? '';
            $this->hours_worked = $this->existingRecord->hours_worked ?? '';
            $this->overall_rating = $this->existingRecord->overall_rating ?? 'good';
            $this->additional_notes = $this->existingRecord->additional_notes ?? '';
            $this->isSubmitted = $this->existingRecord->is_submitted ?? false;
        } else {
            $this->resetFormFields();
        }
    }

    public function resetFormFields()
    {
        $this->tasks_completed = '';
        $this->challenges_faced = '';
        $this->next_day_plan = '';
        $this->hours_worked = '';
        $this->overall_rating = 'good';
        $this->additional_notes = '';
        $this->isSubmitted = false;
    }

    public function loadTodaysTasks()
    {
        $this->todaysTasks = Task::where('assigned_to', auth()->id())
            ->where('due_date', $this->performance_date)
            ->with(['assignedBy', 'department'])
            ->get();

        $this->completedTasksCount = $this->todaysTasks->where('status', 'completed')->count();
    }

    public function loadUserDepartments()
    {
        $this->userDepartments = auth()->user()->departments()->active()->select('id', 'name')->get();
    }

    public function loadAllUserTasks()
    {
        // Load all tasks assigned to the user (not just today's) with limit to prevent timeout
        $this->allUserTasks = Task::where('assigned_to', auth()->id())
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['assignedBy:id,name', 'department:id,name'])
            ->orderBy('due_date', 'asc')
            ->limit(50) // Limit to prevent timeout
            ->get();
    }

    public function updatedPerformanceDate()
    {
        $this->loadExistingRecord();
        $this->loadTodaysTasks();
        $this->loadAllUserTasks();
    }

    public function saveAsDraft()
    {
        $this->validate();

        $task = Task::find($this->selected_task_id);

        $data = [
            'user_id' => auth()->id(),
            'task_id' => $this->selected_task_id,
            'department_id' => $task ? $task->department_id : null,
            'performance_date' => $this->performance_date,
            'tasks_completed' => $this->tasks_completed,
            'challenges_faced' => $this->challenges_faced,
            'next_day_plan' => $this->next_day_plan,
            'hours_worked' => $this->hours_worked,
            'overall_rating' => $this->overall_rating ?: null,
            'additional_notes' => $this->additional_notes,
            'is_submitted' => false,
        ];

        if ($this->existingRecord) {
            $this->existingRecord->update($data);
        } else {
            $this->existingRecord = DailyPerformance::create($data);
        }

        session()->flash('message', 'Performance report saved as draft.');
    }

    public function submitReport()
    {
        $this->validate();

        $task = Task::find($this->selected_task_id);

        $data = [
            'user_id' => auth()->id(),
            'task_id' => $this->selected_task_id,
            'department_id' => $task ? $task->department_id : null,
            'performance_date' => $this->performance_date,
            'tasks_completed' => $this->tasks_completed,
            'challenges_faced' => $this->challenges_faced,
            'next_day_plan' => $this->next_day_plan,
            'hours_worked' => $this->hours_worked,
            'overall_rating' => $this->overall_rating ?: null,
            'additional_notes' => $this->additional_notes,
            'is_submitted' => true,
            'submitted_at' => Carbon::now(),
        ];

        if ($this->existingRecord) {
            $this->existingRecord->update($data);
        } else {
            $this->existingRecord = DailyPerformance::create($data);
        }

        $this->isSubmitted = true;
        session()->flash('message', 'Performance report submitted successfully!');

        // Redirect to dashboard or previous page
        return redirect()->route('dashboard');
    }

    public function getRatingColor($rating)
    {
        return match($rating) {
            'poor' => 'danger',
            'fair' => 'warning',
            'good' => 'info',
            'excellent' => 'success',
            default => 'secondary'
        };
    }

    public function getTaskStatusColor($status)
    {
        return match($status) {
            'pending' => 'warning',
            'in_progress' => 'info',
            'completed' => 'success',
            'cancelled' => 'secondary',
            default => 'secondary'
        };
    }

    public function markTaskCompleted($taskId)
    {
        $task = Task::findOrFail($taskId);
        
        if ($task->assigned_to === auth()->id()) {
            $task->markCompleted();
            $this->loadTodaysTasks();
            session()->flash('message', 'Task marked as completed.');
        }
    }

    public function autoFillTasksCompleted()
    {
        $completedTasks = $this->todaysTasks->where('status', 'completed');
        
        if ($completedTasks->count() > 0) {
            $tasksList = $completedTasks->map(function ($task) {
                return "• {$task->title}" . ($task->completion_notes ? " - {$task->completion_notes}" : "");
            })->implode("\n");
            
            $this->tasks_completed = $tasksList;
        }
    }

    public function canEdit()
    {
        return !$this->isSubmitted || auth()->user()->isNazim();
    }

    public function updateMahlENazarCount()
    {
        // Update the User model method to get actual count
        $user = auth()->user();
        $count = \DB::table('uploaded_files')
            ->where('ftype', 'Mahl e Nazar')
            ->where(function ($query) use ($user) {
                $query->where('checker', $user->name)
                      ->orWhere('transfer_by', $user->name);
            })
            ->count();

        return $count;
    }
}
