<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('parents', function (Blueprint $table) {
            $table->id(); // Primary key
            $table->unsignedBigInteger('header_id'); // Foreign key referencing headers table
            $table->string('name'); // Name of the parent
            $table->integer('serial_number'); // Serial number
            $table->timestamps(); // created_at and updated_at columns

            // Foreign key constraint
            $table->foreign('header_id')->references('id')->on('headers')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parents');
    }
};
