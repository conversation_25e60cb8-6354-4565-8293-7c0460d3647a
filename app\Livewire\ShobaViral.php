<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\ViralChat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use DateTime;



class ShobaViral extends Component
{
    use WithPagination;

    public $filter;
    public $message = '';  // Initialize message property
    public $search;
    public $fileErrorMessage;
    public $messageIdBeingEdited = null;


    protected $queryString = [
        'filter' => ['except' => ''],
        'search' => ['except' => ''],
    ];

    public function mount($filter = null)
    {
        $this->filter = $filter;
        $this->message = '';  // Reset message here for testing purposes

    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function downloadFile($fileName)
    {
        // Define the path to the file in the storage directory
        $filePath = 'public/viral/' . $fileName;

        // Check if the file exists
        if (Storage::exists($filePath)) {
            // Download the file from storage
            return Storage::download($filePath, $fileName);
        } else {
            // Handle the error if the file does not exist
            $this->fileErrorMessage = "The file does not exist.";
        }
    }
    public function sendMessage($iftaCode)
{
    // Save the message to the database
    ViralChat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    // Reset the message to an empty string
    $this->message = '';

    // Reload messages if needed

}
    public function editMessage($messageId)
    {
        $this->messageIdBeingEdited = $messageId;
        $message = ViralChat::findOrFail($messageId);
        $this->message = $message->message;
    }
    public function cancelEdit($messageId)
    {
        $this->messageIdBeingEdited = null;
        $this->message = ''; // Clear the input when canceling
    }
    public function updateMessage($messageId)
{
    if ($this->messageIdBeingEdited) {
        $message = ViralChat::find($this->messageIdBeingEdited);
        if ($message && $message->user_id == auth()->user()->id) {
            $message->update(['message' => $this->message]);
            $this->messageIdBeingEdited = null; // Reset after update
            $this->message = ''; // Clear message input
        }
    }
}

    public function deleteMessage($messageId)
    {
        $message = ViralChat::find($messageId);
        if ($message && $message->user_id == auth()->user()->id) {
            $message->delete();
        }
    }

    public function getCounts()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $userRoleName = $userRoles->pluck('name')->toArray();

        if (!$userRoleName) {
            abort(403, 'Access Denied: No role assigned to the user.');
        }

        $allowedIftas = [];

        if (in_array('Shoba_Viral', $userRoleName)) {
            $allowedIftas = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba'];
        } elseif (in_array('Shoba_Viral_Iec', $userRoleName)) {
            $allowedIftas = ['Markaz-ul-Iqtisaad'];
        }

        // Count total fatawa with shoba_viral
        $totalShobaViral = DB::table('uploaded_files')
            ->when(!empty($allowedIftas), function ($query) use ($allowedIftas) {
                $query->whereIn('darulifta_name', $allowedIftas);
            })
            ->whereNotNull('shoba_viral')
            ->where('checked_folder', 'ok')
            ->count();

        // Count total fatawa with viral_link
        $totalViralLink = DB::table('uploaded_files')
            ->when(!empty($allowedIftas), function ($query) use ($allowedIftas) {
                $query->whereIn('darulifta_name', $allowedIftas);
            })
            ->whereNotNull('viral_link')
            ->where('checked_folder', 'ok')
            ->count();

        // Count total fatawa with web_link
        $totalWebLink = DB::table('uploaded_files')
            ->when(!empty($allowedIftas), function ($query) use ($allowedIftas) {
                $query->whereIn('darulifta_name', $allowedIftas);
            })
            ->whereNotNull('web_link')
            ->where('checked_folder', 'ok')
            ->count();

        // Count remaining fatawa for viral and web
        $remainingForViral = $totalShobaViral - $totalViralLink;
        $remainingForWeb = $totalShobaViral - $totalWebLink;

        return [
            'totalShobaViral' => $totalShobaViral,
            'totalViralLink' => $totalViralLink,
            'totalWebLink' => $totalWebLink,
            'remainingForViral' => $remainingForViral,
            'remainingForWeb' => $remainingForWeb,
        ];
    }
    public function render()
    {
        // Retrieve the current user's role name
        $user = Auth::user();
        $userRoles = $user->roles;
        $userRoleName = $userRoles->pluck('name')->toArray();

        if (!$userRoleName) {
            abort(403, 'Access Denied: No role assigned to the user.');
        }
        $allowedIftas = [];

        if (in_array('Shoba_Viral', $userRoleName)) {
            $allowedIftas = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba'];
        } elseif (in_array('Shoba_Viral_Iec', $userRoleName)) {
            $allowedIftas = ['Markaz-ul-Iqtisaad'];
        }

        // Build the initial query
        $counts = $this->getCounts(); // Make sure this method is defined in your component

        $query = DB::table('uploaded_files')
            ->whereNotNull('shoba_viral')
            ->where('viral', '!=', 0); // Ensures viral is not 0

        // Filter by darulifta_name matching the user's role name
        if (!empty($allowedIftas)) {
            $query->whereIn('darulifta_name', $allowedIftas);
        }

        // Apply the filter conditions
        if ($this->filter === 'all') {
            $query->whereNotNull('shoba_viral');
        } elseif ($this->filter === 'viral') {
            $query->whereNotNull('viral_link');
        } elseif ($this->filter === 'web') {
            $query->whereNotNull('web_link');
        } else {
            $query->whereNotNull('shoba_viral')
                ->where(function ($query) {
                    $query->whereNull('viral_link')
                          ->orWhereNull('web_link');
                });
        }

        // Apply the search conditions
        if ($this->search) {
            $query->where(function ($subQuery) {
                $subQuery->where('title', 'like', '%' . $this->search . '%')
                         ->orWhere('file_code', 'like', '%' . $this->search . '%')
                         ->orWhere('category', 'like', '%' . $this->search . '%');
            });
        }

        // Paginate results
        $question_b = $query->orderBy('shoba_viral', 'asc')->paginate(10);
        $messages = ViralChat::latest()->get();

        // Pass data to the view
        return view('livewire.shoba-viral', compact('question_b', 'messages', 'counts'))
            ->layout('layouts.app');
    }
}
