<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Header;
use App\Models\ParentModel;
use App\Models\Child;

class TalaqSidebar extends Component
{
    public $headers = [];
    public $parents = [];
    public $children = [];
    public $searchQuery = '';

    public $headerChecks = [];
    public $parentChecks = [];

    public function mount()
    {
        $this->headers = Header::all();
        $this->parents = collect();
        $this->children = collect();
    }

    public function updatedHeaderChecks()
    {
        $selectedHeaders = array_keys(array_filter($this->headerChecks));
        if (!empty($selectedHeaders)) {
            $this->parents = ParentModel::whereIn('header_id', $selectedHeaders)->get();
        } else {
            $this->parents = collect();
            $this->children = collect();
        }
        $this->dispatch('updated-parents', ['parents' => $this->parents]);
    }

    public function updatedParentChecks()
    {
        $selectedParents = array_keys(array_filter($this->parentChecks));
        if (!empty($selectedParents)) {
            $query = Child::whereIn('parent_id', $selectedParents);

            if ($this->searchQuery) {
                $query->where('name', 'like', '%' . $this->searchQuery . '%');
            }

            $this->children = $query->get();
        } else {
            $this->children = collect();
        }

        $this->dispatch('updated-children', ['children' => $this->children]);
    }

    public function updatedSearchQuery()
    {
        $this->updatedParentChecks();
    }

    public function render()
    {
        return view('livewire.talaq-sidebar', [
            'headers' => $this->headers,
            'parents' => $this->parents,
            'children' => $this->children,
        ]);
    }
}
