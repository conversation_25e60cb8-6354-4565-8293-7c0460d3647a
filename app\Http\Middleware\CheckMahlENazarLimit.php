<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\UserRestriction;
use Illuminate\Support\Facades\DB;

class CheckMahlENazarLimit
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Skip check for Nazim/Admin users
        if ($user->isNazim()) {
            return $next($request);
        }

        // Check Mahl-e-Nazar count for the user
        $mahlENazarCount = $this->getMahlENazarCount($user->name);

        if ($mahlENazarCount >= 12) {
            // Create or update restriction
            UserRestriction::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'restriction_type' => 'fatawa_limit_exceeded',
                    'is_active' => true,
                ],
                [
                    'reason' => "User has {$mahlENazarCount} fatawa in Mahl-e-Nazar status (limit: 12)",
                    'restricted_by' => $user->id, // System restriction
                    'restricted_at' => now(),
                ]
            );

            return redirect()->back()
                ->with('error', "You have {$mahlENazarCount} fatawa in Mahl-e-Nazar status. The limit is 12. Please resolve existing fatawa before submitting new ones.");
        }

        // Remove limit restriction if it exists and count is below limit
        UserRestriction::where('user_id', $user->id)
            ->where('restriction_type', 'fatawa_limit_exceeded')
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $user->id,
                'lifted_at' => now(),
                'lift_reason' => 'Mahl-e-Nazar count below limit',
            ]);

        return $next($request);
    }

    /**
     * Get the count of Mahl-e-Nazar fatawa for a user.
     */
    private function getMahlENazarCount($userName): int
    {
        // Count from uploaded_files table where ftype is 'Mahl e Nazar'
        // and the fatwa is associated with the user
        return DB::table('uploaded_files')
            ->where('ftype', 'Mahl e Nazar')
            ->where(function ($query) use ($userName) {
                $query->where('checker', $userName)
                      ->orWhere('transfer_by', $userName);
            })
            ->count();
    }
}
