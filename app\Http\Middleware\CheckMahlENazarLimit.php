<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\MahlENazarService;

class CheckMahlENazarLimit
{
    protected $mahlENazarService;

    public function __construct(MahlENazarService $mahlENazarService)
    {
        $this->mahlENazarService = $mahlENazarService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Skip check for Nazim/Admin users
        if ($user->isNazim()) {
            return $next($request);
        }

        // Check if user can submit fatawa
        if (!$this->mahlENazarService->canSubmitFatawa($user)) {
            $message = $this->mahlENazarService->getRestrictionMessage($user);

            return redirect()->back()
                ->with('error', $message ?: 'You are currently restricted from submitting fatawa.');
        }

        return $next($request);
    }
}
