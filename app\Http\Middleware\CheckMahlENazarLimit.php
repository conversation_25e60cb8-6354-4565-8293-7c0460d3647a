<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\MahlENazarService;

class CheckMahlENazarLimit
{
    protected $mahlENazarService;

    public function __construct(MahlENazarService $mahlENazarService)
    {
        $this->mahlENazarService = $mahlENazarService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Skip check for Nazim/Admin users
        if ($user->isNazim()) {
            return $next($request);
        }

        // Check if user can submit fatawa
        if (!$this->mahlENazarService->canSubmitFatawa($user)) {
            $message = $this->mahlENazarService->getRestrictionMessage($user);
            $count = $this->mahlENazarService->getMahlENazarCount($user->name);

            // For AJAX requests, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'error' => 'Access denied. ' . $message,
                    'redirect' => route('dashboard')
                ], 403);
            }

            // Show access denied page with blur effect
            return response()->view('mahl-e-nazar-access-denied', [
                'user' => $user,
                'message' => $message,
                'count' => $count,
                'limit' => \App\Services\MahlENazarService::MAHL_E_NAZAR_LIMIT,
            ]);
        }

        return $next($request);
    }
}
