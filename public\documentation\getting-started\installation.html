<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
    <link rel="icon" href="../../assets/img/favicon.png" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Creative Tim">
    <title>
        Installation | Material Dashboard 2 FREE Laravel by Creative Tim & UPDIVISION
    </title>
    <link rel="canonical" href="https://www.creative-tim.com/learning-lab/bootstrap/colors/material-dashboard" />

    <meta name="keywords" content="creative tim, updivision, html dashboard, laravel, material, html css dashboard laravel, material dashboard laravel, laravel material dashboard, material admin, laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, material dashboard bootstrap 5 laravel, frontend, responsive bootstrap 5 dashboard, material dashboard, material laravel bootstrap 5 dashboard" />
    <meta name="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="name" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta itemprop="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta name="twitter:card" content="product" />
    <meta name="twitter:site" content="@creativetim" />
    <meta name="twitter:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta name="twitter:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta name="twitter:creator" content="@creativetim" />
    <meta name="twitter:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="fb:app_id" content="655968634437471" />
    <meta property="og:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta property="og:type" content="article" />
    <meta property="og:url" content="https://www.creative-tim.com/live/material-dashboard-laravel" />
    <meta property="og:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="og:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta property="og:site_name" content="Creative Tim" />

    <link rel="stylesheet" href="../../assets/css/nucleo-icons.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/nextjs-material-dashboard-pro.min.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/material-dashboard.min.css" type="text/css">
    <link rel="stylesheet" href="../../assets/css/demo.css" type="text/css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Roboto+Slab:400,700" rel="stylesheet">

    <link href="../../assets/css/nucleo-icons.css" rel="stylesheet">

    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
    <!-- Anti-flicker snippet (recommended)  -->
    <style>
        .async-hide {
            opacity: 0 !important
        }

    </style>
    <script>
        (function (a, s, y, n, c, h, i, d, e) {
            s.className += ' ' + y;
            h.start = 1 * new Date;
            h.end = i = function () {
                s.className = s.className.replace(RegExp(' ?' + y), '')
            };
            (a[n] = a[n] || []).hide = h;
            setTimeout(function () {
                i();
                h.end = null
            }, c);
            h.timeout = c;
        })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {
            'GTM-K9BGS8K': true
        });

    </script>
    <!-- Analytics-Optimize Snippet -->
    <script>
        (function (i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function () {
                (i[r].q = i[r].q || []).push(arguments)
            }, i[r].l = 1 * new Date();
            a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m)
        })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
        ga('create', 'UA-46172202-22', 'auto', {
            allowLinker: true
        });
        ga('set', 'anonymizeIp', true);
        ga('require', 'GTM-K9BGS8K');
        ga('require', 'displayfeatures');
        ga('require', 'linker');
        ga('linker:autoLink', ["2checkout.com", "avangate.com"]);

    </script>
    <!-- end Analytics-Optimize Snippet -->
    <!-- Google Tag Manager -->
    <script>
        (function (w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-NKDMSK6');

    </script>
    <!-- End Google Tag Manager -->
    <!-- This is for docs typography and layout -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="../../assets/css/docs.css" rel="stylesheet" />
</head>

<body class="docs ">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NKDMSK6" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <header class="ct-docs-navbar">
      <a class="ct-docs-navbar-brand" href="javascript:void(0)" aria-label="Bootstrap">
        </a><a href="https://www.creative-tim.com/" class="ct-docs-navbar-text" target="_blank">
          Creative Tim
        </a>
        <div class="ct-docs-navbar-border"></div>
        <a href="../../documentation/getting-started/installation.html" class="ct-docs-navbar-text">
          Docs
        </a>

      <ul class="ct-docs-navbar-nav-left">
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Live Preview</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownPreview">
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-pro-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Support</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownSupport">
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/material-dashboard-laravel/issues" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/ct-material-dashboard-pro-laravel/issues" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
      </ul>
      <ul class="ct-docs-navbar-nav-right">
        <li class="ct-docs-navbar-nav-item">
          <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/material-dashboard-laravel" target="_blank">Download Free</a>
        </li>
      </ul>
      <a href="https://www.creative-tim.com/product/material-dashboard-pro-laravel" target="_blank" class="ct-docs-btn-upgrade">
        <span class="ct-docs-btn-inner--icon">
          <i class="fas fa-download mr-2" aria-hidden="true"></i>
        </span>
        <span class="ct-docs-navbar-nav-link-inner--text">Upgrade to PRO</span>
      </a>
      <button class="ct-docs-navbar-toggler" type="button">
        <span class="ct-docs-navbar-toggler-icon"></span>
      </button>
    </header>
    <div class="ct-docs-main-container">
        <div class="ct-docs-main-content-row">
            <div class="ct-docs-sidebar-col">
                <nav class="ct-docs-sidebar-collapse-links">
                    <div class="ct-docs-sidebar-product">
                      <div class="ct-docs-sidebar-product-image">
                        <img src="../../assets/img/bootstrap.png">
                      </div>
                      <p class="ct-docs-sidebar-product-text">Material Dashboard</p>
                    </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-active-40 text-white"></i>
                          </div>
                        </div>
                        Getting started
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                          <a href="../../documentation/getting-started/overview.html">
                            Overview
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/license.html">
                            License
                          </a>
                        </li>
                        <li class="ct-docs-nav-sidenav-active">
                          <a href="../../documentation/getting-started/installation.html">
                            installation
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/build-tools.html">
                            Build Tools
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/bootstrap.html">
                            What is Bootstrap
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                          <div class="d-inline-block">
                            <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                              <i class="ni ni-folder-17 text-white"></i>
                            </div>
                          </div>
                          Laravel
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                          <li class="">
                            <a href="../../documentation/laravel/login.html">
                              Login
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/sign-up.html">
                              Sign Up
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/forgot-password.html">
                              Forgot Password
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/user-profile.html">
                              User Profile
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/user-management.html">
                              User Management
                              <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                          </li>
                        </ul>
                      </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-folder-17 text-white"></i>
                          </div>
                        </div>
                        Foundation
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class=" ct-docs-nav-sidenav ">
                          <a href="../../documentation/foundation/colors.html">
                            Colors
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/grid.html">
                            Grid
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/typography.html">
                            Typography
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/icons.html">
                            Icons
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/utilities.html">
                            Utilities
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-app text-white"></i>
                          </div>
                        </div>
                        Components
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                          <a href="../../documentation/components/alerts.html">
                            Alerts
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/badge.html">
                            Badge
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/buttons.html">
                            Buttons
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/social-buttons.html">
                            Social Buttons
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/cards.html">
                            Cards
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/carousel.html">
                            Carousel
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/collapse.html">
                            Collapse
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/dropdowns.html">
                            Dropdowns
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/forms.html">
                            Forms
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/input-group.html">
                            Input Group
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/list-group.html">
                            List Group
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/modal.html">
                            Modal
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/navs.html">
                            Navs
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/navbar.html">
                            Navbar
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/pagination.html">
                            Pagination
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/popovers.html">
                            Popovers
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/progress.html">
                            Progress
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/spinners.html">
                            Spinners
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/tables.html">
                            Tables
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/tooltips.html">
                            Tooltips
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-settings text-white"></i>
                          </div>
                        </div>
                        Plugins
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                          <a href="../../documentation/plugins/countUpJs.html">
                            CountUp JS
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/charts.html">
                            Charts
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/datepicker.html">
                            Datepicker
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/fullcalendar.html">
                            Fullcalendar
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/sliders.html">
                            Sliders
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/choices.html">
                            Choices
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/dropzone.html">
                            Dropzone
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/datatables.html">
                            Datatables
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/kanban.html">
                            Kanban
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/photo-swipe.html">
                            Photo Swipe
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/quill.html">
                            Quill
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/sweet-alerts.html">
                            Sweet Alerts
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/wizard.html">
                            Wizard
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </nav>
            </div>
            <div class="ct-docs-toc-col">
                <ul class="section-nav">
                    <li class="toc-entry toc-h2">
                        <a href="#prerequisites">Prerequisites</a>
                    </li>
                    <li class="toc-entry toc-h2">
                        <a href="#installation">Installation</a>
                    </li>
                    <li class="toc-entry toc-h2">
                        <a href="#usage">Usage</a>
                    </li>
                    <li class="toc-entry toc-h2"><a href="#tooling-setup">Tooling setup</a></li>
                    <li class="toc-entry toc-h2"><a href="#bootstrap-cdn">Bootstrap CDN</a></li>
                    <li class="toc-entry toc-h2"><a href="#css">CSS</a></li>
                    <li class="toc-entry toc-h2"><a href="#js">JS</a></li>
                    <li class="toc-entry toc-h2"><a href="#dark-mode">Dark mode</a></li>
                    <li class="toc-entry toc-h2"><a href="#bootstrap-starter-template">Bootstrap starter template</a>
                    </li>
                    <li class="toc-entry toc-h2"><a href="#important-globals">Important globals</a>
                        <ul>
                            <li class="toc-entry toc-h3"><a href="#html5-doctype">HTML5 doctype</a></li>
                            <li class="toc-entry toc-h3"><a href="#responsive-meta-tag">Responsive meta tag</a></li>
                            <li class="toc-entry toc-h3"><a href="#bootstrap-components">Bootstrap components</a></li>
                            <li class="toc-entry toc-h3"><a href="#bootstrap-tutorial">Bootstrap tutorial</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
            <main class="ct-docs-content-col" role="main">
                <div class="ct-docs-page-title">
                    <h1 class="ct-docs-page-h1-title" id="content">
                        Installation
                    </h1>
                    <div class="avatar-group mt-3">
                    </div>
                </div>
                <p class="ct-docs-page-title-lead">To start using this dashboard you will need to import some files in
                    your current project or start from scratch using our template (scroll down in this page to view it).
                </p>
                <hr class="ct-docs-hr">
                <h2 id="prerequisites">Prerequisites</h2>
                <p>
                    If you don't already have an Apache local environment
                    with PHP and MySQL, use one of the following links:
                </p>
                <ul>
                    <li>
                        Windows
                        <a
                            href="https://updivision.com/blog/post/beginner-s-guide-to-setting-up-your-local-development-environment-on-windows">https://updivision.com/blog/post/beginner-s-guide-to-setting-up-your-local-development-environment-on-windows</a>
                    </li>
                    <li>
                        Linux
                        <a
                            href="https://howtoubuntu.org/how-to-install-lamp-on-ubuntu">https://howtoubuntu.org/how-to-install-lamp-on-ubuntu</a>
                    </li>
                    <li>
                        Mac
                        <a href=" https://wpshout.com/quick-guides/how-to-install-mamp-on-your-mac/">
                            https://wpshout.com/quick-guides/how-to-install-mamp-on-your-mac/</a>
                    </li>
                </ul>
                <p>
                    Also, you will need to install Composer:
                    <a href="https://getcomposer.org/doc/00-intro.md">https://getcomposer.org/doc/00-intro.md</a>
                </p>
                <p>
                    And Laravel:
                    <a href="https://laravel.com/docs/9.x/installation">https://laravel.com/docs/9.x/installation</a>
                </p>

                <h2 id="installation">Installation</h2>
                <h3>Via Composer</h3>
                <ol>
                  <li><b>Cd</b> to your Laravel app</li>
                  <li>
                    Type in your terminal: <code class="language-html">composer require laravel/ui</code> and <code class="language-html">php artisan ui vue --auth</code>.
                  </li>
                  <li>
                    Install this preset via 
                      <code class="language-html">composer require laravel-frontend-presets/material</code>. No need to register the service provider. Laravel 9.x & up can auto detect the package.
                  </li>
                  <li>
                      Run
                      <code class="language-html">php artisan ui material</code> command to install the Argon preset. This will install all the necessary assets and also the custom auth views, it will also add the auth route in 
                      <code class="language-html">routes/web.php</code> (NOTE: If you run this command several times, be sure to clean up the duplicate Auth entries in routes/web.php)
                  </li>
                  <li>
                      In your terminal run
                      <code class="language-html">composer dump-autoload</code>
                  </li>
                  <li>
                      Add your DB info in 
                      <code class="language-html">.env</code>
                  </li>
                  <li>
                      Run
                      <code class="language-html">php artisan migrate:fresh --seed
                      </code>
                      to create basic users table
                  </li>
                </ol>

                <h3>By using the archive</h3>
                <ol>
                  <li>In your application's root create a <b>presets</b> folder</li>
                  <li>Download the archive of the repo and unzip it</li>
                  <li>Copy and paste the downloaded folder in presets (created in step 2) and rename it to material</li>
                  <li>Open <code class="language-html">composer.json</code></li>
                  <li>
                    Add <code class="language-html">"LaravelFrontendPresets\\MaterialPreset\\": "presets/material/src"</code> to 
                    <code class="language-html">autoload/psr-4</code> and to <code class="language-html">autoload-dev/psr-4</code>.
                  </li>
                  <li>
                    Add <code class="language-html">LaravelFrontendPresets\MaterialPreset\MaterialPresetServiceProvider::class</code> to 
                    <code class="language-html">config/app.php</code>.
                  </li>
                  <li>
                    Type in your terminal: <code class="language-html">composer require laravel/ui</code> and <code class="language-html">php artisan ui vue --auth</code>.
                  </li>
                  <li>
                    In your terminal run
                    <code class="language-html">composer dump-autoload</code>
                  </li>
                  <li>
                      Run
                      <code class="language-html">php artisan ui material</code> command to install the Argon preset. This will install all the necessary assets and also the custom auth views, it will also add the auth route in 
                      <code class="language-html">routes/web.php</code> (NOTE: If you run this command several times, be sure to clean up the duplicate Auth entries in routes/web.php)
                  </li>
                  <li>
                      Add your DB info in 
                      <code class="language-html">.env</code>
                  </li>
                  <li>
                      Run
                      <code class="language-html">php artisan migrate:fresh --seed
                      </code>
                      to create basic users table
                  </li>
                </ol>

                <h2 id="usage">Usage</h2>
                <p>
                    Register an user or login with data from your database and start testing (make sure to run the
                    migrations and seeders for the credentials to be available).
                </p>
                <p>

                    Besides the dashboard, the auth pages, the billing and tables pages, it has also an edit profile
                    page. All the necessary files are installed out of the box and all the needed routes are added to
                    <code class="language-html">routes/web.php</code>. Keep in mind that all of the features can be
                    viewed once you login using the credentials provided or by registering your own user.
                </p>
                <h2 id="tooling-setup">Tooling setup</h2>
                <h2 id="bootstrap-cdn">Bootstrap CDN</h2>
                <p>Skip the download with <a href="https://www.bootstrapcdn.com/" target="_blank"
                        rel="nofollow">BootstrapCDN</a> to deliver cached version of Bootstrap’s compiled CSS and JS to
                    your project.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- CSS only --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"https://cdn.jsdelivr.net/npm/<a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="89ebe6e6fdfafdfbe8f9c9bca7b8a7b9">[email&#160;protected]</a>/dist/css/bootstrap.min.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="na">integrity=</span><span class="s">"sha384-wEmeIV1mKuiNpC+IOBjI7aAzPcEZeedi5yW5f2yOq55WWLwNGmvvx4Um1vskeMj0"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;</span>
<span class="c">&lt;!-- JavaScript Bundle with Popper --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"https://cdn.jsdelivr.net/npm/<a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="a2c0cdcdd6d1d6d0c3d2e2978c938c92">[email&#160;protected]</a>/dist/js/bootstrap.bundle.min.js"</span> <span class="na">integrity=</span><span class="s">"sha384-p34f1UUtsS3wqzfto5wAAmdvj+osOnFyQFpp4Ua3gs/ZVWx6oOypYoCJhGGScy+8"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;&lt;/script&gt;</span></code></pre>
                </figure>
                <h2 id="css">CSS</h2>
                <p>Copy-paste the stylesheet <code class="language-plaintext highlighter-rouge">&lt;link&gt;</code> into
                    your <code class="language-plaintext highlighter-rouge">&lt;head&gt;</code> before all other
                    stylesheets to load our CSS.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- Fonts --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span><span class="nt">&gt;</span>

<span class="c">&lt;!-- Icons --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-icons.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
<span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-svg.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>

<span class="c">&lt;!-- Font Awesome Icons --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"https://kit.fontawesome.com/42d5adcbca.js"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;&lt;/script&gt;</span>

<span class="c">&lt;!-- CSS Files --&gt;</span>
<span class="nt">&lt;link</span> <span class="na">id=</span><span class="s">"pagestyle"</span> <span class="na">href=</span><span class="s">"../assets/css/material-dashboard.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span></code></pre>
                </figure>
                <h2 id="js">JS</h2>
                <p>Many of our components require the use of JavaScript to function. Specifically , <a
                        href="https://popper.js.org/" rel="nofollow">Popper.js</a>, and our own JavaScript plugins.
                    Place the following <code class="language-plaintext highlighter-rouge">&lt;script&gt;</code>s near
                    the end of your pages, right before the closing <code
                        class="language-plaintext highlighter-rouge">&lt;/body&gt;</code> tag, to enable them. Popper.js
                    must come and then our JavaScript plugins.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- Core --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/popper.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/bootstrap.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>

<span class="c">&lt;!-- Theme JS --&gt;</span>
<span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/material-dashboard.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span></code></pre>
                </figure>
                <p>Need to use a certain plugin in your page? You can find out how to integrate them and make them work
                    in the Plugins dedicated page. In this way you will be sure that your website is optimized and uses
                    only the needed resources.</p>
                <h2 id="dark-mode">Dark mode</h2>
                <p>Material Dashboard PRO comes in 2 modes: dark &amp; light. To turn on the dark version you need to
                    add <code class="language-plaintext highlighter-rouge">dark-version</code> class on the body tag.
                </p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="c">&lt;!-- Dark version --&gt;</span>
<span class="nt">&lt;body</span> <span class="na">class=</span><span class="s">"dark-version"</span><span class="nt">&gt;</span>
...
<span class="nt">&lt;/body&gt;</span></code></pre>
                </figure>
                <h2 id="bootstrap-starter-template">Bootstrap starter template</h2>
                <p>Be sure to have your pages set up with the latest design and development standards. That means using
                    an HTML5 doctype and including a viewport meta tag for proper responsive behaviors. Put it all
                    together and your pages should look like this:</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="cp">&lt;!DOCTYPE html&gt;</span>
<span class="nt">&lt;html</span> <span class="na">lang=</span><span class="s">"en"</span><span class="nt">&gt;</span>

<span class="nt">&lt;head&gt;</span>
  <span class="nt">&lt;meta</span> <span class="na">charset=</span><span class="s">"utf-8"</span> <span class="nt">/&gt;</span>
  <span class="nt">&lt;meta</span> <span class="na">name=</span><span class="s">"viewport"</span> <span class="na">content=</span><span class="s">"width=device-width, initial-scale=1, shrink-to-fit=no"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">rel=</span><span class="s">"apple-touch-icon"</span> <span class="na">sizes=</span><span class="s">"76x76"</span> <span class="na">href=</span><span class="s">"../assets/img/apple-icon.png"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">rel=</span><span class="s">"icon"</span> <span class="na">type=</span><span class="s">"image/png"</span> <span class="na">href=</span><span class="s">"../assets/img/favicon.png"</span><span class="nt">&gt;</span>
  <span class="nt">&lt;title&gt;</span>
    Material Dashboard by Creative Tim
  <span class="nt">&lt;/title&gt;</span>
  <span class="c">&lt;!--     Fonts and icons     --&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="c">&lt;!-- Nucleo Icons --&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-icons.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-svg.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="c">&lt;!-- Font Awesome Icons --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"https://kit.fontawesome.com/42d5adcbca.js"</span> <span class="na">crossorigin=</span><span class="s">"anonymous"</span><span class="nt">&gt;&lt;/script&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">href=</span><span class="s">"../assets/css/nucleo-svg.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
  <span class="c">&lt;!-- CSS Files --&gt;</span>
  <span class="nt">&lt;link</span> <span class="na">id=</span><span class="s">"pagestyle"</span> <span class="na">href=</span><span class="s">"../assets/css/material-dashboard.css"</span> <span class="na">rel=</span><span class="s">"stylesheet"</span> <span class="nt">/&gt;</span>
<span class="nt">&lt;/head&gt;</span>

<span class="nt">&lt;body</span> <span class="na">class=</span><span class="s">"g-sidenav-show bg-gray-100"</span><span class="nt">&gt;</span>

  <span class="nt">&lt;h1&gt;</span>Hello, world!<span class="nt">&lt;/h1&gt;</span>

  <span class="c">&lt;!--   Core JS Files   --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/popper.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/core/bootstrap.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>

  <span class="c">&lt;!-- Plugin for the charts, full documentation here: https://www.chartjs.org/ --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/plugins/chartjs.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/plugins/Chart.extension.js"</span><span class="nt">&gt;&lt;/script&gt;</span>

  <span class="c">&lt;!-- Control Center for Material Dashboard: parallax effects, scripts for the example pages etc --&gt;</span>
  <span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../assets/js/material-dashboard.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span>
<span class="nt">&lt;/body&gt;</span>

<span class="nt">&lt;/html&gt;</span></code></pre>
                </figure>
                <h2 id="important-globals">Important globals</h2>
                <p>Material Dashboard employs a handful of important global styles and settings that you’ll need to be
                    aware of when using it, all of which are almost exclusively geared towards the
                    <em>normalization</em> of cross browser styles. Let’s dive in.</p>
                <h3 id="html5-doctype">HTML5 doctype</h3>
                <p>Bootstrap requires the use of the HTML5 doctype. Without it, you’ll see some funky incomplete
                    styling, but including it shouldn’t cause any considerable hiccups.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="cp">&lt;!doctype html&gt;</span>
<span class="nt">&lt;html</span> <span class="na">lang=</span><span class="s">"en"</span><span class="nt">&gt;</span>
  ...
<span class="nt">&lt;/html&gt;</span></code></pre>
                </figure>
                <h3 id="responsive-meta-tag">Responsive meta tag</h3>
                <p>Bootstrap is developed <em>mobile first</em>, a strategy in which we optimize code for mobile devices
                    first and then scale up components as necessary using CSS media queries. To ensure proper rendering
                    and touch zooming for all devices, <strong>add the responsive viewport meta tag</strong> to your
                    <code class="language-plaintext highlighter-rouge">&lt;head&gt;</code>.</p>
                <figure class="highlight">
                    <pre><code class="language-html" data-lang="html"><span class="nt">&lt;meta</span> <span class="na">name=</span><span class="s">"viewport"</span> <span class="na">content=</span><span class="s">"width=device-width, initial-scale=1, shrink-to-fit=no"</span><span class="nt">&gt;</span></code></pre>
                </figure>
                <p>You can see an example of this in action in the <a href="#starter-template">starter template</a>.</p>
                <h3 id="bootstrap-components">Bootstrap components</h3>
                <p>Many of Bootstrap’s components and utilities are built with <code
                        class="language-plaintext highlighter-rouge">@each</code> loops that iterate over a Sass map.
                    This is especially helpful for generating variants of a component by our <code
                        class="language-plaintext highlighter-rouge">$theme-colors</code> and creating responsive
                    variants for each breakpoint. As you customize these Sass maps and recompile, you’ll automatically
                    see your changes reflected in these loops.</p>
                <h3 id="bootstrap-tutorial">Bootstrap tutorial</h3>
                <p>Please check our official <a href="https://www.youtube.com/channel/UCVyTG4sCw-rOvB9oHkzZD1w/videos"
                        rel="nofollow" target="_blank">Youtube channel</a> for more tutorials.</p>
            </main>
        </div>
        <div class="ct-docs-main-footer-row">
            <div class="ct-docs-main-footer-blank-col">
            </div>
            <div class="ct-docs-main-footer-col">
              <footer class="ct-docs-footer">
                <div class="ct-docs-footer-inner-row">
                  <div class="ct-docs-footer-col">
                    <div class="ct-docs-footer-copyright">
                      © <script>
                        document.write(new Date().getFullYear())
                      </script> <a href="https://creative-tim.com" class="ct-docs-footer-copyright-author" target="_blank">Creative Tim</a> & <a href="https://www.updivision.com" class="ct-docs-footer-copyright-author" target="_blank">UPDIVISION</a>
                    </div>
                  </div>
                  <div class="ct-docs-footer-col">
                    <ul class="ct-docs-footer-nav-footer">
                      <li>
                        <a href="https://creative-tim.com" class="ct-docs-footer-nav-link" target="_blank">Creative Tim</a>
                      </li>
                      <li class="nav-item">
                        <a href="https://www.updivision.com" class="ct-docs-footer-nav-link" target="_blank">UPDIVISION</a>
                    </li>
                      <li>
                        <a href="https://www.creative-tim.com/contact-us" class="ct-docs-footer-nav-link" target="_blank">Contact Us</a>
                      </li>
                      <li>
                        <a href="https://creative-tim.com/blog" class="ct-docs-footer-nav-link" target="_blank">Blog</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </footer>
            </div>
        </div>
    </div>
    <script src="../../assets/js/jquery.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/popper.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="../../assets/js/prism.js" type="text/javascript"></script>
    <script src="../../assets/js/docs.min.js" type="text/javascript"></script>
    <script src="../../assets/js/holder.min.js" type="text/javascript"></script>
    <script src="../../assets/js/moment.min.js" type="text/javascript"></script>
    <script src="../../assets/js/material-dashboard.min.js" type="text/javascript"></script>
    <script>
        Holder.addTheme('gray', {
            bg: '#777',
            fg: 'rgba(255,255,255,.75)',
            font: 'Helvetica',
            fontweight: 'normal'
        })

    </script>
    <script>
        // Facebook Pixel Code Don't Delete
        ! function (f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function () {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window,
            document, 'script', '//connect.facebook.net/en_US/fbevents.js');

        try {
            fbq('init', '111649226022273');
            fbq('track', "PageView");

        } catch (err) {
            console.log('Facebook Track Error:', err);
        }

    </script>
    <script src="../../assets/js/docs.js"></script>
</body>

</html>
