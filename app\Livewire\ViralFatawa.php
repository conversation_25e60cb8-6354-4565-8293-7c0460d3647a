<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\WithFileUploads; // Include the trait for file uploads
use Illuminate\Support\Facades\Storage;
use App\Models\Chat;
use Illuminate\Http\Request; // Add this line
use Barryvdh\DomPDF\Facade\Pdf; // Import the PDF facade




// #[Layout('layouts.app')]
class ViralFatawa extends Component
{


    use WithFileUploads;

    public $uploadedFiles = [];
    public $fileErrorMessages = [];
    public $titles = [];
    public $webLinks = [];
    public $viralLinks = [];
    public $isEditingTitles = [];
    public $isEditingWebLinks = [];
    public $isEditingViralLinks = [];

    public $selectedMonths = [];
    public $searchQuery = '';
    public $checkerlist;
    public $munsab;
    public $datefilter;
    public $monthfilter;
    public $showDetail = false;
    public $showQue = false;
    public $showChat = false;
    public $codebylower;
    public $message;
    public $allfatawa;
    public $userName;
    public $checker;
    Public $obtain;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $remainingFatawa;
    Public $sendingFatawa;
    public $daruliftaNames;
    public $checkers;
    public $mailfolderDate;
    public $darulifta;
    public $mailfolder;
    public $mujeebs;
    public $selectedmujeeb = 'all';
    public $selectedfileCode = 'all';
    public $selectedmufti = 'all';
    public $selectedchecked = 'selected_viral';
    public $selectedTimeFrame= 'all';
    public $tempSelectedMonths = [];
    public $tempSelectedTimeFrame;
    public $daruliftalist;
    public $startDate;
    public $endDate;
    public $shobaViralFatawa;
    public $tempStartDate;
    public $tempEndDate;
    public $fileCode;
    // protected $listeners = ['updateSelectedTimeFrame'];

    public function mount(Request $request, $darulifta = null, $mailfolder = null)
        {
            $this->selectedfileCode = $request->query('selectedfileCode', $this->selectedfileCode);
            $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);
            $this->selectedmufti = $request->query('selectedmufti', $this->selectedmufti);
            $this->selectedchecked = $request->query('selectedchecked', $this->selectedchecked);
            $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);
            $this->selectedMonths = request()->query('selectedMonths', []);
            if (is_string($this->selectedMonths)) {
        // Convert comma-separated string to array
                $this->selectedMonths = explode(',', $this->selectedMonths);
            }
            $this->tempSelectedTimeFrame = $this->selectedTimeFrame;
            $this->tempSelectedMonths = $this->selectedMonths;
            $this->startDate = $request->query('startDate');
            $this->endDate = $request->query('endDate');
            $this->tempStartDate = $this->startDate;
            $this->tempEndDate = $this->endDate;
            $this->showDetail = $request->query('showDetail', false) == '1';
            $this->showQue = $request->query('showQue', false) == '1';
            $this->showChat = $request->query('showChat', false) == '1';


            $user = Auth::user();
$userRoles = $user->roles;
$roleNames = $userRoles->pluck('name')->toArray();

// Check if the user has the 'Admin' role
if (in_array('Admin', $roleNames)) {
    $this->userName = Auth::user()->name; // Get the authenticated user's name

    // Check if the username is not 'Mufti Ali Asghar'
    if ($this->userName !== 'Mufti Ali Asghar') {

        // Only execute if selectedmufti is not 'transfer_checked'
        if ($this->selectedmufti !== 'transfer_checked') {

            // Query the 'checker' table to find the folder_id based on the user's name
            $checker = DB::table('checker')
                ->where('checker_name', $this->userName)
                ->first();

            // Set selectedmufti to the folder_id or null if the checker isn't found
            $this->selectedmufti = $checker->folder_id ?? null;
        }
    }
}

            $this->checker = DB::table('checker')
                ->where('checker_name', $this->userName)
                ->first()
                ->folder_id ?? null;
                $this->munsab = DB::table('checker')
            ->where('checker_name', $this->userName)
            ->first()
            ->munsab ?? null;
        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;

        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();

        // Additional initialization code
    }
    // public function updatedShowQue($value)
    // {
    //     $this->dispatch('toggleAllOpen', ['value' => $value]);
    // }

    // Handle file upload for a specific file
    public function updatedUploadedFiles($value, $fileId)
    {

        $uploadedFile = $this->uploadedFiles[$fileId] ?? null;

        if ($uploadedFile) {
            $fileName = $uploadedFile->getClientOriginalName();
            $fileRecord = DB::table('uploaded_files')
                ->where('checked_file_name', $fileName)
                ->where('checked_folder', 'ok')
                ->where('viral', '!=', 0)
                ->where('id', $fileId)
                ->first();

            if ($fileRecord) {
                $storagePath = $uploadedFile->storeAs('viral', $fileName, 'public');
                $currentDate = now()->timezone('Asia/Karachi')->toDateTimeString();

                DB::table('uploaded_files')
                    ->where('id', $fileId)
                    ->update(['viral_upload' => $currentDate]);

                unset($this->uploadedFiles[$fileId]);
                $this->fileErrorMessages[$fileId] = null;
                $this->remainingFatawa = $this->remainingFatawadata(); // Refresh data
            } else {
                $this->fileErrorMessages[$fileId] = 'You cannot change the file name.';
                unset($this->uploadedFiles[$fileId]);
            }
        }
    }

    // Download a file
    public function downloadFile($fileId)
    {
        $file = DB::table('uploaded_files')->find($fileId);
        if (!$file) {
            $this->fileErrorMessages[$fileId] = 'File not found.';
            return;
        }

        $filePath = 'public/viral/' . $file->checked_file_name;
        if (Storage::exists($filePath)) {
            return Storage::download($filePath, $file->checked_file_name);
        } else {
            $this->fileErrorMessages[$fileId] = 'File does not exist.';
        }
    }

    // Delete a file
    public function deleteFile($fileId)
    {
        $file = DB::table('uploaded_files')->find($fileId);
        if ($file && $file->viral_upload && !$file->shoba_viral) {
            Storage::disk('public')->delete('viral/' . $file->checked_file_name);
            DB::table('uploaded_files')
                ->where('id', $fileId)
                ->update(['viral_upload' => null]);

            $this->remainingFatawa = $this->remainingFatawadata(); // Refresh data
        } else {
            $this->fileErrorMessages[$fileId] = 'Cannot delete file.';
        }
    }

    // Additional methods for editing titles, web links, etc.
    public function editTitle($fileId)
    {
        $this->isEditingTitles[$fileId] = true;
        $this->titles[$fileId] = DB::table('uploaded_files')->find($fileId)->title;
    }

    public function saveTitle($fileId)
    {
        DB::table('uploaded_files')
            ->where('id', $fileId)
            ->update(['title' => $this->titles[$fileId]]);
        $this->isEditingTitles[$fileId] = false;
        $this->remainingFatawa = $this->remainingFatawadata();
    }
    public function updated($property)
    {
        if ($property === 'fileId') {
            $file = DB::table('uploaded_files')->where('id', $this->fileId)->first();
            $this->isViral = $file && $file->viral != 0;
        }
    }
    public function selectFileCode($fileCode)
    {
        $this->selectedfileCode = $fileCode;
    }
    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->startDate && $this->endDate && empty($this->selectedMonths)) {
            $this->selectedTimeFrame = 'custom';
            $this->tempSelectedTimeFrame = 'custom';
        }
        $this->remainingFatawadata();
        $this->sendingFatawadata();

    }

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->tempSelectedTimeFrame = $timeFrame;
        if ($timeFrame === 'all') {
            // Resetting the values
            $this->tempStartDate = null;
            $this->tempEndDate = null;
            $this->selectedMonths = [];
            $this->tempSelectedMonths = [];

            // Optional: Debugging statements
            // dd($this->tempStartDate, $this->tempEndDate, $this->selectedMonths, $this->tempSelectedMonths);
        }

    }
    public function updateSelectedFileCode($fileCodeframe)
    {
        $this->selectedfileCode = $fileCodeframe;
    }
    public function updateSelectedMujeeb($mujeebFrame)
    {
        $this->selectedmujeeb = $mujeebFrame;
    }
    public function updateSelectedMufti($muftiFrame)
    {
        $this->selectedmufti = $muftiFrame;
    }
    public function updateSelectedChecked($checked)
    {
        $this->selectedchecked = $checked;
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);
        if ($this->darulifta === null) {
            if (in_array('Nazim_Viral', $roleNames) || count(Auth::user()->roles) > 1) {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('uploaded_files.darulifta_name')
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->pluck('uploaded_files.darulifta_name');

                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                $this->fileCode = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('file_code')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->orderBy('file_code')
                    ->pluck('file_code');
                    $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
            } else {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', $firstRoleName)
                    ->pluck('darulifta_name');

                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                $this->fileCode = DB::table('uploaded_files')
                    ->select('file_code')
                    ->where('selected', 1)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('file_code');
                    $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
            }
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('darulifta_name');

            $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('selected', 1)
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('sender');
            $this->fileCode = DB::table('uploaded_files')
                ->select('file_code')
                ->where('selected', 1)
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('file_code');
                $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
        }
    }
    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')

        ->distinct()

        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()

    ->pluck('mail_folder_date');
    }
}
public function sendMessage($iftaCode)
{

    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.


    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}

public function render()
    {

        // $this->obtain = DB::table('uploaded_files')
        // ->where('selected', 0)
        // // Add any additional conditions here
        // ->select('mail_folder_date', DB::raw('SUM(total_score) as total_score_sum'))
        // ->groupBy('mail_folder_date')
        // ->havingRaw('COUNT(mail_folder_date) > 1')
        // ->get();
        $this->daruliftalist = DB::table('uploaded_files')
        ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('uploaded_files.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('uploaded_files.darulifta_name');

        // $this->mujeebs = DB::table('uploaded_files')
        // ->select('sender')
        // ->where('selected',1)
        // ->where('darulifta_name',$this->darulifta)
        // ->distinct()

        // ->pluck('sender');

        $this->checkerlist = DB::table('checker')
            ->get();
        $this->que_day_r = DB::table('questions')
        ->whereIn('question_branch', $this->daruliftaNames)
        ->get();
        $this->allfatawa = DB::table('uploaded_files')
    ->orderBy('id', 'asc') // Sorting by 'id' in ascending order
    ->get();
    $this->datefilter= DB::table('uploaded_files')
    ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
    ->orderBy('year', 'asc')
    ->orderBy('month', 'asc')
    ->get();
    $this->monthfilter = DB::table('uploaded_files')
    ->where('viral', '!=', 0) // Ensures viral is not 0
    ->selectRaw("YEAR(checked_date) as year, MONTH(checked_date) as month, COUNT(checked_date) as count")
    ->groupBy('year', 'month')
    ->orderBy('year', 'asc')
    ->orderBy('month', 'asc')
    ->get();
    $this->codebylower = DB::table('uploaded_files as uf1')
    ->select('uf1.*')
    ->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files GROUP BY file_code) as uf2'), function($join) {
        $join->on('uf1.id', '=', 'uf2.id');
    })
    ->orderBy('uf1.id', 'asc')
    ->get();

        $this->mahlenazar_null = DB::table('uploaded_files as u1')
        ->whereIn('u1.darulifta_name', $this->daruliftaNames)
        ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

        ->leftJoin('uploaded_files as u2', function ($join) {
            $join->on('u1.file_code', '=', 'u2.file_code')
                ->where(function ($query) {
                    $query->where('u2.checked_folder', 'ok')

                        ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere

                });
        })
        ->whereNull('u2.file_code')

        ->select('u1.*')
        ->get();
        $this->remainingFatawa = $this->remainingFatawadata();
        $this->shobaViralFatawa = $this->ShobaViralFatawadata();

// dd($this->remainingFatawa);

        $messages = Chat::latest()->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.viral-fatawa', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'checkers' => $this->checkers,
            'remainingFatawa' => $this->remainingFatawa,
            'sendingFatawa' => $this->sendingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmujeeb' => $this->selectedmujeeb,
            'selectedmufti' => $this->selectedmufti,
            'selectedchecked' => $this->selectedchecked,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'tempSelectedTimeFrame' => $this->tempSelectedTimeFrame,
            'selectedfileCode' => $this->selectedfileCode,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'monthfilter' => $this->monthfilter,
            'showDetail' => $this->showDetail,
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'userName' => $this->userName,
            'checkerlist' => $this->checkerlist,
            'munsab' => $this->munsab,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'fileCode' => $this->fileCode,
            'searchQuery' => $this->searchQuery,
            'shobaViralFatawa' => $this->shobaViralFatawa,




            // ... (other data to be passed to the view)
        ]

        )
        ->layout('layouts.app');

    }
    public function remainingFatawadata()
    {
        $remainingFatawas = [];

        // Handle case where "Other" is selected but no months are selected
        if ($this->tempSelectedTimeFrame == 'other' && empty($this->selectedMonths)) {
            // Return empty result to avoid loading data
            return $remainingFatawas;
        }

        $formattedSelectedMonths = array_map(function ($date) {
            return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
        }, $this->selectedMonths);



            foreach ($this->daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    ->leftJoin('users', 'uploaded_files.viral', '=', 'users.id') // Join with users table
                    ->select('uploaded_files.*', 'users.name as user_name') // Select user name
                    ->where('darulifta_name', $daruliftaName)
                    ->where('checked_folder', "ok")
                    ->where('viral', '!=', 0); // Ensures viral is not 0
                    if ($this->searchQuery) {
                        $query->where(function ($q) {
                            $q->where('file_code', 'like', '%' . $this->searchQuery . '%')
                              ->orWhere('sender', 'like', '%' . $this->searchQuery . '%')
                              ->orWhere('category', 'like', '%' . $this->searchQuery . '%')
                              ->orWhere('title', 'like', '%' . $this->searchQuery . '%');
                        });
                    }
                // Apply filters based on selected fields
                if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
                    $query->where('sender', $this->selectedmujeeb);
                }

                if ($this->selectedchecked == 'all') {
                    // Do not add any additional query conditions
                } elseif ($this->selectedchecked == 'selected_viral') {
                    $query->where('viral_upload', null);
                } elseif ($this->selectedchecked == 'ready_print') {
                    $query->where(function ($q) {
                        $q->whereNotNull('viral_upload')
                          ->where('viral_upload', '!=', '');
                    });
                } elseif ($this->selectedchecked == 'web_link') {
                    $query->where(function ($q) {
                        $q->whereNotNull('web_link')
                          ->where('web_link', '!=', '');
                    });
                } elseif ($this->selectedchecked == 'viral_link') {
                    $query->where(function ($q) {
                        $q->whereNotNull('viral_link')
                          ->where('viral_link', '!=', '');
                    });
                }

                if ($this->tempSelectedTimeFrame == 'all') {
                    // No date filtering, just return all data
                    $query->whereNotNull('checked_date');
                // Time Frame filters
                }
                elseif ($this->tempSelectedTimeFrame == 'this_month') {
                    $query->whereBetween(DB::raw('DATE(checked_date)'), [
                        now()->startOfMonth(),
                        now()->endOfMonth()
                    ]);
                }
                elseif ($this->tempSelectedTimeFrame == 'last_month') {
                    $query->whereBetween(DB::raw('DATE(checked_date)'), [
                        now()->subMonth()->startOfMonth(),
                        now()->subMonth()->endOfMonth()
                    ]);
                } elseif ($this->tempSelectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
                    $query->whereIn(DB::raw("DATE_FORMAT(checked_date, '%Y-%m')"), $formattedSelectedMonths);
                } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
                    $query->whereBetween(DB::raw('DATE(checked_date)'), [
                        Carbon::parse($this->startDate)->format('Y-m-d'),
                        Carbon::parse($this->endDate)->format('Y-m-d')
                    ]);
                }

                if ($this->selectedfileCode != 'all') {
                    $query->where('file_code', $this->selectedfileCode);
                }

                // Log the SQL query for debugging
                \Log::info($query->toSql());

                if ($query->exists()) {
                    $remainingFatawas[$daruliftaName] = $query->get();
                }

        }

        return $remainingFatawas;
    }
    public function ShobaViralFatawadata()
    {
        $shobaViralFatawas = [];

        // Handle case where "Other" is selected but no months are selected
                      $query = DB::table('uploaded_files')
                      ->where('checked_folder', "ok")
                    ->whereNotNull('shoba_viral')
                    ->where('viral', '!=', 0); // Ensures viral is not 0
                    if ($this->searchQuery) {
                        $query->where(function ($q) {
                            $q->where('file_code', 'like', '%' . $this->searchQuery . '%')
                              ->orWhere('title', 'like', '%' . $this->searchQuery . '%')
                              ->orWhere('category', 'like', '%' . $this->searchQuery . '%');
                        });
                    }
                // Apply filters based on selected fields

                if ($query->exists()) {
                    $shobaViralFatawas = $query->get();
                }



        return $shobaViralFatawas;
    }
    public function updatedSearch()
    {
        $this->remainingFatawa = $this->getFilteredFatawa();
    }

}
