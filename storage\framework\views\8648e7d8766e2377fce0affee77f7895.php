<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
        <div class="page-header min-height-300 border-radius-xl mt-4" 
             style="background-image: url('<?php echo e(asset('assets/img/curved-images/curved0.jpg')); ?>'); background-position-y: 50%;">
            <span class="mask bg-gradient-primary opacity-6"></span>
        </div>
        
        <div class="card card-body blur shadow-blur mx-4 mt-n6 overflow-hidden">
            <div class="row gx-4">
                <div class="col-auto">
                    <div class="avatar avatar-xl position-relative">
                        <div class="avatar avatar-xl position-relative">
                            <div class="w-100 h-100 rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-building text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-auto my-auto">
                    <div class="h-100">
                        <h5 class="mb-1">Department Management</h5>
                        <p class="mb-0 font-weight-bold text-sm">Manage departments and user assignments</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Department Management Component -->
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('department-management', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-2228608511-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
</div>

<style>
    .main-content {
        margin-left: 250px;
    }

    @media (max-width: 991.98px) {
        .main-content {
            margin-left: 0;
        }
    }

    /* Modern styling for the department management */
    .card {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: none;
        border-radius: 0.75rem;
    }

    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .table th {
        border-bottom: 1px solid #e9ecef;
        font-weight: 600;
        color: #6c757d;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table td {
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .badge {
        font-weight: 500;
        border-radius: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        border: 1px solid #d1d5db;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .modal-content {
        border-radius: 1rem;
        border: none;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .modal-header {
        border-bottom: 1px solid #f1f3f4;
        border-radius: 1rem 1rem 0 0;
    }

    .modal-footer {
        border-top: 1px solid #f1f3f4;
        border-radius: 0 0 1rem 1rem;
    }

    .alert {
        border-radius: 0.75rem;
        border: none;
    }

    .pagination .page-link {
        border-radius: 0.5rem;
        margin: 0 2px;
        border: 1px solid #d1d5db;
        color: #6b7280;
    }

    .pagination .page-link:hover {
        background-color: #f3f4f6;
        border-color: #d1d5db;
    }

    .pagination .page-item.active .page-link {
        background-color: #3b82f6;
        border-color: #3b82f6;
    }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user_type.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/departments/index.blade.php ENDPATH**/ ?>