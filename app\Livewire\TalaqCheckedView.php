<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\TalaqFatawa;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;




class TalaqCheckedView extends Component
{
    public $currentId; // The ID of the current record being viewed
    public $record;    // The current record



    public function mount($id = null)
    {
        $this->currentId = $id ?: TalaqFatawa::first()->id; // Default to the first record if no ID is passed
        $this->loadRecord();
    }
    public function loadRecord()
    {
        $this->record = TalaqFatawa::find($this->currentId);
        $this->comments = $this->record ? json_decode($this->record->comments, true) ?? [] : [];

    }

    public function next()
{
    // Fetch the next record based on the criteria
    $nextRecordId = DB::table('talaq_fatawa_manage')
        ->where('talaq_checked_id', '>', $this->currentId)
        ->where('selected', 1)
        ->orderBy('talaq_fatawa_id', 'asc')
        ->value('talaq_fatawa_id'); // Get only the ID

    if ($nextRecordId) {
        $this->currentId = $nextRecordId;
        $this->loadRecord();
    }
}

public function previous()
{
    // Fetch the previous record based on the criteria
    $previousRecordId = DB::table('talaq_fatawa_manage')
        ->where('talaq_checked_id', '<', $this->currentId)
        ->where('selected', 1)
        ->orderBy('talaq_fatawa_id', 'desc')
        ->value('talaq_fatawa_id'); // Get only the ID

    if ($previousRecordId) {
        $this->currentId = $previousRecordId;
        $this->loadRecord();
    }
}
    public function render()
    {
        return view('livewire.talaq-checked-view')->layout('layouts.app');
    }
}
