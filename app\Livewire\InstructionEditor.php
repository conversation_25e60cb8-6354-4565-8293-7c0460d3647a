<?php

namespace App\Livewire;


use Livewire\Component;
use App\Models\Instruction;

class InstructionEditor extends Component
{
    public $instructions;
    public $instruction_text;
    public $editId = null;
    public $newInstructionText = '';

    public function mount()
    {
        $this->loadInstructions();
    }

    public function loadInstructions()
    {
        $this->instructions = Instruction::all();
    }

    public function edit($id)
    {
        $instruction = Instruction::find($id);
        $this->editId = $id;
        $this->instruction_text = $instruction->instruction;
    }

    public function save()
    {
        if ($this->editId) {
            // Update existing instruction
            $instruction = Instruction::find($this->editId);
            $instruction->instruction = $this->instruction_text;
            $instruction->save();
        } else {
            // Create new instruction
            Instruction::create([
                'instruction' => $this->newInstructionText,
            ]);
            $this->newInstructionText = '';
        }

        // Reload instructions list
        $this->loadInstructions();
        $this->resetInput();

        // Dispatch event to refresh the dropdown (if needed)
        $this->dispatch('instruction-updated');
    }

    public function delete($id)
    {
        Instruction::find($id)->delete();
        $this->loadInstructions();
        $this->dispatch('instruction-updated'); // Dispatch event to refresh the dropdown (if needed)
    }

    public function resetInput()
    {
        $this->instruction_text = '';
        $this->editId = null;
    }

    public function render()
    {
        return view('livewire.instruction-editor');
    }
}