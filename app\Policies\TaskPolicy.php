<?php

namespace App\Policies;

use App\Models\Task;
use App\Models\User;

class TaskPolicy
{
    /**
     * Determine whether the user can view any tasks.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view tasks
    }

    /**
     * Determine whether the user can view the task.
     */
    public function view(User $user, Task $task): bool
    {
        return $user->isNazim() || 
               $user->isSuperior() || 
               $task->assigned_to === $user->id ||
               $task->assigned_by === $user->id;
    }

    /**
     * Determine whether the user can create tasks.
     */
    public function create(User $user): bool
    {
        return $user->isNazim() || $user->isSuperior();
    }

    /**
     * Determine whether the user can update the task.
     */
    public function update(User $user, Task $task): bool
    {
        return $user->isNazim() || 
               $task->assigned_by === $user->id ||
               ($user->isSuperior() && $this->isInSameDepartment($user, $task));
    }

    /**
     * Determine whether the user can delete the task.
     */
    public function delete(User $user, Task $task): bool
    {
        return $user->isNazim() || $task->assigned_by === $user->id;
    }

    /**
     * Determine whether the user can complete the task.
     */
    public function complete(User $user, Task $task): bool
    {
        return $task->assigned_to === $user->id;
    }

    /**
     * Determine whether the user can assign tasks to others.
     */
    public function assign(User $user): bool
    {
        return $user->isNazim() || $user->isSuperior();
    }

    /**
     * Check if user and task are in the same department.
     */
    private function isInSameDepartment(User $user, Task $task): bool
    {
        if (!$task->department_id) {
            return false;
        }

        return $user->departments->contains('id', $task->department_id);
    }
}
