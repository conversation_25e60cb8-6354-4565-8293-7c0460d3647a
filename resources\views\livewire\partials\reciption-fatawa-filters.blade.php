<!-- Filter Section -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter Options
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-3">
            <!-- Mujeeb Filter -->
            <div class="col-md-2">
                <label for="mujeebframe" class="form-label">
                    <i class="fas fa-user me-1"></i>
                    Select Mujeeb
                </label>
                <select class="form-select modern-select" id="mujeebframe" wire:model.live="selectedmujeeb" wire:loading.attr="disabled">
                    <option value="all">All Mujeeb</option>
                    @foreach ($mujeebs as $sender)
                        <option value="{{ $sender }}">
                            {{ $sender }}
                        </option>
                    @endforeach
                </select>
                <div wire:loading wire:target="selectedmujeeb" class="text-muted small mt-1">
                    <i class="fas fa-spinner fa-spin me-1"></i>
                    Updating...
                </div>
            </div>

            <!-- Exclude Status Filter -->
            <div class="col-md-2">
                <label for="exclude" class="form-label">
                    <i class="fas fa-eye-slash me-1"></i>
                    Exclude Status
                </label>
                <select class="form-select modern-select" id="exclude" wire:model.live="selectedexclude" wire:loading.attr="disabled">
                    <option value="exclude_ok">Exclude Ok</option>
                    <option value="all">All</option>
                </select>
                <div wire:loading wire:target="selectedexclude" class="text-muted small mt-1">
                    <i class="fas fa-spinner fa-spin me-1"></i>
                    Updating...
                </div>
            </div>

            <!-- Time Frame Filter -->
            <div class="col-md-2">
                <label for="timeframe" class="form-label">
                    <i class="fas fa-calendar me-1"></i>
                    Time Frame
                </label>
                <select class="form-select modern-select" id="timeframe" wire:model.live="tempSelectedTimeFrame" wire:loading.attr="disabled">
                    <option value="this_month">This Month</option>
                    <option value="last_month">Last Month</option>
                    <option value="all">All</option>
                    <option value="other">Other</option>
                    @if ($tempSelectedTimeFrame === 'custom')
                            <option value="custom" selected>Selected Date</option>
                        @endif
                    </select>
                <div wire:loading wire:target="tempSelectedTimeFrame" class="text-muted small mt-1">
                    <i class="fas fa-spinner fa-spin me-1"></i>
                    Updating...
                </div>
                </div>

                <!-- Start Date -->
                <div class="col-md-2">
                    <label for="start_date" class="form-label">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Start Date
                    </label>
                    <input type="date" class="form-control modern-input" id="start_date" wire:model.live="tempStartDate">
                </div>

                <!-- End Date -->
                <div class="col-md-2">
                    <label for="end_date" class="form-label">
                        <i class="fas fa-calendar-check me-1"></i>
                        End Date
                    </label>
                    <input type="date" class="form-control modern-input" id="end_date" wire:model.live="tempEndDate">
                </div>

                <!-- Apply Filters Button -->
                <div class="col-md-2 d-flex align-items-end">
                    <button type="button" wire:click="applyFilters" class="btn btn-primary modern-btn w-100" wire:loading.attr="disabled">
                        <span wire:loading.remove wire:target="applyFilters">
                            <i class="fas fa-search me-1"></i>
                            Apply Filters
                        </span>
                        <span wire:loading wire:target="applyFilters">
                            <i class="fas fa-spinner fa-spin me-1"></i>
                            Applying...
                        </span>
                    </button>
                </div>
            </div>

            <!-- Month Selector (Conditional) -->
            @if ($tempSelectedTimeFrame === 'other')
                <div class="mt-4">
                    <h6 class="mb-3">
                        <i class="fas fa-calendar-week me-2"></i>
                        Select Months
                    </h6>
                    <div class="month-selector-grid">
                        @foreach ($datefilter as $data)
                            @php
                                $monthName = DateTime::createFromFormat('!m', $data->month)->format('F');
                                $monthValue = "$data->year-$data->month";
                            @endphp
                            <div class="month-selector-item">
                                <input type="checkbox"
                                       id="month-{{ $data->year }}-{{ $data->month }}"
                                       value="{{ $monthValue }}"
                                       class="form-check-input"
                                       wire:model.live="tempSelectedMonths"
                                       wire:loading.attr="disabled">
                                <label for="month-{{ $data->year }}-{{ $data->month }}" class="form-check-label">
                                    {{ $monthName }} {{ $data->year }}
                                </label>
                            </div>
                        @endforeach
                    </div>
                    <div class="mt-3">
                        <button type="button" wire:click="applyFilters" class="btn btn-success modern-btn" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="applyFilters">
                                <i class="fas fa-filter me-1"></i>
                                Filter By Date
                            </span>
                            <span wire:loading wire:target="applyFilters">
                                <i class="fas fa-spinner fa-spin me-1"></i>
                                Filtering...
                            </span>
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
