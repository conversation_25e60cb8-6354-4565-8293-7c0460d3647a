<?php

// Command to create Livewire Component
// php artisan make:livewire ManageRecords

namespace App\Livewire;

use Livewire\Component;
use App\Models\Header;
use App\Models\ParentModel;
use App\Models\Child;

class ManageRecords extends Component
{
    protected $listeners = ['updateHeaderOrder'];
    public $headers;
    public $headerName, $headerSerialNumber, $headerId;

    public $parentName, $parentSerialNumber, $parentHeaderId, $parentId;
    public $parents = [];

    public $childName, $childSerialNumber, $childParentId, $childId;
    public $children = [];

    public $editingHeader = false, $editingParent = false, $editingChild = false;

    public function render()
{
    $this->headers = Header::with(['parents.children'])->orderBy('serial_number')->get();
    return view('livewire.manage-records')->layout('layouts.app');
}
public function updateHeaderOrder($ids)
{
    try {
        if (!is_array($ids) || empty($ids)) {
            throw new \Exception('Invalid ID array provided');
        }

        \DB::transaction(function () use ($ids) {
            foreach ($ids as $id) {
                Header::where('id', $id)->update(['serial_number' => null]);
            }

            foreach ($ids as $index => $id) {
                Header::where('id', $id)->update(['serial_number' => $index + 1]);
            }
        });
    } catch (\Exception $e) {
        \Log::error('Error updating header order: ' . $e->getMessage());
        session()->flash('error', 'Failed to update header order');
    }
}


    public function resetInputs()
    {
        $this->headerName = $this->headerSerialNumber = $this->headerId = null;
        $this->parentName = $this->parentSerialNumber = $this->parentHeaderId = $this->parentId = null;
        $this->childName = $this->childSerialNumber = $this->childParentId = $this->childId = null;
        $this->editingHeader = $this->editingParent = $this->editingChild = false;
    }

    public function saveHeader()
    {
        $this->validate([
            'headerName' => 'required',
            'headerSerialNumber' => 'required|integer|unique:headers,serial_number,' . $this->headerId,
        ]);

        Header::updateOrCreate(['id' => $this->headerId], [
            'name' => $this->headerName,
            'serial_number' => $this->headerSerialNumber,
        ]);

        $this->resetInputs();
    }

    public function editHeader($id)
    {
        $header = Header::findOrFail($id);
        $this->headerId = $header->id;
        $this->headerName = $header->name;
        $this->headerSerialNumber = $header->serial_number;
        $this->editingHeader = true;
    }

    public function deleteHeader($id)
    {
        Header::findOrFail($id)->delete();
        $this->resetInputs();
    }

    public function saveParent()
    {
        $this->validate([
            'parentName' => 'required',
            'parentSerialNumber' => 'required|integer',
            'parentHeaderId' => 'required|exists:headers,id',
        ]);

        ParentModel::updateOrCreate(['id' => $this->parentId], [
            'name' => $this->parentName,
            'serial_number' => $this->parentSerialNumber,
            'header_id' => $this->parentHeaderId,
        ]);

        $this->resetInputs();
    }

    public function editParent($id)
    {
        $parent = ParentModel::findOrFail($id);
        $this->parentId = $parent->id;
        $this->parentName = $parent->name;
        $this->parentSerialNumber = $parent->serial_number;
        $this->parentHeaderId = $parent->header_id;
        $this->editingParent = true;
    }

    public function deleteParent($id)
    {
        ParentModel::findOrFail($id)->delete();
        $this->resetInputs();
    }

    public function saveChild()
    {
        dd([
            'childName' => $this->childName,
            'childSerialNumber' => $this->childSerialNumber,
            'childParentId' => $this->childParentId,
            'childId' => $this->childId,
        ]);
        $this->validate([
            'childName' => 'required',
            'childSerialNumber' => 'required|integer',
            'childParentId' => 'required|exists:parents,id',
        ]);

        Child::updateOrCreate(['id' => $this->childId], [
            'name' => $this->childName,
            'serial_number' => $this->childSerialNumber,
            'parent_id' => $this->childParentId,
        ]);

        $this->resetInputs();
    }

    public function editChild($id)
    {
        $child = Child::findOrFail($id);
        $this->childId = $child->id;
        $this->childName = $child->name;
        $this->childSerialNumber = $child->serial_number;
        $this->childParentId = $child->parent_id;
        $this->editingChild = true;
    }

    public function deleteChild($id)
    {
        Child::findOrFail($id)->delete();
        $this->resetInputs();
    }
}
