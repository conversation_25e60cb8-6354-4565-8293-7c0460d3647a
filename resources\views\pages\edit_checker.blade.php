<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="mujeeb"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Edit Mujeeb"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <style>
            /* Styles for the form */
            .checker-form {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: #f7f7f7;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            }
            
            .checker-form .form-group {
                margin-bottom: 15px;
            }
            
            .checker-form label {
                font-weight: bold;
            }
            
            .checker-form .form-control {
                width: 100%;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
            }
            
            .checker-form button {
                display: block;
                width: 100%;
                padding: 10px;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            
            .checker-form button:hover {
                background-color: #0056b3;
            }
            
            /* Styles for the table */
            .checker-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            
            .checker-table th,
            .checker-table td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
        </style>

        <!-- Edit Form for Mujeeb -->
        <div class="container">
            <h1>Edit Checker</h1>
            <div class="checker-form">
                <form action="{{ route('checker.edit', ['id' => $upchecker->id]) }}" method="POST" class="checker-form">
                    @csrf
                    @method('PUT') <!-- Use PUT method for updating -->
                    <div class="form-group">
                        <label for="checker_name">Checker Name</label>
                        <input type="text" class="form-control" id="checker_name" name="checker_name" title="Enter Checker Name" required value="{{ $upchecker->checker_name }}">
                    </div>
                    <div class="form-group">
                        <label for="darul_name">Select Darulifta</label>
                        <select class="form-control" id="darul_name" name="darul_name" required>
                            <option value="">Select Darulifta</option>
                            @foreach($daruliftas as $darulifta)
                                <option value="{{ $darulifta->darul_name }}" @if($darulifta->darul_name == $upchecker->darul_name) selected @endif>{{ $darulifta->darul_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="munsab">Select Munsab</label>
                        <select class="form-control" id="munsab" name="munsab" required>
                            <option value="">Select Munsab</option>
                            <option value="Musaddiq" @if($upchecker->munsab == 'Musaddiq') selected @endif>Musaddiq</option>
                            <option value="Mutakhassis" @if($upchecker->munsab == 'Mutakhassis') selected @endif>Mutakhassis</option>
                            
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Update Checker</button>
                </form>
            </div>
        </div>

        <!-- Other content if needed -->

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
