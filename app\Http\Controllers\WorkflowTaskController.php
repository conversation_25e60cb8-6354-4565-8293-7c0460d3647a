<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use App\Models\DepartmentSupervisorAssistant;
use App\Services\DepartmentTeamManagementService;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Carbon\Carbon;

class WorkflowTaskController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of tasks.
     */
    public function index()
    {
        $this->authorize('assign-tasks');

        return view('workflow-tasks.index');
    }

    /**
     * Show the form for creating a new task.
     */
    public function create()
    {
        $this->authorize('create', Task::class);

        $users = User::select('id', 'name', 'email')->orderBy('name')->get();
        $departments = Department::active()->select('id', 'name')->orderBy('name')->get();

        // Get department team structures for better task assignment
        $departmentTeamService = app(DepartmentTeamManagementService::class);
        $departmentTeams = [];

        foreach ($departments as $department) {
            $departmentTeams[$department->id] = $departmentTeamService->getDepartmentTeamStructure($department);
        }

        return view('workflow-tasks.create', compact('users', 'departments', 'departmentTeams'));
    }

    /**
     * Store a newly created task.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Task::class);
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:daily,weekly',
            'assigned_to' => 'required|exists:users,id',
            'department_id' => 'nullable|exists:departments,id',
            'due_date' => 'required|date|after_or_equal:today',
            'priority' => 'required|integer|min:1|max:3',
        ]);

        $validated['assigned_by'] = auth()->id();

        Task::create($validated);

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task created successfully.');
    }

    /**
     * Display the specified task.
     */
    public function show(Task $task)
    {
        $this->authorize('view', $task);
        
        return view('workflow-tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the task.
     */
    public function edit(Task $task)
    {
        $this->authorize('update', $task);
        
        $users = User::select('id', 'name', 'email')->orderBy('name')->get();
        $departments = Department::active()->select('id', 'name')->orderBy('name')->get();
        
        return view('workflow-tasks.edit', compact('task', 'users', 'departments'));
    }

    /**
     * Update the specified task.
     */
    public function update(Request $request, Task $task)
    {
        $this->authorize('update', $task);
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:daily,weekly',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'assigned_to' => 'required|exists:users,id',
            'department_id' => 'nullable|exists:departments,id',
            'due_date' => 'required|date',
            'priority' => 'required|integer|min:1|max:3',
            'completion_notes' => 'nullable|string',
        ]);

        $task->update($validated);

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task updated successfully.');
    }

    /**
     * Remove the specified task.
     */
    public function destroy(Task $task)
    {
        $this->authorize('delete', $task);
        
        $task->delete();

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task deleted successfully.');
    }

    /**
     * Mark task as completed.
     */
    public function complete(Task $task)
    {
        $this->authorize('complete', $task);
        
        $task->markCompleted();

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task marked as completed.');
    }

    /**
     * Change task status.
     */
    public function changeStatus(Request $request, Task $task)
    {
        $this->authorize('update', $task);
        
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled',
        ]);

        $task->update($validated);

        return response()->json(['message' => 'Task status updated successfully.']);
    }

    /**
     * Get tasks for a specific user.
     */
    public function userTasks(User $user)
    {
        $this->authorize('viewPerformance', $user);
        
        $tasks = Task::where('assigned_to', $user->id)
            ->with(['assignedBy', 'department'])
            ->orderBy('due_date', 'asc')
            ->paginate(10);

        return view('workflow-tasks.user-tasks', compact('user', 'tasks'));
    }

    /**
     * Get task statistics.
     */
    public function statistics()
    {
        $this->authorize('assign-tasks');
        
        $user = auth()->user();
        
        $query = Task::query();
        
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $departmentIds = $user->departments->pluck('id');
                $query->where(function ($q) use ($user, $departmentIds) {
                    $q->where('assigned_by', $user->id)
                      ->orWhereIn('department_id', $departmentIds);
                });
            }
        }

        $stats = [
            'total_tasks' => $query->count(),
            'pending_tasks' => $query->where('status', 'pending')->count(),
            'in_progress_tasks' => $query->where('status', 'in_progress')->count(),
            'completed_tasks' => $query->where('status', 'completed')->count(),
            'overdue_tasks' => $query->where('due_date', '<', Carbon::today())
                                   ->whereNotIn('status', ['completed', 'cancelled'])
                                   ->count(),
            'daily_tasks' => $query->where('type', 'daily')->count(),
            'weekly_tasks' => $query->where('type', 'weekly')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get my tasks (for the authenticated user).
     */
    public function myTasks()
    {
        $tasks = Task::where('assigned_to', auth()->id())
            ->with(['assignedBy', 'department'])
            ->orderBy('due_date', 'asc')
            ->orderBy('priority', 'desc')
            ->paginate(10);

        return view('workflow-tasks.my-tasks', compact('tasks'));
    }
}
