<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CheckerTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        DB::table('checker')->insert([
            [
                'checker_name' => '<PERSON><PERSON><PERSON>',
                'darul_name' => 'Faizan-e-A<PERSON>ir, Gulzar-e-Taiba, Markaz-ul-Iqtisaad, Noorulirfan',
                'munsab' => 'Musaddiq',
                'folder_id' => 'mufti_ali_asghar',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'checker_name' => 'Say<PERSON> Ma<PERSON>od <PERSON>',
                'darul_name' => 'Faizan-e-Ajmair, Gulzar-e-Taiba, Markaz-ul-Iqtisaad, Noorulirfan',
                'munsab' => 'Mu<PERSON><PERSON>ssi<PERSON>',
                'folder_id' => 'sayed_masood',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
