<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="schedules"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Edit Appointment"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <!-- Include jQuery library -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
   

    <!-- Create Appointment Entry Form -->
    <style>
        #emptbl th,
        #emptbl td {
            padding: 8px;
            border: 1px solid #ccc;
            text-align: center;
        }
        
        #emptbl {
            width: 100%;
            border-collapse: collapse;
        }
        
        /* Define alternate row colors */
        #emptbl tr:nth-child(odd) {
            background-color: #f2f2f2;
        }
        
        #emptbl tr:nth-child(even) {
            background-color: #ffffff;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            #emptbl th, #emptbl td {
                display: block;
                text-align: left;
                width: 100%;
            }
            
            #emptbl th {
                font-weight: bold;
            }
            
            #emptbl td:before {
                content: attr(data-label);
                float: left;
                font-weight: normal;
            }
            
            #col5 textarea {
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
        <div class="container">
            <h1>Edit Schedules</h1>
            <form action="{{ route('schedules.update', ['id' => $schedules->id]) }}" method="POST">
                @csrf
                @method('PUT') <!-- Use the PUT method for updates -->
                <div class="mb-3">
                    <label for="date" class="form-label">Schedule Date</label>
                    <input type="date" class="form-control" id="date" name="date" value="{{ $schedules->schedule_date }}">
                </div>
                <div class="mb-3">
                    <label for="day" class="form-label">Schedule Day</label>
                    <select name="day" class="form-control">
                        <option value="Sunday" {{ $schedules->schedule_day === 'Sunday' ? 'selected' : '' }}>Sunday</option>
                        <option value="Monday" {{ $schedules->schedule_day === 'Monday' ? 'selected' : '' }}>Monday</option>
                        <option value="Monday" {{ $schedules->schedule_day === 'Tuesday' ? 'selected' : '' }}>Tuesday</option>
                        <option value="Monday" {{ $schedules->schedule_day === 'Wednesday' ? 'selected' : '' }}>Wednesday</option>
                        <option value="Monday" {{ $schedules->schedule_day === 'Thursday' ? 'selected' : '' }}>Thursday</option>
                        <option value="Monday" {{ $schedules->schedule_day === 'Friday' ? 'selected' : '' }}>Friday</option>
                        <option value="Monday" {{ $schedules->schedule_day === 'Saturday' ? 'selected' : '' }}>Saturday</option>
                        <!-- Add options for other days of the week -->
                    </select>
                </div>
                <div class="mb-3">
                    <label for="time" class="form-label">Schedule Time</label>
                    <input type="time" class="form-control" id="time" name="time" value="{{ $schedules->schedule_time }}">
                </div>
                <div class="mb-3">
                    <label for="scheduler" class="form-label">Event Name</label>
                    <input type="name" class="form-control" id="event" name="event" value="{{ $schedules->event_name }}">
                </div>
                <div class="mb-3">
                    <label for="location" class="form-label">Location</label>
                    <input type="location" class="form-control" id="location" name="location" value="{{ $schedules->location }}">
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="4">{{ $schedules->description }}</textarea>
                </div>
                <button type="submit" class="btn btn-primary">Update Appointment</button>
            </form>
        </div>
    
        <script>
            $(document).ready(function() {
        $('.bootstrap-timepicker').timepicker({
            showMeridian: false, // Use 24-hour format
            showSeconds: false, // Remove seconds from the interface
            defaultTime: false // Don't set a default time
        });
    });
            const dateInput = document.querySelector('input[name="date"]');
            const daySelect = document.querySelector('select[name="day"]');
    
            dateInput.addEventListener('change', () => {
                const selectedDate = new Date(dateInput.value);
                const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const selectedDay = daysOfWeek[selectedDate.getDay()];
    
                // Clear existing options and add the calculated day as the only option
                daySelect.innerHTML = '';
                const option = document.createElement('option');
                option.value = selectedDay;
                option.textContent = selectedDay;
                daySelect.appendChild(option);
            });
        </script>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
