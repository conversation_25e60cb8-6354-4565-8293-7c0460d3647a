<div>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Example of including Bootstrap 5 -->

    <script src="//unpkg.com/alpinejs" defer></script>


    <style>
    .pagination-links {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 10px 0;
}

/* Pagination structure */
.pagination {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

/* Style for individual page items (previous/next buttons and page numbers) */
.pagination .page-item {
    margin: 0 5px;
}

/* Page link styling */
.pagination .page-link {
    display: inline-block;
    padding: 8px 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    background-color: #fff;
}

/* Hover effect on page links */
.pagination .page-link:hover {
    background-color: #f1f1f1;
    color: #0056b3;
}

/* Active page link style */
.pagination .page-item.active .page-link {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Disabled page link style (e.g., "Previous" and "Next" when disabled) */
.pagination .page-item.disabled .page-link {
    background-color: #e0e0e0;
    color: #b0b0b0;
    cursor: not-allowed;
}

/* First and last item rounding */
.pagination .page-item:first-child .page-link {
    border-radius: 4px 0 0 4px;
}

.pagination .page-item:last-child .page-link {
    border-radius: 0 4px 4px 0;
}
table, th, td {
    color: #333; /* Dark black font for table text */
}

h1 {
    color: #333; /* Dark black color for headings */
}

.search-bar input {
    color: #333; /* Dark black font for search bar */
}

/* Ensure icons have the blue color */
i.fas {
    color: #007bff; /* Blue color for font awesome icons */
}
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 0.4rem 0.8rem; /* Slightly smaller padding for mobile */
        font-size: 0.9rem;
    }
}
        .main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }

    .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
    table {
            font-family: Jameel Noori Nastaleeq;
            width: 100%;
            margin: 10px 0;
            border-collapse: collapse;
            background-color: #fff;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #333;
        }
        .search-bar {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .search-bar input {
            padding: 8px;
            width: 100%;
            max-width: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination-links {
            margin-top: 40px;
        }

    </style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
@livewire('navbar', ['titlePage' => 'Talaq Remaining'])


            @php

            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        @endphp
        <div id="loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.7); z-index:9999; text-align:center;">
            <div style="position:relative; top:50%; transform:translateY(-50%);">
                <h2>Loading...</h2>
                <!-- You can replace this with a spinner or any loader image -->
            </div>
        </div>

        <div class="month-container">

    <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">

    <div class="container">
        <h1>Talaq Remaining</h1>
        @php
// Check if the user is authenticated before checking roles
$Admin = Auth::check() && (
    in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
    in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
);
@endphp

    @if ($Admin)
        <div style="display: flex; align-items: center; margin-bottom: 20px;">
            <div>
                @php $isAllIftaActive = request()->route('darulifta') == null; @endphp
                <a href="{{ route('talaq-remaining') }}"
                   class="apply-filters-button {{ $isAllIftaActive ? 'apply-filters-button-active' : '' }}">
                   All Ifta
                </a>
            </div>

            @foreach($daruliftalist as $daruliftalistn)
                @php $isActive = request()->route('darulifta') == $daruliftalistn; @endphp
                <div>
                    <a href="{{ route('talaq-remaining', ['darulifta' => $daruliftalistn]) }}"
                       class="apply-filters-button {{ $isActive ? 'apply-filters-button-active' : '' }}">
                        {{ $daruliftalistn }}
                    </a>
                </div>
            @endforeach
        </div>
    @endif
        <!-- Search Bar -->
        <div class="search-bar">
    <input
        type="text"
        wire:model.live="search"
        placeholder="Search by title, fatwa no, or category...">
</div>
@if ($Admin)
<div class="d-flex justify-content-between mb-3">
        <button wire:click="toggleOrderBy" class="btn btn-primary">
            {{ $orderByDaruliftaName ? 'Order by Checked Date' : 'Order by Darulifta Name' }}
        </button>
    </div>
@endif
<table class="table">
    <thead>
        <tr>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">S.No</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Fatwa No</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Mujeeb</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">New/M.Nazar</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Q Send Date</th>
        <!-- <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Q.R.Date & Days at Reciption</th> -->
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Darulifta</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Category</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Actions</th> <!-- Updated column for actions -->
        </tr>
    </thead>
    <tbody>
        @php
         $serialNumber = ($question_b->currentPage() - 1) * $question_b->perPage() + 1;
         $previousDarulifta = null;
         @endphp
        @foreach($question_b as $question)
          @if($orderByDaruliftaName && $previousDarulifta !== null && $previousDarulifta !== $question->darulifta_name)
                <!-- Add bold black line when darulifta_name changes -->
                <tr>
                    <td colspan="6" style="border-top: 3px solid black;"></td>
                </tr>
            @endif
            <tr wire:key="question-row-{{ $question->id }}">
                <td>{{ $serialNumber++ }}</td>
                <td>{{ $question->file_code }}</td>
                <td>{{ $question->sender }}</td>
                <td class="align-middle text-center">
                <span class="font-weight-bold" style="color: blue;">{{ $question->ftype }}</span>
                </td>
                <td class="align-middle text-center">
                <span class="font-weight-bold"  style="color: green; width: 5%; white-space: normal;">{{ $question->mail_recived_date }}</span>
                </td>
                <td>{{ $question->darulifta_name }}</td>
                <td>{{ $question->category }}</td>

                <td>
                @if ($Admin)
                <span class="view">
                    <a href="{{ route('talaq-fatwa-edit',  $question->talaq_fatawa_id) }}" target="_blank">
                    <i class="fas fa-edit"></i>
                    </a>
                </span>
                @else
                <span class="view">
                    <a href="{{ route('talaq-remaining-view', $question->talaq_fatawa_id) }}" target="_blank">
                        <i class="fas fa-eye"></i>
                    </a>
                </span>
                @endif

                @if ($Admin)
    <span class="delete">
        <button wire:click="deleteRecord({{ $question->talaq_fatawa_id }})" class="btn btn-danger btn-sm">
            <i class="fas fa-trash"></i> Delete
        </button>
    </span>
@else
    @if ($question->downloaded_by_admin === null)
        <span class="delete">
            <button wire:click="deleteRecord({{ $question->talaq_fatawa_id }})" class="btn btn-danger btn-sm">
                <i class="fas fa-trash"></i> Delete
            </button>
        </span>
    @else
        <span class="admin-message text-warning">
            This Fatawa has been viewed by Admin.
        </span>
    @endif
@endif
            </td>
                @php
                $previousDarulifta = $question->darulifta_name;
            @endphp
            </tr>
        @endforeach
    </tbody>
</table>

<!-- Display error message if the file does not exist -->


<!-- Pagination Links -->
<div class="pagination-links d-flex justify-content-center">
    {{ $question_b->links() }}
</div>


    </div>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

<div wire:ignore>
           <x-layout bodyClass="g-sidenav-show  bg-gray-200">
           @if ($Admin)
<x-navbars.sidebar activePage="{{ request()->route('darulifta') ?? 'viral-fatawa' }}"></x-navbars.sidebar>
@else
<x-navbars.msidebar activePage="{{ request()->route('darulifta') ?? 'viral-fatawa' }}"></x-navbars.msidebar>
@endif
</x-layout>
</div>
    <x-footers.auth></x-footers.auth>

</main>



</div>
