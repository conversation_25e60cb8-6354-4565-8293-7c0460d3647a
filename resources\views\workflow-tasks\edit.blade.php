@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Edit Task</h5>
                            <p class="text-sm mb-0">
                                {{ $task->title }}
                                <span class="badge bg-gradient-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning')) }} ms-2">
                                    {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                </span>
                            </p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <a href="{{ route('workflow-tasks.show', $task) }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Task Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('workflow-tasks.update', $task) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" name="title" 
                                           class="form-control @error('title') is-invalid @enderror"
                                           value="{{ old('title', $task->title) }}"
                                           placeholder="Enter task title">
                                    @error('title') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Type *</label>
                                    <select name="type" class="form-select @error('type') is-invalid @enderror">
                                        <option value="">Select Type</option>
                                        <option value="daily" {{ old('type', $task->type) === 'daily' ? 'selected' : '' }}>Daily</option>
                                        <option value="weekly" {{ old('type', $task->type) === 'weekly' ? 'selected' : '' }}>Weekly</option>
                                        <option value="monthly" {{ old('type', $task->type) === 'monthly' ? 'selected' : '' }}>Monthly</option>
                                        <option value="one_time" {{ old('type', $task->type) === 'one_time' ? 'selected' : '' }}>One Time</option>
                                    </select>
                                    @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select name="assigned_to" class="form-select @error('assigned_to') is-invalid @enderror">
                                        <option value="">Select User</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('assigned_to', $task->assigned_to) == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select name="department_id" class="form-select @error('department_id') is-invalid @enderror">
                                        <option value="">Select Department</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}" {{ old('department_id', $task->department_id) == $department->id ? 'selected' : '' }}>
                                                {{ $department->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Priority *</label>
                                    <select name="priority" class="form-select @error('priority') is-invalid @enderror">
                                        <option value="">Select Priority</option>
                                        <option value="1" {{ old('priority', $task->priority) == '1' ? 'selected' : '' }}>Low</option>
                                        <option value="2" {{ old('priority', $task->priority) == '2' ? 'selected' : '' }}>Medium</option>
                                        <option value="3" {{ old('priority', $task->priority) == '3' ? 'selected' : '' }}>High</option>
                                    </select>
                                    @error('priority') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Status</label>
                                    <select name="status" class="form-select @error('status') is-invalid @enderror">
                                        <option value="pending" {{ old('status', $task->status) === 'pending' ? 'selected' : '' }}>Pending</option>
                                        <option value="in_progress" {{ old('status', $task->status) === 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        <option value="completed" {{ old('status', $task->status) === 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="cancelled" {{ old('status', $task->status) === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                    @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Due Date</label>
                                    <input type="date" name="due_date" 
                                           class="form-control @error('due_date') is-invalid @enderror"
                                           value="{{ old('due_date', $task->due_date ? $task->due_date->format('Y-m-d') : '') }}">
                                    @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea name="description" 
                                      class="form-control @error('description') is-invalid @enderror"
                                      rows="4"
                                      placeholder="Describe the task details, requirements, and expectations...">{{ old('description', $task->description) }}</textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        @if($task->status === 'completed')
                            <!-- Completion Notes (Read-only for completed tasks) -->
                            <div class="form-group">
                                <label class="form-label">Completion Notes</label>
                                <textarea class="form-control bg-light" rows="3" readonly>{{ $task->completion_notes }}</textarea>
                            </div>
                        @endif

                        <!-- Submit Buttons -->
                        <div class="form-group text-end">
                            <a href="{{ route('workflow-tasks.show', $task) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i>&nbsp;&nbsp;Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>&nbsp;&nbsp;Update Task
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
