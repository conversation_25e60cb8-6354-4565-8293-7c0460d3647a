<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('uploaded_files', function (Blueprint $table) {
            $table->id();
            $table->string('file_name');
            $table->string('file_code');
            $table->string('sender');
            $table->date('file_created_date');
            $table->enum('ftype', ['New', 'Mahl e Nazar']);
            $table->date('mail_folder_date');
            $table->date('mail_recived_date');
            $table->string('darulifta');
            $table->string('category');
            $table->boolean('selected')->default(false);
            $table->string('checked_folder');
            $table->string('checked_file_name');
            $table->string('checked_grade');
            $table->string('checked_tasurat');
            $table->string('checked_Instructions');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uploaded_files');
    }
};
