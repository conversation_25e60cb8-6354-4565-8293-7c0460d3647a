<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_performance', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->date('performance_date');
            $table->text('tasks_completed')->nullable();
            $table->text('challenges_faced')->nullable();
            $table->text('next_day_plan')->nullable();
            $table->integer('fatawa_processed')->default(0);
            $table->integer('questions_answered')->default(0);
            $table->decimal('hours_worked', 4, 2)->nullable();
            $table->enum('overall_rating', ['poor', 'fair', 'good', 'excellent'])->default('good');
            $table->text('additional_notes')->nullable();
            $table->boolean('is_submitted')->default(false);
            $table->timestamp('submitted_at')->nullable();
            $table->timestamps();
            
            // Ensure one performance record per user per day
            $table->unique(['user_id', 'performance_date']);
            
            // Indexes for efficient queries
            $table->index(['user_id', 'performance_date']);
            $table->index(['performance_date', 'is_submitted']);
            $table->index(['is_submitted', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_performance');
    }
};
