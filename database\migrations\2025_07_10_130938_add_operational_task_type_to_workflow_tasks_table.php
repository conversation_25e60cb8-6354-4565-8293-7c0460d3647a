<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add 'operational' to the task type enum
        DB::statement("ALTER TABLE workflow_tasks MODIFY COLUMN type ENUM('daily', 'weekly', 'operational') DEFAULT 'daily'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'operational' from the task type enum
        DB::statement("ALTER TABLE workflow_tasks MODIFY COLUMN type ENUM('daily', 'weekly') DEFAULT 'daily'");
    }
};
