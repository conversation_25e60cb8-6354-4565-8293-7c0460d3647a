<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class MutakhassisController extends Controller
{
    public function showData()
    {
        $userRoles = auth()->user()->roles->pluck('name')->toArray();
    $userName = auth()->user()->name; // Assuming 'name' is the username field
    // Fetch data from the database

        $checker = DB::table('checker')
            ->where('checker_name', $userName)
            ->first()
            ->folder_id ?? null; // Use folder_id from matched checker, or null if not found
            // dd($checker);

        $daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->pluck('darulifta_name');

        $mailfolderDates = DB::table('uploaded_files')
                    ->select('mail_folder_date')
                    ->where('selected', 0)
                    ->distinct()
                    ->orderBy('mail_folder_date', 'desc') // Sort in descending order
                    ->when($checker, function ($query, $checker) {
                        // Apply this condition only if $checker is not null
                        return $query->where('checker', $checker);
                    })
                    ->pluck('mail_folder_date');


        $summaryReport = [];

        foreach ($daruliftaNames as $daruliftaName) {
            // Get unique mail_folder_date values related to the current darulifta_name
            $mailFolderDates = DB::table('uploaded_files')
                ->select('mail_folder_date')
                ->orderBy('mail_folder_date')
                ->where('selected', 0)
                ->where('darulifta_name', $daruliftaName)
                ->distinct()
                ->when($checker, function ($query, $checker) {
                    // Apply this condition only if $checker is not null
                    return $query->where('checker', $checker);
                })
                ->pluck('mail_folder_date');

            $summaryItem = [
                'Darulifta' => $daruliftaName,
                'Mail Recived' => $mailFolderDates->implode('  |  ')
            ];

            $summaryReport[] = $summaryItem;
        }
        $dataByDaruliftaName = [];
        foreach ($mailfolderDates as $mailfolderDate) {
            $data = [];

            foreach ($daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    // ->select('file_code', 'sender', 'ftype', 'mail_recived_date', 'category')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('selected', 0)
                    ->when($checker, function ($query, $checker) {
                        // Apply this condition only if $checker is not null
                        return $query->where('checker', $checker);
                    })
                    ->get();

                // Store the data for the current combination in the array
                if ($query->isNotEmpty()) {
                $dataByDaruliftaName[$daruliftaName][$mailfolderDate] = $query;
            }
            }
            }
            $checkers = DB::table('checker')->get();
        // Assuming you want to return this data to a view called 'mutakhassis.show'
        // You might want to adjust the view name based on your actual view file

        return view('pages.mutakhassis', compact('daruliftaNames','mailfolderDates','summaryReport','dataByDaruliftaName','checker','checkers'));
    }
    public function byMufti()
    {
        $userRoles = auth()->user()->roles->pluck('name')->toArray();
    $userName = auth()->user()->name; // Assuming 'name' is the username field
    // Fetch data from the database

        $checker = DB::table('checker')
            ->where('checker_name', $userName)
            ->first()
            ->folder_id ?? null; // Use folder_id from matched checker, or null if not found
            // dd($checker);

        $daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->pluck('darulifta_name');

        $mailfolderDates = DB::table('uploaded_files')
                    ->select('mail_folder_date')
                    ->where('selected', 1)
                    ->where('by_mufti', 'to_checker')
                    ->where('transfer_by', $checker)
                    ->distinct()
                    ->pluck('mail_folder_date');


        $summaryReport = [];

        foreach ($daruliftaNames as $daruliftaName) {
            // Get unique mail_folder_date values related to the current darulifta_name
            $mailFolderDates = DB::table('uploaded_files')
                ->select('mail_folder_date')
                ->orderBy('mail_folder_date')
                ->where('selected', 1)
                ->where('by_mufti', 'to_checker')
                ->where('darulifta_name', $daruliftaName)
                ->where('transfer_by', $checker)
                ->distinct()
                ->pluck('mail_folder_date');

            $summaryItem = [
                'Darulifta' => $daruliftaName,
                'Mail Recived' => $mailFolderDates->implode('  |  ')
            ];

            $summaryReport[] = $summaryItem;
        }
        $dataByDaruliftaName = [];
        foreach ($mailfolderDates as $mailfolderDate) {
            $data = [];

            foreach ($daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    // ->select('file_code', 'sender', 'ftype', 'mail_recived_date', 'category')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('selected', 1)
                    ->where('by_mufti', 'to_checker')
                    ->where('transfer_by', $checker)
                    ->get();

                // Store the data for the current combination in the array
                if ($query->isNotEmpty()) {
                $dataByDaruliftaName[$daruliftaName][$mailfolderDate] = $query;
            }
            }
            }
            $checkers = DB::table('checker')->get();
            // dd($userRoles,$userName,$checker,);
        // Assuming you want to return this data to a view called 'mutakhassis.show'
        // You might want to adjust the view name based on your actual view file

        return view('pages.transfer_by_mufti', compact('daruliftaNames','mailfolderDates','summaryReport','dataByDaruliftaName','checker','checkers'));
    }
    public function transferMufti(Request $request, $id)
{

    $selectedMufti = $request->input('selectedMufti');
    $mailFolderDate = $request->input('mailFolderDate');
    $daruliftaName = $request->input('daruliftaName');
    $checker = $request->input('checker');
    $fileName = $request->input('fileName');
    $transferBy = $request->input('transferBy');

    $checkerFolderId = DB::table('checker')
    ->where('checker_name', $selectedMufti)
    ->first()
    ->folder_id ?? null;
    // Construct old and new file paths

    if (!empty($transferBy)) {
        $oldDirectory = 'public/' . $mailFolderDate . $daruliftaName . $checker . '_by_' . $transferBy;
    } else {
        $oldDirectory = 'public/' . $mailFolderDate . $daruliftaName . $checker;
    }
    $newDirectory = 'public/' . $mailFolderDate . $daruliftaName . $checkerFolderId .'_by_'.$checker;
    $oldFilePath = $oldDirectory . '/' . $fileName;
    $newFilePath = $newDirectory . '/' . $fileName;

    if (Storage::exists($oldFilePath)) {
        // Always create the new directory and subfolders
        Storage::makeDirectory($newDirectory, 0755, true); // Ensure newDirectory is created (recursive)
        Storage::makeDirectory("$newDirectory/Checked", 0755, true); // Create Checked folder
        Storage::makeDirectory("$newDirectory/Mahl-e-Nazar", 0755, true); // Create Mahl-e-Nazar folder

        // Move the file
        Storage::move($oldFilePath, $newFilePath);

        // Update database after moving the file
        DB::table('uploaded_files')
            ->where('id', $id)
            ->update([
                'transfer_to' => $selectedMufti,
                'checker' => $checkerFolderId,
                'transfer_by' => $checker,
            ]);

        return response()->json(['success' => true]);
    } else {
        return response()->json(['error' => 'File does not exist'], 404);
    }
}
public function transferMujeeb(Request $request, $id)
{
    $selectedMujeeb = $request->input('selectedMujeeb');
    $mailFolderDate = $request->input('mailFolderDate');
    $daruliftaName = $request->input('daruliftaName');
    $checker = $request->input('checker');
    $checkedFolder = $request->input('checkedFolder');
    $transferTo = $request->input('transferTo');
    $fileName = $request->input('fileName');

    // Construct old and new file paths
    $oldDirectory = 'public/' . $mailFolderDate . $daruliftaName .'Checked_by_'. $checker .'_'. $transferTo .'/'.$checkedFolder;
    $newDirectory = 'public/' . $mailFolderDate . $daruliftaName . 'Checked_by_'. $checker .'_'.$selectedMujeeb .'/'.$checkedFolder;
    $oldFilePath = $oldDirectory . '/' . $fileName;
    $newFilePath = $newDirectory . '/' . $fileName;

    if (Storage::exists($oldFilePath)) {
        // Check if new directory exists, create if not
        if (!Storage::exists($newDirectory)) {
            Storage::makeDirectory($newDirectory);
        }

        // Move the file
        Storage::move($oldFilePath, $newFilePath);

        // Update database after moving the file
        DB::table('uploaded_files')
            ->where('id', $id)
            ->update([
                'by_mufti' => $transferTo,

            ]);

        return response()->json(['success' => true]);
    } else {
        return response()->json(['error' => 'File does not exist'], 404);
    }
}
}
