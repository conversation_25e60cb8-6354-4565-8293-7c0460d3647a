<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Tasurat;

class TasuratDropdown extends Component
{
    public $tasurats;
    public $selectedTasuratId;

    // Listening for the 'tasurat-updated' and 'resetTasuratDropdown' events
    protected $listeners = [
        'tasurat-updated' => 'loadTasurats',
        'resetTasuratDropdown' => 'resetTasurat'
    ];

    public function mount()
    {
        $this->loadTasurats();
    }

    public function loadTasurats()
    {
        $this->tasurats = Tasurat::all();
    }

    public function resetTasurat()
    {
        $this->selectedTasuratId = null; // Reset to default or null
    }

    public function render()
    {
        return view('livewire.tasurat-dropdown');
    }
}
