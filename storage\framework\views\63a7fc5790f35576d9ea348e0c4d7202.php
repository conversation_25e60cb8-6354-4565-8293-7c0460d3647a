<div>
    <!--[if BLOCK]><![endif]--><?php if($showNotifications): ?>
    <!-- Notifications Panel -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-white me-2">notifications</i>
                            <h6 class="text-white mb-0 fw-bold">System Notifications</h6>
                            <span class="badge bg-white text-primary ms-2"><?php echo e(count($notifications)); ?></span>
                        </div>
                        <div class="d-flex gap-2">
                            <button wire:click="refreshNotifications" class="btn btn-sm btn-outline-light" title="Refresh">
                                <i class="material-icons">refresh</i>
                            </button>
                            <button wire:click="dismissAllNotifications" class="btn btn-sm btn-outline-light" title="Dismiss All">
                                <i class="material-icons">clear_all</i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sortedNotifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="list-group-item border-0 py-3 px-4 notification-item notification-<?php echo e($notification['type']); ?>">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <div class="icon icon-shape bg-gradient-<?php echo e($notification['type'] === 'danger' ? 'danger' : ($notification['type'] === 'warning' ? 'warning' : ($notification['type'] === 'info' ? 'info' : 'primary'))); ?> shadow text-center border-radius-md">
                                        <i class="material-icons opacity-10 text-white"><?php echo e($notification['icon']); ?></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 fw-bold text-dark"><?php echo e($notification['title']); ?></h6>
                                            <p class="mb-2 text-sm text-secondary"><?php echo e($notification['message']); ?></p>
                                            <!--[if BLOCK]><![endif]--><?php if($notification['action_url'] && $notification['action_text']): ?>
                                                <a href="<?php echo e($notification['action_url']); ?>" 
                                                   class="btn btn-sm bg-gradient-<?php echo e($notification['type'] === 'danger' ? 'danger' : ($notification['type'] === 'warning' ? 'warning' : ($notification['type'] === 'info' ? 'info' : 'primary'))); ?>">
                                                    <?php echo e($notification['action_text']); ?>

                                                </a>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                        <div class="d-flex flex-column align-items-end">
                                            <span class="badge badge-sm bg-<?php echo e($notification['type'] === 'danger' ? 'danger' : ($notification['type'] === 'warning' ? 'warning' : ($notification['type'] === 'info' ? 'info' : 'primary'))); ?> mb-2">
                                                <?php echo e(ucfirst($notification['priority'])); ?>

                                            </span>
                                            <button wire:click="dismissNotification(<?php echo e($index); ?>)" 
                                                    class="btn btn-sm btn-outline-secondary" 
                                                    title="Dismiss">
                                                <i class="material-icons text-xs">close</i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <style>
    .notification-item {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
        transform: translateX(2px);
    }

    .notification-danger {
        border-left-color: #f5365c;
        background-color: rgba(245, 54, 92, 0.05);
    }

    .notification-warning {
        border-left-color: #fb6340;
        background-color: rgba(251, 99, 64, 0.05);
    }

    .notification-info {
        border-left-color: #11cdef;
        background-color: rgba(17, 205, 239, 0.05);
    }

    .notification-success {
        border-left-color: #2dce89;
        background-color: rgba(45, 206, 137, 0.05);
    }

    .notification-primary {
        border-left-color: #5e72e4;
        background-color: rgba(94, 114, 228, 0.05);
    }

    .notification-icon .icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notification-icon .material-icons {
        font-size: 20px;
    }

    .bg-gradient-danger {
        background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
    }

    .badge-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .btn-outline-light:hover {
        background-color: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
    }

    /* Animation for new notifications */
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .notification-item {
        animation: slideInDown 0.5s ease-out;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .notification-item {
            padding: 1rem !important;
        }
        
        .notification-icon .icon {
            width: 32px;
            height: 32px;
        }
        
        .notification-icon .material-icons {
            font-size: 16px;
        }
        
        .d-flex.justify-content-between {
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .d-flex.flex-column.align-items-end {
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }
    }

    /* Loading state */
    .notification-loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Pulse animation for critical notifications */
    .notification-item.notification-danger .notification-icon .icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(245, 54, 92, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(245, 54, 92, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(245, 54, 92, 0);
        }
    }
</style>

</div>

<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/dashboard-notifications.blade.php ENDPATH**/ ?>