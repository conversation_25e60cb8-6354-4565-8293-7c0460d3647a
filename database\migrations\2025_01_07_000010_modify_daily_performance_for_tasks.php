<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('daily_performance', function (Blueprint $table) {
            // Add task and department relationship
            $table->foreignId('task_id')->nullable()->constrained('workflow_tasks')->onDelete('cascade');
            $table->foreignId('department_id')->nullable()->constrained('departments')->onDelete('set null');
            
            // Remove fatawa-related fields as they're not needed
            $table->dropColumn(['fatawa_processed', 'questions_answered']);
            
            // Add superior rating fields
            $table->text('superior_comments')->nullable();
            $table->enum('superior_rating', ['poor', 'fair', 'good', 'excellent'])->nullable();
            $table->foreignId('rated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('rated_at')->nullable();
            
            // Modify existing fields
            $table->text('tasks_completed')->nullable()->change();
            $table->enum('overall_rating', ['poor', 'fair', 'good', 'excellent'])->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('daily_performance', function (Blueprint $table) {
            // Remove new fields
            $table->dropForeign(['task_id']);
            $table->dropForeign(['department_id']);
            $table->dropForeign(['rated_by']);
            $table->dropColumn(['task_id', 'department_id', 'superior_comments', 'superior_rating', 'rated_by', 'rated_at']);
            
            // Add back removed fields
            $table->integer('fatawa_processed')->default(0);
            $table->integer('questions_answered')->default(0);
            
            // Revert field changes
            $table->text('tasks_completed')->nullable(false)->change();
            $table->enum('overall_rating', ['poor', 'fair', 'good', 'excellent'])->nullable(false)->change();
        });
    }
};
