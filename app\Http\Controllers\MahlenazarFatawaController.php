<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MahlenazarFatawaController extends Controller
{
    public function index(Request $request, $role = null, $fatwa_no = null)
    {

    //  dd($request,$role,$fatwa_no);
    // \Log::info('Controller Roles:', ['role' => $role]);
    // \Log::info('Controller Fatwa No:', ['fatwa_no' => $fatwa_no]);

    $user = Auth::user();

    // Get the first day of the current month
    $firstDayOfMonth = Carbon::now()->startOfMonth();

    // Get the last day of the current month
    $lastDayOfMonth = Carbon::now()->endOfMonth();
    // if ($user) {
        // Get user roles
        $userRoles = $user->roles;
        $username = Auth::user()->name;

            $mailfolderDates = DB::table('uploaded_files')
->select('mail_folder_date')
->where('selected', 0)
->distinct()
->orderBy('mail_folder_date', 'desc') // Sort in descending order
->pluck('mail_folder_date');

$roleNames = $userRoles->pluck('name')->toArray();
$firstRoleName = reset($roleNames);
        if (count(Auth::user()->roles) < 2 && Auth::user()->roles->contains('name', 'Mahlenazar')) {
    $daruliftaNames = DB::table('uploaded_files')
    ->where('sender',$username)
    ->select('darulifta_name')
    ->distinct()

    ->pluck('darulifta_name');

}

else{



if($role == null){
    if(count(Auth::user()->roles) > 1){
        $daruliftaNames = DB::table('uploaded_files')
    ->select('darulifta_name')
    ->where('darulifta_name', 'NOT LIKE', '%3btn%')
    ->distinct()
    ->orderBy(\DB::raw('
        CASE
            WHEN darulifta_name = "Noorulirfan" THEN 1
            WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
            WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
            WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
            ELSE 5 -- Adjust as needed
        END
    '))
    ->pluck('darulifta_name');
    }
        else {
        $daruliftaNames = DB::table('uploaded_files')

        ->select('darulifta_name')
        ->distinct()
        ->where('darulifta_name',$firstRoleName)
        ->pluck('darulifta_name');

    }


}
    else {
        $daruliftaNames = DB::table('uploaded_files')

            ->select('darulifta_name')
            ->distinct()
            ->where('darulifta_name',$role)
            ->pluck('darulifta_name');

    }
}
// dd($daruliftaNames,$firstRoleName,$username);

if($fatwa_no == null){
    $mahl_e_nazarfataw = [];
    $totalCounts = 0;
    
    $mahlenazar_mujeeb = DB::table('uploaded_files as u1')
        ->where('u1.checked_folder', 'Mahl-e-Nazar')
        ->leftJoin('uploaded_files as u2', function ($join) {
            $join->on('u1.file_code', '=', 'u2.file_code')
                ->where(function ($query) {
                    $query->where('u2.checked_folder', 'ok')
                        ->orWhere('u2.checked_folder', 'Tahqiqi');
                });
        })
        ->whereNull('u2.file_code')
        ->distinct()
        ->pluck('u1.sender');
} else {
    $mahl_e_nazarfataw = [];
    $totalCounts = 0;
    
    $mahlenazar_mujeeb = DB::table('uploaded_files as u1')
        ->where('u1.sender', $fatwa_no)
        ->where('u1.checked_folder', 'Mahl-e-Nazar')
        ->leftJoin('uploaded_files as u2', function ($join) {
            $join->on('u1.file_code', '=', 'u2.file_code')
                ->where(function ($query) {
                    $query->where('u2.checked_folder', 'ok')
                        ->orWhere('u2.checked_folder', 'Tahqiqi');
                });
        })
        ->whereNull('u2.file_code')
        ->distinct()
        ->pluck('u1.sender');
}

foreach ($mailfolderDates as $mailfolderDate) {
    foreach ($daruliftaNames as $daruliftaName) {
        $uniquemahle = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->when($fatwa_no != null, function($query) use ($fatwa_no) {
                return $query->where('u1.sender', $fatwa_no);
            })
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.file_code', DB::raw('MAX(u1.id) AS id'))
            ->groupBy('u1.file_code')
            ->get();

        $mahle_que = DB::table('uploaded_files as u1')
            ->whereIn('u1.id', $uniquemahle->pluck('id'))
            ->where('darulifta_name', $daruliftaName)
            ->where('mail_folder_date', $mailfolderDate)
            ->orderBy('u1.id', 'desc')
            ->get();

        if ($mahle_que->isNotEmpty()) {
            $mahl_e_nazarfataw[$daruliftaName][$mailfolderDate] = $mahle_que;
            $totalCounts += count($mahl_e_nazarfataw[$daruliftaName]);
        }
    }
}

$que_day_r = DB::table('questions')
             ->whereIn('question_branch', $daruliftaNames)
             ->get();
    $fatwa_nos =  DB::table('questions')
    ->whereIn('question_branch', $daruliftaNames)
    ->select('ifta_code')
    ->orderBy('ifta_code', 'desc')
    ->get();


            $currentDate = Carbon::now();

            // Get the start of the current week (Monday) and format it
            $startOfWeek = $currentDate->startOfWeek()->format('Y-m-d');

            // Get the end of the current week (Sunday) and format it
            $endOfWeek = $currentDate->endOfWeek()->format('Y-m-d');
            $CurrentWeekokMulti = DB::table('uploaded_files')
            ->where('darulifta_name',$firstRoleName)
            ->where('checked_folder', 'ok')
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$startOfWeek, $endOfWeek])
            ->get();
            // dd($startOfWeek, $endOfWeek);
            // Retrieve records where `selected` is 1 and the date part of `mail_folder_date` is in the current week

            $mailfolderDates = DB::table('uploaded_files')
            ->select('mail_folder_date')

            ->distinct()
            ->orderBy('mail_folder_date', 'desc') // Sort in descending order
            ->pluck('mail_folder_date');


            $netmahlenazar = DB::table('uploaded_files as u1')
            ->whereIn('u1.darulifta_name', $daruliftaNames)
            ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')

                            ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere

                    });
            })
            ->whereNull('u2.file_code')

            ->select('u1.*')
            ->count();

        $uniquemahle = DB::table('uploaded_files as u1')
->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
->leftJoin('uploaded_files as u2', function ($join) {
    $join->on('u1.file_code', '=', 'u2.file_code')
        ->where(function ($query) {
            $query->where('u2.checked_folder', 'ok')
                ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere
                // ->orWhereNull('u2.checked_folder');
        });
})
->whereNull('u2.file_code')
->select('u1.file_code', DB::raw('MAX(u1.id) AS id'))
->groupBy('u1.file_code')
->get();
$mahlenazar = DB::table('uploaded_files as u1')
->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

->leftJoin('uploaded_files as u2', function ($join) {
    $join->on('u1.file_code', '=', 'u2.file_code')
        ->where(function ($query) {
            $query->where('u2.checked_folder', 'ok')

                ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                ->orWhereNull('u2.checked_folder');
        });
})
->whereNull('u2.file_code')

->select('u1.*')
->get();
$mahlenazar_null = DB::table('uploaded_files as u1')
->whereIn('u1.darulifta_name', $daruliftaNames)
->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

->leftJoin('uploaded_files as u2', function ($join) {
    $join->on('u1.file_code', '=', 'u2.file_code')
        ->where(function ($query) {
            $query->where('u2.checked_folder', 'ok')

                ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                ->orWhereNull('u2.checked_folder');
        });
})
->whereNull('u2.file_code')

->select('u1.*')
->get();
// dd($uniquemahle,$mahlenazar,$mahlenazar_null);
if (count(Auth::user()->roles) < 2 && Auth::user()->roles->contains('name', 'Mahlenazar')) {
    $mahl_e_nazarfataw = [];
    $totalCounts = 0;
    foreach ($mailfolderDates as $mailfolderDate) {
                foreach ($daruliftaNames as $daruliftaName) {

                    $uniquemahle = DB::table('uploaded_files as u1')
                    ->where('u1.sender',$username) // Specify the table alias u1
    ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
    ->leftJoin('uploaded_files as u2', function ($join) {
        $join->on('u1.file_code', '=', 'u2.file_code')
            ->where(function ($query) {
                $query->where('u2.checked_folder', 'ok')
                    ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                    // ->orWhereNull('u2.checked_folder')
                    ;
            });
    })
    ->whereNull('u2.file_code')
    ->select('u1.file_code', DB::raw('MAX(u1.id) AS id'))
    ->groupBy('u1.file_code')
    ->get();

                    $mahlenazar = DB::table('uploaded_files as u1')
    ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

    ->leftJoin('uploaded_files as u2', function ($join) {
        $join->on('u1.file_code', '=', 'u2.file_code')
            ->where(function ($query) {
                $query->where('u2.checked_folder', 'ok')

                    ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                    ->orWhereNull('u2.checked_folder');
            });
    })
    ->whereNull('u2.file_code')

    ->select('u1.*')
    ->get();
    $mahlenazar_null = DB::table('uploaded_files as u1')
    ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

    ->leftJoin('uploaded_files as u2', function ($join) {
        $join->on('u1.file_code', '=', 'u2.file_code')
            ->where(function ($query) {
                $query->where('u2.checked_folder', 'ok')

                    ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                    ->orWhereNull('u2.checked_folder');
            });
    })
    ->whereNull('u2.file_code')

    ->select('u1.*')
    ->get();
    // dd($mahlenazar);

    $mahle_que = DB::table('uploaded_files as u1')
                        ->whereIn('u1.id', $uniquemahle->pluck('id'))
                        ->where('darulifta_name', $daruliftaName)
                        ->where('mail_folder_date', $mailfolderDate)

                        ->orderBy('u1.id', 'desc') // Sort by id in descending order
                        ->get();


                    if ($mahle_que->isNotEmpty()) {
                    $mahl_e_nazarfataw[$daruliftaName][$mailfolderDate] = $mahle_que;
                    $totalCounts += count( $mahl_e_nazarfataw[$daruliftaName]);
                    // dd($uniqueData,$mahle_que,$uniqueData,$q_rec_t);

                }
            }
        }

    }
else
{

if($fatwa_no == null){

    $mahl_e_nazarfataw = [];
// $totalCounts = 0;
foreach ($mailfolderDates as $mailfolderDate) {
            foreach ($daruliftaNames as $daruliftaName) {

$mahle_que = DB::table('uploaded_files as u1')
                    ->whereIn('u1.id', $uniquemahle->pluck('id'))
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)

                    ->orderBy('u1.id', 'desc') // Sort by id in descending order
                    ->get();


                if ($mahle_que->isNotEmpty()) {
                $mahl_e_nazarfataw[$daruliftaName][$mailfolderDate] = $mahle_que;
                // $totalCounts += count( $mahl_e_nazarfataw[$daruliftaName]);
                // dd($uniqueData,$mahle_que,$uniqueData,$q_rec_t);

            }
        }
    }

}
else {
    $mahl_e_nazarfataw = [];
$totalCounts = 0;
foreach ($mailfolderDates as $mailfolderDate) {
            foreach ($daruliftaNames as $daruliftaName) {

                $uniquemahle = DB::table('uploaded_files as u1')
                ->where('u1.sender',$fatwa_no) // Specify the table alias u1
->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
->leftJoin('uploaded_files as u2', function ($join) {
    $join->on('u1.file_code', '=', 'u2.file_code')
        ->where(function ($query) {
            $query->where('u2.checked_folder', 'ok')
                ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                // ->orWhereNull('u2.checked_folder')
                ;
        });
})
->whereNull('u2.file_code')
->select('u1.file_code', DB::raw('MAX(u1.id) AS id'))
->groupBy('u1.file_code')
->get();
                $mahlenazar = DB::table('uploaded_files as u1')
->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

->leftJoin('uploaded_files as u2', function ($join) {
    $join->on('u1.file_code', '=', 'u2.file_code')
        ->where(function ($query) {
            $query->where('u2.checked_folder', 'ok')

                ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
                ->orWhereNull('u2.checked_folder');
        });
})
->whereNull('u2.file_code')

->select('u1.*')
->get();
$mahlenazar_null = DB::table('uploaded_files as u1')
->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1

->leftJoin('uploaded_files as u2', function ($join) {
    $join->on('u1.file_code', '=', 'u2.file_code')
        ->where(function ($query) {
            $query->where('u2.checked_folder', 'ok')

                ->orWhere('u2.checked_folder', 'Tahqiqi')
                ->orWhereNull('u2.checked_folder'); // Use OR instead of orwhere

        });
})
->whereNull('u2.file_code')

->select('u1.*')
->get();
// dd($mahlenazar);

$mahle_que = DB::table('uploaded_files as u1')
                    ->whereIn('u1.id', $uniquemahle->pluck('id'))
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)

                    ->orderBy('u1.id', 'desc') // Sort by id in descending order
                    ->get();

//                     $q_rec_t = DB::table('questions')->get();
//                     $q_rec = DB::table('questions')
// ->whereIn(DB::raw('LOWER(ifta_code)'), $uniquemahle->pluck('file_code')->map(fn($code) => strtolower($code)))
// ->get();
//                 $mahle_que = $uniqueData->map(function ($item) use ($q_rec) {
//                     // Use case-insensitive collation in the where clause
//                     $questionData = $q_rec->where('ifta_code', 'ilike', $item->file_code)->first();

//                     return (object) array_merge((array) $item, (array) $questionData);
//                 });

                if ($mahle_que->isNotEmpty()) {
                $mahl_e_nazarfataw[$daruliftaName][$mailfolderDate] = $mahle_que;
                $totalCounts += count( $mahl_e_nazarfataw[$daruliftaName]);
                // dd($uniqueData,$mahle_que,$uniqueData,$q_rec_t);

            }
        }
    }

}
}
// dd($mahlenazar_mujeeb);
    $mahl_e_mujeeb = [];
    $customOrder = [
        'Noorulirfan' => 1,
        'Faizan-e-Ajmair' => 2,
        'Gulzar-e-Taiba' => 3,
        'Markaz-ul-Iqtisaad' => 4,
    ];


    foreach ($mahlenazar_mujeeb as $sender) {
        foreach ($daruliftaNames as $daruliftaName) {
            $m_d_mujeeeb = DB::table('uploaded_files as u1')
                ->whereIn('u1.id', $uniquemahle->pluck('id'))
                ->where('darulifta_name', $daruliftaName)
                ->where('sender', $sender)
                // ->orderBy('u1.id', 'desc') // Sort by id in descending order
                ->select('darulifta_name', 'sender') // Add this line to select only darulifta_name and sender
                ->distinct() // Add this line to remove duplicates
                ->orderBy(\DB::raw('
            CASE
                WHEN darulifta_name = "Noorulirfan" THEN 1
                WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
                WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
                WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
                ELSE 5 -- Adjust as needed
            END
        '))
                ->get();
                if ($m_d_mujeeeb->isNotEmpty()) {
                $mahl_e_mujeeb[$daruliftaName][$sender] = $m_d_mujeeeb;

                }
        }
    }
    // dd($mahl_e_mujeeb);


return view('mahlenazar.mahlenazar', compact('mahl_e_nazarfataw','daruliftaNames','mailfolderDates','que_day_r','mahlenazar_null','role','fatwa_no'));

}
public function mnSidebare()
{


    $Noorulirfan = 'Noorulirfan';
    $Mujeeb = 'Abid Madani';

// dd($mahl_e_mujeeb);
    return view('components.navbars.sidebar', compact('Noorulirfan','Mujeeb'));
}
// public function mnSidebaredash()
// {


//     $customOrder = [
//         'Noorulirfan' => 1,
//         'Faizan-e-Ajmair' => 2,
//         'Gulzar-e-Taiba' => 3,
//         'Markaz-ul-Iqtisaad' => 4,
//     ];

//     $daruliftaNames = DB::table('uploaded_files')
//         ->select('darulifta_name')
//         ->where('darulifta_name', 'NOT LIKE', '%3btn%')
//         ->distinct()
//         ->orderBy(\DB::raw('
//             CASE
//                 WHEN darulifta_name = "Noorulirfan" THEN 1
//                 WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
//                 WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
//                 WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
//                 ELSE 5 -- Adjust as needed
//             END
//         '))
//         ->pluck('darulifta_name');

//     $mahlenazar_mujeeb = DB::table('uploaded_files as u1')
//     ->where('u1.checked_folder', 'Mahl-e-Nazar')
//     ->leftJoin('uploaded_files as u2', function ($join) {
//         $join->on('u1.file_code', '=', 'u2.file_code')
//             ->where(function ($query) {
//                 $query->where('u2.checked_folder', 'ok')
//                     ->orWhere('u2.checked_folder', 'Tahqiqi');
//             });
//     })
//     ->whereNull('u2.file_code')
//     ->distinct() // Add this line to remove duplicates
//     ->pluck('u1.sender');

//     $mahl_e_mujeeb = [];
//     $customOrder = [
//         'Noorulirfan' => 1,
//         'Faizan-e-Ajmair' => 2,
//         'Gulzar-e-Taiba' => 3,
//         'Markaz-ul-Iqtisaad' => 4,
//     ];
//     $uniquemahle = DB::table('uploaded_files as u1')
//     ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
//     ->leftJoin('uploaded_files as u2', function ($join) {
//         $join->on('u1.file_code', '=', 'u2.file_code')
//             ->where(function ($query) {
//                 $query->where('u2.checked_folder', 'ok')
//                     ->orWhere('u2.checked_folder', 'Tahqiqi') // Use OR instead of orwhere
//                     // ->orWhereNull('u2.checked_folder')
//                     ;
//             });
//     })
//     ->whereNull('u2.file_code')
//     ->select('u1.file_code', DB::raw('MAX(u1.id) AS id'))
//     ->groupBy('u1.file_code')
//     ->get();

//     foreach ($mahlenazar_mujeeb as $sender) {
//         foreach ($daruliftaNames as $daruliftaName) {
//             $m_d_mujeeeb = DB::table('uploaded_files as u1')
//                 // ->whereIn('u1.id', $uniquemahle->pluck('id'))
//                 ->where('darulifta_name', $daruliftaName)
//                 ->where('sender', $sender)
//                 // ->orderBy('u1.id', 'desc') // Sort by id in descending order
//                 ->select('darulifta_name', 'sender') // Add this line to select only darulifta_name and sender
//                 ->distinct() // Add this line to remove duplicates
//                 ->orderBy(\DB::raw('
//             CASE
//                 WHEN darulifta_name = "Noorulirfan" THEN 1
//                 WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
//                 WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
//                 WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
//                 ELSE 5 -- Adjust as needed
//             END
//         '))
//                 ->get();
//                 if ($m_d_mujeeeb->isNotEmpty()) {
//                 $mahl_e_mujeeb[$daruliftaName][$sender] = $m_d_mujeeeb;

//                 }
//         }
//     }
// // dd($mahl_e_mujeeb);
//     return view('dashboard.index', compact('mahl_e_mujeeb', 'daruliftaNames', 'mahlenazar_mujeeb'));
// }

}
