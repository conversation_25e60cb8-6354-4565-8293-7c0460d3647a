<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Services\MahlENazarService;
use App\Models\User;
use App\Models\UserRestriction;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class MahlENazarManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $search = '';
    public $filterStatus = 'all';
    public $showStatsModal = false;
    public $showUserModal = false;
    public $showBreakdownModal = false;
    public $selectedUser = null;

    public $statistics = [];
    public $userReport = [];
    public $detailedBreakdown = [];

    protected $mahlENazarService;

    public function boot(MahlENazarService $mahlENazarService)
    {
        $this->mahlENazarService = $mahlENazarService;
    }

    public function mount()
    {
        $this->authorize('manage-users');
        $this->loadStatistics();
        $this->loadUserReport();
        $this->loadDetailedBreakdown();
    }

    public function render()
    {
        $filteredReport = collect($this->userReport)
            ->when($this->search, function ($collection) {
                return $collection->filter(function ($item) {
                    return stripos($item['user']->name, $this->search) !== false ||
                           stripos($item['user']->email, $this->search) !== false;
                });
            })
            ->when($this->filterStatus !== 'all', function ($collection) {
                return $collection->filter(function ($item) {
                    return $item['status'] === $this->filterStatus;
                });
            });

        // Paginate manually
        $perPage = 15;
        $currentPage = $this->getPage();
        $items = $filteredReport->forPage($currentPage, $perPage);
        
        return view('livewire.mahl-e-nazar-management', [
            'userReport' => $items,
            'totalUsers' => $filteredReport->count(),
        ]);
    }

    public function loadStatistics()
    {
        $this->statistics = $this->mahlENazarService->getStatistics();
    }

    public function loadUserReport()
    {
        $this->userReport = $this->mahlENazarService->getUserReport();
    }

    public function loadDetailedBreakdown()
    {
        $this->detailedBreakdown = $this->mahlENazarService->getDetailedBreakdown();
    }

    public function refreshData()
    {
        $this->loadStatistics();
        $this->loadUserReport();
        $this->loadDetailedBreakdown();
        session()->flash('message', 'Data refreshed successfully.');
    }

    public function updateAllRestrictions()
    {
        $this->authorize('manage-users');
        
        $result = $this->mahlENazarService->updateAllUserRestrictions();
        
        $this->loadStatistics();
        $this->loadUserReport();
        $this->loadDetailedBreakdown();

        session()->flash('message',
            "Updated restrictions for {$result['total_checked']} users. " .
            "Restricted: {$result['restricted']}, Unrestricted: {$result['unrestricted']}"
        );
    }

    public function openStatsModal()
    {
        $this->showStatsModal = true;
    }

    public function openBreakdownModal()
    {
        $this->showBreakdownModal = true;
    }

    public function openUserModal($userId)
    {
        $this->selectedUser = User::with(['activeRestrictions'])->findOrFail($userId);
        $this->showUserModal = true;
    }

    public function closeModals()
    {
        $this->showStatsModal = false;
        $this->showUserModal = false;
        $this->showBreakdownModal = false;
        $this->selectedUser = null;
    }

    public function toggleUserRestriction($userId)
    {
        $this->authorize('unlock-users');
        
        $user = User::findOrFail($userId);
        $hasRestriction = UserRestriction::where('user_id', $userId)
            ->where('restriction_type', 'fatawa_limit_exceeded')
            ->where('is_active', true)
            ->exists();

        if ($hasRestriction) {
            // Remove restriction
            UserRestriction::where('user_id', $userId)
                ->where('restriction_type', 'fatawa_limit_exceeded')
                ->where('is_active', true)
                ->update([
                    'is_active' => false,
                    'lifted_by' => auth()->id(),
                    'lifted_at' => now(),
                    'lift_reason' => 'Manually lifted by admin',
                ]);
            
            session()->flash('message', "Restriction removed for {$user->name}.");
        } else {
            // Apply restriction
            $this->mahlENazarService->applyLimitRestriction($user);
            session()->flash('message', "Restriction applied to {$user->name}.");
        }

        $this->loadUserReport();
        $this->loadDetailedBreakdown();
    }

    public function getStatusColor($status)
    {
        return match($status) {
            'restricted' => 'danger',
            'at_limit' => 'warning',
            'near_limit' => 'info',
            'normal' => 'success',
            default => 'secondary'
        };
    }

    public function getStatusText($status)
    {
        return match($status) {
            'restricted' => 'Restricted',
            'at_limit' => 'At Limit',
            'near_limit' => 'Near Limit',
            'normal' => 'Normal',
            default => 'Unknown'
        };
    }

    public function getProgressPercentage($count)
    {
        return min(100, ($count / MahlENazarService::MAHL_E_NAZAR_LIMIT) * 100);
    }

    public function getProgressColor($count)
    {
        $percentage = $this->getProgressPercentage($count);
        
        if ($percentage >= 100) return 'danger';
        if ($percentage >= 83) return 'warning'; // 10/12
        if ($percentage >= 67) return 'info';    // 8/12
        return 'success';
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilterStatus()
    {
        $this->resetPage();
    }

    public function allowUser($userId)
    {
        $this->authorize('unlock-users');

        $user = User::findOrFail($userId);
        $this->mahlENazarService->manuallyAllowUser($user, auth()->user());

        $this->loadUserReport();
        $this->loadDetailedBreakdown();

        session()->flash('message', "User {$user->name} has been allowed to submit fatawa.");
    }

    public function restrictUser($userId)
    {
        $this->authorize('unlock-users');

        $user = User::findOrFail($userId);
        $this->mahlENazarService->removeManualAllow($user, auth()->user());

        $this->loadUserReport();
        $this->loadDetailedBreakdown();

        session()->flash('message', "User {$user->name} has been restricted from submitting fatawa.");
    }

    public function initializeDefaultRestrictions()
    {
        $this->authorize('unlock-users');

        $result = $this->mahlENazarService->initializeDefaultRestrictions();

        $this->loadUserReport();
        $this->loadDetailedBreakdown();

        session()->flash('message',
            "Initialized default restrictions for {$result['total_checked']} users. " .
            "Newly restricted: {$result['restricted']}, Already allowed: {$result['already_allowed']}"
        );
    }

    public function exportReport()
    {
        $this->authorize('generate-reports');

        // This would generate a PDF or Excel report
        session()->flash('message', 'Report export functionality will be implemented.');
    }

    public function sendLimitWarnings()
    {
        $this->authorize('manage-users');
        
        $nearLimitUsers = collect($this->userReport)
            ->filter(function ($item) {
                return $item['status'] === 'near_limit' || $item['status'] === 'at_limit';
            });

        // Here you would implement the actual warning notification logic
        $warningCount = $nearLimitUsers->count();
        
        session()->flash('message', "Limit warnings sent to {$warningCount} users.");
    }

    public function getUserMahlENazarCount($userName)
    {
        return $this->mahlENazarService->getMahlENazarCount($userName);
    }
}
