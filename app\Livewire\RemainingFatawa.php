<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\Chat;
use Illuminate\Http\Request;
use Livewire\WithPagination;

class RemainingFatawa extends Component
{
    use WithPagination;

    // Filter Properties
    public $selectedMonths = [];
    public $selectedmujeeb = 'all';
    public $selectedmufti = 'all';
    public $selectedTimeFrame;
    public $tempSelectedTimeFrame = 'all';
    public $tempSelectedMonths = [];
    public $startDate;
    public $endDate;
    public $tempStartDate;
    public $tempEndDate;

    // Display Properties
    public $showDetail = false;
    public $showQue = false;
    public $showChat = false;
    public $expandedFolders = [];
    public $expandedDaruliftaNames = [];

    // Data Properties
    public $checkerlist;
    public $datefilter;
    public $daruliftaNames;
    public $checkers;
    public $mailfolderDate;
    public $mujeebs;
    public $daruliftalist;
    public $remainingFatawa;
    public $sendingFatawa;
    public $que_day_r;
    public $mahlenazar_null;
    public $allfatawa;
    public $codebylower;

    // Route Parameters
    public $darulifta;
    public $mailfolder;

    // User Properties
    public $roleInfo = [];
    public $userName;
    public $munsab;

    // Chat Properties
    public $message;

    // Legacy Properties (keeping for compatibility)
    public $obtain;
    public $checker;


    public function mount(Request $request, $darulifta = null, $mailfolder = null)
        {
            $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);
            $this->selectedmufti = $request->query('selectedmufti', $this->selectedmufti);
            $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);
            $this->selectedMonths = request()->query('selectedMonths', []);
            if (is_string($this->selectedMonths)) {
        // Convert comma-separated string to array
                $this->selectedMonths = explode(',', $this->selectedMonths);
            }
            $this->tempSelectedTimeFrame = $this->selectedTimeFrame;
            $this->tempSelectedMonths = $this->selectedMonths;
            $this->startDate = $request->query('startDate');
            $this->endDate = $request->query('endDate');
            $this->tempStartDate = $this->startDate;
            $this->tempEndDate = $this->endDate;
            $this->showDetail = $request->query('showDetail', false) == '1';
            $this->showQue = $request->query('showQue', false) == '1';
            $this->showChat = $request->query('showChat', false) == '1';

            $this->determineUserRole();
            $this->loadDaruliftaNames();
            $this->loadMailfolderDate();


        // Additional initialization code
    }
    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->startDate && $this->endDate && empty($this->selectedMonths)) {
            $this->selectedTimeFrame = 'custom';
            $this->tempSelectedTimeFrame = 'custom';
        }
        $this->remainingFatawadata();
        $this->sendingFatawadata();

    }

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->tempSelectedTimeFrame = $timeFrame;
        if ($timeFrame === 'all') {
            // Resetting the values
            $this->tempStartDate = null;
            $this->tempEndDate = null;
            $this->selectedMonths = [];
            $this->tempSelectedMonths = [];

            // Optional: Debugging statements
            // dd($this->tempStartDate, $this->tempEndDate, $this->selectedMonths, $this->tempSelectedMonths);
        }

    }
    public function updateSelectedMujeeb($mujeebframe)
    {
        $this->selectedmujeeb = $mujeebframe;
    }
    public function updateSelectedMufti($muftiframe)
    {
        $this->selectedmufti = $muftiframe;
    }

    public function toggleFolder($folderId)
    {
        if (in_array($folderId, $this->expandedFolders)) {
            $this->expandedFolders = array_diff($this->expandedFolders, [$folderId]);
        } else {
            $this->expandedFolders[] = $folderId;
        }
    }

    public function toggleDarulifta($daruliftaName)
    {
        if (in_array($daruliftaName, $this->expandedDaruliftaNames)) {
            $this->expandedDaruliftaNames = array_diff($this->expandedDaruliftaNames, [$daruliftaName]);
        } else {
            $this->expandedDaruliftaNames[] = $daruliftaName;
        }
    }

    public function expandAllFolders()
    {
        $this->expandedFolders = [];
        foreach ($this->remainingFatawa as $daruliftaName => $folders) {
            foreach ($folders as $folderId => $files) {
                $this->expandedFolders[] = $folderId;
            }
        }
    }

    public function collapseAllFolders()
    {
        $this->expandedFolders = [];
    }

    private function loadStaticData()
    {
        // Load data that doesn't change frequently
        $this->daruliftalist = DB::table('uploaded_files')
            ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
            ->select('uploaded_files.darulifta_name')
            ->distinct()
            ->orderBy('daruliftas.id')
            ->pluck('uploaded_files.darulifta_name');

        $this->checkerlist = DB::table('checker')->get();

        $this->datefilter = DB::table('uploaded_files')
            ->where('selected', 0)
            ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();
    }

    private function loadDynamicData()
    {
        // Load data that changes based on filters
        $this->que_day_r = DB::table('questions')
            ->whereIn('question_branch', $this->daruliftaNames)
            ->get();

        $this->mahlenazar_null = DB::table('uploaded_files as u1')
            ->whereIn('u1.darulifta_name', $this->daruliftaNames)
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.*')
            ->get();

        $this->remainingFatawa = $this->remainingFatawadata();
        $this->sendingFatawa = $this->sendingFatawadata();
    }





    private function determineUserRole()
    {
        $user = Auth::user();
        $userName = $user->name;
        $userRoles = $user->roles->pluck('name')->toArray();

        // Set default values
        $this->roleInfo = [
            'isSuperAdmin' => false,
            'isChecker' => false,
            'isMujeeb' => false,
            'userName' => $userName,
            'checkerName' => null,
            'mujeebName' => null,
            'darulName' => null,
            'assignedMujeebs' => [],
        ];

        // Check for SuperAdmin role (highest priority)
        if (in_array('SuperAdmin', $userRoles)) {
            $this->roleInfo['isSuperAdmin'] = true;
            return;
        }

        // Check for Checker role (second priority)
        if (in_array('Checker', $userRoles)) {
            $checker = DB::table('checker')
                ->where('checker_name', $userName)
                ->first();

            if ($checker) {
                $this->roleInfo['isChecker'] = true;
                $this->roleInfo['checkerName'] = $userName;
                $this->roleInfo['checkerFolderId'] = $checker->folder_id ?? null;
                return;
            }
        }

        // Check for Mujeeb role (lowest priority)
        if (in_array('mujeeb', $userRoles)) {
            $mujeeb = DB::table('mujeebs')
                ->where('mujeeb_name', $userName)
                ->first();

            if ($mujeeb) {
                $this->roleInfo['isMujeeb'] = true;
                $this->roleInfo['mujeebName'] = $userName;
                $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
                return;
            }
        }

        // Check if user is in mujeebs table but not assigned the mujeeb role
        $mujeeb = DB::table('mujeebs')
            ->where('mujeeb_name', $userName)
            ->first();

        if ($mujeeb) {
            $this->roleInfo['isMujeeb'] = true;
            $this->roleInfo['mujeebName'] = $userName;
            $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
        }
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);

        // Initialize checkers with default value
        $this->checkers = DB::table('checker')
            ->select('folder_id')
            ->distinct()
            ->pluck('folder_id')
            ->toArray();

        if ($this->roleInfo['isMujeeb']) {
            // If user is mujeeb, only show their darul's data
            $this->daruliftaNames = [$this->roleInfo['darulName']];
            $this->mujeebs = [$this->roleInfo['userName']];
        } else if ($this->darulifta === null) {
            if(count(Auth::user()->roles) > 1){
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('uploaded_files.darulifta_name')
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->pluck('uploaded_files.darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 0)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
            } else {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', $firstRoleName)
                    ->pluck('darulifta_name');

                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 0)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
            }
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('darulifta_name');
            $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('selected', 1)
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('sender');
        }
    }
    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')

        ->distinct()

        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()

    ->pluck('mail_folder_date');
    }
}
public function sendMessage($iftaCode)
{

    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.


    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}
public function render()
    {
        // Load data efficiently
        $this->loadStaticData();
        $this->loadDynamicData();

        $messages = Chat::latest()->limit(50)->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.remaining-fatawa', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'checkers' => $this->checkers,
            'remainingFatawa' => $this->remainingFatawa,
            'sendingFatawa' => $this->sendingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmufti' => $this->selectedmufti,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'tempSelectedTimeFrame' => $this->tempSelectedTimeFrame,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'showDetail' => $this->showDetail,
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'isAdmin' => $this->roleInfo['isSuperAdmin'],
            'userName' => $this->userName,
            'checkerlist' => $this->checkerlist,
            'munsab' => $this->munsab,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'tempStartDate' => $this->tempStartDate,
            'tempEndDate' => $this->tempEndDate,

            // 'checker' => $this->checker,

            // ... (other data to be passed to the view)
        ]

        );

    }
    /**
     * Optimized method to get remaining fatawa data with improved performance
     */
    public function remainingFatawadata()
    {
        // Early return for invalid filter combinations
        if ($this->tempSelectedTimeFrame == 'other' && empty($this->selectedMonths)) {
            return [];
        }

        // Build base query with common conditions
        $baseQuery = DB::table('uploaded_files')
            ->where('selected', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames);

        // Apply role-based filtering
        $this->applyRoleBasedFiltering($baseQuery);

        // Apply mufti filtering
        $this->applyMuftiFiltering($baseQuery);

        // Apply date filtering
        $this->applyDateFiltering($baseQuery);

        // Get results and group by darulifta and mail folder date
        $results = $baseQuery->get();

        return $this->groupFatawaResults($results);
    }

    /**
     * Apply role-based filtering to the query
     */
    private function applyRoleBasedFiltering($query)
    {
        if ($this->roleInfo['isMujeeb']) {
            $query->where('sender', $this->roleInfo['userName']);
        } elseif ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }
    }

    /**
     * Apply mufti/checker filtering to the query
     */
    private function applyMuftiFiltering($query)
    {
        if ($this->selectedmufti && $this->selectedmufti != 'all') {
            if ($this->selectedmufti == 'mufti_ali_asghar') {
                $query->where(function($subQuery) {
                    $subQuery->where('checker', 'mufti_ali_asghar')
                             ->orWhereNull('checker');
                });
            } elseif ($this->selectedmufti == 'transfer_checked') {
                $checkerFolderId = DB::table('checker')
                    ->where('checker_name', $this->userName)
                    ->value('folder_id');
                if ($checkerFolderId) {
                    $query->where('transfer_by', $checkerFolderId);
                }
            } else {
                $query->where('checker', $this->selectedmufti);
            }
        }
    }

    /**
     * Apply date filtering to the query
     */
    private function applyDateFiltering($query)
    {
        switch ($this->tempSelectedTimeFrame) {
            case 'all':
                $query->whereNotNull('mail_folder_date');
                break;

            case 'this_month':
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    now()->startOfMonth()->format('Y-m-d'),
                    now()->endOfMonth()->format('Y-m-d')
                ]);
                break;

            case 'other':
                if (!empty($this->selectedMonths)) {
                    $formattedMonths = array_map(function ($date) {
                        return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
                    }, $this->selectedMonths);
                    $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedMonths);
                }
                break;

            case 'custom':
                if ($this->startDate && $this->endDate) {
                    $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                        Carbon::parse($this->startDate)->format('Y-m-d'),
                        Carbon::parse($this->endDate)->format('Y-m-d')
                    ]);
                }
                break;
        }
    }

    /**
     * Group fatawa results by darulifta and mail folder date
     */
    private function groupFatawaResults($results)
    {
        $groupedResults = [];

        foreach ($results as $fatawa) {
            if (!isset($groupedResults[$fatawa->darulifta_name][$fatawa->mail_folder_date])) {
                $groupedResults[$fatawa->darulifta_name][$fatawa->mail_folder_date] = collect();
            }
            $groupedResults[$fatawa->darulifta_name][$fatawa->mail_folder_date]->push($fatawa);
        }

        return $groupedResults;
    }

    /**
     * Optimized method to get sending fatawa data with improved performance
     */
    public function sendingFatawadata()
    {
        // Initialize checkers if empty
        $this->initializeCheckers();

        // Build optimized query for all data at once
        $baseQuery = DB::table('uploaded_files')
            ->where('selected', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->whereIn('mail_folder_date', $this->mailfolderDate);

        // Apply checker filtering
        $this->applyCheckerFiltering($baseQuery);

        // Apply role-based filtering
        $this->applyRoleBasedFiltering($baseQuery);

        // Apply date filtering
        $this->applyDateFiltering($baseQuery);

        // Get all results at once
        $results = $baseQuery->get();

        // Group and order results
        return $this->groupAndOrderSendingResults($results);
    }

    /**
     * Initialize checkers list
     */
    private function initializeCheckers()
    {
        if (empty($this->checkers)) {
            $this->checkers = DB::table('checker')
                ->select('folder_id')
                ->distinct()
                ->pluck('folder_id')
                ->toArray();
        }
    }

    /**
     * Apply checker filtering to the query
     */
    private function applyCheckerFiltering($query)
    {
        $query->where(function($mainQuery) {
            foreach ($this->checkers as $checker) {
                $mainQuery->orWhere(function($subQuery) use ($checker) {
                    $subQuery->where('checker', $checker);
                    if ($checker === 'mufti_ali_asghar') {
                        $subQuery->orWhereNull('checker');
                    }
                });
            }
        });
    }

    /**
     * Group and order sending fatawa results
     */
    private function groupAndOrderSendingResults($results)
    {
        $sendingFatawas = [];

        // Group results by darulifta, checker, and mail folder date
        foreach ($results as $fatawa) {
            $checker = $fatawa->checker ?? 'mufti_ali_asghar';
            if (!isset($sendingFatawas[$fatawa->darulifta_name][$checker][$fatawa->mail_folder_date])) {
                $sendingFatawas[$fatawa->darulifta_name][$checker][$fatawa->mail_folder_date] = collect();
            }
            $sendingFatawas[$fatawa->darulifta_name][$checker][$fatawa->mail_folder_date]->push($fatawa);
        }

        // Reorder based on checker order
        foreach ($sendingFatawas as $daruliftaName => $checkers) {
            $orderedCheckers = [];
            foreach ($this->checkers as $checker) {
                if (isset($checkers[$checker])) {
                    $orderedCheckers[$checker] = $checkers[$checker];
                }
            }
            $sendingFatawas[$daruliftaName] = $orderedCheckers;
        }

        return $sendingFatawas;
    }

}

