import ClassicEditorBase from '@ckeditor/ckeditor5-build-classic';
import Font from '@ckeditor/ckeditor5-font/src/font';
import Alignment from '@ckeditor/ckeditor5-alignment/src/alignment';

ClassicEditorBase.builtinPlugins = [
    ...ClassicEditorBase.builtinPlugins,
    Font,
    Alignment
];

ClassicEditorBase.defaultConfig = {
    toolbar: [
        'heading', '|',
        'bold', 'italic', 'underline', '|',
        'fontFamily', 'fontSize', 'fontColor', '|',
        'alignment', '|',
        'bulletedList', 'numberedList', '|',
        'undo', 'redo'
    ],
    fontFamily: {
        options: [
            'default',
            'Jameel Noori Nastaleeq',
            'Naskh Unicode',
            'Al_Mushaf',
            'Arial',
            'Times New Roman'
        ]
    },
    language: 'ur'
};

export default ClassicEditorBase;
