@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Performance Report Details</h5>
                            <p class="text-sm mb-0">
                                {{ $dailyPerformance->user->name }} - {{ $dailyPerformance->performance_date->format('F d, Y') }}
                                @if($dailyPerformance->is_submitted)
                                    <span class="badge bg-gradient-success ms-2">Submitted</span>
                                @else
                                    <span class="badge bg-gradient-warning ms-2">Draft</span>
                                @endif
                            </p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                @can('update', $dailyPerformance)
                                    @if(!$dailyPerformance->is_submitted || auth()->user()->isNazim())
                                        <a href="{{ route('daily-performance.edit', $dailyPerformance) }}" class="btn btn-outline-primary btn-sm mb-0 me-2">
                                            <i class="fas fa-edit"></i>&nbsp;&nbsp;Edit
                                        </a>
                                    @endif
                                @endcan
                                <a href="{{ route('performance-management') }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to List
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Details -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Performance Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="mb-3">Basic Information</h6>
                                    <p class="mb-2"><strong>User:</strong> {{ $dailyPerformance->user->name }}</p>
                                    <p class="mb-2"><strong>Email:</strong> {{ $dailyPerformance->user->email }}</p>
                                    <p class="mb-2"><strong>Date:</strong> {{ $dailyPerformance->performance_date->format('F d, Y') }}</p>
                                    <p class="mb-2"><strong>Task:</strong> {{ $dailyPerformance->task->title ?? 'No Task' }}</p>
                                    <p class="mb-2"><strong>Department:</strong> {{ $dailyPerformance->department->name ?? 'No Department' }}</p>
                                    <p class="mb-2"><strong>Hours Worked:</strong> {{ $dailyPerformance->hours_worked ?? '-' }}</p>
                                    <p class="mb-0"><strong>Status:</strong> 
                                        <span class="badge bg-{{ $dailyPerformance->is_submitted ? 'success' : 'warning' }}">
                                            {{ $dailyPerformance->is_submitted ? 'Submitted' : 'Draft' }}
                                        </span>
                                        @if($dailyPerformance->submitted_at)
                                            <br><small class="text-muted">Submitted at: {{ $dailyPerformance->submitted_at->format('M d, Y H:i') }}</small>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Ratings -->
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="mb-3">Ratings</h6>
                                    <p class="mb-2"><strong>Self Rating:</strong> 
                                        @if($dailyPerformance->overall_rating)
                                            <span class="badge bg-{{ $dailyPerformance->overall_rating === 'excellent' ? 'success' : ($dailyPerformance->overall_rating === 'good' ? 'info' : ($dailyPerformance->overall_rating === 'fair' ? 'warning' : 'danger')) }}">
                                                {{ ucfirst($dailyPerformance->overall_rating) }}
                                            </span>
                                        @else
                                            <span class="text-muted">No self rating</span>
                                        @endif
                                    </p>
                                    <p class="mb-2"><strong>Superior Rating:</strong> 
                                        @if($dailyPerformance->superior_rating)
                                            <span class="badge bg-{{ $dailyPerformance->superior_rating === 'excellent' ? 'success' : ($dailyPerformance->superior_rating === 'good' ? 'info' : ($dailyPerformance->superior_rating === 'fair' ? 'warning' : 'danger')) }}">
                                                {{ ucfirst($dailyPerformance->superior_rating) }}
                                            </span>
                                            @if($dailyPerformance->ratedBy)
                                                <br><small class="text-muted">Rated by: {{ $dailyPerformance->ratedBy->name }}</small>
                                            @endif
                                            @if($dailyPerformance->rated_at)
                                                <br><small class="text-muted">Rated at: {{ $dailyPerformance->rated_at->format('M d, Y H:i') }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">Not rated by superior</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Content -->
                    <div class="row mt-4">
                        <div class="col-12">
                            @if($dailyPerformance->tasks_completed)
                                <div class="mb-4">
                                    <h6 class="mb-2">Tasks Completed</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p class="mb-0">{{ $dailyPerformance->tasks_completed }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($dailyPerformance->challenges_faced)
                                <div class="mb-4">
                                    <h6 class="mb-2">Challenges Faced</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p class="mb-0">{{ $dailyPerformance->challenges_faced }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($dailyPerformance->next_day_plan)
                                <div class="mb-4">
                                    <h6 class="mb-2">Next Day Plan</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p class="mb-0">{{ $dailyPerformance->next_day_plan }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($dailyPerformance->additional_notes)
                                <div class="mb-4">
                                    <h6 class="mb-2">Additional Notes</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p class="mb-0">{{ $dailyPerformance->additional_notes }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($dailyPerformance->superior_comments)
                                <div class="mb-4">
                                    <h6 class="mb-2">Superior Comments & Instructions</h6>
                                    <div class="alert alert-info">
                                        <p class="mb-0">{{ $dailyPerformance->superior_comments }}</p>
                                        @if($dailyPerformance->ratedBy)
                                            <hr class="my-2">
                                            <small class="text-muted">- {{ $dailyPerformance->ratedBy->name }}</small>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
