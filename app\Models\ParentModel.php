<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ParentModel extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'parents';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'header_id',
        'name',
        'serial_number',
    ];

    /**
     * Get the header that owns the parent.
     */
    public function header()
    {
        return $this->belongsTo(Header::class);
    }

    /**
     * Get the children associated with the parent.
     */
    public function children()
    {
        return $this->hasMany(Child::class,'parent_id');
    }
}
