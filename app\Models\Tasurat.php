<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tasurat extends Model
{
    use HasFactory;

    // Define the table name (optional if it matches the model name convention)
    protected $table = 'tasurats';

    // Define the fillable fields to allow mass assignment
    protected $fillable = [
        'option', 
        'tasurat_no'
    ];

    // If the table doesn't have the `created_at` or `updated_at` timestamps, disable them like this
    // public $timestamps = false;
}
