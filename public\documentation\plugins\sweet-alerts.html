
<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8">
    <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
    <link rel="icon" href="../../assets/img/favicon.png" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Creative Tim">
    <title>
      Sweet Alerts | Material Dashboard 2 FREE Laravel by Creative Tim & UPDIVISION
    </title>
    <link rel="canonical" href="https://www.creative-tim.com/learning-lab/bootstrap/colors/material-dashboard" />

    <meta name="keywords" content="creative tim, updivision, html dashboard, laravel, material, html css dashboard laravel, material dashboard laravel, laravel material dashboard, material admin, laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, material dashboard bootstrap 5 laravel, frontend, responsive bootstrap 5 dashboard, material dashboard, material laravel bootstrap 5 dashboard" />
    <meta name="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="name" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta itemprop="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta name="twitter:card" content="product" />
    <meta name="twitter:site" content="@creativetim" />
    <meta name="twitter:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta name="twitter:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta name="twitter:creator" content="@creativetim" />
    <meta name="twitter:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="fb:app_id" content="655968634437471" />
    <meta property="og:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta property="og:type" content="article" />
    <meta property="og:url" content="https://www.creative-tim.com/live/material-dashboard-laravel" />
    <meta property="og:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="og:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta property="og:site_name" content="Creative Tim" />

    <link rel="stylesheet" href="../../assets/css/nucleo-icons.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/nextjs-material-dashboard-pro.min.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/material-dashboard.min.css" type="text/css">
    <link rel="stylesheet" href="../../assets/css/demo.css" type="text/css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Roboto+Slab:400,700" rel="stylesheet">

    <link href="../../assets/css/nucleo-icons.css" rel="stylesheet">

    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
    <!-- Anti-flicker snippet (recommended)  -->
    <style>
      .async-hide {
        opacity: 0 !important
      }
    </style>
    <script>
      (function(a, s, y, n, c, h, i, d, e) {
        s.className += ' ' + y;
        h.start = 1 * new Date;
        h.end = i = function() {
          s.className = s.className.replace(RegExp(' ?' + y), '')
        };
        (a[n] = a[n] || []).hide = h;
        setTimeout(function() {
          i();
          h.end = null
        }, c);
        h.timeout = c;
      })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {
        'GTM-K9BGS8K': true
      });
    </script>
    <!-- Analytics-Optimize Snippet -->
    <script>
      (function(i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function() {
          (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
          m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
      })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
      ga('create', 'UA-46172202-22', 'auto', {
        allowLinker: true
      });
      ga('set', 'anonymizeIp', true);
      ga('require', 'GTM-K9BGS8K');
      ga('require', 'displayfeatures');
      ga('require', 'linker');
      ga('linker:autoLink', ["2checkout.com", "avangate.com"]);
    </script>
    <!-- end Analytics-Optimize Snippet -->
    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js'
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-NKDMSK6');
    </script>
    <!-- End Google Tag Manager -->
    <!-- This is for docs typography and layout -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="../../assets/css/docs.css" rel="stylesheet" />
  </head>
  <body class="docs" style="padding-right: 0px;">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NKDMSK6" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <header class="ct-docs-navbar">
      <a class="ct-docs-navbar-brand" href="javascript:void(0)" aria-label="Bootstrap">
        </a><a href="https://www.creative-tim.com/" class="ct-docs-navbar-text" target="_blank">
          Creative Tim
        </a>
        <div class="ct-docs-navbar-border"></div>
        <a href="../../documentation/getting-started/installation.html" class="ct-docs-navbar-text">
          Docs
        </a>

      <ul class="ct-docs-navbar-nav-left">
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Live Preview</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownPreview">
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-pro-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Support</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownSupport">
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/material-dashboard-laravel/issues" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/ct-material-dashboard-pro-laravel/issues" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
      </ul>
      <ul class="ct-docs-navbar-nav-right">
        <li class="ct-docs-navbar-nav-item">
          <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/material-dashboard-laravel" target="_blank">Download Free</a>
        </li>
      </ul>
      <a href="https://www.creative-tim.com/product/material-dashboard-pro-laravel" target="_blank" class="ct-docs-btn-upgrade">
        <span class="ct-docs-btn-inner--icon">
          <i class="fas fa-download mr-2" aria-hidden="true"></i>
        </span>
        <span class="ct-docs-navbar-nav-link-inner--text">Upgrade to PRO</span>
      </a>
      <button class="ct-docs-navbar-toggler" type="button">
        <span class="ct-docs-navbar-toggler-icon"></span>
      </button>
    </header>
    <div class="ct-docs-main-container">
      <div class="ct-docs-main-content-row">
        <div class="ct-docs-sidebar-col">
          <nav class="ct-docs-sidebar-collapse-links">
            <div class="ct-docs-sidebar-product">
              <div class="ct-docs-sidebar-product-image">
                <img src="../../assets/img/bootstrap.png">
              </div>
              <p class="ct-docs-sidebar-product-text">Material Dashboard</p>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-active-40 text-white"></i>
                  </div>
                </div>
                Getting started
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/getting-started/overview.html">
                    Overview
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/license.html">
                    License
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/installation.html">
                    Installation
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/build-tools.html">
                    Build Tools
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/bootstrap.html">
                    What is Bootstrap
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
                <a class="ct-docs-toc-link" href="javascript:void(0)">
                  <div class="d-inline-block">
                    <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                      <i class="ni ni-folder-17 text-white"></i>
                    </div>
                  </div>
                  Laravel
                </a>
                <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                  <li class="">
                    <a href="../../documentation/laravel/login.html">
                      Login
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/sign-up.html">
                      Sign Up
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/forgot-password.html">
                      Forgot Password
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/user-profile.html">
                      User Profile
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/user-management.html">
                      User Management
                      <span class="ct-docs-sidenav-pro-badge">Pro</span>
                    </a>
                  </li>
                </ul>
              </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-folder-17 text-white"></i>
                  </div>
                </div>
                Foundation
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class=" ">
                  <a href="../../documentation/foundation/colors.html">
                    Colors
                  </a>
                </li>
                <li class=" ">
                  <a href="../../documentation/foundation/grid.html">
                    Grid
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/typography.html">
                    Typography
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/icons.html">
                    Icons
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/utilities.html">
                    Utilities
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-app text-white"></i>
                  </div>
                </div>
                Components
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/components/alerts.html">
                    Alerts
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/badge.html">
                    Badge
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/buttons.html">
                    Buttons
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/social-buttons.html">
                    Social Buttons
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/cards.html">
                    Cards
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/carousel.html">
                    Carousel
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/collapse.html">
                    Collapse
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/dropdowns.html">
                    Dropdowns
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/forms.html">
                    Forms
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/input-group.html">
                    Input Group
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/list-group.html">
                    List Group
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/modal.html">
                    Modal
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/navs.html">
                    Navs
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/navbar.html">
                    Navbar
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/pagination.html">
                    Pagination
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/popovers.html">
                    Popovers
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/progress.html">
                    Progress
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/spinners.html">
                    Spinners
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/tables.html">
                    Tables
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/tooltips.html">
                    Tooltips
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-settings text-white"></i>
                  </div>
                </div>
                Plugins
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/plugins/countUpJs.html">
                    CountUp JS
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/charts.html">
                    Charts
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/datepicker.html">
                    Datepicker
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/fullcalendar.html">
                    Fullcalendar
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/sliders.html">
                    Sliders
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/choices.html">
                    Choices
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/dropzone.html">
                    Dropzone
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/datatables.html">
                    Datatables
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/kanban.html">
                    Kanban
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/photo-swipe.html">
                    Photo Swipe
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/quill.html">
                    Quill
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="ct-docs-nav-sidenav-active">
                  <a href="../../documentation/plugins/sweet-alerts.html">
                    Sweet Alerts
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/wizard.html">
                    Wizard
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
              </ul>
            </div>
          </nav>
        </div>
        <div class="ct-docs-toc-col">
          <ul class="section-nav">
            <li class="toc-entry toc-h2"><a href="#usage">Usage</a>
              <ul>
                <li class="toc-entry toc-h3"><a href="#js">JS</a></li>
              </ul>
            </li>
            <li class="toc-entry toc-h2"><a href="#examples">Examples</a></li>
          </ul>
        </div>
        <main class="ct-docs-content-col" role="main">
            <div class="ct-docs-page-title">
              <h1 class="ct-docs-page-h1-title" id="content">
                Bootstrap Sweet Alerts
              </h1>
              <span class="ct-docs-page-title-pro-line"> - </span>
              <div class="ct-docs-page-title-pro-bage">Pro Component</div>
              <div class="avatar-group mt-3">
              </div>
            </div>
            <p class="ct-docs-page-title-lead">Our Bootstrap Sweet Alerts are beautiful, responsive, customisable, accessible replacements for Javascript’s popup boxes. <br /> Keep reading our Bootstrap Alerts examples and learn how to use this plugin.</p>
            <hr class="ct-docs-hr">
            <h2 id="usage">Usage</h2>
            <h3 id="js">JS</h3>
            <p>In order to use this plugin on your page you will need to include the following script in the “Optional JS” area from the page’s footer:</p>
            <div class="position-relative">
              <figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;script </span><span class="na">src=</span><span class="s">"../../assets/js/plugins/sweetalert.min.js"</span><span class="nt">&gt;&lt;/script&gt;</span></code></pre>
              </figure>
            </div>
            <h2 id="examples">Examples</h2>
            <div class="row mb-5">
              <div class="col-lg-12 col-md-12 mx-auto">
                <div class="row mt-5">
                  <div class="col-md-4">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">Basic example</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('basic')">Try me!</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 mt-4 mt-md-0">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">A success message</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('success-message')">Try me!</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 mt-4 mt-md-0">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">Custom HTML description</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('custom-html')">Try me!</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row mt-4">
                  <div class="col-md-4">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">Gitgub avatar request</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('input-field')">Try me!</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 mt-4 mt-md-0">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">A title with a text under</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('title-and-text')">Try me!</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 mt-4 mt-md-0">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">A message with auto close</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('auto-close')">Try me!</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row mt-4 mb-5">
                  <div class="col-md-4">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">A warning message, with a function attached to the "Confirm" Button...</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('warning-message-and-confirmation')">Try me!</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 mt-4 mt-md-0">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">...and by passing a parameter, you can execute something else for "Cancel"</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('warning-message-and-cancel')">Try me!</button>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-4 mt-4 mt-md-0">
                    <div class="card">
                      <div class="card-body text-center">
                        <p class="card-text">Right-to-left support for Arabic, Persian, Hebrew, and other RTL languages</p>
                        <button class="btn bg-gradient-primary mb-0" onclick="material.showSwal('rtl-language')">Try me!</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="position-relative">
              <figure class="highlight"><pre><code class="language-html" data-lang="html"><span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('basic')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('success-message')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('custom-html')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('input-field')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('title-and-text')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('auto-close')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('warning-message-and-confirmation')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('warning-message-and-cancel')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span>
  <span class="nt">&lt;button</span> <span class="na">class=</span><span class="s">"btn bg-gradient-primary mb-0"</span> <span class="na">onclick=</span><span class="s">"material.showSwal('rtl-language')"</span><span class="nt">&gt;</span>Try me!<span class="nt">&lt;/button&gt;</span></code></pre>
              </figure>
            </div>
          </main>
        </div>
      <div class="ct-docs-main-footer-row">
        <div class="ct-docs-main-footer-blank-col">
        </div>
        <div class="ct-docs-main-footer-col">
          <footer class="ct-docs-footer">
            <div class="ct-docs-footer-inner-row">
              <div class="ct-docs-footer-col">
                <div class="ct-docs-footer-copyright">
                  © <script>
                    document.write(new Date().getFullYear())
                  </script> <a href="https://creative-tim.com" class="ct-docs-footer-copyright-author" target="_blank">Creative Tim</a> & <a href="https://www.updivision.com" class="ct-docs-footer-copyright-author" target="_blank">UPDIVISION</a>
                </div>
              </div>
              <div class="ct-docs-footer-col">
                <ul class="ct-docs-footer-nav-footer">
                  <li>
                    <a href="https://creative-tim.com" class="ct-docs-footer-nav-link" target="_blank">Creative Tim</a>
                  </li>
                  <li class="nav-item">
                    <a href="https://www.updivision.com" class="ct-docs-footer-nav-link" target="_blank">UPDIVISION</a>
                </li>
                  <li>
                    <a href="https://www.creative-tim.com/contact-us" class="ct-docs-footer-nav-link" target="_blank">Contact Us</a>
                  </li>
                  <li>
                    <a href="https://creative-tim.com/blog" class="ct-docs-footer-nav-link" target="_blank">Blog</a>
                  </li>
                </ul>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
    <script src="../../assets/js/jquery.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/popper.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="../../assets/js/prism.js" type="text/javascript"></script>
    <script src="../../assets/js/docs.min.js" type="text/javascript"></script>
    <script src="../../assets/js/holder.min.js" type="text/javascript"></script>
    <script src="../../assets/js/moment.min.js" type="text/javascript"></script>
    <script src="../../assets/js/material-dashboard.min.js" type="text/javascript"></script>
    <script src="../../assets/js/plugins/sweetalert.min.js"></script>
    <script>
      Holder.addTheme('gray', {
        bg: '#777',
        fg: 'rgba(255,255,255,.75)',
        font: 'Helvetica',
        fontweight: 'normal'
      })
    </script>
    <script>
      // Facebook Pixel Code Don't Delete
      ! function(f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function() {
          n.callMethod ?
            n.callMethod.apply(n, arguments) : n.queue.push(arguments)
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s)
      }(window,
        document, 'script', '//connect.facebook.net/en_US/fbevents.js');

      try {
        fbq('init', '111649226022273');
        fbq('track', "PageView");

      } catch (err) {
        console.log('Facebook Track Error:', err);
      }
    </script>
    <script src="../../assets/js/docs.js"></script>
  </body>
  </html>
