<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('uploaded_files', function (Blueprint $table) {
            // Add columns to track Mahl-e-Nazar status and count
            $table->boolean('is_mahl_e_nazar')->default(false)->after('ftype');
            $table->integer('mahl_e_nazar_count')->default(0)->after('is_mahl_e_nazar');
            $table->date('last_mahl_e_nazar_date')->nullable()->after('mahl_e_nazar_count');
            $table->text('mahl_e_nazar_reason')->nullable()->after('last_mahl_e_nazar_date');
            
            // Add index for efficient Mahl-e-Nazar queries
            $table->index(['is_mahl_e_nazar', 'mahl_e_nazar_count']);
            $table->index(['last_mahl_e_nazar_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('uploaded_files', function (Blueprint $table) {
            $table->dropIndex(['is_mahl_e_nazar', 'mahl_e_nazar_count']);
            $table->dropIndex(['last_mahl_e_nazar_date']);
            $table->dropColumn([
                'is_mahl_e_nazar',
                'mahl_e_nazar_count',
                'last_mahl_e_nazar_date',
                'mahl_e_nazar_reason'
            ]);
        });
    }
};
