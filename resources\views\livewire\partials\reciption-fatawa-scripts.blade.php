<script>
// Reception Fatawa JavaScript Functions

// Show loading overlay
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

// Hide loading overlay
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Legacy updateUrl function for compatibility (now handled by Livewire)
function updateUrl(reload = false) {
    // This function is now handled by Livewire wire:model.live
    // Keeping for backward compatibility but doing nothing
    console.log('updateUrl called - now handled by Livewire');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading overlay when page is loaded
    hideLoading();

    // Force hide full-page loading on page load
    const fullPageLoading = document.querySelector('.full-page-loading');
    if (fullPageLoading) {
        fullPageLoading.style.display = 'none';
        fullPageLoading.style.opacity = '0';
        fullPageLoading.style.visibility = 'hidden';
    }

    // Add instant loading for Apply Filters button ONLY
    setTimeout(() => {
        const applyFilterButtons = document.querySelectorAll('[wire\\:click="applyFilters"]');
        applyFilterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Show full-page loading immediately
                const fullPageLoading = document.querySelector('.full-page-loading');
                if (fullPageLoading) {
                    fullPageLoading.style.display = 'flex';
                    fullPageLoading.style.opacity = '1';
                    fullPageLoading.style.visibility = 'visible';
                }
            });
        });

        // Add instant loading for month navigation buttons ONLY
        const monthNavButtons = document.querySelectorAll('[wire\\:click^="nextMonth"], [wire\\:click^="previousMonth"], [wire\\:click^="goToMonth"]');
        monthNavButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Show full-page loading immediately
                const fullPageLoading = document.querySelector('.full-page-loading');
                if (fullPageLoading) {
                    fullPageLoading.style.display = 'flex';
                    fullPageLoading.style.opacity = '1';
                    fullPageLoading.style.visibility = 'visible';
                }
            });
        });
    }, 100);

    // Aggressively clear all loading states on page load
    function clearAllLoading() {
        hideLoading();
        // Force hide any stuck loading overlays including full-page loading
        const loadingOverlays = document.querySelectorAll('.loading-overlay');
        loadingOverlays.forEach(overlay => {
            overlay.style.display = 'none';
        });

        // Force hide full-page loading specifically
        const fullPageLoading = document.querySelector('.full-page-loading');
        if (fullPageLoading) {
            fullPageLoading.style.display = 'none !important';
            fullPageLoading.style.opacity = '0 !important';
            fullPageLoading.style.visibility = 'hidden !important';
        }

        // Remove wire:loading attributes that might be stuck
        const wireLoadingElements = document.querySelectorAll('[wire\\:loading\\.class]');
        wireLoadingElements.forEach(element => {
            element.classList.remove('opacity-50');
        });
    }

    // Clear loading immediately and with delays
    clearAllLoading();
    setTimeout(clearAllLoading, 100);
    setTimeout(clearAllLoading, 500);
    setTimeout(clearAllLoading, 1000);
});

    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-dismissible')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
});

// Backup: Also hide loading on window load
window.addEventListener('load', function() {
    // Use the same aggressive clearing function
    function clearAllLoading() {
        hideLoading();
        // Force hide any stuck loading overlays
        const loadingOverlays = document.querySelectorAll('.loading-overlay');
        loadingOverlays.forEach(overlay => {
            overlay.style.display = 'none';
        });

        // Force hide full-page loading specifically
        const fullPageLoading = document.querySelector('.full-page-loading');
        if (fullPageLoading) {
            fullPageLoading.style.display = 'none !important';
            fullPageLoading.style.opacity = '0 !important';
            fullPageLoading.style.visibility = 'hidden !important';
        }

        // Remove wire:loading attributes that might be stuck
        const wireLoadingElements = document.querySelectorAll('[wire\\:loading\\.class]');
        wireLoadingElements.forEach(element => {
            element.classList.remove('opacity-50');
        });
    }

    clearAllLoading();
    setTimeout(clearAllLoading, 100);
});

// Form submission with loading
document.addEventListener('submit', function(e) {
    if (e.target.tagName === 'FORM') {
        showLoading();
    }
});

// Link clicks with loading (except for same-page anchors)
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' || e.target.closest('a')) {
        const link = e.target.tagName === 'A' ? e.target : e.target.closest('a');
        const href = link.getAttribute('href');
        
        // Don't show loading for anchor links, external links, or javascript links
        if (href && !href.startsWith('#') && !href.startsWith('javascript:') && !href.includes('mailto:') && !href.includes('tel:')) {
            // Check if it's an external link
            try {
                const url = new URL(href, window.location.origin);
                if (url.origin === window.location.origin) {
                    showLoading();
                }
            } catch (e) {
                // If URL parsing fails, assume it's internal
                showLoading();
            }
        }
    }
});

// Chat functionality
function initializeChat() {
    // Auto-scroll chat to bottom
    const chatContainers = document.querySelectorAll('.chat-messages-container');
    chatContainers.forEach(container => {
        const messagesDiv = container.querySelector('.chat-messages');
        if (messagesDiv) {
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
    });
    
    // Handle chat form submissions
    const chatForms = document.querySelectorAll('.chat-form');
    chatForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const input = form.querySelector('.chat-input');
            if (input && input.value.trim() === '') {
                e.preventDefault();
                input.focus();
                return false;
            }
        });
    });
}

// Initialize chat when page loads
document.addEventListener('DOMContentLoaded', initializeChat);

// Simplified Livewire hooks (if Livewire is available)
if (typeof Livewire !== 'undefined') {
    // Hide loading when Livewire request completes
    Livewire.hook('message.processed', (message, component) => {
        hideLoading();
        initializeChat();
    });

    // Handle Livewire errors
    Livewire.hook('message.failed', (message, component) => {
        hideLoading();
    });

    // Component loaded hook
    Livewire.hook('component.initialized', (component) => {
        hideLoading();
    });

    // Additional hook for element updates
    Livewire.hook('element.updated', (el, component) => {
        // Re-initialize tooltips after element updates
        if (typeof bootstrap !== 'undefined') {
            const tooltips = el.querySelectorAll('[data-bs-toggle="tooltip"]');
            tooltips.forEach(tooltip => {
                new bootstrap.Tooltip(tooltip);
            });
        }
    });
}

// Alpine.js data for global state management
document.addEventListener('alpine:init', () => {
    Alpine.data('receptionFatawa', () => ({
        loading: false,
        
        init() {
            // Initialize component
            this.loading = false;
        },
        
        toggleLoading() {
            this.loading = !this.loading;
        },
        
        showLoading() {
            this.loading = true;
        },
        
        hideLoading() {
            this.loading = false;
        }
    }));
});

// Utility functions
const ReceptionFatawaUtils = {
    // Format numbers with commas
    formatNumber: function(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    
    // Copy text to clipboard
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            ReceptionFatawaUtils.showToast('Copied to clipboard!', 'success');
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            ReceptionFatawaUtils.showToast('Failed to copy to clipboard', 'error');
        });
    },
    
    // Show toast notification
    showToast: function(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    },
    
    // Debounce function for search inputs
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// Month Navigation functionality with performance optimizations
const MonthNavigationUtils = {
    // Debounce function to prevent rapid clicks
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Smooth scroll to top when month changes
    scrollToTop: function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    },

    // Update URL without page reload
    updateMonthUrl: function(monthIndex) {
        const url = new URL(window.location);
        url.searchParams.set('currentMonthIndex', monthIndex);
        window.history.replaceState({}, '', url.toString());
    },

    // Handle keyboard navigation with debouncing
    handleKeyboardNavigation: function(event) {
        if (event.ctrlKey || event.metaKey) {
            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    MonthNavigationUtils.debouncedPreviousMonth();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    MonthNavigationUtils.debouncedNextMonth();
                    break;
            }
        }
    },

    // Debounced navigation methods
    debouncedPreviousMonth: null,
    debouncedNextMonth: null,

    // Initialize debounced methods
    init: function() {
        this.debouncedPreviousMonth = this.debounce(() => {
            if (typeof Livewire !== 'undefined') {
                Livewire.emit('previousMonth');
            }
        }, 300);

        this.debouncedNextMonth = this.debounce(() => {
            if (typeof Livewire !== 'undefined') {
                Livewire.emit('nextMonth');
            }
        }, 300);
    }
};

// Listen for month navigation events with performance optimizations
document.addEventListener('DOMContentLoaded', function() {
    // Initialize debounced navigation
    MonthNavigationUtils.init();

    // Add keyboard navigation
    document.addEventListener('keydown', MonthNavigationUtils.handleKeyboardNavigation);

    // Debounced click handler for navigation buttons
    const debouncedNavigationClick = MonthNavigationUtils.debounce(function(e) {
        if (e.target.closest('.month-nav-btn') || e.target.closest('.month-grid-item')) {
            showLoading();
        }
    }, 200);

    document.addEventListener('click', debouncedNavigationClick);

    // Optimized hover effects using requestAnimationFrame
    const addHoverEffects = () => {
        const monthGridItems = document.querySelectorAll('.month-grid-item');
        monthGridItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                requestAnimationFrame(() => {
                    this.style.transform = 'translateY(-3px) scale(1.02)';
                });
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    requestAnimationFrame(() => {
                        this.style.transform = 'translateY(0) scale(1)';
                    });
                }
            });
        });
    };

    // Add hover effects with delay to ensure DOM is ready
    setTimeout(addHoverEffects, 100);
});

// Livewire hooks for month navigation
if (typeof Livewire !== 'undefined') {
    Livewire.hook('message.processed', (message, component) => {
        hideLoading();

        // Scroll to top after month navigation
        if (message.updateQueue.some(update =>
            ['nextMonth', 'previousMonth', 'goToMonth'].includes(update.method))) {
            MonthNavigationUtils.scrollToTop();
        }
    });
}

// Export for global use
window.ReceptionFatawaUtils = ReceptionFatawaUtils;
window.MonthNavigationUtils = MonthNavigationUtils;
</script>
