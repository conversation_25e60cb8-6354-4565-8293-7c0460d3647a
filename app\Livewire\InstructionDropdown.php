<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Instruction; // Assuming you have an Instruction model

class InstructionDropdown extends Component
{
    public $instructions;
    public $selectedInstructionId;

    // Listening for the 'instruction-updated' and 'resetInstructionDropdown' events
    protected $listeners = [
        'instruction-updated' => 'loadInstructions',
        'resetInstructionDropdown' => 'resetInstruction'
    ];

    public function mount()
    {
        $this->loadInstructions();
    }

    public function loadInstructions()
    {
        $this->instructions = Instruction::all();
    }

    public function resetInstruction()
    {
        $this->selectedInstructionId = null; // Reset to default or null
    }

    public function render()
    {
        return view('livewire.instruction-dropdown');
    }
}
