<div>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <style>
         .main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }
        .custom-select {
            display: block;
            width: 100%;
            padding: 0.375rem 1.75rem 0.375rem 0.75rem;
            font-size: 1rem;
            line-height: 1.5;
            color: #495057;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .custom-select:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .result-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
            margin-bottom: 30px;
        }

        .result-box {
            flex: 1 1 22%;
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
            font-family: Arial, sans-serif;
        }

        .result-box h3 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #007bff;
            text-transform: uppercase;
            font-weight: bold;
        }

        .result-box ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .result-box ul li {
            font-size: 16px;
            margin: 5px 0;
            color: #333;
        }

        .result-box .total {
            font-weight: bold;
            margin-top: 15px;
        }

        .pagination-links {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            padding: 10px 0;
        }

        .pagination .page-item {
            margin: 0 5px;
        }

        .pagination .page-link {
            display: inline-block;
            padding: 8px 16px;
            border: 1px solid #ccc;
            border-radius: 4px;
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
            background-color: #fff;
        }

        .pagination .page-link:hover {
            background-color: #f1f1f1;
            color: #0056b3;
        }

        .pagination .page-item.active .page-link {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .pagination .page-item.disabled .page-link {
            background-color: #e0e0e0;
            color: #b0b0b0;
            cursor: not-allowed;
        }

        table, th, td {
            color: #333;
        }

        h1 {
            color: #333;
        }

        .search-bar input {
            color: #333;
            width: 100%;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ced4da;
        }

        i.fas {
            color: #007bff;
        }

        .star-btn {
            cursor: pointer;
            font-size: 1.2rem;
        }

        .yellow-star {
            color: #FFD700;
        }

        .blue-star {
            color: #007bff;
        }

        /* Add styles for filter buttons */
        .apply-filters-button {
            display: inline-block;
            padding: 8px 16px;
            margin-right: 10px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            color: #495057;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .apply-filters-button:hover {
            background-color: #e9ecef;
            color: #212529;
        }

        .apply-filters-button-active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }

        .apply-filters-button-active:hover {
            background-color: #0056b3;
            color: white;
        }

        /* --- General Table Styling --- */
        .card {
            border: none; /* Remove default card border if shadow is enough */
        }

        /* Folder column styling */
        .folder-column {
            vertical-align: middle;
        }

        .folder-column .folder-dates {
            display: flex;
            flex-direction: column;
            gap: 3px;
            max-height: 150px; /* Increased max height to show more items */
            overflow-y: auto;
        }

        .folder-column .badge {
            display: inline-block;
            font-size: 0.8rem;
            padding: 0.3rem 0.5rem;
            margin: 1px 0;
        }

        /* --- Table Styling --- */
        .table {
            font-size: 0.9rem;
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            margin-bottom: 1rem;
            background-color: #fff;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        }

        /* Table Header Styling */
        .table thead th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            padding: 1rem;
            border-bottom: 2px solid #dee2e6;
            text-align: center;
            vertical-align: middle;
        }

        /* Table Body Styling */
        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:nth-child(odd) {
            background-color: #ffffff;
        }

        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table tbody tr:hover {
            background-color: #e9f0f7;
        }

        .table tbody td {
            padding: 0.75rem 1rem;
            vertical-align: middle;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
        }

        /* Column-specific text colors */
        .serial-column {
            color: #1a237e;
            font-weight: 600;
        }

        .fatwa-no-column {
            color: #0d47a1;
            font-weight: 500;
        }

        .mujeeb-column {
            color: #2e7d32;
            font-weight: 500;
        }

        .darulifta-column {
            color: #4a148c;
            font-weight: 500;
        }

        .date-column {
            color: #e65100;
            font-weight: 500;
        }

        .category-column {
            color: #01579b;
            font-weight: 500;
        }

        .days-column {
            color: #455a64;
            font-weight: 500;
        }

        .pending-days {
            font-weight: 600;
        }

        .pending-days .text-danger {
            color: #c62828 !important;
        }

        .pending-days .text-orange {
            color: #ef6c00 !important;
        }

        .pending-days .text-teal {
            color: #00796b !important;
        }

        .folder-column {
            color: #006064;
        }

        .folder-column .badge {
            background-color: #e0f7fa;
            color: #006064;
            font-weight: 500;
            margin: 2px;
            padding: 0.35rem 0.65rem;
        }

        /* Action buttons styling */
        .actions-column .btn {
            padding: 0.375rem 0.75rem;
            font-size: 0.85rem;
            font-weight: 500;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .actions-column .btn-outline-primary {
            color: #0d47a1;
            border-color: #0d47a1;
        }

        .actions-column .btn-outline-primary:hover {
            background-color: #0d47a1;
            color: #ffffff;
        }

        .actions-column .btn-outline-success {
            color: #2e7d32;
            border-color: #2e7d32;
        }

        .actions-column .btn-outline-success:hover {
            background-color: #2e7d32;
            color: #ffffff;
        }

        /* Toggle section styling */
        .toggle-header-row {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            border-bottom: 1px solid #ced4da;
        }

        .toggle-header-row td {
            color: #495057;
            font-weight: 500;
            padding: 0.6rem 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;
        }

        .toggle-header-row td:hover {
            background-color: #e9ecef;
        }

        .toggle-section i {
            transition: transform 0.3s ease;
        }

        .toggle-section.active i {
            transform: rotate(180deg);
        }

        /* Question and Chat sections */
        .question-section {
            background-color: #f8f9fa;
        }

        .question-content {
            background-color: #f2f7fb;
            border-left: 4px solid #0d47a1;
            padding: 1rem;
            color: #2c3e50;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .chat-section {
            background-color: #ffffff;
        }

        /* Pagination styling */
        .pagination {
            margin-top: 1.5rem;
            justify-content: center;
        }

        .pagination .page-link {
            color: #0d47a1;
            border: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
        }

        .pagination .page-item.active .page-link {
            background-color: #0d47a1;
            border-color: #0d47a1;
            color: #ffffff;
        }

        .pagination .page-link:hover {
            background-color: #e9f0f7;
            border-color: #0d47a1;
        }

        /* --- Pagination Styling --- */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 2rem 0;
            padding: 1rem;
            background-color: #ffffff;
            border-radius: 0.75rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            padding: 0;
            list-style: none;
        }

        .page-item {
            margin: 0;
        }

        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 2.5rem;
            height: 2.5rem;
            padding: 0 0.75rem;
            font-size: 0.95rem;
            font-weight: 500;
            color: #1a237e;
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 0.5rem;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .page-link:hover {
            background-color: #e9f0f7;
            border-color: #1a237e;
            color: #1a237e;
            transform: translateY(-1px);
        }

        .page-item.active .page-link {
            background: linear-gradient(135deg, #1a237e, #283593);
            border-color: #1a237e;
            color: #ffffff;
            box-shadow: 0 2px 4px rgba(26, 35, 126, 0.2);
        }

        .page-item.disabled .page-link {
            color: #9e9e9e;
            background-color: #f5f5f5;
            border-color: #e0e0e0;
            cursor: not-allowed;
        }

        .page-item:first-child .page-link,
        .page-item:last-child .page-link {
            padding: 0 1rem;
        }

        .page-item:first-child .page-link {
            border-top-left-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
        }

        .page-item:last-child .page-link {
            border-top-right-radius: 0.5rem;
            border-bottom-right-radius: 0.5rem;
        }

        /* Pagination Info */
        .pagination-info {
            display: flex;
            align-items: center;
            margin-left: 1rem;
            padding: 0.5rem 1rem;
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            color: #455a64;
        }

        /* Responsive Pagination */
        @media (max-width: 768px) {
            .pagination-container {
                padding: 0.75rem;
            }

            .page-link {
                min-width: 2rem;
                height: 2rem;
                padding: 0 0.5rem;
                font-size: 0.85rem;
            }

            .pagination-info {
                display: none;
            }
        }

    </style>

<div wire:ignore>
           <?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show  bg-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show  bg-gray-200']); ?>
           <?php

$ViralNazim = Auth::check() && in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray());

?>
<!--[if BLOCK]><![endif]--><?php if($ViralNazim): ?>
    <?php if (isset($component)) { $__componentOriginal32a1eee73574e3db9eebcd9b7dfbed05 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal32a1eee73574e3db9eebcd9b7dfbed05 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.vsidebar','data' => ['activePage' => ''.e(request()->route('darulifta') ?? 'viral-fatawa').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.vsidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => ''.e(request()->route('darulifta') ?? 'viral-fatawa').'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal32a1eee73574e3db9eebcd9b7dfbed05)): ?>
<?php $attributes = $__attributesOriginal32a1eee73574e3db9eebcd9b7dfbed05; ?>
<?php unset($__attributesOriginal32a1eee73574e3db9eebcd9b7dfbed05); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal32a1eee73574e3db9eebcd9b7dfbed05)): ?>
<?php $component = $__componentOriginal32a1eee73574e3db9eebcd9b7dfbed05; ?>
<?php unset($__componentOriginal32a1eee73574e3db9eebcd9b7dfbed05); ?>
<?php endif; ?>
<?php else: ?>
<?php if (isset($component)) { $__componentOriginaleeb4de73933bf6c97cffe74e5846276e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.sidebar','data' => ['activePage' => ''.e(request()->route('darulifta') ?? 'viral-fatawa').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => ''.e(request()->route('darulifta') ?? 'viral-fatawa').'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $attributes = $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $component = $__componentOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
</div>

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('navbar', ['titlePage' => 'Mahl-e-Nazar Fatawa']);

$__html = app('livewire')->mount($__name, $__params, 'lw-639352040-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <div class="container">
            <?php
                // Check if the user is authenticated before checking roles
                $Admin = Auth::check() && (
                    in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
                    in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
                );
            ?>

            <div class="card mb-4">
                <div class="card-body">
                    <div>
                        <h2 class="card-title text-primary mb-4">Mahl-e-Nazar Fatawa Filter</h2>
                        <form method="GET" wire:submit.prevent>
                            <div class="row">
                                <!-- Mujeeb Filter -->
                                <div class="col-md-2 form-group">
                                    <label for="mujeebframe" class="text-success fw-medium">Select Mujeeb:</label>
                                    <select class="custom-select bg-light border-success" id="mujeebframe" wire:model.live="selectedmujeeb">
                                        <option value="all">All Mujeeb</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mujeebs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sender): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($sender); ?>"><?php echo e($sender); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                </div>

                                <!-- Time Frame Filter -->
                                <div class="col-md-2 form-group">
                                    <label for="timeframe" class="text-indigo fw-medium">Time Frame:</label>
                                    <select class="custom-select bg-light border-indigo" id="timeframe" wire:model.live="selectedTimeFrame">
                                        <option value="all">All</option>
                                        <option value="this_month">This Month</option>
                                        <option value="last_month">Last Month</option>
                                        <option value="custom">Custom Date Range</option>
                                    </select>
                                </div>

                                <!-- Date Range Inputs -->
                                <!--[if BLOCK]><![endif]--><?php if($selectedTimeFrame == 'custom'): ?>
                                    <div class="col-md-2 form-group">
                                        <label for="start_date" class="text-warning fw-medium">Start Date:</label>
                                        <input type="date" class="form-control bg-light border-warning" id="start_date" wire:model.live="startDate">
                                    </div>
                                    <div class="col-md-2 form-group">
                                        <label for="end_date" class="text-warning fw-medium">End Date:</label>
                                        <input type="date" class="form-control bg-light border-warning" id="end_date" wire:model.live="endDate">
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </form>

                        <!-- Darulifta Filter Buttons -->
                        <!--[if BLOCK]><![endif]--><?php if($Admin): ?>
                            <div style="display: flex; align-items: center; margin-top: 20px; margin-bottom: 20px;" wire:key="darulifta-filter-container">
                                <!-- All Ifta Button -->
                                <?php
                                    $isAllIftaActive = request()->route('darulifta') == null;
                                    $queryParams = [
                                        'selectedmujeeb' => request('selectedmujeeb'),
                                        'selectedTimeFrame' => request('selectedTimeFrame'),
                                        'startDate' => request('startDate'),
                                        'endDate' => request('endDate')
                                    ];
                                ?>
                                <a href="<?php echo e(route('mahlenazar-fatawa', array_merge($queryParams))); ?>"
                                   class="apply-filters-button <?php echo e($isAllIftaActive ? 'apply-filters-button-active' : ''); ?>"
                                   wire:key="all-ifta-button">
                                   All Ifta
                                </a>

                                <!-- Darulifta Buttons -->
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $daruliftalist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftalistn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $isActive = request()->route('darulifta') == $daruliftalistn;
                                    ?>
                                    <a href="<?php echo e(route('mahlenazar-fatawa', array_merge(['darulifta' => $daruliftalistn], $queryParams))); ?>"
                                       class="apply-filters-button <?php echo e($isActive ? 'apply-filters-button-active' : ''); ?>"
                                       wire:key="darulifta-button-<?php echo e($daruliftalistn); ?>">
                                        <?php echo e($daruliftalistn); ?>

                                    </a>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>


            <!-- Summary Report Section -->
            <div class="card mb-4 shadow-sm rounded" style="overflow: hidden;">
    <div class="card-body" style="overflow-x: auto;">
        <h3 class="mb-3 text-primary">Still In Mahl-e-Nazar</h3>

        <h4 class="mb-4 text-secondary">Darulifta & Mujeeb Summary of Mahl-e-Nazar</h4>

        <div style="overflow-x: auto;">
            <table class="table table-bordered table-hover table-striped" style="table-layout: fixed; width: 100%;">
                <thead class="/*table-dark*/ text-center" style="background-color: #f8f9fa; color: #495057;">
                    <tr>
                        <th style="width: 20%;">Darulifta</th>
                        <th style="width: 60%;">Mujeeb Mahl-e-Nazar Details</th>
                        <th style="width: 20%;">Total</th>
                    </tr>
                </thead>
                <tbody>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $summaryData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td style="word-wrap: break-word;" class="fw-bold text-center align-middle">
                                <?php echo e($daruliftaName); ?>

                            </td>
                            <td style="white-space: normal;" class="text-wrap">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['senders']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sender => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <a href="#"
                                       wire:click.prevent="filterByMujeeb('<?php echo e($sender); ?>')"
                                       class="badge bg-light text-primary me-1 mb-1">
                                        <?php echo e($sender); ?>: <?php echo e($count); ?>

                                    </a>
                                    <!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>
                                        <span class="text-muted">|</span>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </td>
                            <td class="text-center align-middle">
                                <span class="badge bg-light text-success fs-6">Total: <?php echo e($data['total']); ?></span>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
            </table>
        </div>

        <h5 class="mt-4 text-end text-success fw-semibold">
            Overall Total:
            <?php
                $totalCounts = 0;
                foreach($summaryData as $data) {
                    $totalCounts += $data['total'];
                }
                echo $totalCounts;
            ?>
        </h5>
    </div>
</div>


          <!-- Search Bar -->
<div class="card shadow-sm mb-4 p-3" wire:key="search-container">
    <input type="text"
           class="form-control"
           wire:model.live.debounce.300ms="search"
           placeholder="Search by Fatwa No, Mujeeb, or Darulifta...">
</div>

<!-- Data Table -->
<div class="card shadow-sm mb-4" wire:key="data-table-container">
    <div class="table-responsive">
        <table class="table table-bordered table-hover align-middle mb-0 table-striped" style="table-layout: auto; width: 100%;">
            <thead class="table-header">
                <tr>
                    <th class="serial-header text-center" style="width: 4%; white-space: normal;">
                        <span class="header-text">S.No</span>
                    </th>
                    <th class="fatwa-header" style="width: 8%; white-space: normal;">
                        <span class="header-text">Fatwa No</span>
                    </th>
                    <th class="mujeeb-header" style="width: 12%; white-space: normal;">
                        <span class="header-text">Mujeeb</span>
                    </th>
                    <th class="darulifta-header" style="width: 12%; white-space: normal;">
                        <span class="header-text">Darulifta</span>
                    </th>
                    <th class="date-header" style="width: 10%; white-space: normal;">
                        <span class="header-text">Mail Folder Date</span>
                    </th>
                    <th class="category-header" style="width: 13%; white-space: normal;">
                        <span class="header-text">Category</span>
                    </th>
                    <th class="days-header text-center" style="width: 7%; white-space: normal;">
                        <span class="header-text">Q.Rec. Days</span>
                    </th>
                    <th class="pending-header text-center" style="width: 7%; white-space: normal;">
                        <span class="header-text">Pending Days</span>
                    </th>
                    <th class="folder-header" style="width: 12%; white-space: normal;">
                        <span class="header-text">Folder</span>
                    </th>
                    <th class="actions-header text-center" style="width: 15%; white-space: normal;">
                        <span class="header-text">Actions</span>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php $serialNumber = ($mahlenazar->currentPage() - 1) * $mahlenazar->perPage() + 1; ?>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mahlenazar; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fatwa): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    
                    <tr class="data-row <?php echo e($loop->even ? 'even-row' : 'odd-row'); ?>" wire:key="fatwa-row-<?php echo e($fatwa->id); ?>">
                        <td class="text-center serial-column">
                           <span class="font-weight-bold"><?php echo e($serialNumber++); ?></span>
                        </td>
                        <td class="fatwa-no-column">
                            <span class="fw-medium text-primary"><?php echo e($fatwa->file_code); ?></span>
                        </td>
                        <td class="mujeeb-column" style="width: 12%; white-space: normal;">
                            <span class="text-success"><?php echo e($fatwa->sender); ?></span>
                        </td>
                        <td class="darulifta-column">
                            <span class="text-indigo"><?php echo e($fatwa->darulifta_name); ?></span>
                        </td>
                        <td class="date-column">
                            <span class="text-warning fw-medium"><?php echo e($fatwa->mail_folder_date); ?></span>
                        </td>
                        <td class="category-column">
                            <span class="text-info"><?php echo e($fatwa->category); ?></span>
                        </td>
                        <td class="text-center days-column">
                            
                             <?php $qDays = '-'; ?>
                             <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $que_day_r; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <!--[if BLOCK]><![endif]--><?php if(Str::lower($fatwa->file_code) == Str::lower($day->ifta_code)): ?>
                                     <?php
                                         $recDate = new \DateTime($day->rec_date);
                                         $daysDifference = now()->diff($recDate)->days;
                                         $qDays = $daysDifference . ' days';
                                     ?>
                                     <?php break; ?>
                                 <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                             <span class="text-secondary fw-medium"><?php echo e($qDays); ?></span>
                        </td>
                        <td class="text-center days-column pending-days">
                            <?php
                                $checkedDate = $fatwa->checked_date ?? $fatwa->mail_folder_date;
                                $mahle_today = now()->diffInDays(\Carbon\Carbon::createFromFormat('Y-m-d', $checkedDate));

                                // Define class based on number of days
                                $pendingClass = '';
                                if ($mahle_today > 30) {
                                    $pendingClass = 'text-danger fw-bold';
                                } elseif ($mahle_today > 15) {
                                    $pendingClass = 'text-orange fw-medium';
                                } else {
                                    $pendingClass = 'text-teal fw-medium';
                                }
                            ?>
                            <span class="<?php echo e($pendingClass); ?>"><?php echo e($mahle_today); ?> days</span>
                        </td>
                        <td class="text-center folder-column">
                            <?php
                                $folderDates = [];
                            ?>
                            <?php $__currentLoopData = $mahlenazar_null; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mahle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($fatwa->file_code == $mahle->file_code && $fatwa->sender == $mahle->sender): ?>
                                    <?php
                                        $folderDates[] = $mahle->mail_folder_date;
                                    ?>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                            <!--[if BLOCK]><![endif]--><?php if(count($folderDates) > 0): ?>
                                <div class="folder-dates">
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $folderDates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge"><?php echo e($date); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </td>
                        <td class="text-center actions-column">
                            <div class="d-flex gap-2 justify-content-center">
                                
                                <?php
                                    $baseDateParam = $fatwa->mail_folder_date . $fatwa->darulifta_name;
                                    if (empty($fatwa->by_mufti)) {
                                        $checkerPart = empty($fatwa->checker) ? 'Checked' : 'Checked_by_' . $fatwa->checker;
                                    } else {
                                        $checkerPart = 'Checked_by_' . $fatwa->checker . '_' . $fatwa->by_mufti;
                                    }
                                    $dateParam = $baseDateParam . $checkerPart;
                                ?>
                                <a href="<?php echo e(route('viewCheck', [
        'date' => $dateParam,
        'folder' => $fatwa->checked_folder,
        'filename' => $fatwa->file_name
    ])); ?>" target="_blank" class="btn btn-sm btn-outline-primary">View</a>
                                <a href="<?php echo e(route('downloadCheck', [
        'date' => $dateParam,
        'filename' => $fatwa->file_name,
        'folder' => $fatwa->checked_folder
    ])); ?>" class="btn btn-sm btn-outline-success">Download</a>
                                
                                <?php
                                    $isAdmin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                                ?>
                                
                                <!--[if BLOCK]><![endif]--><?php if($isAdmin): ?>
                                    <a href="<?php echo e(route('transfer-fatwa', ['fatwaId' => $fatwa->id])); ?>" class="btn btn-sm btn-outline-warning">Transfer</a>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </td>
                    </tr>

                    <!-- Toggle Section Headers -->
                    <tr wire:key="toggle-row-<?php echo e($fatwa->id); ?>" class="toggle-header-row">
                         <td colspan="5" class="text-center toggle-section toggle-question"
                             id="toggle-question-<?php echo e($fatwa->id); ?>"
                             data-section="question"
                             data-fatwa-id="<?php echo e($fatwa->id); ?>">
                            سوال <i class="fas fa-chevron-down ms-1"></i>
                         </td>
                         <td colspan="5" class="text-center toggle-section toggle-chat"
                             id="toggle-chat-<?php echo e($fatwa->id); ?>"
                             data-section="chat"
                             data-fatwa-id="<?php echo e($fatwa->id); ?>">
                            Chat <i class="fas <?php echo e(in_array($fatwa->id, $expandedChats) ? 'fa-chevron-up' : 'fa-chevron-down'); ?> ms-1"></i>
                         </td>
                    </tr>

                   <!-- Question Section -->
                     <tr class="hidden-section question-section"
                         wire:key="question-row-<?php echo e($fatwa->id); ?>"
                         id="question-section-<?php echo e($fatwa->id); ?>"
                         style="display: <?php echo e(in_array($fatwa->id, $expandedQuestions) ? 'table-row' : 'none'); ?>">
                        <td colspan="10" class="p-2 question-content" style="text-align: right; direction: rtl; white-space: normal; word-wrap: break-word; font-size: 14px; line-height: 1.5;">
                             <?php $questionText = ''; ?>
                             <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $que_day_r; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <!--[if BLOCK]><![endif]--><?php if(Str::lower($fatwa->file_code) == Str::lower($day->ifta_code)): ?>
                                     <?php $questionText = $day->question; ?>
                                     <?php break; ?>
                                 <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                             <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                             <!--[if BLOCK]><![endif]--><?php if(!empty($questionText)): ?>
                                 <strong>سوال:</strong> <?php echo e($questionText); ?>

                             <?php else: ?>
                                 <em>سوال دستیاب نہیں ہے۔</em>
                             <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </td>
                    </tr>

                    <!-- Chat Section -->
                     <tr wire:key="chat-row-<?php echo e($fatwa->id); ?>"
                         id="chat-section-<?php echo e($fatwa->id); ?>"
                         style="display: <?php echo e(in_array($fatwa->id, $expandedChats) ? 'table-row' : 'none'); ?>">
                        <td colspan="10" class="p-0 chat-content">
                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('chat-box', ['iftaCode' => $fatwa->file_code,'userName' => auth()->user()->name,'userId' => auth()->user()->id,'ifta_code' => $fatwa->file_code,'user_name' => auth()->user()->name,'user_id' => auth()->user()->id]);

$__html = app('livewire')->mount($__name, $__params, 'chat-box-'.e($fatwa->id).'', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                        </td>
                    </tr>

                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="d-flex justify-content-between align-items-center pagination-wrapper">
        <div class="pagination-info">
            Showing <?php echo e($mahlenazar->firstItem() ?? 0); ?> to <?php echo e($mahlenazar->lastItem() ?? 0); ?> of <?php echo e($mahlenazar->total()); ?> entries
        </div>

        <?php echo e($mahlenazar->onEachSide(1)->links('vendor.livewire.custom')); ?>

    </div>
</div>



<style>
    /* --- General Table Styling --- */
    .card {
        border: none; /* Remove default card border if shadow is enough */
    }

    .table {
        font-size: 0.9rem; /* Slightly smaller font for data density */
        border-color: #e9ecef; /* Lighter border color */
    }

    /* --- Table Header --- */
    .table-header {
        background: linear-gradient(to right, #1a237e, #283593); /* Deep blue gradient */
    }

    .table-header th {
        color: #ffffff;
        font-weight: 600;
        border-bottom-width: 2px;
        border-color: rgba(255, 255, 255, 0.2);
        padding: 0.8rem 0.75rem;
        vertical-align: middle;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
    }

    /* Individual header column styling */
    .serial-header {
        background-color: #1a237e; /* Deep indigo */
    }

    .fatwa-header {
        background-color: #0d47a1; /* Deep blue */
    }

    .mujeeb-header {
        background-color: #1b5e20; /* Deep green */
    }

    .darulifta-header {
        background-color: #4a148c; /* Deep purple */
    }

    .date-header {
        background-color: #e65100; /* Deep orange */
    }

    .category-header {
        background-color: #01579b; /* Deep light blue */
    }

    .days-header {
        background-color: #455a64; /* Blue grey */
    }

    .pending-header {
        background-color: #b71c1c; /* Deep red */
    }

    .folder-header {
        background-color: #006064; /* Deep cyan */
    }

    .actions-header {
        background-color: #263238; /* Dark blue grey */
    }

    /* Header text shadow for better readability */
    .header-text {
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
    }

    /* --- Table Body --- */
    .table tbody td {
        padding: 0.75rem;
        vertical-align: middle; /* Ensure vertical alignment */
        border-color: #e9ecef; /* Match lighter border */
    }

    /* Professional alternating row colors */
    .table tbody tr.odd-row {
        background-color: #ffffff; /* Clean white for odd rows */
    }

    .table tbody tr.even-row {
        background-color: #f8f9fa; /* Very light gray for even rows */
    }

    /* Row hover effect */
    .table tbody tr.data-row:hover {
        background-color: #e9f0f7 !important; /* Light blue hover state */
        transition: background-color 0.2s ease;
    }

    /* Professional color palette */
    .text-primary {
        color: #0056b3 !important; /* Deeper blue */
    }

    .text-success {
        color: #2e7d32 !important; /* Deeper green */
    }

    .text-indigo {
        color: #4b0082 !important; /* Indigo */
    }

    .text-warning {
        color: #e65100 !important; /* Deep orange */
    }

    .text-info {
        color: #0277bd !important; /* Blue */
    }

    .text-secondary {
        color: #455a64 !important; /* Blue-grey */
    }

    .text-orange {
        color: #ff6d00 !important; /* Orange */
    }

    .text-teal {
        color: #00796b !important; /* Teal */
    }

    .fw-medium {
        font-weight: 500 !important;
    }

    /* Pending days styling based on urgency */
    .pending-days .text-danger {
        font-weight: 700 !important;
    }

    /* Folder column styling */
    .folder-column {
        vertical-align: middle;
    }

    .folder-column .folder-dates {
        display: flex;
        flex-direction: column;
        gap: 3px;
        max-height: 150px; /* Increased max height to show more items */
        overflow-y: auto;
    }

    .folder-column .badge {
        display: inline-block;
        font-size: 0.8rem;
        padding: 0.3rem 0.5rem;
        margin: 1px 0;
    }

    /* --- Toggle Header Row Styling --- */
    .toggle-header-row {
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-bottom: 1px solid #ced4da;
    }

    .toggle-header-row td.toggle-section {
        color: #495057;
        font-weight: 500;
        padding: 0.6rem 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
    }
    .toggle-header-row td.toggle-section:last-child {
        border-right: none;
    }

    .toggle-header-row td.toggle-section:hover {
        background-color: #e9ecef;
    }

    /* --- Toggle Icon Styling --- */
    .toggle-section i.fas {
        font-size: 0.8em;
        margin-left: 5px;
        transition: transform 0.3s ease;
    }
    .toggle-section.active i.fas {
        transform: rotate(180deg);
    }

    /* --- Hidden Section Content Styling --- */
    .hidden-section {
        display: none;
        border-width: 0 1px 1px 1px;
        border-style: solid;
        border-color: #dee2e6;
    }

    .question-section {
        background-color: #f8f9fa;
    }

    .question-content {
        color: #343a40;
        font-size: 0.95rem;
        line-height: 1.6;
        direction: rtl;
        text-align: right;
        white-space: normal;
        word-wrap: break-word;
        background-color: #f2f7fb;
        border-left: 4px solid #007bff;
        padding: 1rem;
    }

    .question-content strong {
        color: #0056b3;
        font-weight: 600;
        margin-right: 8px;
    }

    .question-content em {
        color: #6c757d;
    }

    /* --- Toggle Section Headers --- */
    .toggle-header-row {
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        border-bottom: 1px solid #ced4da;
    }

    .toggle-header-row td.toggle-section {
        color: #495057;
        font-weight: 500;
        padding: 0.6rem 0.75rem;
        cursor: pointer;
        transition: background-color 0.2s ease-in-out;
    }

    .toggle-header-row td.toggle-section:hover {
        background-color: #e9ecef;
    }

    .toggle-section.toggle-question {
    background-color:rgb(169, 207, 245);  /* Soft blue */
    color: #000000;
}

.toggle-section.toggle-chat {
    background-color:rgb(159, 250, 189);  /* Light green */
    color: #000000;
}

    .chat-section {
        background-color: #ffffff;
    }
    .chat-content {
    }

    /* --- Pagination Styling --- */
    .pagination-container {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
        padding: 1rem;
        background-color: #ffffff;
        border-radius: 0.75rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .pagination {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .page-item {
        margin: 0;
    }

    .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 2.5rem;
        height: 2.5rem;
        padding: 0 0.75rem;
        font-size: 0.95rem;
        font-weight: 500;
        color: #1a237e;
        background-color: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .page-link:hover {
        background-color: #e9f0f7;
        border-color: #1a237e;
        color: #1a237e;
        transform: translateY(-1px);
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, #1a237e, #283593);
        border-color: #1a237e;
        color: #ffffff;
        box-shadow: 0 2px 4px rgba(26, 35, 126, 0.2);
    }

    .page-item.disabled .page-link {
        color: #9e9e9e;
        background-color: #f5f5f5;
        border-color: #e0e0e0;
        cursor: not-allowed;
    }

    .page-item:first-child .page-link,
    .page-item:last-child .page-link {
        padding: 0 1rem;
    }

    .page-item:first-child .page-link {
        border-top-left-radius: 0.5rem;
        border-bottom-left-radius: 0.5rem;
    }

    .page-item:last-child .page-link {
        border-top-right-radius: 0.5rem;
        border-bottom-right-radius: 0.5rem;
    }

    /* Pagination Info */
    .pagination-info {
        display: flex;
        align-items: center;
        margin-left: 1rem;
        padding: 0.5rem 1rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        font-size: 0.9rem;
        color: #455a64;
    }

    /* Responsive Pagination */
    @media (max-width: 768px) {
        .pagination-container {
            padding: 0.75rem;
        }

        .page-link {
            min-width: 2rem;
            height: 2rem;
            padding: 0 0.5rem;
            font-size: 0.85rem;
        }

        .pagination-info {
            display: none;
        }
    }

    /* Filter Section Styling */
    .card-title {
        font-size: 1.5rem;
        font-weight: 600;
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 0.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        margin-bottom: 0.5rem;
        display: block;
    }

    .custom-select, .form-control {
        border-radius: 0.25rem;
        transition: all 0.2s ease-in-out;
    }

    .custom-select:focus, .form-control:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        border-color: #80bdff;
    }

    /* Filter Buttons Styling */
    .apply-filters-button {
        display: inline-block;
        padding: 0.5rem 1rem;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        color: #495057;
        text-decoration: none;
        transition: all 0.2s ease-in-out;
        font-weight: 500;
    }

    .apply-filters-button:hover {
        background-color: #e9ecef;
        color: #212529;
        text-decoration: none;
    }

    .apply-filters-button-active {
        background-color: #0056b3;
        color: white;
        border-color: #0056b3;
    }

    .apply-filters-button-active:hover {
        background-color: #004494;
        color: white;
    }

    /* Border colors for form elements */
    .border-success {
        border-color: #2e7d32 !important;
    }

    .border-indigo {
        border-color: #4b0082 !important;
    }

    .border-warning {
        border-color: #e65100 !important;
    }

    /* Background colors for form elements */
    .bg-light {
        background-color: #f8f9fa !important;
    }

    /* Updated Pagination Styles */
    .pagination-wrapper {
        background: #ffffff;
        padding: 1rem;
        border-radius: 0.75rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin: 2rem 0;
    }

    .pagination {
        margin: 0;
        padding: 0;
        list-style: none;
        display: flex;
        gap: 0.5rem;
    }

    .page-item {
        margin: 0;
    }

    .page-link {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 2.5rem;
        height: 2.5rem;
        padding: 0 0.75rem;
        font-size: 0.95rem;
        font-weight: 500;
        color: #1a237e;
        background-color: #ffffff;
        border: 1px solid #e0e0e0;
        border-radius: 0.5rem;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .page-link:hover:not(.disabled) {
        background-color: #e9f0f7;
        border-color: #1a237e;
        color: #1a237e;
        transform: translateY(-1px);
    }

    .page-item.active .page-link {
        background: linear-gradient(135deg, #1a237e, #283593);
        border-color: #1a237e;
        color: #ffffff;
        box-shadow: 0 2px 4px rgba(26, 35, 126, 0.2);
    }

    .page-item.disabled .page-link {
        color: #9e9e9e;
        background-color: #f5f5f5;
        border-color: #e0e0e0;
        cursor: not-allowed;
        pointer-events: none;
    }

    .pagination-info {
        color: #455a64;
        font-size: 0.95rem;
        padding: 0.5rem 1rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
    }

    @media (max-width: 768px) {
        .pagination-wrapper {
            flex-direction: column;
            gap: 1rem;
        }

        .pagination {
            justify-content: center;
        }

        .page-link {
            min-width: 2rem;
            height: 2rem;
            padding: 0 0.5rem;
            font-size: 0.85rem;
        }

        .pagination-info {
            text-align: center;
            width: 100%;
        }
    }

</style>

<!-- Add a simple inline script that will work reliably -->
<script>
// Define the initialization function so we can call it whenever needed
function initializeToggles() {
    console.log('Initializing toggle handlers');
    // Add click handlers to all toggle sections
    document.querySelectorAll('[id^="toggle-"]').forEach(function(element) {
        // Remove existing event listeners first to prevent duplicates
        element.removeEventListener('click', handleToggleClick);
        // Add new event listener
        element.addEventListener('click', handleToggleClick);
    });
}

// Extract the click handler function to make it reusable
function handleToggleClick() {
    var section = this.getAttribute('data-section');
    var fatwaId = this.getAttribute('data-fatwa-id');

    var sectionElement = document.getElementById(section + '-section-' + fatwaId);
    var icon = this.querySelector('i');

    if (!sectionElement) {
        console.error('Section element not found:', section + '-section-' + fatwaId);
        return;
    }

    // Toggle display immediately
    if (sectionElement.style.display === 'none') {
        sectionElement.style.display = 'table-row';
        if (icon) {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
        }
    } else {
        sectionElement.style.display = 'none';
        if (icon) {
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
        }
    }

    // Update Livewire state in the background
    try {
        var component = Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id'));
        if (section === 'question') {
            component.call('toggleQuestion', fatwaId);
        } else {
            component.call('toggleChat', fatwaId);
        }
    } catch (e) {
        console.error('Error updating Livewire state:', e);
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeToggles();

    // Re-initialize whenever Livewire updates the DOM
    document.addEventListener('livewire:navigating', function() {
        console.log('Livewire navigating');
    });

    document.addEventListener('livewire:navigated', function() {
        console.log('Livewire navigated - reinitializing toggles');
        setTimeout(initializeToggles, 100); // Small delay to ensure DOM is updated
    });

    document.addEventListener('livewire:update', function() {
        console.log('Livewire updated - reinitializing toggles');
        setTimeout(initializeToggles, 100);
    });
});

// For backwards compatibility with Livewire v2
if (window.Livewire) {
    window.Livewire.hook('message.processed', function() {
        console.log('Livewire message processed - reinitializing toggles');
        setTimeout(initializeToggles, 100);
    });
}
</script>

<?php if (isset($component)) { $__componentOriginalf30276552b63aa6c9559a1667ce359f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf30276552b63aa6c9559a1667ce359f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footers.auth','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('footers.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $attributes = $__attributesOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $component = $__componentOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__componentOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>

</main>
</div>

<?php $__env->startPush('scripts'); ?>
// ... existing pushed scripts ...
<?php $__env->stopPush(); ?>

<?php echo $__env->yieldPushContent('scripts'); ?>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/mahlenazar-fatawa.blade.php ENDPATH**/ ?>