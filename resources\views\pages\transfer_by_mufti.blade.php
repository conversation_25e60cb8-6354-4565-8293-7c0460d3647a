<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="mutakhassis"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->


        <x-navbars.navs.auth titlePage="mutakhassis"></x-navbars.navs.auth>
        <style>
        table{
            font-family: <PERSON><PERSON> Nastaleeq;
            
          }
          </style>
        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
            <!-- Link to create Fatawa entry -->
            <div class="container">
                
                <!-- Link to view Fatawa entries -->
                <meta name="csrf-token" content="{{ csrf_token() }}">
                <a href="{{ route('create', ['checker' => 'mufti_ali_asghar', 'transfer_by' => $checker]) }}" class="btn btn-primary">Send Fataw To Mufti Sahib</a>

                     <!-- You can add more content here as needed -->
                     <div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
                        <div class="card mb-4" style="background-color: #FFFFCC;"> <!-- Added "mb-4" for margin at the bottom -->
                            <div class="card-body">
                                <h2 class="card-title">Recived Fataw Folder For Checking</h2>
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Darulifta</th>
                                            <th>Folder For Checking</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($summaryReport as $item)
                                            <tr>
                                                <td>{{ $item['Darulifta'] }}</td>
                                                <td>{{ $item['Mail Recived'] }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
                        <div class="card z-index-2 mb-4" style="background-color: #FFDAB9;">
                        @php
                        $backgroundColors = ['custom-bg-light-red', 'custom-bg-light-blue', 'custom-bg-light-green', 'custom-bg-light-yellow'];
                        
                        
                        $colorIndex = 0;
                        @endphp
                        
                        @foreach ($mailfolderDates as $mailfolderDate)
                        <div class="card mb-4 {{ $backgroundColors[$colorIndex] }}">
                            <div class="card-body">
                                
                    
                                @if (empty($dataByDaruliftaName))
                                    <p>No data found for the selected criteria.</p>
                                @else
                                
                                
                                
                                <h3>Recived Folder Date {{ $mailfolderDate }}</h3>
                            
                                @foreach ($daruliftaNames as $daruliftaName)
                                    <h4>{{ $daruliftaName }}</h4>
                            
                                    @if (isset($dataByDaruliftaName[$daruliftaName][$mailfolderDate]))
                                        <div class="table-responsive">
                                            <table class="table text-center">
                    
                                                <thead>
                                                    <tr>
                                                        <th>S.No</th>
                                                        <th>File Code</th>
                                                        <th>Sender</th>
                                                        <th>Fatwa Type</th>
                                                        <th>Sending Time & Date</th>
                                                        <th>Category</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                            $serialNumber = 1; // Initialize the serial number
                                            
                                            @endphp
                                                    @foreach ($dataByDaruliftaName[$daruliftaName][$mailfolderDate] as $item)
                                                        <tr>
                                                            <td>{{ $serialNumber++ }}</td>
                                                            <td>{{ $item->id }}</td>
                                                            <td>{{ $item->file_code }}</td>
                                                            <td>{{ $item->sender }}</td>
                                                            <td>{{ $item->ftype }}</td>
                                                            <td>{{ $item->mail_recived_date }}</td>
                                                            <td>{{ $item->category }}</td>
                                                            <td>{{ $item->mail_folder_date}}</td>
                                                            <td>{{$item->darulifta_name}}</td>
                                                            <td>{{$item->checker}}</td>
                                                            <td>{{$item->file_name}}</td>
                                                            <td style="width: 15%;">
                                                            <select class="mujeeb-select" data-fatwa-id="{{ $item->id }}"
                                                                                        data-mail-folder-date="{{ $item->mail_folder_date }}"
                                                                                        data-darulifta-name="{{ $item->darulifta_name }}"
                                                                                        data-checker="{{ $item->checker }}"
                                                                                        data-checked-folder="{{ $item->checked_folder }}"
                                                                                        data-transfer-to="{{ $item->by_mufti }}"
                                                                                        data-file-name="{{ $item->file_name }}">
                                                                <option value="">Transfer To</option>
                                                                <option value="to_mujeeb">To Mujeeb</option>
                                                                
                                                            </select>
                                                            </td>
                                                            <td>
                                <input type="file" class="upload-file" data-mail-folder-date="{{ $item->mail_folder_date }}" 
                                       data-darulifta-name="{{ $item->darulifta_name }}" 
                                       data-checker="{{ $item->checker }}"
                                       data-checked-folder="{{ $item->checked_folder }}"
                                       data-transfer-to="{{ $item->by_mufti }}"
                                       >
                                       
                                <button class="upload-button" data-id="{{ $item->id }}">Upload</button>
                            </td>
                    
                    
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <p>No data available for this combination.</p>
                                    @endif
                                @endforeach
                            
                                @endif
                            </div>
                        </div>
                        @php
                    $colorIndex = ($colorIndex + 1) % count($backgroundColors);
                    @endphp
                        @endforeach
                    </div>
                    </div>
                 
            </div>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
            <script>
                $(document).ready(function () {
                  $('#myTable').DataTable();
            
                });
              </script>
              <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

              <script>
                 document.addEventListener("DOMContentLoaded", function() {
    // Your JavaScript code here
    const mujeebSelects = document.querySelectorAll('.mujeeb-select');

    mujeebSelects.forEach(select => {
        select.addEventListener('change', async function() {
            const fatwaId = this.getAttribute('data-fatwa-id');
            const selectedMujeeb = this.value;
            const mailFolderDate = this.getAttribute('data-mail-folder-date');
            const daruliftaName = this.getAttribute('data-darulifta-name');
            const checkedFolder = this.getAttribute('data-checked-folder');
            const checker = this.getAttribute('data-checker');
            const fileName = this.getAttribute('data-file-name');
            const transferTo = this.getAttribute('data-transfer-to');
            

            try {
                // Include the CSRF token in the request headers
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                const response = await axios.post(`/transfer-mujeeb/${fatwaId}`, {
                    selectedMujeeb,
                    mailFolderDate,
                    daruliftaName,
                    checker,
                    checkedFolder,
                    transferTo,
                    fileName
                }, {
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                    },
                });

                if (response.data.success) {
                    // You can update the view to reflect the change if needed.
                    // For example, show the selected Mujeeb's name next to the dropdown.
                }
            } catch (error) {
                console.error(error);
            }
        });
    });

    // Add file upload functionality
    const uploadButtons = document.querySelectorAll('.upload-button');

    uploadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.dataset.id;
            const fileInput = this.previousElementSibling;
            const file = fileInput.files[0];
            const mailFolderDate = fileInput.dataset.mailFolderDate;
            const daruliftaName = fileInput.dataset.daruliftaName;
            const checker = fileInput.dataset.checker;
            const transferTo = fileInput.dataset.transferTo;
            const checkedFolder = fileInput.dataset.checkedFolder;

            if (file) {
                const formData = new FormData();
                formData.append('file', file);
                formData.append('id', id);
                formData.append('mail_folder_date', mailFolderDate);
                formData.append('darulifta_name', daruliftaName);
                formData.append('checker', checker);
                formData.append('by_mufti', transferTo);
                formData.append('checked_folder', checkedFolder);

                fetch('/update-fatwa', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                }).then(response => response.json())
                  .then(data => {
                      if (data.success) {
                          alert('File uploaded successfully');
                      } else {
                          alert('File upload failed');
                      }
                  }).catch(error => {
                      console.error('Error:', error);
                      alert('An error occurred while uploading the file');
                  });
            } else {
                alert('Please select a file to upload');
            }
        });
    });
});    
     </script>
            <x-footers.auth></x-footers.auth>
        </div>
    </main>
    <x-plugins></x-plugins>

</x-layout>
