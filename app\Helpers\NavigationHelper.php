<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Gate;

class NavigationHelper
{
    /**
     * Check if user can access a specific navigation item.
     */
    public static function canAccess($permission, $user = null)
    {
        $user = $user ?: auth()->user();
        
        if (!$user) {
            return false;
        }

        return Gate::forUser($user)->allows($permission);
    }

    /**
     * Get navigation items based on user roles.
     */
    public static function getNavigationItems($user = null)
    {
        $user = $user ?: auth()->user();
        
        if (!$user) {
            return [];
        }

        $items = [];

        // System Management Section (Admin/Superior)
        if (self::canAccess('manage-users', $user) || self::canAccess('manage-assistants', $user)) {
            $systemItems = [];

            // Admin-only items
            if (self::canAccess('manage-users', $user)) {
                $systemItems[] = [
                    'name' => 'Department Management',
                    'route' => 'departments.index',
                    'icon' => 'business',
                    'permission' => 'manage-departments',
                ];

                $systemItems[] = [
                    'name' => 'Role Management',
                    'route' => 'role-management',
                    'icon' => 'admin_panel_settings',
                    'permission' => 'manage-users',
                ];

                $systemItems[] = [
                    'name' => 'Team Management',
                    'route' => 'supervisor-assistant-mapping',
                    'icon' => 'group',
                    'permission' => 'assign-supervisors',
                ];

                $systemItems[] = [
                    'name' => 'Limit Control',
                    'route' => 'mahl-e-nazar-limits',
                    'icon' => 'shield',
                    'permission' => 'manage-mahl-e-nazar-limits',
                ];
            }

            // Admin and Superior items
            $systemItems[] = [
                'name' => 'Task Management',
                'route' => 'workflow-tasks.index',
                'icon' => 'task',
                'permission' => 'assign-tasks',
            ];

            $systemItems[] = [
                'name' => 'Performance Reports',
                'route' => 'performance-management',
                'icon' => 'analytics',
                'permission' => 'view-all-performance',
            ];

            if (!empty($systemItems)) {
                $items['System Management'] = $systemItems;
            }
        }

        // User Management Section (Admin only)
        if (self::canAccess('manage-users', $user)) {
            $items['User Management'] = [
                [
                    'name' => 'Darulifta',
                    'route' => 'darulifta',
                    'icon' => 'fas fa-user-circle',
                    'permission' => 'manage-users',
                ],
                [
                    'name' => 'Mujeeb',
                    'route' => 'mujeeb.store',
                    'icon' => 'fas fa-user-circle',
                    'permission' => 'manage-users',
                ],
                [
                    'name' => 'User Profile',
                    'route' => 'user-profile',
                    'icon' => 'fas fa-user-circle',
                    'permission' => 'manage-users',
                ],
                [
                    'name' => 'User Management',
                    'route' => 'user-management',
                    'icon' => 'fas fa-list-ul',
                    'permission' => 'manage-users',
                ],
            ];
        }

        // Personal Section (All authenticated users)
        $personalItems = [
            [
                'name' => 'Daily Performance',
                'route' => 'daily-performance.create',
                'icon' => 'assignment_turned_in',
                'permission' => 'submit-performance',
            ],
            [
                'name' => 'My Performance',
                'route' => 'my-performance',
                'icon' => 'trending_up',
                'permission' => 'submit-performance',
            ],
        ];

        // Add My Tasks for eligible users
        if (self::hasRole($user, ['Superior', 'mujeeb', 'Muawin'])) {
            $personalItems[] = [
                'name' => 'My Tasks',
                'route' => 'my-tasks',
                'icon' => 'checklist',
                'permission' => null, // No specific permission needed
            ];
        }

        $items['Personal'] = $personalItems;

        return $items;
    }

    /**
     * Check if user has specific role(s).
     */
    public static function hasRole($user, $roles)
    {
        if (!$user) {
            return false;
        }

        $roles = is_array($roles) ? $roles : [$roles];
        $userRoles = $user->roles->pluck('name')->toArray();

        return !empty(array_intersect($roles, $userRoles));
    }

    /**
     * Get user's role-based dashboard widgets.
     */
    public static function getDashboardWidgets($user = null)
    {
        $user = $user ?: auth()->user();
        
        if (!$user) {
            return [];
        }

        $widgets = [];

        // Management widgets for Admin/Superior
        if (self::canAccess('assign-tasks', $user) || self::canAccess('view-all-performance', $user)) {
            $widgets[] = [
                'name' => 'Task Management',
                'route' => 'workflow-tasks.index',
                'icon' => 'task',
                'color' => 'info',
                'stats' => [
                    'active_tasks' => 'active-tasks-count',
                    'pending_tasks' => 'pending-tasks-count',
                ],
            ];

            $widgets[] = [
                'name' => 'Performance Reports',
                'route' => 'performance-management',
                'icon' => 'analytics',
                'color' => 'success',
                'stats' => [
                    'todays_reports' => 'todays-reports-count',
                    'pending_reports' => 'pending-reports-count',
                ],
            ];
        }

        // Admin-only widgets
        if (self::canAccess('manage-mahl-e-nazar-limits', $user)) {
            $widgets[] = [
                'name' => 'Limit Control',
                'route' => 'mahl-e-nazar-limits',
                'icon' => 'shield',
                'color' => 'warning',
                'stats' => [
                    'at_limit' => 'at-limit-count',
                    'restricted' => 'restricted-count',
                ],
            ];

            $widgets[] = [
                'name' => 'Team Management',
                'route' => 'supervisor-assistant-mapping',
                'icon' => 'group',
                'color' => 'secondary',
                'stats' => [
                    'superiors' => 'superiors-count',
                    'assistants' => 'assistants-count',
                ],
            ];
        }

        // Personal widget for all users
        $widgets[] = [
            'name' => 'My Performance',
            'route' => 'daily-performance.create',
            'icon' => 'assignment_turned_in',
            'color' => 'primary',
            'stats' => [
                'performance_status' => 'my-performance-status',
                'limit_status' => 'my-limit-status',
            ],
        ];

        return $widgets;
    }

    /**
     * Get notification permissions for user.
     */
    public static function getNotificationPermissions($user = null)
    {
        $user = $user ?: auth()->user();
        
        if (!$user) {
            return [];
        }

        $permissions = [
            'view_personal_notifications' => true,
        ];

        if (self::canAccess('view-team-performance', $user)) {
            $permissions['view_team_notifications'] = true;
        }

        if (self::canAccess('manage-users', $user)) {
            $permissions['view_system_notifications'] = true;
        }

        return $permissions;
    }

    /**
     * Check if route is accessible by user.
     */
    public static function canAccessRoute($routeName, $user = null)
    {
        $user = $user ?: auth()->user();
        
        if (!$user) {
            return false;
        }

        // Route permission mapping
        $routePermissions = [
            'departments.*' => 'manage-departments',
            'role-management' => 'manage-users',
            'supervisor-assistant-mapping' => 'assign-supervisors',
            'mahl-e-nazar-limits' => 'manage-mahl-e-nazar-limits',
            'workflow-tasks.*' => 'assign-tasks',
            'performance-management' => 'view-all-performance',
            'daily-performance.*' => 'submit-performance',
            'my-tasks' => null, // No specific permission
            'my-performance' => 'submit-performance',
        ];

        foreach ($routePermissions as $pattern => $permission) {
            if (fnmatch($pattern, $routeName)) {
                return $permission ? self::canAccess($permission, $user) : true;
            }
        }

        return true; // Default allow for unspecified routes
    }

    /**
     * Get user's accessible menu items with active state.
     */
    public static function getMenuWithActiveState($currentRoute, $user = null)
    {
        $navigation = self::getNavigationItems($user);
        
        foreach ($navigation as $sectionName => &$section) {
            foreach ($section as &$item) {
                $item['active'] = self::isRouteActive($currentRoute, $item['route']);
                $item['accessible'] = $item['permission'] ? self::canAccess($item['permission'], $user) : true;
            }
        }

        return $navigation;
    }

    /**
     * Check if route is currently active.
     */
    private static function isRouteActive($currentRoute, $targetRoute)
    {
        if ($currentRoute === $targetRoute) {
            return true;
        }

        // Check for wildcard matches
        if (str_contains($targetRoute, '*')) {
            return fnmatch($targetRoute, $currentRoute);
        }

        // Check for route prefixes
        $routeParts = explode('.', $currentRoute);
        $targetParts = explode('.', $targetRoute);

        if (count($targetParts) <= count($routeParts)) {
            return array_slice($routeParts, 0, count($targetParts)) === $targetParts;
        }

        return false;
    }
}
