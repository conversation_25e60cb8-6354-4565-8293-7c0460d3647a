<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ViralChatComponent extends Component
{
    public $inputmessage;  // Store message text
    public $messages;      // Store all messages for the chat
    public $file_code;
    public $messageIdBeingEdited; // Track the message being edited
    public $message;       // Store message text for editing

    public function mount()
    {
        // Fetch all messages at component load
        $this->messages = DB::table('viralchat')->latest()->get();
    }

    // Send a new message
    public function sendMessage($file_code)
{
    if (trim($this->inputmessage) !== '') {
        DB::table('viralchat')->insert([
            'ifta_code' => $file_code,
            'user_name' => Auth::user()->name,
            'user_id' => Auth::id(),
            'message' => $this->inputmessage,
            'created_at' => now(),
            'updated_at' => now(),
            'seen_by_user' => false, // Initially, the message hasn't been seen
        ]);

        $this->inputmessage = ''; // Reset the input field
    }

    // Reload the messages to update the view
    $this->messages = DB::table('viralchat')->latest()->get();
}

    // Start editing a message
    public function editMessage($messageId)
    {
        $this->messageIdBeingEdited = $messageId;
        $message = DB::table('viralchat')->where('id', $messageId)->first();

        if ($message) {
            $this->message = $message->message;  // Set message text for editing
        }
    }

    // Cancel editing a message
    public function cancelEdit()
    {
        $this->messageIdBeingEdited = null;
        $this->message = '';  // Clear input
    }

    // Update an edited message
    public function updateMessage()
    {
        if ($this->messageIdBeingEdited) {
            $message = DB::table('viralchat')->where('id', $this->messageIdBeingEdited)->first();

            if ($message && $message->user_id == Auth::id()) {
                DB::table('viralchat')->where('id', $this->messageIdBeingEdited)->update([
                    'message' => $this->message,
                    'updated_at' => now(),
                ]);

                // Clear editing state and reload messages
                $this->messageIdBeingEdited = null;
                $this->message = '';
            }
        }
    }

    // Delete a message
    public function deleteMessage($messageId)
    {
        $message = DB::table('viralchat')->where('id', $messageId)->first();

        if ($message && $message->user_id == Auth::id()) {
            DB::table('viralchat')->where('id', $messageId)->delete();

            // Reload messages
        }
    }

    public function render()
    {
        $this->messages = DB::table('viralchat')->latest()->get();

        // Mark the message as seen if the user is viewing it
        foreach ($this->messages as $message) {
            if ($message->user_id != Auth::id() && !$message->seen_by_user) {
                DB::table('viralchat')->where('id', $message->id)->update([
                    'seen_by_user' => true,
                ]);
            }
        }
        return view('livewire.viral-chat-component');
    }
}
