
<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8">
    <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
    <link rel="icon" href="../../assets/img/favicon.png" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Creative Tim">
    <title>
      Kanban | Material Dashboard 2 FREE Laravel by Creative Tim & UPDIVISION
    </title>
    <link rel="canonical" href="https://www.creative-tim.com/learning-lab/bootstrap/colors/material-dashboard" />

    <meta name="keywords" content="creative tim, updivision, html dashboard, laravel, material, html css dashboard laravel, material dashboard laravel, laravel material dashboard, material admin, laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, material dashboard bootstrap 5 laravel, frontend, responsive bootstrap 5 dashboard, material dashboard, material laravel bootstrap 5 dashboard" />
    <meta name="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="name" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta itemprop="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta name="twitter:card" content="product" />
    <meta name="twitter:site" content="@creativetim" />
    <meta name="twitter:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta name="twitter:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta name="twitter:creator" content="@creativetim" />
    <meta name="twitter:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="fb:app_id" content="655968634437471" />
    <meta property="og:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta property="og:type" content="article" />
    <meta property="og:url" content="https://www.creative-tim.com/live/material-dashboard-laravel" />
    <meta property="og:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="og:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta property="og:site_name" content="Creative Tim" />


    <link rel="stylesheet" href="../../assets/css/nucleo-icons.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/nextjs-material-dashboard-pro.min.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/material-dashboard.min.css" type="text/css">
    <link rel="stylesheet" href="../../assets/css/demo.css" type="text/css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Roboto+Slab:400,700" rel="stylesheet">

    <link href="../../assets/css/nucleo-icons.css" rel="stylesheet">

    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
    <!-- Anti-flicker snippet (recommended)  -->
    <style>
      .async-hide {
        opacity: 0 !important
      }
    </style>
    <script>
      (function(a, s, y, n, c, h, i, d, e) {
        s.className += ' ' + y;
        h.start = 1 * new Date;
        h.end = i = function() {
          s.className = s.className.replace(RegExp(' ?' + y), '')
        };
        (a[n] = a[n] || []).hide = h;
        setTimeout(function() {
          i();
          h.end = null
        }, c);
        h.timeout = c;
      })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {
        'GTM-K9BGS8K': true
      });
    </script>
    <!-- Analytics-Optimize Snippet -->
    <script>
      (function(i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function() {
          (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
          m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
      })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
      ga('create', 'UA-46172202-22', 'auto', {
        allowLinker: true
      });
      ga('set', 'anonymizeIp', true);
      ga('require', 'GTM-K9BGS8K');
      ga('require', 'displayfeatures');
      ga('require', 'linker');
      ga('linker:autoLink', ["2checkout.com", "avangate.com"]);
    </script>
    <!-- end Analytics-Optimize Snippet -->
    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js'
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-NKDMSK6');
    </script>
    <!-- End Google Tag Manager -->
    <!-- This is for docs typography and layout -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="../../assets/css/docs.css" rel="stylesheet" />
  </head>
  <body class="docs">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NKDMSK6" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <header class="ct-docs-navbar">
      <a class="ct-docs-navbar-brand" href="javascript:void(0)" aria-label="Bootstrap">
        </a><a href="https://www.creative-tim.com/" class="ct-docs-navbar-text" target="_blank">
          Creative Tim
        </a>
        <div class="ct-docs-navbar-border"></div>
        <a href="../../documentation/getting-started/installation.html" class="ct-docs-navbar-text">
          Docs
        </a>

      <ul class="ct-docs-navbar-nav-left">
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Live Preview</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownPreview">
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-pro-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Support</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownSupport">
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/material-dashboard-laravel/issues" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/ct-material-dashboard-pro-laravel/issues" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
      </ul>
      <ul class="ct-docs-navbar-nav-right">
        <li class="ct-docs-navbar-nav-item">
          <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/material-dashboard-laravel" target="_blank">Download Free</a>
        </li>
      </ul>
      <a href="https://www.creative-tim.com/product/material-dashboard-pro-laravel" target="_blank" class="ct-docs-btn-upgrade">
        <span class="ct-docs-btn-inner--icon">
          <i class="fas fa-download mr-2" aria-hidden="true"></i>
        </span>
        <span class="ct-docs-navbar-nav-link-inner--text">Upgrade to PRO</span>
      </a>
      <button class="ct-docs-navbar-toggler" type="button">
        <span class="ct-docs-navbar-toggler-icon"></span>
      </button>
    </header>
    <div class="ct-docs-main-container">
      <div class="ct-docs-main-content-row">
        <div class="ct-docs-sidebar-col">
          <nav class="ct-docs-sidebar-collapse-links">
            <div class="ct-docs-sidebar-product">
              <div class="ct-docs-sidebar-product-image">
                <img src="../../assets/img/bootstrap.png">
              </div>
              <p class="ct-docs-sidebar-product-text">Material Dashboard</p>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-active-40 text-white"></i>
                  </div>
                </div>
                Getting started
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/getting-started/overview.html">
                    Overview
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/license.html">
                    License
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/installation.html">
                    Installation
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/build-tools.html">
                    Build Tools
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/bootstrap.html">
                    What is Bootstrap
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
                <a class="ct-docs-toc-link" href="javascript:void(0)">
                  <div class="d-inline-block">
                    <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                      <i class="ni ni-folder-17 text-white"></i>
                    </div>
                  </div>
                  Laravel
                </a>
                <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                  <li class="">
                    <a href="../../documentation/laravel/login.html">
                      Login
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/sign-up.html">
                      Sign Up
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/forgot-password.html">
                      Forgot Password
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/user-profile.html">
                      User Profile
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/user-management.html">
                      User Management
                      <span class="ct-docs-sidenav-pro-badge">Pro</span>
                    </a>
                  </li>
                </ul>
              </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-folder-17 text-white"></i>
                  </div>
                </div>
                Foundation
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class=" ">
                  <a href="../../documentation/foundation/colors.html">
                    Colors
                  </a>
                </li>
                <li class=" ">
                  <a href="../../documentation/foundation/grid.html">
                    Grid
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/typography.html">
                    Typography
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/icons.html">
                    Icons
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/utilities.html">
                    Utilities
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-app text-white"></i>
                  </div>
                </div>
                Components
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/components/alerts.html">
                    Alerts
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/badge.html">
                    Badge
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/buttons.html">
                    Buttons
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/social-buttons.html">
                    Social Buttons
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/cards.html">
                    Cards
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/carousel.html">
                    Carousel
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/collapse.html">
                    Collapse
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/dropdowns.html">
                    Dropdowns
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/forms.html">
                    Forms
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/input-group.html">
                    Input Group
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/list-group.html">
                    List Group
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/modal.html">
                    Modal
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/navs.html">
                    Navs
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/navbar.html">
                    Navbar
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/pagination.html">
                    Pagination
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/popovers.html">
                    Popovers
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/progress.html">
                    Progress
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/spinners.html">
                    Spinners
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/tables.html">
                    Tables
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/tooltips.html">
                    Tooltips
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-settings text-white"></i>
                  </div>
                </div>
                Plugins
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/plugins/countUpJs.html">
                    CountUp JS
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/charts.html">
                    Charts
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/datepicker.html">
                    Datepicker
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/fullcalendar.html">
                    Fullcalendar
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/sliders.html">
                    Sliders
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/choices.html">
                    Choices
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/dropzone.html">
                    Dropzone
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/datatables.html">
                    Datatables
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="ct-docs-nav-sidenav-active">
                  <a href="../../documentation/plugins/kanban.html">
                    Kanban
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/photo-swipe.html">
                    Photo Swipe
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/quill.html">
                    Quill
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/sweet-alerts.html">
                    Sweet Alerts
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/wizard.html">
                    Wizard
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
              </ul>
            </div>
          </nav>
        </div>
        <div class="ct-docs-toc-col">
          <ul class="section-nav">
            <li class="toc-entry toc-h2"><a href="#usage">Usage</a>
              <ul>
                <li class="toc-entry toc-h3"><a href="#js">JS</a></li>
                <li class="toc-entry toc-h3"><a href="#example">Example</a>
                  <ul>
                    <li class="toc-entry toc-h5"><a href="#choose-your-new-board-name">Choose your new Board Name</a></li>
                    <li class="toc-entry toc-h5"><a href="#task-details">Task details</a></li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <main class="ct-docs-content-col" role="main">
          <div class="ct-docs-page-title">
            <h1 class="ct-docs-page-h1-title" id="content">
              Bootstrap Kanban
            </h1>
            <span class="ct-docs-page-title-pro-line"> - </span>
            <div class="ct-docs-page-title-pro-bage">Pro Component</div>
            <div class="avatar-group mt-3">
            </div>
          </div>
          <p class="ct-docs-page-title-lead">Pure agnostic Javascript plugin for Kanban boards</p>
          <hr class="ct-docs-hr">
          <h2 id="usage">Usage</h2>
          <p>Our kanban is built using jKanban. You cand <a href="http://www.riccardotartaglia.it/jkanban/" rel="nofollow" target="_blank">www.riccardotartaglia.it/jkanban/</a>.</p>
          <h3 id="js">JS</h3>
          <p>In order to use this plugin on your page you will need to include the following scripts in the “Optional JS” area from the page’s footer:</p>
          <div class="position-relative">
            <div class="bd-clipboard"><span class="btn-clipboard" title="" data-bs-original-title="Copy to clipboard">Copy</span></div><figure class="highlight"><pre class=" language-html"><code class=" language-html" data-lang="html"><span class="token comment">&lt;!-- Kanban scripts --&gt;</span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>../../assets/js/plugins/dragula/dragula.min.js<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token script"></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>../../assets/js/plugins/jkanban/jkanban.js<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token script"></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span></code></pre>
            </figure>
          </div>
          <h3 id="example">Example</h3>
          <div class="mt-3 kanban-container">
            <div class="py-2 min-vh-100 d-inline-flex" style="overflow-x: auto">
              <div id="myKanban"><div class="kanban-container" style="width: 1880px;"><div data-id="_backlog" data-order="1" class="kanban-board" style="width: 450px; margin-left: 10px; margin-right: 10px;"><header class="kanban-board-header"><div class="kanban-title-board">Backlog</div><button class="kanban-title-button btn btn-sm btn-white">+</button></header><main class="kanban-drag"><div class="kanban-item border-radius-md" data-eid="_task_1_title_id" data-class="border-radius-md">Click me to change title</div><div class="kanban-item border-radius-md" data-eid="_task_2_title_id" data-class="border-radius-md">Drag me to 'In progress' section</div><div class="kanban-item border-radius-md is-moving" data-eid="_task_do_something_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><img src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/office-dark.jpg" class="w-100"><span class="mt-3 badge badge-sm bg-gradient-primary">Pending</span><p class="text mt-2">Website Design: New cards for blog section and profile details</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">3</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a><a href="#" class="avatar avatar-xs rounded-circle me-2" data-toggle="tooltip" data-original-title="Audrey Love"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class="rounded-circle"></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class="rounded-circle"></a></div></div></div><div class="kanban-item border-radius-md is-moving" data-eid="_task_do_something_4_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><img src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/meeting.jpg" class="w-100"><span class="mt-3 badge badge-sm bg-gradient-info">Updates</span><p class="text mt-2">Vue 3 Updates</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">9</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a><a href="#" class="avatar avatar-xs rounded-circle me-2" data-toggle="tooltip" data-original-title="Audrey Love"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class="rounded-circle"></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class="rounded-circle"></a></div></div></div><div class="kanban-item border-radius-md" data-eid="_task_ok_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><span class="mt-2 badge badge-sm bg-gradient-success">Done</span><p class="text mt-2">Schedule winter campaign</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">2</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Laurence"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class="rounded-circle"></a></div></div></div></main><footer></footer></div><div data-id="_progress" data-order="2" class="kanban-board" style="width: 450px; margin-left: 10px; margin-right: 10px;"><header class="kanban-board-header"><div class="kanban-title-board">In progress</div><button class="kanban-title-button btn btn-sm btn-white">+</button></header><main class="kanban-drag"><div class="kanban-item border-radius-md" data-eid="_task_3_title_id" data-class="border-radius-md"><span class="mt-2 badge badge-sm bg-gradient-warning">Errors</span><p class="text mt-2">Fix Firefox errors</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">11</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class=""></a></div></div></div><div class="kanban-item border-radius-md" data-eid="_task_4_title_id" data-class="border-radius-md"><span class="mt-2 badge badge-sm bg-gradient-info">Updates</span><p class="text mt-2">Argon Dashboard PRO - Angular 11</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">3</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-5.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class=""></a></div></div></div></main><footer></footer></div><div data-id="_working" data-order="3" class="kanban-board" style="width: 450px; margin-left: 10px; margin-right: 10px;"><header class="kanban-board-header"><div class="kanban-title-board">In review</div><button class="kanban-title-button btn btn-sm btn-white">+</button></header><main class="kanban-drag"><div class="kanban-item border-radius-md" data-eid="_task_do_something_2_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><span class="mt-2 badge badge-sm bg-gradient-warning">In Testing</span><p class="text mt-2">Responsive Changes</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">11</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class=""></a></div></div></div><div class="kanban-item border-radius-md" data-eid="_task_run_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><span class="mt-2 badge badge-sm bg-gradient-success">In review</span><p class="text mt-2 mb-1">Change images dimension</p><div class="col"><div class="progress progressm mb-3 w5"><div class="progress-bar bg-gradient-success" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%;"></div></div></div><div class="d-flex"><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class=""></a></div></div></div><div class="kanban-item border-radius-md" data-eid="_task_do_something_3_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><span class="mt-2 badge badge-sm bg-gradient-info">In Review</span><p class="text mt-2 mb-1">Update Links</p><div class="col"><div class="progress progressm mb-3 w5"><div class="progress-bar bg-gradient-info" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%;"></div></div></div><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">6</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-5.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Mike Alis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a></div></div></div></main><footer></footer></div><div data-id="_done" data-order="4" class="kanban-board" style="width: 450px; margin-left: 10px; margin-right: 10px;"><header class="kanban-board-header"><div class="kanban-title-board">Done</div><button class="kanban-title-button btn btn-sm btn-white">+</button></header><main class="kanban-drag"><div class="kanban-item border-radius-md is-moving" data-eid="_task_all_right_id" data-assignee="Done Joe" data-description="This task's description is for something, but not for anything" data-class="border-radius-md"><img src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/home-decor-1.jpg" class="w-100"><span class="mt-3 badge badge-sm bg-gradient-success">Done</span><p class="text mt-2">Redesign for the home page</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm" aria-hidden="true"></i><span class="text-sm">8</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-5.jpg" class=""></a><a href="#" class="avatar avatar-xs rounded-circle me-2" data-toggle="tooltip" data-original-title="Audrey Love"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class="rounded-circle"></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class="rounded-circle"></a></div></div></div></main><footer></footer></div></div></div>
            </div>
          </div>
          <div class="modal fade" id="new-board-modal" role="dialog">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="h5 modal-title">Choose your new Board Name</h5>
                  <button type="button" class="btn close pe-1" data-dismiss="modal" data-target="#new-board-modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                  </button>
                </div>
                <div class="pt-4 modal-body">
                  <div class="mb-4 input-group">
                    <span class="input-group-text">
                      <i class="far fa-edit" aria-hidden="true"></i>
                    </span>
                    <input class="form-control" placeholder="Board Name" type="text" id="jkanban-new-board-name" onfocus="focused(this)" onfocusout="defocused(this)">
                  </div>
                  <div class="text-end">
                    <button class="m-1 btn btn-primary" id="jkanban-add-new-board" data-toggle="modal" data-target="#new-board-modal">
                      Save changes
                    </button>
                    <button class="m-1 btn btn-secondary" data-dismiss="modal" data-target="#new-board-modal">
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="hidden opacity-50 fixed inset-0 z-40 bg-black" id="new-board-modal-backdrop"></div>
          <div class="modal fade" id="jkanban-info-modal" style="display: none" tabindex="-1" role="dialog">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="h5 modal-title">Task details</h5>
                  <button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                  </button>
                </div>
                <div class="pt-4 modal-body">
                  <input id="jkanban-task-id" class="d-none">
                  <div class="mb-4 input-group">
                    <span class="input-group-text">
                      <i class="far fa-edit" aria-hidden="true"></i>
                    </span>
                    <input class="form-control" placeholder="Task Title" type="text" id="jkanban-task-title" onfocus="focused(this)" onfocusout="defocused(this)">
                  </div>
                  <div class="mb-4 input-group">
                    <span class="input-group-text">
                      <i class="fas fa-user" aria-hidden="true"></i>
                    </span>
                    <input class="form-control" placeholder="Task Assignee" type="text" id="jkanban-task-assignee" onfocus="focused(this)" onfocusout="defocused(this)">
                  </div>
                  <div class="form-group">
                    <textarea class="form-control" placeholder="Task Description" id="jkanban-task-description" type="textarea" rows="4"></textarea>
                  </div>
                  <div class="alert alert-success d-none">Changes saved!</div>
                  <div class="text-end">
                    <button class="m-1 btn btn-primary" id="jkanban-update-task" data-toggle="modal" data-target="#jkanban-info-modal">
                      Save changes
                    </button>
                    <button class="m-1 btn btn-secondary" data-dismiss="modal" data-target="#jkanban-info-modal">
                      Close
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="hidden opacity-50 fixed inset-0 z-40 bg-black" id="jkanban-info-modal-backdrop"></div>
          <div class="position-relative">
            <div class="bd-clipboard"><span class="btn-clipboard" title="" data-bs-original-title="Copy to clipboard">Copy</span></div><figure class="highlight"><pre class=" language-html"><code class=" language-html" data-lang="html"> <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>mt-3 kanban-container<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>py-2 min-vh-100 d-inline-flex<span class="token punctuation">"</span></span><span class="token style-attr language-css"><span class="token attr-name"> <span class="token attr-name">style</span></span><span class="token punctuation">="</span><span class="token attr-value"><span class="token property">overflow-x</span><span class="token punctuation">:</span> auto</span><span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>myKanban<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal fade<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>new-board-modal<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>dialog<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal-dialog<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal-content<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal-header<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h5</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>h5 modal-title<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Choose your new Board Name<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h5</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn close pe-1<span class="token punctuation">"</span></span> <span class="token attr-name">data-dismiss</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">data-target</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#new-board-modal<span class="token punctuation">"</span></span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Close<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">aria-hidden</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>×<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pt-4 modal-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>mb-4 input-group<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>input-group-text<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>i</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>far fa-edit<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>i</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>form-control<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Board Name<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-new-board-name<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>text-end<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>m-1 btn btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-add-new-board<span class="token punctuation">"</span></span> <span class="token attr-name">data-toggle</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">data-target</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#new-board-modal<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              Save changes
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>m-1 btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-dismiss</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">data-target</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#new-board-modal<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              Close
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>hidden opacity-50 fixed inset-0 z-40 bg-black<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>new-board-modal-backdrop<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal fade<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-info-modal<span class="token punctuation">"</span></span><span class="token style-attr language-css"><span class="token attr-name"> <span class="token attr-name">style</span></span><span class="token punctuation">="</span><span class="token attr-value"><span class="token property">display</span><span class="token punctuation">:</span> none</span><span class="token punctuation">"</span></span> <span class="token attr-name">tabindex</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>-1<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>dialog<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal-dialog<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal-content<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal-header<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>h5</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>h5 modal-title<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Task details<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>h5</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn-close text-dark<span class="token punctuation">"</span></span> <span class="token attr-name">data-bs-dismiss</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">aria-label</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Close<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">aria-hidden</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token entity" title="×">&amp;times;</span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>pt-4 modal-body<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-task-id<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>d-none<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>mb-4 input-group<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>input-group-text<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>i</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>far fa-edit<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>i</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>form-control<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Task Title<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-task-title<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>mb-4 input-group<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>input-group-text<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>i</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>fas fa-user<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>i</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>input</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>form-control<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Task Assignee<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>text<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-task-assignee<span class="token punctuation">"</span></span> <span class="token punctuation">/&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>form-group<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>textarea</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>form-control<span class="token punctuation">"</span></span> <span class="token attr-name">placeholder</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>Task Description<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-task-description<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>textarea<span class="token punctuation">"</span></span> <span class="token attr-name">rows</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>4<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>textarea</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>alert alert-success d-none<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Changes saved!<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>text-end<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>m-1 btn btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-update-task<span class="token punctuation">"</span></span> <span class="token attr-name">data-toggle</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">data-target</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#jkanban-info-modal<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              Save changes
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>m-1 btn btn-secondary<span class="token punctuation">"</span></span> <span class="token attr-name">data-dismiss</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>modal<span class="token punctuation">"</span></span> <span class="token attr-name">data-target</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#jkanban-info-modal<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
              Close
            <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
          <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
        <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
      <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
    <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>div</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>hidden opacity-50 fixed inset-0 z-40 bg-black<span class="token punctuation">"</span></span> <span class="token attr-name">id</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>jkanban-info-modal-backdrop<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>div</span><span class="token punctuation">&gt;</span></span>

 </code></pre>
            </figure>
          </div>
        </main>
      </div>
      <div class="ct-docs-main-footer-row">
        <div class="ct-docs-main-footer-blank-col">
        </div>
        <div class="ct-docs-main-footer-col">
          <footer class="ct-docs-footer">
            <div class="ct-docs-footer-inner-row">
              <div class="ct-docs-footer-col">
                <div class="ct-docs-footer-copyright">
                  © <script>
                    document.write(new Date().getFullYear())
                  </script> <a href="https://creative-tim.com" class="ct-docs-footer-copyright-author" target="_blank">Creative Tim</a> & <a href="https://www.updivision.com" class="ct-docs-footer-copyright-author" target="_blank">UPDIVISION</a>
                </div>
              </div>
              <div class="ct-docs-footer-col">
                <ul class="ct-docs-footer-nav-footer">
                  <li>
                    <a href="https://creative-tim.com" class="ct-docs-footer-nav-link" target="_blank">Creative Tim</a>
                  </li>
                  <li class="nav-item">
                    <a href="https://www.updivision.com" class="ct-docs-footer-nav-link" target="_blank">UPDIVISION</a>
                </li>
                  <li>
                    <a href="https://www.creative-tim.com/contact-us" class="ct-docs-footer-nav-link" target="_blank">Contact Us</a>
                  </li>
                  <li>
                    <a href="https://creative-tim.com/blog" class="ct-docs-footer-nav-link" target="_blank">Blog</a>
                  </li>
                </ul>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
    <script src="../../assets/js/jquery.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/popper.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="../../assets/js/prism.js" type="text/javascript"></script>
    <script src="../../assets/js/docs.min.js" type="text/javascript"></script>
    <script src="../../assets/js/holder.min.js" type="text/javascript"></script>
    <script src="../../assets/js/moment.min.js" type="text/javascript"></script>

    <script src="../../assets/js/dragula.min.js" type="text/javascript"></script>
    <script src="../../assets/js/jkanban.js" type="text/javascript"></script>
    <script type="text/javascript">
      (function() {
        if (document.getElementById("myKanban")) {
          var KanbanTest = new jKanban({
            element: "#myKanban",
            gutter: "10px",
            widthBoard: "450px",
            click: el => {
              let jkanbanInfoModal = document.getElementById("jkanban-info-modal");

              let jkanbanInfoModalTaskId = document.querySelector(
                "#jkanban-info-modal #jkanban-task-id"
              );
              let jkanbanInfoModalTaskTitle = document.querySelector(
                "#jkanban-info-modal #jkanban-task-title"
              );
              let jkanbanInfoModalTaskAssignee = document.querySelector(
                "#jkanban-info-modal #jkanban-task-assignee"
              );
              let jkanbanInfoModalTaskDescription = document.querySelector(
                "#jkanban-info-modal #jkanban-task-description"
              );
              let taskId = el.getAttribute("data-eid");
              let taskTitle = el.querySelector('p.text').innerHTML;
              let taskAssignee = el.getAttribute("data-assignee");
              let taskDescription = el.getAttribute("data-description");
              jkanbanInfoModalTaskId.value = taskId;
              jkanbanInfoModalTaskTitle.value = taskTitle;
              jkanbanInfoModalTaskAssignee.value = taskAssignee;
              jkanbanInfoModalTaskDescription.value = taskDescription;
              var myModal = new bootstrap.Modal(jkanbanInfoModal, {
                show: true
              });
              myModal.show();
            },
            buttonClick: function(el, boardId) {
              if (
                document.querySelector("[data-id='" + boardId + "'] .itemform") ===
                null
              ) {
                // create a form to enter element
                var formItem = document.createElement("form");
                formItem.setAttribute("class", "itemform");
                formItem.innerHTML = `<div class="form-group">
        <textarea class="form-control" rows="2" autofocus></textarea>
        </div>
        <div class="form-group">
            <button type="submit" class="btn bg-gradient-success btn-sm pull-end">Add</button>
            <button type="button" id="kanban-cancel-item" class="btn bg-gradient-light btn-sm pull-end me-2">Cancel</button>
        </div>`;

                KanbanTest.addForm(boardId, formItem);
                formItem.addEventListener("submit", function(e) {
                  e.preventDefault();
                  var text = e.target[0].value;
                  let newTaskId =
                    "_" + text.toLowerCase().replace(/ /g, "_") + boardId;
                  KanbanTest.addElement(boardId, {
                    id: newTaskId,
                    title: text,
                    class: ["border-radius-md"]
                  });
                  formItem.parentNode.removeChild(formItem);
                });
                document.getElementById("kanban-cancel-item").onclick = function() {
                  formItem.parentNode.removeChild(formItem);
                };
              }
            },
            addItemButton: true,
            boards: [{
                id: "_backlog",
                title: "Backlog",
                item: [{
                    id: "_task_1_title_id",
                    title: "Click me to change title",
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_2_title_id",
                    title: "Drag me to 'In progress' section",
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_do_something_id",
                    title: '<img src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/office-dark.jpg" class="w-100"><span class="mt-3 badge badge-sm bg-gradient-primary">Pending</span><p class="text mt-2">Website Design: New cards for blog section and profile details</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">3</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a><a href="#" class="avatar avatar-xs rounded-circle me-2" data-toggle="tooltip" data-original-title="Audrey Love"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class="rounded-circle"></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class="rounded-circle"></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  },
                ]
              },
              {
                id: "_progress",
                title: "In progress",
                item: [{
                    id: "_task_3_title_id",
                    title: '<span class="mt-2 badge badge-sm bg-gradient-warning">Errors</span><p class="text mt-2">Fix Firefox errors</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">11</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class=""></a></div></div>',
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_4_title_id",
                    title: '<span class="mt-2 badge badge-sm bg-gradient-info">Updates</span><p class="text mt-2">Argon Dashboard PRO - Angular 11</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">3</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-5.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class=""></a></div></div>',
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_do_something_4_id",
                    title: '<img src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/meeting.jpg" class="w-100"><span class="mt-3 badge badge-sm bg-gradient-info">Updates</span><p class="text mt-2">Vue 3 Updates</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">9</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a><a href="#" class="avatar avatar-xs rounded-circle me-2" data-toggle="tooltip" data-original-title="Audrey Love"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class="rounded-circle"></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class="rounded-circle"></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  }
                ]

              },
              {
                id: "_working",
                title: "In review",
                item: [{
                    id: "_task_do_something_2_id",
                    title: '<span class="mt-2 badge badge-sm bg-gradient-warning">In Testing</span><p class="text mt-2">Responsive Changes</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">11</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-2.jpg" class=""></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_run_id",
                    title: '<span class="mt-2 badge badge-sm bg-gradient-success">In review</span><p class="text mt-2 mb-1">Change images dimension</p><div class="col"><div class="progress progressm mb-3 w5"><div class="progress-bar bg-gradient-success" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%;"></div></div></div><div class="d-flex"><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-3.jpg" class=""></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_do_something_3_id",
                    title: '<span class="mt-2 badge badge-sm bg-gradient-info">In Review</span><p class="text mt-2 mb-1">Update Links</p><div class="col"><div class="progress progressm mb-3 w5"><div class="progress-bar bg-gradient-info" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: 60%;"></div></div></div><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">6</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jana Lucie"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-5.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Mike Alis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  }
                ]
              },
              {
                id: "_done",
                title: "Done",
                item: [{
                    id: "_task_all_right_id",
                    title: '<img src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/home-decor-1.jpg" class="w-100"><span class="mt-3 badge badge-sm bg-gradient-success">Done</span><p class="text mt-2">Redesign for the home page</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">8</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Jessica Rowland"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-5.jpg" class=""></a><a href="#" class="avatar avatar-xs rounded-circle me-2" data-toggle="tooltip" data-original-title="Audrey Love"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class="rounded-circle"></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class="rounded-circle"></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  },
                  {
                    id: "_task_ok_id",
                    title: '<span class="mt-2 badge badge-sm bg-gradient-success">Done</span><p class="text mt-2">Schedule winter campaign</p><div class="d-flex"><div> <i class="fa fa-paperclip me-1 text-sm"></i><span class="text-sm">2</span></div><div class="avatar-group ms-auto"><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Laurence"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-1.jpg" class=""></a><a href="#" class="avatar avatar-xs me-2 rounded-circle" data-toggle="tooltip" data-original-title="Michael Lewis"><img alt="Image placeholder" src="https://demos.creative-tim.com/test/material-dashboard-pro/assets/img/team-4.jpg" class="rounded-circle"></a></div></div>',
                    assignee: "Done Joe",
                    description: "This task's description is for something, but not for anything",
                    class: ["border-radius-md"]
                  }
                ]
              }
            ]
          });

          var addBoardDefault = document.getElementById("jkanban-add-new-board");
          addBoardDefault.addEventListener("click", function() {
            let newBoardName = document.getElementById("jkanban-new-board-name")
              .value;
            let newBoardId = "_" + newBoardName.toLowerCase().replace(/ /g, "_");
            KanbanTest.addBoards([{
              id: newBoardId,
              title: newBoardName,
              item: []
            }]);
            document.querySelector('#new-board-modal').classList.remove('show');
            document.querySelector('body').classList.remove('modal-open');

            document.querySelector('.modal-backdrop').remove();
          });

          var updateTask = document.getElementById("jkanban-update-task");
          updateTask.addEventListener("click", function() {
            let jkanbanInfoModalTaskId = document.querySelector(
              "#jkanban-info-modal #jkanban-task-id"
            );
            let jkanbanInfoModalTaskTitle = document.querySelector(
              "#jkanban-info-modal #jkanban-task-title"
            );
            let jkanbanInfoModalTaskAssignee = document.querySelector(
              "#jkanban-info-modal #jkanban-task-assignee"
            );
            let jkanbanInfoModalTaskDescription = document.querySelector(
              "#jkanban-info-modal #jkanban-task-description"
            );
            KanbanTest.replaceElement(jkanbanInfoModalTaskId.value, {
              title: jkanbanInfoModalTaskTitle.value,
              assignee: jkanbanInfoModalTaskAssignee.value,
              description: jkanbanInfoModalTaskDescription.value
            });
            jkanbanInfoModalTaskId.value = jkanbanInfoModalTaskId.value;
            jkanbanInfoModalTaskTitle.value = jkanbanInfoModalTaskTitle.value;
            jkanbanInfoModalTaskAssignee.value = jkanbanInfoModalTaskAssignee.value;
            jkanbanInfoModalTaskDescription.value = jkanbanInfoModalTaskDescription.value;
            document.querySelector('#jkanban-info-modal').classList.remove('show');
            document.querySelector('body').classList.remove('modal-open');
            document.querySelector('.modal-backdrop').remove();
          });
        }
      })();
    </script>
        <script src="../../assets/js/material-dashboard.min.js" type="text/javascript"></script>
    <script>
      Holder.addTheme('gray', {
        bg: '#777',
        fg: 'rgba(255,255,255,.75)',
        font: 'Helvetica',
        fontweight: 'normal'
      })
    </script>
    <script>
      // Facebook Pixel Code Don't Delete
      ! function(f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function() {
          n.callMethod ?
            n.callMethod.apply(n, arguments) : n.queue.push(arguments)
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s)
      }(window,
        document, 'script', '//connect.facebook.net/en_US/fbevents.js');

      try {
        fbq('init', '111649226022273');
        fbq('track', "PageView");

      } catch (err) {
        console.log('Facebook Track Error:', err);
      }
    </script>
    <script src="../../assets/js/docs.js"></script>

</body>
  </html>
