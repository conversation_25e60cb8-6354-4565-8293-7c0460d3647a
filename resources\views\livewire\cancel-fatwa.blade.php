<div>
    <x-layout bodyClass="g-sidenav-show bg-gray-100">
        <x-navbars.sidebar activePage="cancel-fatwa"></x-navbars.sidebar>

        <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
            @livewire('navbar', ['titlePage' => 'Cancel Fatwa'])

            <div class="container-fluid py-4">
                @if($successMessage)
                    <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ $successMessage }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif
                
                @if($errorMessage)
                    <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ $errorMessage }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                <!-- Section 1: Select and Cancel Fatwa -->
                <div class="card card-body mb-4">
                    <div class="bg-gradient-primary text-white rounded-3 p-3 mb-4">
                        <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i> Select Fatwa To Cancel</h5>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8 mx-auto">
                            <div class="mb-4 position-relative">
                                <label class="form-label mb-2"><i class="fas fa-search me-1"></i> Search & Select Fatwa No</label>
                                <input type="text" class="form-control border ps-2" 
                                    wire:model.live.debounce.300ms="fileCodeSearch" 
                                    placeholder="Type to search fatwa codes..."
                                    autocomplete="off">
                                
                                @if($fileCodeSearch && count($uniqueFileCodes) > 0)
                                    <div class="dropdown-menu d-block w-100 shadow position-absolute" style="max-height: 250px; overflow-y: auto;">
                                        @foreach($uniqueFileCodes as $fileCode)
                                            <button class="dropdown-item py-2" wire:click="selectFatwa('{{ $fileCode }}')">
                                                {{ $fileCode }}
                                            </button>
                                        @endforeach
                                    </div>
                                @elseif($fileCodeSearch && count($uniqueFileCodes) == 0)
                                    <div class="dropdown-menu d-block w-100 shadow position-absolute">
                                        <div class="dropdown-item py-2 text-center text-muted">
                                            No matching fatwas found
                                        </div>
                                    </div>
                                @endif
                            </div>
                            
                            @if($selectedFatwaDetails)
                                <div class="mt-4">
                                    <div class="card border shadow-none">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0">Fatwa Details</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <p class="mb-2"><strong>Fatwa No:</strong> {{ $selectedFatwaDetails->file_code }}</p>
                                                    <p class="mb-2"><strong>Sender:</strong> {{ $selectedFatwaDetails->sender }}</p>
                                                    <p class="mb-0"><strong>Darulifta:</strong> {{ $selectedFatwaDetails->darulifta_name }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p class="mb-2"><strong>Status:</strong> 
                                                        @if($selectedFatwaDetails->selected == 0)
                                                            <span class="badge bg-info">Unprocessed</span>
                                                        @elseif($selectedFatwaDetails->selected == 1)
                                                            <span class="badge bg-success">Processed</span>
                                                        @elseif($selectedFatwaDetails->selected == 2)
                                                            <span class="badge bg-warning">In Progress</span>
                                                        @endif
                                                    </p>
                                                    <p class="mb-0"><strong>Date:</strong> {{ $selectedFatwaDetails->file_created_date ?? 'N/A' }}</p>
                                                </div>
                                            </div>
                                            <div>
                                                <button class="btn btn-danger" wire:click="confirmCancel('{{ $selectedFatwaDetails->file_code }}')">
                                                    <i class="fas fa-ban me-2"></i>Cancel All Fatwas with this Code ({{ $count }} total)
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @elseif($selectedFileCode)
                                <div class="alert alert-warning mt-4">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    No active fatwa found with the selected file code.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Section 2: Canceled Fatwas -->
                <div class="card card-body mb-4">
                    <div class="bg-gradient-danger text-white rounded-3 p-3 mb-4">
                        <h5 class="mb-0"><i class="fas fa-ban me-2"></i> Canceled Fatwas</h5>
                    </div>
                    
                    <div class="mb-4">
                        <div class="input-group">
                            <span class="input-group-text border-end-0 bg-white">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control border-start-0 ps-0" 
                                placeholder="Search canceled fatwas" 
                                wire:model.live.debounce.300ms="search">
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7">FATWA NO</th>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7 ps-2">SENDER</th>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7 ps-2">DARULIFTA</th>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7 ps-2">PREVIOUS STATUS</th>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7 text-center">COUNT</th>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7 text-center">FATWA FOlDER DATE</th>
                                    <th class="text-uppercase text-dark text-xxs font-weight-bolder opacity-7 text-center">ACTIONS</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($canceledFatwas as $fatwa)
                                    <tr>
                                        <td class="ps-3">
                                            <div class="d-flex align-items-center">
                                                <div class="icon-box bg-gradient-danger shadow-danger text-white rounded-circle me-3">
                                                    <i class="fas fa-file-alt"></i>
                                                </div>
                                                <span class="font-weight-bold text-sm">{{ $fatwa->file_code }}</span>
                                            </div>
                                        </td>
                                        <td>{{ $fatwa->sender }}</td>
                                        <td>{{ $fatwa->darulifta_name }}</td>
                                        <td>
                                            @if($fatwa->previous_selected == 0)
                                                <span class="badge bg-info">Unprocessed</span>
                                            @elseif($fatwa->previous_selected == 1)
                                                <span class="badge bg-success">Processed</span>
                                            @elseif($fatwa->previous_selected == 2)
                                                <span class="badge bg-warning">In Progress</span>
                                            @else
                                                <span class="badge bg-secondary">Unknown</span>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-dark">{{ $fatwa->count }}</span>
                                        </td>
                                        <td>
                                            <!-- Files and History -->
                                            <div class="folder-dates mb-2">
                                                @php
                                                    $folderDates = [];
                                                    $senderDates = [];
                                                    $matchFound = false;
                                                @endphp
                                                
                                                @foreach ($mahlenazar_null as $mahle)
                                                    @if ($fatwa->file_code == $mahle->file_code)
                                                        @php
                                                            $matchFound = true;
                                                            $folderDates[] = $mahle->mail_folder_date;
                                                            $senderDates[] = [
                                                                'date' => $mahle->mail_folder_date,
                                                                'sender' => $mahle->sender ?? 'Unknown',
                                                                'file_name' => $mahle->file_name,
                                                                'darulifta' => $mahle->darulifta_name,
                                                                'checker' => $mahle->checker,
                                                                'by_mufti' => $mahle->by_mufti
                                                            ];
                                                        @endphp
                                                    @endif
                                                @endforeach
                                                
                                                @if ($matchFound)
                                                    @foreach ($senderDates as $item)
                                                        <div class="date-entry mb-1">
                                                            <span class="badge bg-light text-dark py-1 px-2 border">
                                                                <small>{{ $item['sender'] }} ({{ $item['date'] }})</small>
                                                            </span>
                                                            <div class="d-flex gap-1 mt-1">
                                                                @php
                                                                    $baseDateParam = $item['date'] . $item['darulifta'];
                                                                    if (empty($item['by_mufti'])) {
                                                                        $checkerPart = empty($item['checker']) ? 'Checked' : 'Checked_by_' . $item['checker'];
                                                                    } else {
                                                                        $checkerPart = 'Checked_by_' . $item['checker'] . '_' . $item['by_mufti'];
                                                                    }
                                                                    $dateParam = $baseDateParam . $checkerPart;
                                                                @endphp
                                                                
                                                                <a href="{{ route('viewCheck', [
                                                                    'date' => $dateParam,
                                                                    'folder' => 'Mahl-e-Nazar',
                                                                    'filename' => $item['file_name']
                                                                ]) }}" target="_blank" class="btn btn-xs btn-primary">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                
                                                                <a href="{{ route('downloadCheck', [
                                                                    'date' => $dateParam,
                                                                    'filename' => $item['file_name'],
                                                                    'folder' => 'Mahl-e-Nazar'
                                                                ]) }}" class="btn btn-xs btn-success">
                                                                    <i class="fas fa-download"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <span class="badge bg-secondary">No files available</span>
                                                @endif
                                            </div>
                                                                </td>
                                            <td class="text-center py-3">
                                            <!-- Action Buttons -->
                                            <div class="d-flex gap-2 mt-2">
                                                <button class="btn btn-sm btn-success" wire:click="confirmResume('{{ $fatwa->file_code }}')">
                                                    <i class="fas fa-redo me-1"></i>Resume
                                                </button>
                                                <button class="btn btn-sm btn-danger" wire:click="confirmDelete('{{ $fatwa->file_code }}')">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center py-5">
                                            <div class="d-flex flex-column align-items-center">
                                                <i class="fas fa-folder-open text-secondary mb-3" style="font-size: 3rem;"></i>
                                                <p class="text-secondary mb-0">No canceled fatwas found.</p>
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4">
                        {{ $canceledFatwas->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>

            <!-- Cancel Confirmation Modal -->
            @if($confirmingCancel)
            <div class="modal-backdrop show" style="display: block;"></div>
            <div class="modal fade show" tabindex="-1" role="dialog" style="display: block;">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content border-0 shadow">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>Confirm Cancel
                            </h5>
                            <button type="button" class="btn-close btn-close-white" wire:click="cancelAction"></button>
                        </div>
                        <div class="modal-body p-4">
                            <p class="mb-2">Are you sure you want to cancel <strong>all fatwas</strong> with file code <strong>{{ $selectedFileCodeForAction }}</strong>?</p>
                            <p class="mb-2"><strong>{{ $count }}</strong> fatwa(s) will be affected.</p>
                            <p class="text-danger mb-0"><i class="fas fa-info-circle me-1"></i>This action can be reversed later.</p>
                        </div>
                        <div class="modal-footer border-top-0">
                            <button type="button" class="btn bg-gradient-secondary" wire:click="cancelAction">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn bg-gradient-danger" wire:click="cancelFatwa">
                                <i class="fas fa-ban me-1"></i>Confirm Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Resume Confirmation Modal -->
            @if($confirmingResume)
            <div class="modal-backdrop show" style="display: block;"></div>
            <div class="modal fade show" tabindex="-1" role="dialog" style="display: block;">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content border-0 shadow">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-check-circle me-2"></i>Confirm Resume
                            </h5>
                            <button type="button" class="btn-close btn-close-white" wire:click="cancelAction"></button>
                        </div>
                        <div class="modal-body p-4">
                            <p class="mb-2">Are you sure you want to resume <strong>all fatwas</strong> with file code <strong>{{ $selectedFileCodeForAction }}</strong>?</p>
                            <p class="mb-2"><strong>{{ $count }}</strong> fatwa(s) will be restored to their previous state.</p>
                            <p class="text-success mb-0"><i class="fas fa-info-circle me-1"></i>The fatwas will be restored to their previous state.</p>
                        </div>
                        <div class="modal-footer border-top-0">
                            <button type="button" class="btn bg-gradient-secondary" wire:click="cancelAction">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn bg-gradient-success" wire:click="resumeFatwa">
                                <i class="fas fa-redo me-1"></i>Confirm Resume
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Delete Confirmation Modal -->
            @if($confirmingDelete)
            <div class="modal-backdrop show" style="display: block;"></div>
            <div class="modal fade show" tabindex="-1" role="dialog" style="display: block;">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content border-0 shadow">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>Confirm Permanent Deletion
                            </h5>
                            <button type="button" class="btn-close btn-close-white" wire:click="cancelAction"></button>
                        </div>
                        <div class="modal-body p-4">
                            <p class="mb-2">Are you sure you want to <strong>permanently delete all fatwas</strong> with file code <strong>{{ $selectedFileCodeForAction }}</strong>?</p>
                            <p class="mb-2"><strong>{{ $count }}</strong> fatwa(s) will be permanently deleted.</p>
                            <p class="text-danger mb-0"><i class="fas fa-exclamation-circle me-1"></i><strong>Warning:</strong> This action cannot be undone. All files and records will be permanently removed from the system.</p>
                        </div>
                        <div class="modal-footer border-top-0">
                            <button type="button" class="btn bg-gradient-secondary" wire:click="cancelAction">
                                <i class="fas fa-times me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn bg-gradient-danger" wire:click="deleteFatwas">
                                <i class="fas fa-trash me-1"></i>Confirm Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <x-footers.auth></x-footers.auth>
        </main>
    </x-layout>

    <style>
        /* Fix for sidebar overlap issue */
        .main-content {
            margin-left: 250px;
        }

        @media (max-width: 991.98px) {
            .main-content {
                margin-left: 0;
            }
        }

        /* Dropdown and input styling */
        .dropdown-menu {
            z-index: 1000; /* Ensures dropdown is on top of other content */
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.5rem;
            max-height: 250px; /* Max height for the dropdown */
            overflow-y: auto; /* Allows scrolling within the dropdown if content exceeds max-height */
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            color: #344767;
            transition: all 0.2s ease;
            white-space: normal;
        }

        .dropdown-item:hover, .dropdown-item:focus {
            background-color: #f8f9fa;
            color: #1A73E8;
        }

        .form-control {
            font-size: 0.875rem;
        }

        .icon-box {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
        }

        /* Card and section styling */
        .card {
            border: none;
            border-radius: 0.75rem;
            /* overflow: hidden; */ /* This was causing the dropdown to be clipped */
            overflow: visible;   /* Changed to visible to allow dropdown to overflow */
        }

        .bg-gradient-primary {
            background-image: linear-gradient(195deg, #49a3f1 0%, #1A73E8 100%);
        }

        .bg-gradient-danger {
            background-image: linear-gradient(195deg, #ec407a 0%, #D81B60 100%);
        }

        /* Table styling */
        .table thead th {
            font-size: 0.65rem;
            letter-spacing: 0.5px;
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #e9ecef;
        }

        .table tbody tr {
            border-bottom: 1px solid #f0f2f5;
        }
        
        /* File history and actions styling */
        .folder-dates {
            max-height: 200px;
            overflow-y: auto;
            padding-right: 5px;
        }
        
        .folder-dates::-webkit-scrollbar {
            width: 4px;
        }
        
        .folder-dates::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        .folder-dates::-webkit-scrollbar-thumb {
            background: #d1d1d1;
            border-radius: 10px;
        }
        
        .folder-dates::-webkit-scrollbar-thumb:hover {
            background: #b1b1b1;
        }
        
        .date-entry {
            margin-bottom: 0.5rem;
        }
        
        .btn-xs {
            padding: 0.25rem 0.5rem;
            font-size: 0.65rem;
            border-radius: 0.25rem;
        }
        
        .gap-1 {
            gap: 0.25rem;
        }
        
        .gap-2 {
            gap: 0.5rem;
        }
    </style>
</div> 