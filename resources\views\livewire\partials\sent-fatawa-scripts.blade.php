<!-- Scripts for Sent Fatawa -->
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('alert-dismissible')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);

    // Loading overlay functionality
    window.showLoadingOverlay = function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'flex';
        }
    };

    window.hideLoadingOverlay = function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    };

    // Loading state for forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            showLoadingOverlay();
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>Loading...';
            }
        });
    });

    // Hide loading overlay on page load
    window.addEventListener('load', function() {
        hideLoadingOverlay();
    });

    // Enhanced table responsiveness
    const tables = document.querySelectorAll('.table-responsive');
    tables.forEach(table => {
        if (table.scrollWidth > table.clientWidth) {
            table.classList.add('has-scroll');
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"], input[name*="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to close modals/dropdowns
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                const bsModal = bootstrap.Modal.getInstance(modal);
                if (bsModal) {
                    bsModal.hide();
                }
            });
        }
    });

    // Enhanced form validation
    const inputs = document.querySelectorAll('.form-control-modern');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateInput(this);
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateInput(this);
            }
        });
    });

    function validateInput(input) {
        const value = input.value.trim();
        const isRequired = input.hasAttribute('required');

        // Remove existing validation classes
        input.classList.remove('is-valid', 'is-invalid');

        if (isRequired && !value) {
            input.classList.add('is-invalid');
            showInputError(input, 'This field is required');
        } else if (value) {
            input.classList.add('is-valid');
            hideInputError(input);
        }
    }

    function showInputError(input, message) {
        let errorDiv = input.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            input.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    function hideInputError(input) {
        const errorDiv = input.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // Auto-save form data to localStorage
    const formInputs = document.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        const storageKey = `sent_fatawa_${input.name || input.id}`;

        // Load saved value
        const savedValue = localStorage.getItem(storageKey);
        if (savedValue && !input.value) {
            input.value = savedValue;
        }

        // Save on change
        input.addEventListener('change', function() {
            if (this.type !== 'password') {
                localStorage.setItem(storageKey, this.value);
            }
        });
    });

    // Clear saved form data on successful submission
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            setTimeout(() => {
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    const storageKey = `sent_fatawa_${input.name || input.id}`;
                    localStorage.removeItem(storageKey);
                });
            }, 1000);
        });
    });

    // Enhanced download progress tracking
    const downloadLinks = document.querySelectorAll('a[href*="download"]');
    downloadLinks.forEach(link => {
        link.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                setTimeout(() => {
                    icon.className = originalClass;
                }, 3000);
            }
        });
    });

    // Print functionality
    window.printPage = function() {
        window.print();
    };

    // Export functionality
    window.exportData = function(format) {
        const data = collectTableData();

        switch(format) {
            case 'csv':
                downloadCSV(data);
                break;
            case 'json':
                downloadJSON(data);
                break;
            default:
                console.log('Unsupported export format');
        }
    };

    function collectTableData() {
        const tables = document.querySelectorAll('.table-modern');
        const data = [];

        tables.forEach(table => {
            const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
            const rows = Array.from(table.querySelectorAll('tbody tr')).map(tr => {
                return Array.from(tr.querySelectorAll('td')).map(td => td.textContent.trim());
            });

            data.push({ headers, rows });
        });

        return data;
    }

    function downloadCSV(data) {
        let csvContent = '';

        data.forEach(table => {
            csvContent += table.headers.join(',') + '\n';
            table.rows.forEach(row => {
                csvContent += row.join(',') + '\n';
            });
            csvContent += '\n';
        });

        downloadFile(csvContent, 'sent-fatawa-data.csv', 'text/csv');
    }

    function downloadJSON(data) {
        const jsonContent = JSON.stringify(data, null, 2);
        downloadFile(jsonContent, 'sent-fatawa-data.json', 'application/json');
    }

    function downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }

    // Error handling for images
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.addEventListener('error', function() {
            this.style.display = 'none';
        });
    });

    // Accessibility improvements
    const buttons = document.querySelectorAll('button, .btn');
    buttons.forEach(button => {
        if (!button.hasAttribute('aria-label') && !button.textContent.trim()) {
            const icon = button.querySelector('i');
            if (icon) {
                const iconClass = icon.className;
                if (iconClass.includes('download')) {
                    button.setAttribute('aria-label', 'Download');
                } else if (iconClass.includes('delete') || iconClass.includes('trash')) {
                    button.setAttribute('aria-label', 'Delete');
                } else if (iconClass.includes('view') || iconClass.includes('eye')) {
                    button.setAttribute('aria-label', 'View');
                } else if (iconClass.includes('edit')) {
                    button.setAttribute('aria-label', 'Edit');
                }
            }
        }
    });

    // Focus management for modals
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            const firstInput = this.querySelector('input, select, textarea, button');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });
});

// Global utility functions
window.showNotification = function(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
};

window.confirmAction = function(message, callback) {
    if (confirm(message)) {
        callback();
    }
};

// Theme toggle functionality
window.toggleTheme = function() {
    const body = document.body;
    const isDark = body.classList.contains('dark-theme');

    if (isDark) {
        body.classList.remove('dark-theme');
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.add('dark-theme');
        localStorage.setItem('theme', 'dark');
    }
};

// Initialize theme from localStorage
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
    }
});
</script>

<style>
/* Additional responsive styles */
@media (max-width: 576px) {
    .modern-card-header {
        padding: 1rem;
    }

    .modern-card-body {
        padding: 1rem;
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .btn-modern {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

/* Form validation styles */
.form-control-modern.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control-modern.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* Dark theme styles */
.dark-theme {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #ffffff;
}

.dark-theme .modern-card {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.dark-theme .modern-card-body {
    background: #2c3e50;
    color: white;
}

.dark-theme .table-modern {
    background: #2c3e50;
    color: white;
}

.dark-theme .table-modern tbody tr:hover {
    background-color: #34495e;
}

/* Print styles */
@media print {
    .btn-modern,
    .navigation-section,
    .display-options {
        display: none !important;
    }

    .modern-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .table-modern {
        font-size: 12px;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .modern-card {
        border: 2px solid #000;
    }

    .btn-modern {
        border: 2px solid #000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
</style>
