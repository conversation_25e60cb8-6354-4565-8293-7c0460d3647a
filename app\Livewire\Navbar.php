<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class Navbar extends Component
{
    public $titlePage;
    public $user;

    public function mount($titlePage)
    {
        $this->titlePage = $titlePage;
        $this->user = Auth::user();
    }

    public function logout()
    {
        // Instead of logging out directly, redirect to the sign-out route
        return redirect()->route('logout');
    }

    public function render()
    {
        return view('livewire.navbar');
    }
}
