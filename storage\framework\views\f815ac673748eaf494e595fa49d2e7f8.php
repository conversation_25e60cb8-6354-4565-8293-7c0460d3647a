<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">My Tasks</h5>
                            <p class="text-sm mb-0">Tasks assigned to you</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <?php if($tasks->where('status', '!=', 'completed')->count() > 0): ?>
                                    <a href="<?php echo e(route('daily-performance.create')); ?>" class="btn bg-gradient-primary btn-sm mb-0">
                                        <i class="fas fa-clipboard-check"></i>&nbsp;&nbsp;Submit Performance
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="row mt-4">
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Tasks</p>
                                <h5 class="font-weight-bolder mb-0">
                                    <?php echo e($tasks->total()); ?>

                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="ni ni-collection text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Pending</p>
                                <h5 class="font-weight-bolder mb-0">
                                    <?php echo e($tasks->where('status', 'pending')->count()); ?>

                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="ni ni-time-alarm text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">In Progress</p>
                                <h5 class="font-weight-bolder mb-0">
                                    <?php echo e($tasks->where('status', 'in_progress')->count()); ?>

                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                <i class="ni ni-settings text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Completed</p>
                                <h5 class="font-weight-bolder mb-0">
                                    <?php echo e($tasks->where('status', 'completed')->count()); ?>

                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                <i class="ni ni-check-bold text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">My Tasks</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Department</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Type</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="<?php echo e($task->isOverdue() ? 'table-warning' : ''); ?>">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($task->title); ?></h6>
                                                <?php if($task->description): ?>
                                                    <p class="text-xs text-secondary mb-0"><?php echo e(Str::limit($task->description, 50)); ?></p>
                                                <?php endif; ?>
                                                <?php if($task->isOverdue()): ?>
                                                    <span class="badge badge-sm bg-gradient-danger">Overdue</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm"><?php echo e($task->department->name ?? 'No Department'); ?></h6>
                                            <?php if($task->assignedBy): ?>
                                                <p class="text-xs text-secondary mb-0">Assigned by: <?php echo e($task->assignedBy->name); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary')); ?>">
                                            <?php echo e(ucfirst($task->type)); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success')); ?>">
                                            <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <?php if($task->due_date): ?>
                                            <span class="text-secondary text-xs font-weight-bold">
                                                <?php echo e($task->due_date->format('M d, Y')); ?>

                                            </span>
                                            <?php if($task->isOverdue()): ?>
                                                <br><small class="text-danger"><?php echo e($task->due_date->diffForHumans()); ?></small>
                                            <?php else: ?>
                                                <br><small class="text-muted"><?php echo e($task->due_date->diffForHumans()); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-secondary text-xs">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning'))); ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button class="btn btn-sm btn-outline-info mb-0" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#viewModal<?php echo e($task->id); ?>" 
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if($task->status !== 'completed' && $task->status !== 'cancelled'): ?>
                                                <form action="<?php echo e(route('workflow-tasks.change-status', $task)); ?>" method="POST" class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <?php echo method_field('PATCH'); ?>
                                                    <input type="hidden" name="status" value="<?php echo e($task->status === 'pending' ? 'in_progress' : 'completed'); ?>">
                                                    <button type="submit" class="btn btn-sm btn-outline-<?php echo e($task->status === 'pending' ? 'primary' : 'success'); ?> mb-0"
                                                            title="<?php echo e($task->status === 'pending' ? 'Start Task' : 'Complete Task'); ?>">
                                                        <i class="fas fa-<?php echo e($task->status === 'pending' ? 'play' : 'check'); ?>"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>

                                <!-- View Modal -->
                                <div class="modal fade" id="viewModal<?php echo e($task->id); ?>" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-tasks me-2"></i>
                                                    Task Details
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Title:</strong> <?php echo e($task->title); ?></p>
                                                        <p class="mb-1"><strong>Department:</strong> <?php echo e($task->department->name ?? 'No Department'); ?></p>
                                                        <p class="mb-1"><strong>Assigned By:</strong> <?php echo e($task->assignedBy->name ?? 'System'); ?></p>
                                                        <p class="mb-1"><strong>Type:</strong> 
                                                            <span class="badge bg-<?php echo e($task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary')); ?>">
                                                                <?php echo e(ucfirst($task->type)); ?>

                                                            </span>
                                                        </p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Priority:</strong> 
                                                            <span class="badge bg-<?php echo e($task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success')); ?>">
                                                                <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                                            </span>
                                                        </p>
                                                        <p class="mb-1"><strong>Status:</strong> 
                                                            <span class="badge bg-<?php echo e($task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning'))); ?>">
                                                                <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                                            </span>
                                                        </p>
                                                        <p class="mb-1"><strong>Due Date:</strong> 
                                                            <?php echo e($task->due_date ? $task->due_date->format('M d, Y') : 'No due date'); ?>

                                                        </p>
                                                        <p class="mb-1"><strong>Created:</strong> <?php echo e($task->created_at->format('M d, Y')); ?></p>
                                                    </div>
                                                </div>
                                                
                                                <?php if($task->description): ?>
                                                    <div class="mt-3">
                                                        <strong>Description:</strong>
                                                        <p class="mb-0 mt-1"><?php echo e($task->description); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if($task->completion_notes && $task->status === 'completed'): ?>
                                                    <div class="mt-3">
                                                        <strong>Completion Notes:</strong>
                                                        <div class="alert alert-success mt-1">
                                                            <?php echo e($task->completion_notes); ?>

                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                <?php if($task->status !== 'completed' && $task->status !== 'cancelled'): ?>
                                                    <form action="<?php echo e(route('workflow-tasks.change-status', $task)); ?>" method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('PATCH'); ?>
                                                        <input type="hidden" name="status" value="<?php echo e($task->status === 'pending' ? 'in_progress' : 'completed'); ?>">
                                                        <button type="submit" class="btn btn-<?php echo e($task->status === 'pending' ? 'primary' : 'success'); ?>">
                                                            <i class="fas fa-<?php echo e($task->status === 'pending' ? 'play' : 'check'); ?> me-2"></i>
                                                            <?php echo e($task->status === 'pending' ? 'Start Task' : 'Complete Task'); ?>

                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-tasks text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted">No Tasks Assigned</h6>
                                            <p class="text-sm text-muted mb-0">You don't have any tasks assigned to you yet.</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if($tasks->hasPages()): ?>
                        <div class="d-flex justify-content-center mt-3">
                            <?php echo e($tasks->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user_type.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/workflow-tasks/my-tasks.blade.php ENDPATH**/ ?>