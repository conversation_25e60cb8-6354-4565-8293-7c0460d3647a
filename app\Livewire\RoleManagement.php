<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User;
use App\Models\Role;
use App\Services\RoleManagementService;
use Illuminate\Support\Facades\Gate;

class RoleManagement extends Component
{
    use WithPagination;

    public $users;
    public $roles;
    public $selectedUser = null;
    public $selectedRoles = [];
    public $search = '';
    public $showModal = false;
    public $modalTitle = '';
    public $successMessage = '';
    public $errorMessage = '';

    protected $roleManagementService;

    public function mount()
    {
        // Check authorization
        if (!Gate::allows('manage-users')) {
            abort(403, 'Unauthorized access to role management.');
        }

        $this->roleManagementService = app(RoleManagementService::class);
        $this->loadRoles();
        $this->loadUsers();
    }

    public function render()
    {
        return view('livewire.role-management');
    }

    public function loadUsers()
    {
        $query = User::with('roles');

        if ($this->search) {
            $query->where(function($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        $this->users = $query->get(); // Changed from paginate to get for simplicity
    }

    public function loadRoles()
    {
        $this->roles = Role::all();
    }

    public function updatedSearch()
    {
        $this->resetPage();
        $this->loadUsers();
    }

    public function openRoleModal($userId)
    {
        $this->selectedUser = User::with('roles')->find($userId);
        
        if (!$this->selectedUser) {
            $this->errorMessage = 'User not found.';
            return;
        }

        $this->selectedRoles = $this->selectedUser->roles->pluck('id')->toArray();
        $this->modalTitle = 'Manage Roles for ' . $this->selectedUser->name;
        $this->showModal = true;
        $this->clearMessages();
    }

    public function updateUserRoles()
    {
        try {
            if (!$this->selectedUser) {
                throw new \Exception('No user selected.');
            }

            // Validate that at least one role is selected
            if (empty($this->selectedRoles)) {
                throw new \Exception('Please select at least one role for the user.');
            }

            // Get role names for the selected role IDs
            $roleNames = Role::whereIn('id', $this->selectedRoles)->pluck('name')->toArray();

            // Update user roles using the service
            $this->roleManagementService->assignRoles($this->selectedUser, $roleNames);

            $this->successMessage = 'User roles updated successfully!';
            $this->showModal = false;
            $this->loadUsers();
            $this->resetSelectedUser();

        } catch (\Exception $e) {
            $this->errorMessage = 'Error updating roles: ' . $e->getMessage();
        }
    }

    public function removeRole($userId, $roleId)
    {
        try {
            $user = User::find($userId);
            $role = Role::find($roleId);

            if (!$user || !$role) {
                throw new \Exception('User or role not found.');
            }

            // Check if user has other roles
            if ($user->roles->count() <= 1) {
                throw new \Exception('Cannot remove the last role from a user.');
            }

            $this->roleManagementService->removeRole($user, $role->name);

            $this->successMessage = "Role '{$role->name}' removed from {$user->name} successfully!";
            $this->loadUsers();

        } catch (\Exception $e) {
            $this->errorMessage = 'Error removing role: ' . $e->getMessage();
        }
    }

    public function addSuperiorRole($userId)
    {
        try {
            $user = User::find($userId);

            if (!$user) {
                throw new \Exception('User not found.');
            }

            // Add Superior role using the service
            $this->roleManagementService->addRole($user, 'Superior');

            $this->successMessage = "Superior role assigned to {$user->name} successfully!";
            $this->loadUsers();

        } catch (\Exception $e) {
            $this->errorMessage = 'Error assigning Superior role: ' . $e->getMessage();
        }
    }

    public function removeSuperiorRole($userId)
    {
        try {
            $user = User::find($userId);

            if (!$user) {
                throw new \Exception('User not found.');
            }

            // Remove Superior role using the service
            $this->roleManagementService->removeRole($user, 'Superior');

            $this->successMessage = "Superior role removed from {$user->name} successfully!";
            $this->loadUsers();

        } catch (\Exception $e) {
            $this->errorMessage = 'Error removing Superior role: ' . $e->getMessage();
        }
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetSelectedUser();
        $this->clearMessages();
    }

    public function clearMessages()
    {
        $this->successMessage = '';
        $this->errorMessage = '';
    }

    private function resetSelectedUser()
    {
        $this->selectedUser = null;
        $this->selectedRoles = [];
        $this->modalTitle = '';
    }

    public function getUserRoleNames($user)
    {
        return $user->roles->pluck('name')->join(', ');
    }

    public function isUserSuperior($user)
    {
        return $user->roles->contains('name', 'Superior');
    }

    public function canManageUser($user)
    {
        // Admin can manage all users
        if (auth()->user()->roles->contains('name', 'Admin')) {
            return true;
        }

        // Superior can manage their assistants
        if (auth()->user()->roles->contains('name', 'Superior')) {
            return auth()->user()->assistants->contains('id', $user->id);
        }

        return false;
    }

    public function getRoleColor($roleName)
    {
        $colors = [
            'Admin' => 'danger',
            'Superior' => 'warning',
            'mujeeb' => 'info',
            'Muawin' => 'secondary',
            'Checker' => 'success',
            'Mahlenazar' => 'primary',
        ];

        return $colors[$roleName] ?? 'secondary';
    }

    public function refreshData()
    {
        $this->loadUsers();
        $this->loadRoles();
        $this->clearMessages();
    }
}
