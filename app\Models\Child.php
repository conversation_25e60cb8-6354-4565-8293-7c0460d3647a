<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Child extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'name',
        'serial_number',
    ];

    /**
     * Get the parent that owns the child.
     */
    public function parent()
    {
        return $this->belongsTo(ParentModel::class,'parent_id');
    }
}
