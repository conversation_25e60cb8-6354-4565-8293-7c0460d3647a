<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;
use Illuminate\Http\Request;

class RecivedFatawa extends Component
{
    use WithPagination;

    // Filter Properties
    public $selectedMonths = [];
    public $selectedmujeeb = 'all';
    public $selectedmufti = 'all';
    public $selectedexclude = 'exclude_checked';
    public $selectedTimeFrame = 'this_month';
    public $tempSelectedTimeFrame = 'this_month';
    public $tempSelectedMonths = [];
    public $startDate;
    public $endDate;
    public $tempStartDate;
    public $tempEndDate;

    // Display Properties
    public $showDetail = false;
    public $showQue = false;
    public $showChat = false;
    public $expandedFolders = [];
    public $expandedDaruliftaNames = [];

    // Data Properties
    public $checkerlist;
    public $datefilter;
    public $daruliftaNames;
    public $checkers;
    public $mailfolderDate;
    public $mujeebs;
    public $daruliftalist;
    public $remainingFatawa;
    public $sendingFatawa;
    public $que_day_r;
    public $mahlenazar_null;
    public $allfatawa;
    public $codebylower;

    // Route Parameters
    public $darulifta;
    public $mailfolder;

    // User Properties
    public $roleInfo = [];
    public $userName;
    public $munsab;

    // Chat Properties
    public $message;

    // Legacy Properties (keeping for compatibility)
    public $obtain;
    public $checker;



    public function mount(Request $request, $darulifta = null, $mailfolder = null)
    {
        // Set route parameters
        $this->darulifta = $darulifta;
        $this->mailfolder = $mailfolder;

        // Initialize user information and role-based settings
        $this->initializeUserInfo();

        // Load filter parameters from request
        $this->loadFilterParameters($request);

        // Load display options from request
        $this->loadDisplayOptions($request);

        // Load initial data
        $this->loadInitialData();
    }

    /**
     * Initialize user information and role-based settings
     */
    private function initializeUserInfo()
    {
        $user = Auth::user();
        $this->userName = $user->name;

        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();

        // Set role information for filtering
        $this->roleInfo = [
            'userName' => $this->userName,
            'roles' => $roleNames,
            'isAdmin' => in_array('Admin', $roleNames),
            'isMujeeb' => in_array('mujeeb', $roleNames),
            'isMusaddiq' => in_array('Musaddiq', $roleNames),
        ];

        // Get user's checker information
        $checker = DB::table('checker')
            ->where('checker_name', $this->userName)
            ->first();

        $this->checker = $checker->folder_id ?? null;
        $this->munsab = $checker->munsab ?? null;

        // Apply role-based default mufti selection
        if ($this->roleInfo['isAdmin'] && $this->userName !== 'Mufti Ali Asghar') {
            if ($this->selectedmufti !== 'transfer_checked') {
                $this->selectedmufti = $this->checker;
            }
        }
    }

    /**
     * Load filter parameters from request
     */
    private function loadFilterParameters($request)
    {
        $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);
        $this->selectedmufti = $request->query('selectedmufti', $this->selectedmufti);
        $this->selectedexclude = $request->query('selectedexclude', $this->selectedexclude);
        $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);

        // Handle selected months
        $this->selectedMonths = $request->query('selectedMonths', []);
        if (is_string($this->selectedMonths)) {
            $this->selectedMonths = explode(',', $this->selectedMonths);
        }

        // Handle date range
        $this->startDate = $request->query('startDate');
        $this->endDate = $request->query('endDate');

        // Set temporary values for filters
        $this->tempSelectedTimeFrame = $this->selectedTimeFrame;
        $this->tempSelectedMonths = $this->selectedMonths;
        $this->tempStartDate = $this->startDate;
        $this->tempEndDate = $this->endDate;
    }

    /**
     * Load display options from request
     */
    private function loadDisplayOptions($request)
    {
        $this->showDetail = $request->query('showDetail', false) == '1';
        $this->showQue = $request->query('showQue', false) == '1';
        $this->showChat = $request->query('showChat', false) == '1';
    }

    /**
     * Load initial data
     */
    private function loadInitialData()
    {
        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();
        $this->loadStaticData();
    }

    /**
     * Load static data that doesn't change based on filters
     */
    private function loadStaticData()
    {
        $this->checkerlist = DB::table('checker')->get();
        $this->datefilter = DB::table('uploaded_files')
            ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();
    }

    /**
     * Load dynamic data that changes based on filters
     */
    private function loadDynamicData()
    {
        // Load data that changes based on filters
        $this->daruliftalist = DB::table('uploaded_files')
            ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
            ->select('uploaded_files.darulifta_name')
            ->distinct()
            ->orderBy('daruliftas.id')
            ->pluck('uploaded_files.darulifta_name');

        $this->que_day_r = DB::table('questions')
            ->whereIn('question_branch', $this->daruliftaNames)
            ->get();

        $this->allfatawa = DB::table('uploaded_files')
            ->orderBy('id', 'asc')
            ->get();

        $this->codebylower = DB::table('uploaded_files as uf1')
            ->select('uf1.*')
            ->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files GROUP BY file_code) as uf2'), function($join) {
                $join->on('uf1.id', '=', 'uf2.id');
            })
            ->orderBy('uf1.id', 'asc')
            ->get();

        $this->mahlenazar_null = DB::table('uploaded_files as u1')
            ->whereIn('u1.darulifta_name', $this->daruliftaNames)
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.*')
            ->get();

        $this->remainingFatawa = $this->remainingFatawadata();
        $this->sendingFatawa = $this->sendingFatawadata();
    }
    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->startDate && $this->endDate && empty($this->selectedMonths)) {
            $this->selectedTimeFrame = 'custom';
            $this->tempSelectedTimeFrame = 'custom';
        }
        $this->remainingFatawadata();
        $this->sendingFatawadata();
    }

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->tempSelectedTimeFrame = $timeFrame;
        if ($timeFrame === 'this_month') {
            // Resetting the values
            $this->tempStartDate = null;
            $this->tempEndDate = null;
            $this->selectedMonths = [];
            $this->tempSelectedMonths = [];

            // Optional: Debugging statements
            // dd($this->tempStartDate, $this->tempEndDate, $this->selectedMonths, $this->tempSelectedMonths);
        }

    }
    public function updateSelectedMufti($muftiFrame)
    {
        $this->selectedmufti = $muftiFrame;
    }
    public function updateSelectedExclude($exclude)
    {
        $this->selectedexclude = $exclude;
    }
    public function updateSelectedMujeeb($mujeebFrame)
    {
        $this->selectedmujeeb = $mujeebFrame;
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);
        if ($this->darulifta === null) {
            if(count(Auth::user()->roles) > 1){
                $this->daruliftaNames = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                ->select('uploaded_files.darulifta_name')
                ->distinct()
                ->orderBy('daruliftas.id')
                ->pluck('uploaded_files.darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                     $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');

            }
            else {
                $this->daruliftaNames = DB::table('uploaded_files')

                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name',$firstRoleName)
                ->pluck('darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->whereIn('darulifta_name', $this->daruliftaNames)
                ->distinct()
                ->pluck('sender');
                $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');

            }

        } else {
            // $this->daruliftaNames = $this->darulifta;
            $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name',$this->darulifta)

                ->distinct()
                ->pluck('darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('sender');
                $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
        }
    }
    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')

        ->distinct()

        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()

    ->pluck('mail_folder_date');
    }
}
public function sendMessage($iftaCode)
{

    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.


    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}

    public function toggleShowDetail()
    {
        $this->showDetail = !$this->showDetail;
        $this->updateUrl();
    }

    public function toggleShowQue()
    {
        $this->showQue = !$this->showQue;
        $this->updateUrl();
    }

    public function toggleShowChat()
    {
        $this->showChat = !$this->showChat;
        $this->updateUrl();
    }

    private function updateUrl()
    {
        $params = [
            'showDetail' => $this->showDetail ? '1' : '0',
            'showQue' => $this->showQue ? '1' : '0',
            'showChat' => $this->showChat ? '1' : '0',
            'selectedmujeeb' => $this->selectedmujeeb,
            'selectedmufti' => $this->selectedmufti,
            'selectedexclude' => $this->selectedexclude,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];

        if (!empty($this->selectedMonths)) {
            $params['selectedMonths'] = implode(',', $this->selectedMonths);
        }

        $this->js('window.history.replaceState({}, "", "' . request()->url() . '?' . http_build_query($params) . '")');
    }

    public function render()
    {
        // Load dynamic data that changes based on filters
        $this->loadDynamicData();

        $messages = Chat::latest()->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.recived-fatawa', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'checkers' => $this->checkers,
            'remainingFatawa' => $this->remainingFatawa,
            'sendingFatawa' => $this->sendingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmufti' => $this->selectedmufti,
            'selectedexclude' => $this->selectedexclude,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'tempSelectedTimeFrame' => $this->tempSelectedTimeFrame,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'showDetail' => $this->showDetail,
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'userName' => $this->userName,
            'checkerlist' => $this->checkerlist,
            'munsab' => $this->munsab,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'tempStartDate' => $this->tempStartDate,
            'tempEndDate' => $this->tempEndDate,

            // ... (other data to be passed to the view)
        ]

        );

    }
    /**
     * Optimized method to get remaining fatawa data with improved performance
     */
    public function remainingFatawadata()
    {
        // Early return for invalid filter combinations
        if ($this->tempSelectedTimeFrame == 'other' && empty($this->selectedMonths)) {
            return [];
        }

        // Build base query with common conditions
        $baseQuery = DB::table('uploaded_files')
            ->whereIn('darulifta_name', $this->daruliftaNames);

        // Apply exclude filtering first
        if ($this->selectedexclude == 'exclude_checked') {
            $baseQuery->where('selected', 0);
        }

        // Apply role-based filtering
        $this->applyRoleBasedFiltering($baseQuery);

        // Apply mufti filtering
        $this->applyMuftiFiltering($baseQuery);

        // Apply date filtering
        $this->applyDateFiltering($baseQuery);

        // Get results and group by darulifta and mail folder date
        $results = $baseQuery->get();

        return $this->groupFatawaResults($results);
    }

    /**
     * Apply role-based filtering to the query
     */
    private function applyRoleBasedFiltering($query)
    {
        if ($this->roleInfo['isMujeeb']) {
            $query->where('sender', $this->roleInfo['userName']);
        } elseif ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }
    }

    /**
     * Apply mufti filtering to the query
     */
    private function applyMuftiFiltering($query)
    {
        if ($this->selectedmufti && $this->selectedmufti != 'all') {
            if ($this->selectedmufti == 'mufti_ali_asghar') {
                $query->where(function($q) {
                    $q->where('checker', $this->selectedmufti)
                      ->orWhereNull('checker');
                });
            } else {
                $query->where('checker', $this->selectedmufti);
            }
        }
    }

    /**
     * Apply date filtering to the query
     */
    private function applyDateFiltering($query)
    {
        if ($this->tempSelectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->startOfMonth()->format('Y-m-d'),
                now()->endOfMonth()->format('Y-m-d')
            ]);
        } elseif ($this->tempSelectedTimeFrame == 'last_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->subMonth()->startOfMonth()->format('Y-m-d'),
                now()->subMonth()->endOfMonth()->format('Y-m-d')
            ]);
        } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, $this->selectedMonths);
            $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }
    }

    /**
     * Group fatawa results by darulifta and mail folder date
     */
    private function groupFatawaResults($results)
    {
        $groupedResults = [];

        foreach ($results as $fatawa) {
            $groupedResults[$fatawa->darulifta_name][$fatawa->mail_folder_date][] = $fatawa;
        }

        return $groupedResults;
    }
    /**
     * Optimized method to get sending fatawa data with improved performance
     */
    public function sendingFatawadata()
    {
        // Initialize checkers if empty
        $this->initializeCheckers();

        // Build optimized query for all data at once
        $baseQuery = DB::table('uploaded_files')
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->whereIn('mail_folder_date', $this->mailfolderDate);

        // Apply exclude filtering
        if ($this->selectedexclude == 'exclude_checked') {
            $baseQuery->where('selected', 0);
        }

        // Apply checker filtering
        $this->applyCheckerFiltering($baseQuery);

        // Apply role-based filtering
        $this->applyRoleBasedFiltering($baseQuery);

        // Apply date filtering
        $this->applyDateFiltering($baseQuery);

        // Get all results at once
        $results = $baseQuery->get();

        // Group and order results
        return $this->groupAndOrderSendingResults($results);
    }

    /**
     * Initialize checkers if empty
     */
    private function initializeCheckers()
    {
        if (empty($this->checkers)) {
            $this->checkers = DB::table('checker')
                ->select('folder_id')
                ->distinct()
                ->pluck('folder_id')
                ->toArray();
        }
    }

    /**
     * Apply checker filtering to the query
     */
    private function applyCheckerFiltering($query)
    {
        if (!empty($this->checkers)) {
            $query->where(function($q) {
                foreach ($this->checkers as $checker) {
                    $q->orWhere(function($subQuery) use ($checker) {
                        $subQuery->where('checker', $checker);
                        if ($checker === 'mufti_ali_asghar') {
                            $subQuery->orWhereNull('checker');
                        }
                    });
                }
            });
        }
    }

    /**
     * Group and order sending results
     */
    private function groupAndOrderSendingResults($results)
    {
        $sendingFatawas = [];

        // Group results by darulifta, checker, and mail folder date
        foreach ($results as $fatawa) {
            $checker = $fatawa->checker ?? 'mufti_ali_asghar';
            $sendingFatawas[$fatawa->darulifta_name][$checker][$fatawa->mail_folder_date][] = $fatawa;
        }

        // Reorder based on checker order
        foreach ($sendingFatawas as $daruliftaName => $checkers) {
            $orderedCheckers = [];
            foreach ($this->checkers as $checker) {
                if (isset($checkers[$checker])) {
                    $orderedCheckers[$checker] = $checkers[$checker];
                }
            }
            $sendingFatawas[$daruliftaName] = $orderedCheckers;
        }

        return $sendingFatawas;
    }

    /**
     * Toggle folder expansion state
     */
    public function toggleFolder($daruliftaName, $folderId)
    {
        $key = $daruliftaName . '_' . $folderId;

        if (in_array($key, $this->expandedFolders)) {
            $this->expandedFolders = array_diff($this->expandedFolders, [$key]);
        } else {
            $this->expandedFolders[] = $key;
        }
    }

    /**
     * Toggle darulifta expansion state
     */
    public function toggleDarulifta($daruliftaName)
    {
        if (in_array($daruliftaName, $this->expandedDaruliftaNames)) {
            $this->expandedDaruliftaNames = array_diff($this->expandedDaruliftaNames, [$daruliftaName]);
        } else {
            $this->expandedDaruliftaNames[] = $daruliftaName;
        }
    }

    /**
     * Check if folder is expanded
     */
    public function isFolderExpanded($daruliftaName, $folderId)
    {
        $key = $daruliftaName . '_' . $folderId;
        return in_array($key, $this->expandedFolders);
    }

    /**
     * Check if darulifta is expanded
     */
    public function isDaruliftaExpanded($daruliftaName)
    {
        return in_array($daruliftaName, $this->expandedDaruliftaNames);
    }

    /**
     * Get formatted date for display
     */
    public function getFormattedDate($date)
    {
        return \Carbon\Carbon::parse($date)->format('d M Y');
    }

    /**
     * Get days difference from current date
     */
    public function getDaysDifference($date)
    {
        return \Carbon\Carbon::parse($date)->diffInDays(\Carbon\Carbon::now());
    }

    /**
     * Get badge class based on days difference
     */
    public function getBadgeClass($daysDifference)
    {
        return $daysDifference > 180 ? 'bg-success' : 'bg-warning';
    }

    /**
     * Check if user can delete file
     */
    public function canDeleteFile($file)
    {
        $downloadfileByadmin = $file->downloaded_by_admin ?? null;
        return is_null($downloadfileByadmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
    }

}

