<x-layout bodyClass="g-sidenav-show bg-gray-200">
    
    
    <!-- Include Bootstrap Timepicker JS -->
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-timepicker/0.5.2/js/bootstrap-timepicker.min.js"></script> --}}
    
    <x-navbars.sidebar activePage="reciption"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Reciption Page"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <!-- Include jQuery library -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
       

        <!-- Create Appointment Entry Form -->
        <style>
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
        </style>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                // Get the input element by its ID
                var dateInput = document.getElementById("rec_date");
                
                // Create a new date object with the current date
                var currentDate = new Date();
                
                // Format the current date as "YYYY-MM-DD"
                var formattedDate = currentDate.toISOString().split('T')[0];
                
                // Set the formatted date as the input's value
                dateInput.value = formattedDate;
            });
        </script>
         <div class="white-box">
            <h1 class="text-center">Edit Darulifta Ahlesunnat Reception</h1>
            <div class="row">
            <div class="col-md-12">
                <form action="{{ route('reciptque.update', $reciptque->id) }}" method="POST" enctype="multipart/form-data" class="form-horizontal answer-form" role="form" id="action">
                    @method('PUT')
                    @csrf
                    <div class="row">
                    <div class="form-group col-md-4 pull-right">
                        <label for="rec_date" class="col-md-12">Date</label>
                        <div class="col-md-12">
                            <input type="date" name="rec_date" id="rec_date" tabindex="1" class="form-control" value="{{ $reciptque->rec_date }}">
                            @error('rec_date')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="form-group col-md-4 pull-right">
                        <label for="question_type" class="col-md-12">Question Type</label>
                        <div class="col-md-12">
                            <select name="question_type" id="question_type" tabindex="2" class="form-control searchSelect2">
                                <option value="Daily" {{ $reciptque->question_type === 'Daily' ? 'selected' : '' }}>Daily</option>
                                <option value="Email" {{ $reciptque->question_type === 'Email' ? 'selected' : '' }}>Email</option>
                            </select>
                            @error('question_type')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group col-md-4 pull-right">
                        <label for="ifta_code" class="col-md-12">Ifta Code</label>
                        <div class="col-md-12">
                            <input type="text" name="ifta_code" id="ifta_code" tabindex="1" class="form-control" value="{{ $reciptque->ifta_code }}">
                            @error('ifta_code')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    <div class="form-group col-md-4 pull-right">
                        <label for="phone" class="col-md-12">Phone No:</label>
                        <div class="col-md-12">
                            <input type="text" name="phone" id="number_phone" tabindex="1" class="form-control" placeholder="Enter Phone No" minlength="10" maxlength="16" value="{{ $reciptque->phone }}">
                            @error('phone')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                          <!-- Email -->
                    <div class="form-group col-md-4 pull-right">
                        <label for="email" class="col-md-12">Email<i class="req"></i></label>
                        <div class="col-md-12">
                            <input type="email" name="email" id="qs_email" tabindex="3" class="form-control" placeholder="Enter Email" value="{{ $reciptque->email }}">
                            @error('email')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
        
                    <!-- Sayel (Requester Title) -->
                    <div class="form-group col-md-4 pull-right">
                        <label for="sayel" class="col-md-12">Sayel<i class="req">*</i></label>
                        <div class="col-md-12">
                            <input type="text" name="sayel" id="qs_title" tabindex="2" class="form-control" placeholder="Sayel" value="{{ $reciptque->sayel }}">
                            @error('sayel')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>        
                            
        
                    <!-- Address -->
                    <div class="form-group col-md-4 pull-right">
                        <label for="address" class="col-md-12">Address<i class="req">*</i></label>
                        <div class="col-md-12">
                            <input type="text" name="address" id="qs_address" tabindex="4" class="form-control" placeholder="Address" value="{{ $reciptque->address }}">
                            @error('address')
                            <div class="alert-danger error-message">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>                            {{-- @endif --}}
        
                            <!-- Issues -->
                           <!-- Muzo (Issues Dropdown) -->
                           <div class="form-group col-md-4 pull-right">
                            <label for="issue" class="col-md-12">Muzo<i class="req">*</i></label>
                            <div class="col-md-12">
                                <select id="issue" name="issue" tabindex="5" class="form-control searchSelect2">
                                    <option value="">Select Muzo</option>
                                    @foreach($issues as $issue)
                                        <option value="{{ $issue->id }}" {{$reciptque->issue == $issue->name ? 'selected' : '' }}>
                                            {{ $issue->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('issue')
                                <div class="alert-danger error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        
                        <!-- Zeli Muzo Dropdown (for Edit) -->
                        <div class="form-group col-md-4 pull-right">
                            <label for="issue_subcategory_id" class="col-md-12">Zeli Muzo</label>
                            <div class="col-md-12">
                                <select id="sub_issue" name="sub_issue" tabindex="6" class="form-control searchSelect2">
                                    <option value="">Select a Zeli-Muzo</option>
                                    
                                        @foreach($subissues as $subIssue)
                                            <option value="{{ $subIssue->id }}" {{ $reciptque->sub_issue == $subIssue->name ? 'selected' : '' }}>
                                                {{ $subIssue->name }}
                                            </option>
                                        @endforeach
                                    
                                </select>
                                @error('sub_issue')
                                <div class="alert-danger error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                            <!-- Title -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="question_title" class="col-md-12">Question Title<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <input type="text" name="question_title" tabindex="7" class="form-control" placeholder="Title" value="{{ $reciptque->question_title }}">
                                    @error('question_title')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
        
                            <!-- Expected Date -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="expected_date" class="col-md-12">Expected Date<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <input type="date" name="expected_date" tabindex="8" class="form-control" id="expected_date" placeholder="dd-mm-yyyy" value="{{ $reciptque->expected_date }}">
                                    @error('expected_date')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
        
                            {{-- @if (count($branches) > 1) --}}
                            <!-- User Branches -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="question_branch" class="col-md-12">Branch</label>
                                <div class="col-md-12">
                                    <select name="question_branch" tabindex="9" class="form-control">
                                      
                                            <option value="{{$reciptque->question_branch }}">{{$reciptque->question_branch }}                                               
                                            </option>
                                        
                                    </select>
                                    @error('question_branch')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
        
                        <div class="row">
                            <!-- Rest of your form fields as needed -->
                        </div>
        
                        <div class="row">
                            <!-- Question Text Area -->
                            <div class="col-md-12">
                                <h2>Question<i style="font-size: 25px;" class="req"></i></h2>
                                @error('question')
                                <div for="question" class="alert-danger error-message font-Nastalleq">{{ $message }}</div>
                                @enderror
                                <div class="form-group">
                                    <textarea name="question" tabindex="10" class="summernote form-control font-Nastalleq" id="summernote" rows="12" placeholder="Enter Question Here">{{ $reciptque->question }}</textarea>
                                    @error('question')
                                    <div for="question" class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
        
                        <div class="row" id="template-row">
                            <!-- File -->
                            <!-- HTML for file upload can be added here -->
                        </div>
        
                        <div class="row" id="actions">
                            <div class="col-md-6">
                                <label for="assign_id" class="col-md-12">Assign Question To Mujeeb</label>
                                <div class="col-md-6 pull-right">
                                    <select name="assign_id" tabindex="12" class="form-control">
                                        <option value="">Select Mujeeb For Send Fatwa</option>
                                        @foreach($mujeeb as $assignUser)
                                            <option value="{{ $assignUser->mujeeb_name }}" {{ $reciptque->assign_id == $assignUser->mujeeb_name ? 'selected' : '' }}>
                                                {{ $assignUser->mujeeb_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assign_id')
                                    <div for="assign_id" class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="clearfix"></div>
                        <div class="row">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <button id="btn_submit" class="btn btn-info btn_submit" type="submit">update</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>        
        
        
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    		
    $(document).ready(function () {
        $('#issue').on('change', function () {
            var issueId = $(this).val();
            if (issueId) {
                $.ajax({
                    url: '{{ route("get-sub-issues") }}',
                    type: 'GET',
                    data: {issue_id: issueId},
                    success: function (data) {
                        $('#sub_issue').empty();
                        $('#sub_issue').append('<option value="">Select a Zali-Muzo</option>');
                        $.each(data, function (key, value) {
                            $('#sub_issue').append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    }
                });
            } else {
                $('#sub_issue').empty();
                $('#sub_issue').append('<option value="">Select a Zali-Muzo</option>');
            }
        });
    });
    $(document).ready(function () {
    // Add an event listener for the phone input
    $('#number_phone').on('blur', function () {
        let phone = $(this).val();
        if (phone) {
            // Send an AJAX request to check if the phone exists
            $.ajax({
                url: '{{ route("checkPhoneExist") }}',
                type: 'GET',
                data: { phone: phone },
                success: function (data) {
                    if (data) {
                        // Populate the "sayel" and "address" fields
                        if (data.sayel) {
                            $('#qs_title').val(data.sayel);
                        }
                        if (data.address) {
                            $('#qs_address').val(data.address);
                        }
                    }
                }
            });
        }
    });

    // Add an event listener for the email input
    $('#qs_email').on('blur', function () {
        let email = $(this).val();
        if (email) {
            // Send an AJAX request to check if the email exists
            $.ajax({
                url: '{{ route("checkEmailExist") }}',
                type: 'GET',
                data: { email: email },
                success: function (data) {
                    if (data) {
                        // Populate the "sayel" and "address" fields
                        if (data.sayel) {
                            $('#qs_title').val(data.sayel);
                        }
                        if (data.address) {
                            $('#qs_address').val(data.address);
                        }
                    }
                }
            });
        }
    });
});
$(document).ready(function () {
    // Add an event listener for the question_type select
    $('#question_type').on('change', function () {
        if ($(this).val() === 'Email') {
            // If Email is selected, disable the phone input
            $('#number_phone').prop('disabled', true);
        } else {
            // If Daily is selected, enable the phone input
            $('#number_phone').prop('disabled', false);
        }
    });
});
$(document).ready(function () {
    // Function to calculate and set expected_date
    function setExpectedDate() {
        const recDate = new Date($('#rec_date').val());
        const expectedDate = new Date(recDate);
        expectedDate.setDate(recDate.getDate() + 3);
        const formattedExpectedDate = expectedDate.toISOString().split('T')[0];
        $('#expected_date').val(formattedExpectedDate);
    }

    // Initialize rec_date with the current date
    const currentDate = new Date();
    const formattedCurrentDate = currentDate.toISOString().split('T')[0];
    $('#rec_date').val(formattedCurrentDate);

    // Call the function to set expected_date on initial load
    setExpectedDate();

    // Add event listeners for the rec_date input
    $('#rec_date').on('change', setExpectedDate);

    // Add an event listener for manually adjusting expected_date
    $('#expected_date').on('change', function () {
        // You can add validation or further handling here if needed
    });
});
</script>


        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
