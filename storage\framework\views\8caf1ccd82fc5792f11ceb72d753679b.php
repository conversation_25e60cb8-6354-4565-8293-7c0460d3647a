<div>
    <!-- Modern Drag & Drop File Upload Zones -->
    <style>
        .upload-zone {
            border: 2px dashed #e0e6ed;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-zone:hover {
            border-color: #0d6efd;
            background: linear-gradient(135deg, #e7f3ff 0%, #f8f9fa 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
        }

        .upload-zone.drag-over {
            border-color: #198754;
            background: linear-gradient(135deg, #d1e7dd 0%, #f8f9fa 100%);
            transform: scale(1.02);
            box-shadow: 0 12px 35px rgba(25, 135, 84, 0.2);
        }

        .upload-zone.uploading {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd 0%, #f8f9fa 100%);
        }

        .upload-icon {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .upload-zone:hover .upload-icon {
            transform: scale(1.1);
        }

        .upload-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background: linear-gradient(90deg, #0d6efd, #198754);
            transition: width 0.3s ease;
            border-radius: 0 0 12px 12px;
        }

        .file-count-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #0d6efd, #6610f2);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            font-weight: bold;
        }
    </style>

    <div class="row g-4">
        <?php
            $uploadTypes = [
                'ok' => [
                    'label' => 'Fatawa For Ok',
                    'icon' => 'fas fa-check-circle',
                    'model' => 'ok',
                    'data' => 'ok',
                    'color' => 'text-success',
                    'description' => 'Upload approved fatawa files'
                ],
                'mahlENazar' => [
                    'label' => 'Fatawa For Mahl-e-Nazar',
                    'icon' => 'fas fa-eye',
                    'model' => 'mahlENazar',
                    'data' => 'Mahl-e-Nazar',
                    'color' => 'text-info',
                    'description' => 'Upload files for review'
                ],
                'tahqiqi' => [
                    'label' => 'Fatawa For Tahqiqi',
                    'icon' => 'fas fa-search',
                    'model' => 'tahqiqi',
                    'data' => 'Tahqiqi',
                    'color' => 'text-warning',
                    'description' => 'Upload research files'
                ],
                'other' => [
                    'label' => 'Other Files',
                    'icon' => 'fas fa-paperclip',
                    'model' => 'other',
                    'data' => 'Other',
                    'color' => 'text-secondary',
                    'description' => 'Upload miscellaneous files'
                ],
            ];
        ?>

        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $uploadTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-lg-3 col-md-6">
            <div class="upload-zone p-4 text-center position-relative"
                 data-upload-type="<?php echo e($key); ?>"
                 onclick="document.getElementById('<?php echo e($key); ?>').click()">

                <!--[if BLOCK]><![endif]--><?php if(isset($fileList[$type['data']]) && count($fileList[$type['data']]) > 0): ?>
                    <div class="file-count-badge"><?php echo e(count($fileList[$type['data']])); ?></div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <div wire:loading.remove wire:target="<?php echo e($type['model']); ?>">
                    <i class="<?php echo e($type['icon']); ?> upload-icon <?php echo e($type['color']); ?>"></i>
                    <h6 class="fw-bold text-dark mb-2"><?php echo e($type['label']); ?></h6>
                    <p class="text-muted small mb-2"><?php echo e($type['description']); ?></p>
                    <div class="d-flex align-items-center justify-content-center gap-2">
                        <i class="fas fa-cloud-upload-alt text-primary"></i>
                        <span class="text-primary fw-medium">Click or drag files here</span>
                    </div>
                    <small class="text-muted d-block mt-2">Supports multiple files</small>
                </div>

                <div wire:loading wire:target="<?php echo e($type['model']); ?>" class="text-center">
                    <div class="spinner-border text-primary mb-2" role="status">
                        <span class="visually-hidden">Uploading...</span>
                    </div>
                    <p class="text-primary fw-medium mb-0">Uploading files...</p>
                    <div class="upload-progress" style="width: 75%;"></div>
                </div>

                <input type="file"
                       id="<?php echo e($key); ?>"
                       wire:model.live="<?php echo e($type['model']); ?>"
                       data-label="<?php echo e($type['data']); ?>"
                       multiple
                       class="d-none"
                       accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Uploaded File List -->
    <!--[if BLOCK]><![endif]--><?php if(!empty($fileList) && collect($fileList)->flatten()->isNotEmpty()): ?>
    <div class="mt-4">
        <h6 class="mb-3">Uploaded Files:</h6>
        <div class="accordion" id="fileListAccordion">
            <?php $__currentLoopData = $fileList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $files): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <!--[if BLOCK]><![endif]--><?php if(count($files) > 0): ?>
                <div class="accordion-item mb-2">
                    <h2 class="accordion-header" id="heading-<?php echo e($type); ?>">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-<?php echo e($type); ?>" aria-expanded="false" aria-controls="collapse-<?php echo e($type); ?>">
                            <?php echo e(ucfirst(str_replace('-', ' ', $type))); ?> Files
                            <span class="badge rounded-pill bg-primary ms-2"><?php echo e(count($files)); ?></span>
                        </button>
                    </h2>
                    <div id="collapse-<?php echo e($type); ?>" class="accordion-collapse collapse" aria-labelledby="heading-<?php echo e($type); ?>" data-bs-parent="#fileListAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-group list-group-flush">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        // This logic is preserved
                                        $baseFolder = explode('/', $file['folder'])[0];
                                    ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="fw-bold"><?php echo e($file['name']); ?></span><br>
                                            <span class="text-muted small">Folder: <?php echo e($file['folder']); ?></span>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="<?php echo e(route('viewCheck', ['date' => $baseFolder, 'folder' => $type, 'filename' => $file['name']])); ?>" target="_blank" class="btn btn-sm btn-outline-info mb-0">View</a>
                                            <button wire:click="removeFile('<?php echo e($type); ?>', <?php echo e($index); ?>)" class="btn btn-sm btn-outline-danger mb-0">Remove</button>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Session message (no change in logic) -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="alert alert-success mt-4 alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo e(session('message')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Drag & Drop JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const uploadZones = document.querySelectorAll('.upload-zone');

            uploadZones.forEach(zone => {
                const uploadType = zone.getAttribute('data-upload-type');
                const fileInput = zone.querySelector('input[type="file"]');

                // Prevent default drag behaviors
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    zone.addEventListener(eventName, preventDefaults, false);
                    document.body.addEventListener(eventName, preventDefaults, false);
                });

                // Highlight drop zone when item is dragged over it
                ['dragenter', 'dragover'].forEach(eventName => {
                    zone.addEventListener(eventName, () => {
                        zone.classList.add('drag-over');
                    }, false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    zone.addEventListener(eventName, () => {
                        zone.classList.remove('drag-over');
                    }, false);
                });

                // Handle dropped files
                zone.addEventListener('drop', handleDrop, false);

                function preventDefaults(e) {
                    e.preventDefault();
                    e.stopPropagation();
                }

                function handleDrop(e) {
                    const dt = e.dataTransfer;
                    const files = dt.files;

                    if (files.length > 0) {
                        // Create a new FileList-like object
                        const fileArray = Array.from(files);

                        // Validate file types
                        const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
                        const validFiles = fileArray.filter(file => {
                            const extension = '.' + file.name.split('.').pop().toLowerCase();
                            return allowedTypes.includes(extension);
                        });

                        if (validFiles.length !== fileArray.length) {
                            alert('Some files were skipped. Only PDF, DOC, DOCX, JPG, JPEG, and PNG files are allowed.');
                        }

                        if (validFiles.length > 0) {
                            // Create a new DataTransfer object to set files
                            const dataTransfer = new DataTransfer();
                            validFiles.forEach(file => dataTransfer.items.add(file));

                            // Set the files to the input
                            fileInput.files = dataTransfer.files;

                            // Trigger the change event to notify Livewire
                            const event = new Event('change', { bubbles: true });
                            fileInput.dispatchEvent(event);

                            // Add uploading class
                            zone.classList.add('uploading');

                            // Remove uploading class after a delay (simulated)
                            setTimeout(() => {
                                zone.classList.remove('uploading');
                            }, 2000);
                        }
                    }
                }
            });

            // Add file count animation
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        const badges = document.querySelectorAll('.file-count-badge');
                        badges.forEach(badge => {
                            badge.style.animation = 'none';
                            badge.offsetHeight; // Trigger reflow
                            badge.style.animation = 'pulse 0.5s ease-in-out';
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });

        // Add CSS animation for badge pulse
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</div><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/file-upload.blade.php ENDPATH**/ ?>