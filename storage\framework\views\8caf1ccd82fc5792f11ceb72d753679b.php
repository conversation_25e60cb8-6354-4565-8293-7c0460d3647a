<div>
    <!-- File Upload Zones - Modernized UI -->
    <div class="row g-3">
        <?php
            $uploadTypes = [
                'ok' => ['label' => 'Fatawa For Ok', 'icon' => 'check_circle', 'model' => 'ok', 'data' => 'ok'],
                'mahlENazar' => ['label' => 'Fatawa For Mahl-e-Nazar', 'icon' => 'visibility', 'model' => 'mahlENazar', 'data' => 'Mahl-e-Nazar'],
                'tahqiqi' => ['label' => 'Fatawa For Tahqiqi', 'icon' => 'search', 'model' => 'tahqiqi', 'data' => 'Tahqiqi'],
                'other' => ['label' => 'Other Files', 'icon' => 'attach_file', 'model' => 'other', 'data' => 'Other'],
            ];
        ?>

        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $uploadTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-md-3">
            <label for="<?php echo e($key); ?>" class="border rounded-3 p-3 text-center d-flex flex-column justify-content-center align-items-center w-100" style="cursor: pointer; min-height: 120px; background-color: #f8f9fa;">
                <i class="material-icons text-success mb-2 fs-3"><?php echo e($type['icon']); ?></i>
                <span class="text-dark fw-bold small"><?php echo e($type['label']); ?></span>
                <span class="text-muted small">Click to upload</span>
                <input type="file" id="<?php echo e($key); ?>" wire:model.live="<?php echo e($type['model']); ?>" data-label="<?php echo e($type['data']); ?>" multiple class="d-none">
            </label>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Uploaded File List -->
    <!--[if BLOCK]><![endif]--><?php if(!empty($fileList) && collect($fileList)->flatten()->isNotEmpty()): ?>
    <div class="mt-4">
        <h6 class="mb-3">Uploaded Files:</h6>
        <div class="accordion" id="fileListAccordion">
            <?php $__currentLoopData = $fileList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $files): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <!--[if BLOCK]><![endif]--><?php if(count($files) > 0): ?>
                <div class="accordion-item mb-2">
                    <h2 class="accordion-header" id="heading-<?php echo e($type); ?>">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-<?php echo e($type); ?>" aria-expanded="false" aria-controls="collapse-<?php echo e($type); ?>">
                            <?php echo e(ucfirst(str_replace('-', ' ', $type))); ?> Files
                            <span class="badge rounded-pill bg-primary ms-2"><?php echo e(count($files)); ?></span>
                        </button>
                    </h2>
                    <div id="collapse-<?php echo e($type); ?>" class="accordion-collapse collapse" aria-labelledby="heading-<?php echo e($type); ?>" data-bs-parent="#fileListAccordion">
                        <div class="accordion-body p-0">
                            <ul class="list-group list-group-flush">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        // This logic is preserved
                                        $baseFolder = explode('/', $file['folder'])[0];
                                    ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <span class="fw-bold"><?php echo e($file['name']); ?></span><br>
                                            <span class="text-muted small">Folder: <?php echo e($file['folder']); ?></span>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="<?php echo e(route('viewCheck', ['date' => $baseFolder, 'folder' => $type, 'filename' => $file['name']])); ?>" target="_blank" class="btn btn-sm btn-outline-info mb-0">View</a>
                                            <button wire:click="removeFile('<?php echo e($type); ?>', <?php echo e($index); ?>)" class="btn btn-sm btn-outline-danger mb-0">Remove</button>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </ul>
                        </div>
                    </div>
                </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Session message (no change in logic) -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="alert alert-success mt-3">
            <?php echo e(session('message')); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/file-upload.blade.php ENDPATH**/ ?>