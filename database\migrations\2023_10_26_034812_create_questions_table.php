<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('questions', function (Blueprint $table) {
            $table->id();
            $table->date('rec_date');
            $table->enum('question_type',['Daily', 'Email']);
            $table->string('ifta_code')->unique(); // Add the unique constraint here
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('sayel');
            $table->string('address')->nullable();
            $table->string('issue');
            $table->string('sub_issue');
            $table->string('question_title');
            $table->date('expected_date');
            $table->string('question_branch');
            $table->text('question');
            $table->string('assign_id')->nullable();
            $table->date('mujeeb_send_date')->nullable();
            $table->string('uploaded')->nullable();
            $table->boolean('send_to_mufti')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questions');
    }
};
