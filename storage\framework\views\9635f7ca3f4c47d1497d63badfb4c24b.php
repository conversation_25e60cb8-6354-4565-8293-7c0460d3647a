<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Lock System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4"><i class="fas fa-cogs"></i> Performance Lock System Test</h1>
                
                <!-- System Status -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle"></i> System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Today's Date:</strong> <?php echo e(now()->format('M d, Y (l)')); ?></p>
                                <p><strong>Is Sunday:</strong> 
                                    <?php if(now()->isSunday()): ?>
                                        <span class="badge bg-warning">Yes (Performance Not Required)</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">No (Performance Required)</span>
                                    <?php endif; ?>
                                </p>
                                <p><strong>Performance Required Today:</strong> 
                                    <?php if(\App\Models\PerformanceHoliday::requiresPerformance(now())): ?>
                                        <span class="badge bg-success">Yes</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">No (Holiday/Sunday)</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Total Holidays:</strong> <?php echo e(\App\Models\PerformanceHoliday::count()); ?></p>
                                <p><strong>Active Restrictions:</strong> <?php echo e(\App\Models\UserRestriction::where('is_active', true)->count()); ?></p>
                                <p><strong>Performance Restrictions:</strong> <?php echo e(\App\Models\UserRestriction::where('restriction_type', 'performance_not_submitted')->where('is_active', true)->count()); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users with Tasks -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-users"></i> Users with Assigned Tasks</h5>
                    </div>
                    <div class="card-body">
                        <?php
                            $usersWithTasks = \App\Models\User::whereHas('assignedTasks', function($q) {
                                $q->whereNotIn('status', ['completed', 'cancelled']);
                            })->with(['assignedTasks', 'activeRestrictions'])->get();
                        ?>
                        
                        <?php if($usersWithTasks->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Active Tasks</th>
                                            <th>Today's Performance</th>
                                            <th>Active Restrictions</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $usersWithTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($user->name); ?></strong><br>
                                                <small class="text-muted"><?php echo e($user->email); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo e($user->assignedTasks->whereNotIn('status', ['completed', 'cancelled'])->count()); ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                    $hasSubmittedToday = \App\Models\DailyPerformance::where('user_id', $user->id)
                                                        ->whereDate('created_at', now()->format('Y-m-d'))
                                                        ->exists();
                                                ?>
                                                <?php if($hasSubmittedToday): ?>
                                                    <span class="badge bg-success">Submitted</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Not Submitted</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($user->activeRestrictions->count() > 0): ?>
                                                    <?php $__currentLoopData = $user->activeRestrictions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $restriction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <span class="badge bg-warning mb-1"><?php echo e($restriction->restriction_type); ?></span><br>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php else: ?>
                                                    <span class="badge bg-success">None</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($user->activeRestrictions->where('restriction_type', 'performance_not_submitted')->count() > 0): ?>
                                                    <span class="badge bg-danger">Account Locked</span>
                                                <?php elseif(!$hasSubmittedToday && \App\Models\PerformanceHoliday::requiresPerformance(now())): ?>
                                                    <span class="badge bg-warning">Performance Due</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No users with assigned tasks found.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Holidays -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-calendar-alt"></i> Recent & Upcoming Holidays</h5>
                    </div>
                    <div class="card-body">
                        <?php
                            $holidays = \App\Models\PerformanceHoliday::where('holiday_date', '>=', now()->subDays(30))
                                ->where('holiday_date', '<=', now()->addDays(30))
                                ->orderBy('holiday_date')
                                ->get();
                        ?>
                        
                        <?php if($holidays->count() > 0): ?>
                            <div class="row">
                                <?php $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-4 mb-3">
                                    <div class="card border-left-<?php echo e($holiday->holiday_date->isPast() ? 'secondary' : 'primary'); ?>">
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo e($holiday->name); ?></h6>
                                            <p class="card-text">
                                                <small class="text-muted"><?php echo e($holiday->holiday_date->format('M d, Y (l)')); ?></small><br>
                                                <span class="badge bg-<?php echo e($holiday->type === 'religious' ? 'success' : ($holiday->type === 'national' ? 'info' : 'secondary')); ?>">
                                                    <?php echo e(ucfirst($holiday->type)); ?>

                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No holidays found in the current period.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Test Actions -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-tools"></i> Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>For Admin Users:</h6>
                                <a href="<?php echo e(route('performance-holidays')); ?>" class="btn btn-primary mb-2">
                                    <i class="fas fa-calendar-alt"></i> Manage Holidays
                                </a><br>
                                <a href="<?php echo e(route('locked-accounts')); ?>" class="btn btn-danger mb-2">
                                    <i class="fas fa-lock"></i> Manage Locked Accounts
                                </a>
                            </div>
                            <div class="col-md-6">
                                <h6>For Regular Users:</h6>
                                <a href="<?php echo e(route('daily-performance.create')); ?>" class="btn btn-success mb-2">
                                    <i class="fas fa-clipboard-check"></i> Submit Performance
                                </a><br>
                                <a href="<?php echo e(route('my-performance')); ?>" class="btn btn-info mb-2">
                                    <i class="fas fa-chart-line"></i> View My Performance
                                </a>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> How the System Works:</h6>
                            <ol>
                                <li><strong>Daily Check:</strong> Users with assigned tasks must submit daily performance reports</li>
                                <li><strong>Smart Exclusions:</strong> Sundays and configured holidays are automatically excluded</li>
                                <li><strong>Account Locking:</strong> Users who miss performance submissions get their accounts locked</li>
                                <li><strong>Admin Control:</strong> Admins can manage holidays and unlock accounts</li>
                                <li><strong>Automatic Recovery:</strong> Accounts unlock when performance is submitted</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/test-performance-system.blade.php ENDPATH**/ ?>