@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Task Details</h5>
                            <p class="text-sm mb-0">
                                {{ $task->title }}
                                <span class="badge bg-gradient-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning')) }} ms-2">
                                    {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                </span>
                            </p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                @can('update', $task)
                                    <a href="{{ route('workflow-tasks.edit', $task) }}" class="btn btn-outline-primary btn-sm mb-0 me-2">
                                        <i class="fas fa-edit"></i>&nbsp;&nbsp;Edit
                                    </a>
                                @endcan
                                <a href="{{ route('workflow-tasks.index') }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to Tasks
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Details -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Task Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="mb-3">Basic Information</h6>
                                    <p class="mb-2"><strong>Title:</strong> {{ $task->title }}</p>
                                    <p class="mb-2"><strong>Type:</strong> 
                                        <span class="badge bg-{{ $task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary') }}">
                                            {{ ucfirst($task->type) }}
                                        </span>
                                    </p>
                                    <p class="mb-2"><strong>Priority:</strong> 
                                        <span class="badge bg-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </p>
                                    <p class="mb-2"><strong>Status:</strong> 
                                        <span class="badge bg-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning')) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </p>
                                    <p class="mb-0"><strong>Created:</strong> {{ $task->created_at->format('M d, Y H:i') }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Assignment Information -->
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="mb-3">Assignment Information</h6>
                                    <p class="mb-2"><strong>Assigned To:</strong> {{ $task->assignedTo->name ?? 'Not Assigned' }}</p>
                                    @if($task->assignedTo)
                                        <p class="mb-2"><strong>Email:</strong> {{ $task->assignedTo->email }}</p>
                                    @endif
                                    <p class="mb-2"><strong>Assigned By:</strong> {{ $task->assignedBy->name ?? 'System' }}</p>
                                    <p class="mb-2"><strong>Department:</strong> {{ $task->department->name ?? 'No Department' }}</p>
                                    <p class="mb-2"><strong>Due Date:</strong> 
                                        @if($task->due_date)
                                            {{ $task->due_date->format('M d, Y') }}
                                            @if($task->isOverdue())
                                                <span class="badge bg-danger ms-1">Overdue</span>
                                            @endif
                                        @else
                                            <span class="text-muted">No due date</span>
                                        @endif
                                    </p>
                                    @if($task->completed_at)
                                        <p class="mb-0"><strong>Completed:</strong> {{ $task->completed_at->format('M d, Y H:i') }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Task Content -->
                    <div class="row mt-4">
                        <div class="col-12">
                            @if($task->description)
                                <div class="mb-4">
                                    <h6 class="mb-2">Description</h6>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p class="mb-0">{{ $task->description }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($task->completion_notes && $task->status === 'completed')
                                <div class="mb-4">
                                    <h6 class="mb-2">Completion Notes</h6>
                                    <div class="alert alert-success">
                                        <p class="mb-0">{{ $task->completion_notes }}</p>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Actions -->
                    @if($task->assigned_to === auth()->id() && $task->status !== 'completed' && $task->status !== 'cancelled')
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="mb-3">Task Actions</h6>
                                        <div class="d-flex gap-2">
                                            @if($task->status === 'pending')
                                                <form action="{{ route('workflow-tasks.change-status', $task) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="status" value="in_progress">
                                                    <button type="submit" class="btn btn-primary">
                                                        <i class="fas fa-play me-2"></i>Start Task
                                                    </button>
                                                </form>
                                            @elseif($task->status === 'in_progress')
                                                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#completeModal">
                                                    <i class="fas fa-check me-2"></i>Complete Task
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Complete Task Modal -->
@if($task->assigned_to === auth()->id() && $task->status === 'in_progress')
<div class="modal fade" id="completeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-check text-success me-2"></i>
                    Complete Task
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('workflow-tasks.change-status', $task) }}" method="POST">
                @csrf
                @method('PATCH')
                <input type="hidden" name="status" value="completed">
                <div class="modal-body">
                    <p class="mb-3">Are you sure you want to mark this task as completed?</p>
                    <div class="form-group">
                        <label class="form-label">Completion Notes (Optional)</label>
                        <textarea name="completion_notes" class="form-control" rows="3" 
                                  placeholder="Add any notes about the completion of this task..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check me-2"></i>Complete Task
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection
