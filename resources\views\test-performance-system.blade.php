<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Lock System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4"><i class="fas fa-cogs"></i> Performance Lock System Test</h1>
                
                <!-- System Status -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-info-circle"></i> System Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Today's Date:</strong> {{ now()->format('M d, Y (l)') }}</p>
                                <p><strong>Is Sunday:</strong> 
                                    @if(now()->isSunday())
                                        <span class="badge bg-warning">Yes (Performance Not Required)</span>
                                    @else
                                        <span class="badge bg-success">No (Performance Required)</span>
                                    @endif
                                </p>
                                <p><strong>Performance Required Today:</strong> 
                                    @if(\App\Models\PerformanceHoliday::requiresPerformance(now()))
                                        <span class="badge bg-success">Yes</span>
                                    @else
                                        <span class="badge bg-warning">No (Holiday/Sunday)</span>
                                    @endif
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Total Holidays:</strong> {{ \App\Models\PerformanceHoliday::count() }}</p>
                                <p><strong>Active Restrictions:</strong> {{ \App\Models\UserRestriction::where('is_active', true)->count() }}</p>
                                <p><strong>Performance Restrictions:</strong> {{ \App\Models\UserRestriction::where('restriction_type', 'performance_not_submitted')->where('is_active', true)->count() }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users with Tasks -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-users"></i> Users with Assigned Tasks</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $usersWithTasks = \App\Models\User::whereHas('assignedTasks', function($q) {
                                $q->whereNotIn('status', ['completed', 'cancelled']);
                            })->with(['assignedTasks', 'activeRestrictions'])->get();
                        @endphp
                        
                        @if($usersWithTasks->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Active Tasks</th>
                                            <th>Today's Performance</th>
                                            <th>Active Restrictions</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($usersWithTasks as $user)
                                        <tr>
                                            <td>
                                                <strong>{{ $user->name }}</strong><br>
                                                <small class="text-muted">{{ $user->email }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary">{{ $user->assignedTasks->whereNotIn('status', ['completed', 'cancelled'])->count() }}</span>
                                            </td>
                                            <td>
                                                @php
                                                    $hasSubmittedToday = \App\Models\DailyPerformance::where('user_id', $user->id)
                                                        ->whereDate('created_at', now()->format('Y-m-d'))
                                                        ->exists();
                                                @endphp
                                                @if($hasSubmittedToday)
                                                    <span class="badge bg-success">Submitted</span>
                                                @else
                                                    <span class="badge bg-danger">Not Submitted</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($user->activeRestrictions->count() > 0)
                                                    @foreach($user->activeRestrictions as $restriction)
                                                        <span class="badge bg-warning mb-1">{{ $restriction->restriction_type }}</span><br>
                                                    @endforeach
                                                @else
                                                    <span class="badge bg-success">None</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($user->activeRestrictions->where('restriction_type', 'performance_not_submitted')->count() > 0)
                                                    <span class="badge bg-danger">Account Locked</span>
                                                @elseif(!$hasSubmittedToday && \App\Models\PerformanceHoliday::requiresPerformance(now()))
                                                    <span class="badge bg-warning">Performance Due</span>
                                                @else
                                                    <span class="badge bg-success">Active</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No users with assigned tasks found.
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Recent Holidays -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-calendar-alt"></i> Recent & Upcoming Holidays</h5>
                    </div>
                    <div class="card-body">
                        @php
                            $holidays = \App\Models\PerformanceHoliday::where('holiday_date', '>=', now()->subDays(30))
                                ->where('holiday_date', '<=', now()->addDays(30))
                                ->orderBy('holiday_date')
                                ->get();
                        @endphp
                        
                        @if($holidays->count() > 0)
                            <div class="row">
                                @foreach($holidays as $holiday)
                                <div class="col-md-4 mb-3">
                                    <div class="card border-left-{{ $holiday->holiday_date->isPast() ? 'secondary' : 'primary' }}">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $holiday->name }}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">{{ $holiday->holiday_date->format('M d, Y (l)') }}</small><br>
                                                <span class="badge bg-{{ $holiday->type === 'religious' ? 'success' : ($holiday->type === 'national' ? 'info' : 'secondary') }}">
                                                    {{ ucfirst($holiday->type) }}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No holidays found in the current period.
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Test Actions -->
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h5><i class="fas fa-tools"></i> Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>For Admin Users:</h6>
                                <a href="{{ route('performance-holidays') }}" class="btn btn-primary mb-2">
                                    <i class="fas fa-calendar-alt"></i> Manage Holidays
                                </a><br>
                                <a href="{{ route('locked-accounts') }}" class="btn btn-danger mb-2">
                                    <i class="fas fa-lock"></i> Manage Locked Accounts
                                </a>
                            </div>
                            <div class="col-md-6">
                                <h6>For Regular Users:</h6>
                                <a href="{{ route('daily-performance.create') }}" class="btn btn-success mb-2">
                                    <i class="fas fa-clipboard-check"></i> Submit Performance
                                </a><br>
                                <a href="{{ route('my-performance') }}" class="btn btn-info mb-2">
                                    <i class="fas fa-chart-line"></i> View My Performance
                                </a>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-lightbulb"></i> How the System Works:</h6>
                            <ol>
                                <li><strong>Daily Check:</strong> Users with assigned tasks must submit daily performance reports</li>
                                <li><strong>Smart Exclusions:</strong> Sundays and configured holidays are automatically excluded</li>
                                <li><strong>Account Locking:</strong> Users who miss performance submissions get their accounts locked</li>
                                <li><strong>Admin Control:</strong> Admins can manage holidays and unlock accounts</li>
                                <li><strong>Automatic Recovery:</strong> Accounts unlock when performance is submitted</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
