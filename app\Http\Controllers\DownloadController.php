<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class DownloadController extends Controller
{
   public function downloadWord(Request $request)
{
     // Get the ifta_code from the request
     $iftaCode = $request->input('ifta_code', '');

     $iftaPrefix = '';
    if (!empty($iftaCode) && strpos($iftaCode, '-') !== false) {
        $iftaPrefix = explode('-', $iftaCode)[0];
    }

    // Determine the additional content based on the ifta_code prefix
    $additionalContent = '';
    switch ($iftaPrefix) {
        case 'Faj':
            $additionalContent = "جامع مسجد رضائے مصطفی کورنگی 4،کراچی";
            break;
        case 'Nor':
            $additionalContent = "جامع مسجد سید معصوم شاہ بخاری،پولیس چوکی کھارادر کراچی، پاکستان";
            break;
        case 'Gul':
            $additionalContent = "فیضانِ مدینہ، علی ٹاؤن، گلزارِ طیبہ، سرگودھا(پاکستان)";
            break;
        case 'IEC':
            $additionalContent = "عالمی مدنی مرکزفیضان مدینہ،پرانی سبزی منڈی ،یونیورسٹی روڈ، کراچی، پاکستان";
            break;
        default:
            $additionalContent = '';
    }

    // Debugging to confirm values
    // dd($iftaCode, $iftaPrefix, $additionalContent);

    // Pre-defined header content
    $headerContent = "
        <p dir='RTL' style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:Calibri,sans-serif;margin-bottom:0in;line-height:27.0pt;'>
            <span style='font-size:19px;font-family:Al_Mushaf;color:black;'>بسمِ اللہ الرَّحمنِ ا لرَّ حِیم</span>
        </p>
        <p dir='RTL' style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:Calibri,sans-serif;margin-bottom:0in;'>
            <span style='font-size:17px;font-family:Dubai Unicode;color:black;'>الصلوۃ والسلام علیک یارسول اللہ&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;وعلی آلک واصحبک یا حبیب اللہ</span>
        </p>
        <p dir='RTL' style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:Calibri,sans-serif;margin-bottom:0in;line-height:80%;'>
            <span style='font-size:80px;line-height:80%;font-family:Makkah Contour Unicode;color:black;'>دارالافتاء اہلسنت</span>
        </p>
         <p dir='RTL' style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:Calibri,sans-serif;margin-bottom:0in;line-height:80%;'>
            <span style='font-size:19px;font-family:Asad;color:black;'>$additionalContent</span>
        </p>
    ";
    $content = $request->input('content', 'No content provided.');

    // Combine the header with the provided content
    $combinedContent = $headerContent . $content;

    // Remove <br> tags and update font-family syntax
  // Remove class attributes from <br> tags
  $combinedContent = preg_replace('/\n/', '<br />', $combinedContent);

$combinedContent = preg_replace('/<br\s*\/?>/i', ' <br> ', $combinedContent);


// Process the content further (removing unnecessary tags and fixing font-family)
$processedContent = str_replace(
    ['\n', "&quot;", '</o:p>', '<o:p>', '<br>', "font-family:'Jameel Noori Nastaleeq';", "font-family:'Al_Mushaf';", "font-family:'Trad Arabic Bold Unicode';", "font-family: 'Jameel Noori Nastaleeq';", "font-family: 'Naskh Unicode';"],
    [" ", '', '', '', '', "font-family:Jameel Noori Nastaleeq;", "font-family:Al_Mushaf;", "font-family:Trad Arabic Bold Unicode;", "font-family:Jameel Noori Nastaleeq;", "font-family:Naskh Unicode;"],
    $combinedContent
);


// Additional regex to fix <br> followed by </div> (invalid structure)
$processedContent = preg_replace([
    '/<br[^>]*>\s*<\/div>/i',  // Fixes <br...></div>
    '/<br\s*\/?>/i'            // Ensures proper <br> or <br />
], [
    '</div>',                  // Replace <br...></div> with </div>
    '<br>'                     // Normalize <br> to a standard format
], $processedContent);


    // Create a new PhpWord instance
    $phpWord = new PhpWord();

    // Add a section to the Word document
    $section = $phpWord->addSection();

    // Add the processed HTML content
    \PhpOffice\PhpWord\Shared\Html::addHtml($section, $processedContent, false, false);

    // Define the file name
    $fileName = $iftaCode ? "$iftaCode.docx" : 'record_content.docx';

    // Save the file to a temporary path
    $tempFile = tempnam(sys_get_temp_dir(), 'word');
    $phpWordWriter = IOFactory::createWriter($phpWord, 'Word2007');
    $phpWordWriter->save($tempFile);

    // Return the file as a response for download
    return response()->download($tempFile, $fileName)->deleteFileAfterSend(true);
}


    /**
     * Sanitize and clean HTML content.
     *
     * @param string $html
     * @return string
     */
    private function sanitizeHtml($html)
{
    // Use DOMDocument to clean and fix HTML
    $dom = new \DOMDocument();

    // Suppress warnings from invalid HTML and load the content
    libxml_use_internal_errors(true);
    $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'), LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    libxml_clear_errors();

    // Save and return the cleaned HTML
    return $dom->saveHTML();
}
}
