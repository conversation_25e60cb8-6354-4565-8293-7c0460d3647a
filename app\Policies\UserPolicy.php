<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine whether the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        return $user->isNazim() || $user->isSuperior();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        // Users can view their own profile
        if ($user->id === $model->id) {
            return true;
        }

        // Nazim can view all users
        if ($user->isNazim()) {
            return true;
        }

        // Superior can view their assistants
        if ($user->isSuperior()) {
            return $user->assistants->contains($model);
        }

        return false;
    }

    /**
     * Determine whether the user can create users.
     */
    public function create(User $user): bool
    {
        return $user->isNazim();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // Users can update their own profile (limited fields)
        if ($user->id === $model->id) {
            return true;
        }

        // Nazim can update all users
        return $user->isNazim();
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        return $user->isNazim() && $user->id !== $model->id;
    }

    /**
     * Determine whether the user can assign roles.
     */
    public function assignRoles(User $user, User $model): bool
    {
        return $user->isNazim();
    }

    /**
     * Determine whether the user can assign departments.
     */
    public function assignDepartments(User $user, User $model): bool
    {
        return $user->isNazim();
    }

    /**
     * Determine whether the user can assign assistants.
     */
    public function assignAssistants(User $user, User $model): bool
    {
        return $user->isNazim();
    }

    /**
     * Determine whether the user can view performance of another user.
     */
    public function viewPerformance(User $user, User $model): bool
    {
        // Users can view their own performance
        if ($user->id === $model->id) {
            return true;
        }

        // Nazim can view all performance
        if ($user->isNazim()) {
            return true;
        }

        // Superior can view their assistants' performance
        if ($user->isSuperior()) {
            return $user->assistants->contains($model);
        }

        return false;
    }

    /**
     * Determine whether the user can unlock another user.
     */
    public function unlock(User $user, User $model): bool
    {
        return $user->isNazim();
    }
}
