<!-- Summary Table Section -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Darulifta and Reception Date Summary
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="table-responsive">
            <table class="table modern-table align-items-center mb-0">
                <thead>
                    <tr>
                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                            <i class="fas fa-building me-1"></i>
                            Darulifta
                        </th>
                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Reception Dates
                        </th>
                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                            <i class="fas fa-chart-bar me-1"></i>
                            Statistics
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $totalCounts = 0;
                        $overallFolderCount = 0;
                        $overallEmailCount = 0;
                        $overallDailyCount = 0;
                        $overallNullAssignIdCount = 0;
                        $overallNotSentIftaCodeCount = 0;
                        $notSentIftaCodes = $this->notsentiftacode->pluck('ifta_code')->toArray();
                    @endphp
                    
                    @foreach($daruliftaNames as $daruliftaName)
                        @if(isset($reciptionFatawa[$daruliftaName]))
                            @php
                                $daruliftaTotalCounts = 0;
                                $folderCounts = [];
                                $daruliftaEmailCount = 0;
                                $daruliftaDailyCount = 0;
                                $daruliftaNullAssignIdCount = 0;
                                $daruliftaNotSentIftaCodeCount = 0;
                            @endphp

                            @foreach($recdate as $mailfolderDates)
                                @if(isset($reciptionFatawa[$daruliftaName][$mailfolderDates]))
                                    @foreach($reciptionFatawa[$daruliftaName][$mailfolderDates] as $file)
                                        @php
                                            $folder = $file->rec_date;
                                            $folderCounts[$daruliftaName][$folder] = isset($folderCounts[$daruliftaName][$folder]) ? $folderCounts[$daruliftaName][$folder] + 1 : 1;
                                            $daruliftaTotalCounts++;
                                            $totalCounts++;

                                            if ($file->question_type == 'Email') {
                                                $daruliftaEmailCount++;
                                                $overallEmailCount++;
                                            } elseif ($file->question_type == 'Daily') {
                                                $daruliftaDailyCount++;
                                                $overallDailyCount++;
                                            }

                                            if (is_null($file->assign_id)) {
                                                $daruliftaNullAssignIdCount++;
                                                $overallNullAssignIdCount++;
                                            }

                                            if (in_array($file->ifta_code, $notSentIftaCodes)) {
                                                $daruliftaNotSentIftaCodeCount++;
                                                $overallNotSentIftaCodeCount++;
                                            }
                                        @endphp
                                    @endforeach
                                @endif
                            @endforeach

                            <tr class="table-row-hover">
                                <td>
                                    @php
                                        $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                    @endphp
                                    <div class="d-flex px-2 py-1">
                                        <div class="d-flex flex-column justify-content-center">
                                            <a href="{{ route('reciption-fatawa', [
                                                'darulifta' => $daruliftaName,
                                                'selectedmujeeb' => $this->selectedmujeeb,
                                                'selectedexclude' => $this->selectedexclude,
                                                'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                'startDate' => $tempStartDate,
                                                'endDate' => $tempEndDate,
                                            ]) }}&{{ $selectedMonthsQuery }}" class="text-decoration-none">
                                                <h6 class="mb-0 text-sm font-weight-bold text-primary">{{ $daruliftaName }}</h6>
                                            </a>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="folder-entries-modern">
                                        @php $foldercount = 0; @endphp
                                        @if(isset($folderCounts[$daruliftaName]))
                                            @foreach ($folderCounts[$daruliftaName] as $folder => $count)
                                            <div class="folder-entry-modern">
                                                <a href="{{ route('reciption-fatawa', [
                                                    'darulifta' => $daruliftaName,
                                                    'mailfolder' => $folder,
                                                    'selectedmujeeb' => $this->selectedmujeeb,
                                                    'selectedexclude' => $this->selectedexclude,
                                                    'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                    'startDate' => $tempStartDate,
                                                    'endDate' => $tempEndDate,
                                                ]) }}&{{ $selectedMonthsQuery }}" class="folder-link-modern">
                                                    <i class="fas fa-calendar-day me-1"></i>
                                                    {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                    <span class="badge bg-primary ms-1">{{ $count }}</span>
                                                </a>
                                            </div>
                                            @php
                                                $foldercount++;
                                                $overallFolderCount++;
                                            @endphp
                                            @endforeach
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <div class="stats-summary-professional">
                                        <div class="stat-item-professional">
                                            <span class="stat-label-professional">Fatawa:</span>
                                            <span class="stat-value-professional">{{ $daruliftaTotalCounts }}</span>
                                        </div>
                                        <div class="stat-item-professional">
                                            <span class="stat-label-professional">Folders:</span>
                                            <span class="stat-value-professional">{{ $foldercount }}</span>
                                        </div>
                                        <div class="stat-item-professional">
                                            <span class="stat-label-professional">Email:</span>
                                            <span class="stat-value-professional text-success">{{ $daruliftaEmailCount }}</span>
                                        </div>
                                        <div class="stat-item-professional">
                                            <span class="stat-label-professional">Daily:</span>
                                            <span class="stat-value-professional text-info">{{ $daruliftaDailyCount }}</span>
                                        </div>
                                        <div class="stat-item-professional">
                                            <span class="stat-label-professional">Not Assigned:</span>
                                            <span class="stat-value-professional text-danger">{{ $daruliftaNullAssignIdCount }}</span>
                                        </div>
                                        <div class="stat-item-professional">
                                            <span class="stat-label-professional">Not Sent:</span>
                                            <span class="stat-value-professional text-warning">{{ $daruliftaNotSentIftaCodeCount }}</span>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
