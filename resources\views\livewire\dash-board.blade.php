<div>
    <!-- Performance Notifications -->
    @if(auth()->user()->hasAssignedTasks() && !auth()->user()->isNazim())
        @php
            $hasSubmittedToday = auth()->user()->hasSubmittedTodaysPerformance();
            $requiresPerformance = \App\Models\PerformanceHoliday::requiresPerformance(now());
        @endphp

        @if($requiresPerformance && !$hasSubmittedToday)
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Daily Performance Report Required</h6>
                            <p class="mb-2">You haven't submitted your daily performance report for today ({{ now()->format('M d, Y') }}).</p>
                            <a href="{{ route('daily-performance.create') }}" class="btn btn-warning btn-sm">
                                <i class="fas fa-clipboard-check me-1"></i>
                                Submit Performance Report
                            </a>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        @endif
    @endif

    <!-- Account Lock Notification for Admins -->
    @if(auth()->user()->isNazim())
        @php
            $lockedAccountsCount = \App\Models\UserRestriction::where('is_active', true)->distinct('user_id')->count('user_id');
        @endphp

        @if($lockedAccountsCount > 0)
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-lock fa-2x me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Locked Accounts Require Attention</h6>
                            <p class="mb-2">{{ $lockedAccountsCount }} user account(s) are currently locked and may need your review.</p>
                            <a href="{{ route('locked-accounts') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-unlock me-1"></i>
                                Manage Locked Accounts
                            </a>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        </div>
        @endif
    @endif

    <div class="row no-gutters">
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4" wire:click="switchCard('remaining_status')">
    <div class="card" x-data="{ selectedTimeFrame: 'all' }">
                    <div class="card"  data-card-name="Remaining Status">
                        <div class="card-header p-3 pt-2">
                            <div
                                class="icon icon-lg icon-shape bg-gradient-dark shadow-dark text-center border-radius-xl mt-n5 position-absolute">
                                <i class="material-icons opacity-10">weekend</i>
                            </div>
                           
                            <div class="pt-1">
                                <div class=" mb-0 text-capitalize mb-0 text-center"><h6>Remaining Status</h6></div>
                                <div class="card custom-card mt-3 text-center" style="height: 25px;" data-card-name="Remaining Status">
                                    <p class=" mb-0 text-capitalize"><h5>Remaining Folder(
                                        @if ($remainfolderDate->count() > 0)
                                            {{ $remainfolderDate->count() }}
                                            @endif)</h5></p>
                                    </div>
                                    <div class="card custom-card mt-3 text-center" style="height: 25px;" data-card-name="Remaining Status">        
                                    <p class=" mb-0 text-capitalize"><h5>Remaining Fatawa(
                                
                                                @if ($unselectedFiles->count() > 0)
                                                {{ $unselectedFiles->count() }}
                                                @endif)</h5></p>
                                    </div>
                                    <div class="card custom-card mt-3 text-center" style="height: 35px;" data-card-name="Remaining Status">
                                        <label for="timeframe">Select Time Frame:</label>
                                        <select wire:model.live="selectedTimeFrame" id="timeframe">
                                            <option value="all" selected>All</option>
                                            <option value="this_month">This Month</option>
                                        </select>
                                    </div>
                                    
                            </div>
                        </div>
                        <hr class="dark horizontal my-0">
                        <div class="card-footer p-3">
                            <p class="mb-0"><span class="text-success  font-weight-bolder"></span>Total Till Today</p>
                        </div>
                    </div>
                </div>
</div>
<div class="col-xl-3 col-sm-6 mb-xl-0 mb-4" wire:click="switchCard('reciption_status')">
    <div class="card" data-card-name="Reciption Status">
        <div class="card-header p-3 pt-2">
            <div
                class="icon icon-lg icon-shape bg-gradient-primary shadow-primary text-center border-radius-xl mt-n5 position-absolute">
                <i class="material-icons opacity-10">verified_user</i>
            </div>
            <div class="pt-1">
                
                <div class=" mb-0 text-capitalize mb-0 text-center"><h6>Reciption Status</h6></div>
                <div class="card custom-card mt-3 text-center" style="height: 25px;" data-card-name="Reciption Status">
                <p class=" mb-0 text-capitalize"><h6>Reciption Total(
                    @if ($totalQuestionsCount > 0)
                    {{ $totalQuestionsCount }}
                    
                    @endif)</h6></p>
                </div>
                <div class="card custom-card mt-3 text-center" style="height: 25px;" data-card-name="Reciption Status">
                    <p class=" mb-0 text-capitalize"><h6>Total Sent(
                
                        @if ($sendToMufti > 0)
                        {{ $sendToMufti }}
                        @endif)</h6></p>
                    </div>
                    <div class="card custom-card mt-3 text-center" style="height: 35px;" data-card-name="Reciption Status">
                    <label for="status">Select Status:</label>
                    <select wire:model.live="selectedTimeFrame1" id="timeframe1">
            <option value="exclude_ok" selected>Exclude 'ok' All</option>
            <option value="exclude_ok_this_month">Exclude 'ok' This Month</option>
            <option value="exclude_ok_last_month">Exclude 'ok' Last Month</option>
            <option value="all">All</option>
            <option value="all_this_month">All of this Month</option>
            <option value="all_last_month">All of Last Month</option>
            
        </select>
    </div>  
            </div>
        </div>
        <hr class="dark horizontal my-0">
        <div class="card-footer p-3">
            <p class="mb-0"><span class="text-success  font-weight-bolder"></span>Monthly</p>
        </div>
    </div>
</div>

</div>
@if($selectedCard == 'remaining_status')
@php
    
    $totalCounts = 0; // Initialize a variable to store the overall total count
    $overallFolderCount = 0;
@endphp
<div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
    <div class="card mb-4" id="big-card-10" style="background-color: #FFFFCC; "> <!-- Added "mb-4" for margin at the bottom -->
        <div class="card-body">
            <h2 class="card-title">Darulifta And Mujeeb Summary of Remaining Fatawa</h2>       


            <table class="table">
                    <thead>
                        <tr>
                            <th>Darulifta</th>
                            <th>Remainig Folder</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($remainingData[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0; // Initialize a variable to store the total count for the current $daruliftaName
                                    
                                    
                                @endphp
                
                                @foreach($mailfolderDates as $mailfolderDate)
                                    @if(isset($remainingData[$daruliftaName][$mailfolderDate]))
                                        @foreach($remainingData[$daruliftaName][$mailfolderDate] as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$daruliftaName][$folder] = isset($folderCounts[$daruliftaName][$folder]) ? $folderCounts[$daruliftaName][$folder] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach
                
                                <tr>
                                    <td>
                                        <a href="{{ route('mahlenazar', ['role' => $daruliftaName]) }}" target="_blank">{{ $daruliftaName }}</a>
                                    </td>
                                    <td>
                                        @php
                                            $foldercount = 0;
                                        @endphp
                                        @foreach ($folderCounts[$daruliftaName] as $folder => $count)
                                        <a href="{{ route('mahlenazar', ['role' => $daruliftaName, 'fatwa_no' => $folder]) }}" target="_blank">
                                         {{ $folder }}</a>({{ $count }}) , 
                                         @php
                                         $foldercount++;    
                                         $overallFolderCount++;
                                         @endphp
                                         
                                        @endforeach
                                    </td>
                                    <td>
                                        Fatawa: {{ $daruliftaTotalCounts }} | Folder:{{$foldercount}}
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
                
                <h5>Overall Total Fatawa: {{ $totalCounts }} And Folder: {{ $overallFolderCount }}</h5>
        </div>
    </div>
</div>
@endif
@php
    
    $totalCounts = 0; // Initialize a variable to store the overall total count
    $overallFolderCount = 0;
@endphp
@if($selectedCard == 'reciption_status')
<div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
    <div class="card mb-4" id="big-card-10" style="background-color: #FFFFCC; "> <!-- Added "mb-4" for margin at the bottom -->
        <div class="card-body">
            <h2 class="card-title">Darulifta And Mujeeb Summary of Remaining Fatawa</h2>       


            <table class="table">
                    <thead>
                        <tr>
                            <th>Darulifta</th>
                            <th>Reciption Fatawa Date</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($reciptionData[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0; // Initialize a variable to store the total count for the current $daruliftaName
                                    
                                    
                                @endphp
                
                                @foreach($reciptDates as $mailfolderDate)
                                    @if(isset($reciptionData[$daruliftaName][$mailfolderDate]))
                                        @foreach($reciptionData[$daruliftaName][$mailfolderDate] as $file)
                                            @php
                                                $folder = $file->assign_id;
                                                $folderCounts[$daruliftaName][$folder] = isset($folderCounts[$daruliftaName][$folder]) ? $folderCounts[$daruliftaName][$folder] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach
                
                                <tr>
                                    <td>
                                        <a href="{{ route('mahlenazar', ['role' => $daruliftaName]) }}" target="_blank">{{ $daruliftaName }}</a>
                                    </td>
                                    <td>
                                        @php
                                            $foldercount = 0;
                                        @endphp
                                        @foreach ($folderCounts[$daruliftaName] as $folder => $count)
                                        <a href="{{ route('mahlenazar', ['role' => $daruliftaName, 'fatwa_no' => $folder]) }}" target="_blank">
                                         {{ $folder }}</a>({{ $count }}) , 
                                         @php
                                         $foldercount++;    
                                         $overallFolderCount++;
                                         @endphp
                                         
                                        @endforeach
                                    </td>
                                    <td>
                                        Fatawa: {{ $daruliftaTotalCounts }} | Folder:{{$foldercount}}
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
                
                <h5>Overall Total Fatawa: {{ $totalCounts }} And Folder: {{ $overallFolderCount }}</h5>
        </div>
    </div>
</div>
@endif
{{-- @push('scripts')
<script>
    document.getElementById('timeframe1').addEventListener('change', function () {
        Livewire.emit('updateSelectedTimeFrame', this.value);
    });
</script>
@endpush --}}

    
</div>
