<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\ViralChat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use DateTime;
use Illuminate\Http\Request;



class SelectedViral extends Component
{
    use WithPagination;

    public $filter;
    public $search;
    public $daruliftaNames;
    public $darulifta;
    public $daruliftalist;
    public $mujeebs = [];
    public $selectedmujeeb = 'all';
    public $selectedchecked = 'all';
    public $selectedTimeFrame = 'all';
    public $startDate;
    public $endDate;




    protected $queryString = [
        'filter' => ['except' => ''],
        'search' => ['except' => ''],
        'selectedmujeeb' => ['except' => 'all'],
        'selectedchecked' => ['except' => 'all'],
        'selectedTimeFrame' => ['except' => 'all'],
        'startDate' => ['except' => ''],
        'endDate' => ['except' => ''],
    ];

    public function mount($filter = null, $darulifta = null)
    {
        $this->filter = $filter;
        $this->darulifta = $darulifta;
        $this->loadDaruliftaNames();

        // Load filter values from the query string
        $this->selectedmujeeb = request()->query('selectedmujeeb', $this->selectedmujeeb);
        $this->selectedchecked = request()->query('selectedchecked', $this->selectedchecked);
        $this->selectedTimeFrame = request()->query('selectedTimeFrame', $this->selectedTimeFrame);
        $this->startDate = request()->query('startDate', $this->startDate);
        $this->endDate = request()->query('endDate', $this->endDate);

        // Load Mujeeb options from the database
        $this->mujeebs = DB::table('uploaded_files')
            ->where('checked_folder', 'ok')
            ->where('viral', '!=', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->distinct()
            ->pluck('sender')
            ->toArray();
    }
    private function loadDaruliftaNames()
{
    $user = Auth::user();
    $userRoles = $user->roles;
    $roleNames = $userRoles->pluck('name')->toArray();
    $firstRoleName = reset($roleNames);

    if ($this->darulifta === null) {
        // Check if user has more than one role OR if one of the roles is Nazim_Viral
        if (count($userRoles) > 1 || in_array('Nazim_Viral', $roleNames)) {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                ->select('uploaded_files.darulifta_name')
                ->distinct()
                ->orderBy('daruliftas.id')
                ->pluck('uploaded_files.darulifta_name');
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name', $firstRoleName)
                ->pluck('darulifta_name');
        }
    } else {
        $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name', $this->darulifta)
            ->distinct()
            ->pluck('darulifta_name');
    }

    $this->daruliftalist = DB::table('uploaded_files')
        ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('uploaded_files.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('uploaded_files.darulifta_name');
}

    public function updatedSearch()
{
    $this->resetPage();
}

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function downloadFile($fileName)
    {
        // Define the path to the file in the storage directory
        $filePath = 'public/viral/' . $fileName;

        // Check if the file exists
        if (Storage::exists($filePath)) {
            // Download the file from storage
            return Storage::download($filePath, $fileName);
        } else {
            // Handle the error if the file does not exist
            $this->fileErrorMessage = "The file does not exist.";
        }
    }
    public function toggleViral($id)
    {
        $file = DB::table('uploaded_files')->where('id', $id)->first();

        if ($file) {
            $currentUserId = Auth::id();
            $newViralValue = $file->viral == $currentUserId ? 0 : $currentUserId;

            DB::table('uploaded_files')
                ->where('id', $id)
                ->update(['viral' => $newViralValue]);
        }
    }
    public function render()
    {
        $query = DB::table('uploaded_files')
            ->leftJoin('users', 'uploaded_files.viral', '=', 'users.id')
            ->select('uploaded_files.*', 'users.name as user_name')
            ->where('checked_folder', 'ok')
            ->where('viral', '!=', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames);

        // Apply search conditions
        if ($this->search) {
            $query->where(function ($subQuery) {
                $subQuery->where('sender', 'like', '%' . $this->search . '%')
                         ->orWhere('file_code', 'like', '%' . $this->search . '%')
                         ->orWhere('category', 'like', '%' . $this->search . '%')
                         ->orWhere('darulifta_name', 'like', '%' . $this->search . '%');
            });
        }

        // Apply filter conditions
        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }

        if ($this->selectedchecked == 'all') {
            // No additional conditions
        } elseif ($this->selectedchecked == 'selected_viral') {
            $query->whereNull('viral_upload');
        } elseif ($this->selectedchecked == 'ready_print') {
            $query->whereNotNull('viral_upload')
                  ->where('viral_upload', '!=', '');
        } elseif ($this->selectedchecked == 'web_link') {
            $query->whereNotNull('web_link')
                  ->where('web_link', '!=', '');
        } elseif ($this->selectedchecked == 'viral_link') {
            $query->whereNotNull('viral_link')
                  ->where('viral_link', '!=', '');
        }

        if ($this->selectedTimeFrame == 'all') {
            $query->whereNotNull('checked_date');
        } elseif ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(checked_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'last_month') {
            $query->whereBetween(DB::raw('DATE(checked_date)'), [
                now()->subMonth()->startOfMonth(),
                now()->subMonth()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween(DB::raw('DATE(checked_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }

        // Get all data for counts
        $allData = $query->get();

        // Calculate counts
        $counts = [
            'totalViral' => $allData->count(),
            'totalReadyToPrint' => $allData->where('viral_upload', '!=', '')->count(),
            'totalNotReady' => $allData->where('viral_upload', '')->count(),
            'totalViralLink' => $allData->where('viral_link', '!=', '')->count(),
            'totalWebUpload' => $allData->where('web_link', '!=', '')->count(),
            'groupedData' => $allData->groupBy('darulifta_name')
        ];

        // Paginate results for the table
        $question_b = $query->orderBy('checked_date', 'desc')->paginate(50);

        return view('livewire.selected-viral', [
            'question_b' => $question_b,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'counts' => $counts
        ])->layout('layouts.app');
    }
}
