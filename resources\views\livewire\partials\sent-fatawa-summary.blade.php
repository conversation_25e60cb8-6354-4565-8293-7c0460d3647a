<!-- Summary Table -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Darulifta Summary
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="table-responsive">
            <table class="table-modern">
                <thead>
                    <tr>
                        <th>
                            <i class="fas fa-building me-2"></i>
                            Darulifta
                        </th>
                        @if ($selectedmufti == 'all')
                            <th>
                                <i class="fas fa-user-check me-2"></i>
                                Checker
                            </th>
                        @endif
                        <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Sent Fatawa Dates
                        </th>
                        <th>
                            <i class="fas fa-chart-bar me-2"></i>
                            Total
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $totalOkCount = 0;
                        $totalMahlENazarCount = 0;
                        $totalDuplicateFileCodes = 0;
                        $totalUniqueFileCodes = 0;
                        $seenFileCodes = [];
                        $duplicateFileCodesList = [];
                    @endphp

                    @if ($selectedmufti == 'all')
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($sendingFatawa[$daruliftaName]))
                                @foreach($sendingFatawa[$daruliftaName] as $checked => $dates)
                                    @php
                                        $daruliftaTotalCounts = 0;
                                        $folderCounts = [];
                                        $checkedFolderCounts = [];
                                        $daruliftaOkCount = 0;
                                        $daruliftaMahlENazarCount = 0;
                                        $daruliftaDuplicateCount = 0;
                                        $daruliftaUniqueCount = 0;
                                        $transferByCounts = [];
                                    @endphp

                                    @foreach($dates as $mailfolderDates => $files)
                                        @foreach($files as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $checkedFolder = $file->checked_folder;

                                                if ($checkedFolder) {
                                                    $checkedFolderCounts[$folder][$checkedFolder] = isset($checkedFolderCounts[$folder][$checkedFolder]) ? $checkedFolderCounts[$folder][$checkedFolder] + 1 : 1;
                                                }

                                                $fileCode = $file->file_code;
                                                if (isset($seenFileCodes[$fileCode])) {
                                                    if ($seenFileCodes[$fileCode] == 1) {
                                                        $totalDuplicateFileCodes++;
                                                        $daruliftaDuplicateCount++;
                                                        $seenFileCodes[$fileCode]++;
                                                    }
                                                    $duplicateFileCodesList[$fileCode] = isset($duplicateFileCodesList[$fileCode]) ? $duplicateFileCodesList[$fileCode] + 1 : 2;
                                                } else {
                                                    $seenFileCodes[$fileCode] = 1;
                                                    $totalUniqueFileCodes++;
                                                    $daruliftaUniqueCount++;
                                                }

                                                if ($checkedFolder == 'ok') {
                                                    $totalOkCount++;
                                                    $daruliftaOkCount++;
                                                } elseif ($checkedFolder == 'Mahl-e-Nazar') {
                                                    $totalMahlENazarCount++;
                                                    $daruliftaMahlENazarCount++;
                                                }

                                                $byMufti = $file->by_mufti ?? "";
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] = isset($transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti]) ? $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                            @endphp
                                        @endforeach
                                    @endforeach

                                    <tr>
                                        <td>
                                            @if ($loop->first || $loop->parent->first)
                                                @php
                                                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                                @endphp
                                                <a href="{{ route('sent-fatawa', [
                                                    'darulifta' => $daruliftaName,
                                                    'selectedmujeeb' => $this->selectedmujeeb,
                                                    'selectedmufti' => $this->selectedmufti,
                                                    'selectedchecked' => $selectedchecked,
                                                    'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                    'startDate' => $this->startDate,
                                                    'endDate' => $this->endDate,
                                                ]) }}&{{ $selectedMonthsQuery }}"
                                                   class="text-decoration-none fw-bold text-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i>
                                                    {{ $daruliftaName }}
                                                </a>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info text-white">{{ $checked }}</span>
                                        </td>
                                        <td>
                                            <div class="folder-entries-modern">
                                                @php $foldercount = 0; @endphp
                                                @foreach ($folderCounts as $folder => $count)
                                                    <div class="folder-entry-modern">
                                                        <div class="folder-date-modern">
                                                            <a href="{{ route('sent-fatawa', [
                                                                'darulifta' => $daruliftaName,
                                                                'mailfolder' => $folder,
                                                                'selectedmujeeb' => $this->selectedmujeeb,
                                                                'selectedmufti' => $this->selectedmufti,
                                                                'selectedchecked' => $this->selectedchecked,
                                                                'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                                'startDate' => $tempStartDate,
                                                                'endDate' => $tempEndDate,
                                                            ]) }}&{{ $selectedMonthsQuery }}" class="text-white text-decoration-none">
                                                                {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                            </a>
                                                        </div>
                                                        <div class="folder-count-modern">
                                                            Total: {{ $count }}
                                                        </div>

                                                        @if(isset($transferByCounts[$folder]))
                                                            @foreach($transferByCounts[$folder] as $transferBy => $checkedFolders)
                                                                @foreach($checkedFolders as $checkedFolder => $byMuftis)
                                                                    @foreach($byMuftis as $byMufti => $transferByCount)
                                                                        <div class="folder-count-modern">
                                                                            <a href="{{ route('downloadFolder', [
                                                                                'daruliftaName' => $daruliftaName,
                                                                                'mailfolderDates' => $folder,
                                                                                'transferBy' => $transferBy,
                                                                                'checker' => $checked,
                                                                                'checkedFolder' => $checkedFolder,
                                                                                'byMufti' => $byMufti,
                                                                                'currentRoute' => 'sent-fatawa'
                                                                            ]) }}" class="text-white text-decoration-none">
                                                                                <i class="fas fa-download me-1"></i>
                                                                                {{ $transferBy }}-{{ $checkedFolder }}: {{ $transferByCount }}
                                                                            </a>
                                                                        </div>
                                                                    @endforeach
                                                                @endforeach
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                    @php $foldercount++; @endphp
                                                @endforeach
                                            </div>
                                        </td>
                                        <td>
                                            <div class="stats-summary-professional">
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Fatawa:</span>
                                                    <span class="stat-value-professional">{{ $daruliftaTotalCounts }}</span>
                                                </div>
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Folders:</span>
                                                    <span class="stat-value-professional">{{ $foldercount }}</span>
                                                </div>
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">OK:</span>
                                                    <span class="stat-value-professional text-success">{{ $daruliftaOkCount }}</span>
                                                </div>
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Mahl-e-Nazar:</span>
                                                    <span class="stat-value-professional text-warning">{{ $daruliftaMahlENazarCount }}</span>
                                                </div>
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Unique:</span>
                                                    <span class="stat-value-professional text-info">{{ $daruliftaUniqueCount - $daruliftaDuplicateCount }}</span>
                                                </div>
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Repeated:</span>
                                                    <span class="stat-value-professional text-danger">{{ $daruliftaTotalCounts - ($daruliftaUniqueCount - $daruliftaDuplicateCount) }}</span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        @endforeach
                    @else
                        {{-- Single mufti view logic here --}}
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($remainingFatawa[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0;
                                    $folderCounts = [];
                                    $checkedFolderCounts = [];
                                    $daruliftaOkCount = 0;
                                    $daruliftaMahlENazarCount = 0;
                                    $daruliftaDuplicateCount = 0;
                                    $daruliftaUniqueCount = 0;
                                    $transferByCounts = [];
                                @endphp

                                @foreach($mailfolderDate as $mailfolderDates)
                                    @if(isset($remainingFatawa[$daruliftaName][$mailfolderDates]))
                                        @foreach($remainingFatawa[$daruliftaName][$mailfolderDates] as $file)
                                            @php
                                                $checked = $file->checker;
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $checkedFolder = $file->checked_folder;

                                                if ($checkedFolder) {
                                                    $checkedFolderCounts[$folder][$checkedFolder] = isset($checkedFolderCounts[$folder][$checkedFolder]) ? $checkedFolderCounts[$folder][$checkedFolder] + 1 : 1;
                                                }

                                                $fileCode = $file->file_code;
                                                if (isset($seenFileCodes[$fileCode])) {
                                                    if ($seenFileCodes[$fileCode] == 1) {
                                                        $totalDuplicateFileCodes++;
                                                        $daruliftaDuplicateCount++;
                                                        $seenFileCodes[$fileCode]++;
                                                    }
                                                    $duplicateFileCodesList[$fileCode] = isset($duplicateFileCodesList[$fileCode]) ? $duplicateFileCodesList[$fileCode] + 1 : 2;
                                                } else {
                                                    $seenFileCodes[$fileCode] = 1;
                                                    $totalUniqueFileCodes++;
                                                    $daruliftaUniqueCount++;
                                                }

                                                if ($checkedFolder == 'ok') {
                                                    $totalOkCount++;
                                                    $daruliftaOkCount++;
                                                } elseif ($checkedFolder == 'Mahl-e-Nazar') {
                                                    $totalMahlENazarCount++;
                                                    $daruliftaMahlENazarCount++;
                                                }

                                                $byMufti = $file->by_mufti ?? "";
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] = isset($transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti]) ? $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] + 1 : 1;

                                                $daruliftaTotalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach

                                <tr>
                                    <td>
                                        @php
                                            $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                        @endphp
                                        <a href="{{ route('sent-fatawa', [
                                            'darulifta' => $daruliftaName,
                                            'selectedmujeeb' => $this->selectedmujeeb,
                                            'selectedmufti' => $this->selectedmufti,
                                            'selectedchecked' => $this->selectedchecked,
                                            'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                            'startDate' => $tempStartDate,
                                            'endDate' => $tempEndDate,
                                        ]) }}&{{ $selectedMonthsQuery }}"
                                           class="text-decoration-none fw-bold text-primary">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            {{ $daruliftaName }}
                                        </a>
                                    </td>
                                    <td>
                                        <div class="folder-entries-modern">
                                            @php $foldercount = 0; @endphp
                                            @foreach ($folderCounts as $folder => $count)
                                                <div class="folder-entry-modern">
                                                    <div class="folder-date-modern">
                                                        <a href="{{ route('sent-fatawa', [
                                                            'darulifta' => $daruliftaName,
                                                            'mailfolder' => $folder,
                                                            'selectedmujeeb' => $this->selectedmujeeb,
                                                            'selectedmufti' => $this->selectedmufti,
                                                            'selectedchecked' => $this->selectedchecked,
                                                            'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                                                            'startDate' => $tempStartDate,
                                                            'endDate' => $tempEndDate,
                                                        ]) }}&{{ $selectedMonthsQuery }}" class="text-white text-decoration-none">
                                                            {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                        </a>
                                                    </div>
                                                    <div class="folder-count-modern">
                                                        Total: {{ $count }}
                                                    </div>

                                                    @if(isset($transferByCounts[$folder]))
                                                        @foreach($transferByCounts[$folder] as $transferBy => $checkedFolders)
                                                            @foreach($checkedFolders as $checkedFolder => $byMuftis)
                                                                @foreach($byMuftis as $byMufti => $transferByCount)
                                                                    <div class="folder-count-modern">
                                                                        <a href="{{ route('downloadFolder', [
                                                                            'daruliftaName' => $daruliftaName,
                                                                            'mailfolderDates' => $folder,
                                                                            'transferBy' => $transferBy,
                                                                            'checker' => $checked,
                                                                            'checkedFolder' => $checkedFolder,
                                                                            'byMufti' => $byMufti,
                                                                            'currentRoute' => 'sent-fatawa'
                                                                        ]) }}" class="text-white text-decoration-none">
                                                                            <i class="fas fa-download me-1"></i>
                                                                            {{ $transferBy }}-{{ $checkedFolder }}: {{ $transferByCount }}
                                                                        </a>
                                                                    </div>
                                                                @endforeach
                                                            @endforeach
                                                        @endforeach
                                                    @endif
                                                </div>
                                                @php $foldercount++; @endphp
                                            @endforeach
                                        </div>
                                    </td>
                                    <td>
                                        <div class="stats-summary-professional">
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Fatawa:</span>
                                                <span class="stat-value-professional">{{ $daruliftaTotalCounts }}</span>
                                            </div>
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Folders:</span>
                                                <span class="stat-value-professional">{{ $foldercount }}</span>
                                            </div>
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">OK:</span>
                                                <span class="stat-value-professional text-success">{{ $daruliftaOkCount }}</span>
                                            </div>
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Mahl-e-Nazar:</span>
                                                <span class="stat-value-professional text-warning">{{ $daruliftaMahlENazarCount }}</span>
                                            </div>
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Unique:</span>
                                                <span class="stat-value-professional text-info">{{ $daruliftaUniqueCount - $daruliftaDuplicateCount }}</span>
                                            </div>
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Repeated:</span>
                                                <span class="stat-value-professional text-danger">{{ $daruliftaTotalCounts - ($daruliftaUniqueCount - $daruliftaDuplicateCount) }}</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
