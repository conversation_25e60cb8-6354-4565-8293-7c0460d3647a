<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use App\Models\User;
use App\Models\Department;
use App\Models\Task;
use App\Models\DailyPerformance;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Department::class => \App\Policies\DepartmentPolicy::class,
        Task::class => \App\Policies\TaskPolicy::class,
        User::class => \App\Policies\UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Define Gates for role-based access control

        // Nazim (Admin) Gates
        Gate::define('manage-users', function (User $user) {
            return $user->isNazim();
        });

        Gate::define('manage-departments', function (User $user) {
            return $user->isNazim();
        });

        Gate::define('manage-roles', function (User $user) {
            return $user->isNazim();
        });

        Gate::define('assign-supervisors', function (User $user) {
            return $user->isNazim();
        });

        Gate::define('unlock-users', function (User $user) {
            return $user->isNazim();
        });

        Gate::define('view-all-performance', function (User $user) {
            return $user->isNazim();
        });

        // Superior (Shoba Zimmedar) Gates
        Gate::define('manage-assistants', function (User $user) {
            return $user->isSuperior() || $user->isNazim();
        });

        Gate::define('assign-tasks', function (User $user) {
            return $user->isSuperior() || $user->isNazim();
        });

        Gate::define('view-team-performance', function (User $user) {
            return $user->isSuperior() || $user->isNazim();
        });

        // Question Assignment Gates
        Gate::define('assign-questions', function (User $user) {
            return $user->isNazim();
        });

        Gate::define('submit-fatawa', function (User $user) {
            // Check if user has submitted daily performance
            if (!$user->hasSubmittedTodaysPerformance()) {
                return false;
            }

            // Check if user has exceeded Mahl-e-Nazar limit
            if ($user->getMahlENazarCount() >= 12) {
                return false;
            }

            // Check for active restrictions
            if ($user->hasActiveRestrictions()) {
                return false;
            }

            return $user->isMujeeb() || $user->isSuperior() || $user->isNazim();
        });

        // Performance Gates
        Gate::define('submit-performance', function (User $user) {
            return true; // All authenticated users can submit performance
        });

        Gate::define('view-own-performance', function (User $user) {
            return true; // All authenticated users can view their own performance
        });

        // Department Gates
        Gate::define('view-department-users', function (User $user) {
            return $user->isSuperior() || $user->isNazim();
        });

        // Reporting Gates
        Gate::define('generate-reports', function (User $user) {
            return $user->isSuperior() || $user->isNazim();
        });

        Gate::define('export-pdf-reports', function (User $user) {
            return $user->isSuperior() || $user->isNazim();
        });
    }
}
