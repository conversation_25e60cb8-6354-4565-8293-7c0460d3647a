<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DailyPerformance;
use Carbon\Carbon;

class AccessDeniedPage extends Component
{
    public $hasSubmittedToday = false;
    public $todayDate;
    
    public function mount()
    {
        $this->todayDate = Carbon::today()->format('F d, Y');
        $this->checkPerformanceStatus();
    }
    
    public function checkPerformanceStatus()
    {
        $user = auth()->user();
        if ($user) {
            $this->hasSubmittedToday = DailyPerformance::where('user_id', $user->id)
                ->where('performance_date', Carbon::today())
                ->where('is_submitted', true)
                ->exists();
        }
    }
    
    public function redirectToPerformance()
    {
        return redirect()->route('daily-performance.create');
    }
    
    public function render()
    {
        return view('livewire.access-denied-page');
    }
}
