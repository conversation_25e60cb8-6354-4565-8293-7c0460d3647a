<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;
use Illuminate\Http\Request; // Add this line



// #[Layout('layouts.app')]
class SentChecking extends Component
{
    public $selectedMonths = [];
    public $checkerlist;
    public $munsab;
    public $datefilter;
    public $showDetail = false;
    public $showQue = false;
    public $showChat = false;
    public $codebylower;
    public $message;
    public $allfatawa;
    public $userName;
    public $checker;
    Public $obtain;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $remainingFatawa;
    Public $sendingFatawa;
    public $daruliftaNames;
    public $checkers;
    public $mailfolderDate;
    public $darulifta;
    public $mailfolder;
    public $mujeebs;
    public $selectedmujeeb = 'all';
    public $selectedmufti = 'mufti_ali_asghar';
    public $selectedexclude = 'exclude_checked';
    public $selectedTimeFrame= 'this_month';
    public $tempSelectedMonths = [];
    public $tempSelectedTimeFrame = 'this_month';
    public $daruliftalist;
    public $startDate;
    public $endDate;
    public $tempStartDate;
    public $tempEndDate;
    public $roleInfo = [];
         
   
   
    public function mount(Request $request, $darulifta = null, $mailfolder = null)
    {
        $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);
        $this->selectedmufti = $request->query('selectedmufti', $this->selectedmufti);
        $this->selectedexclude = $request->query('selectedexclude', $this->selectedexclude);
        $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);
        
        // Handle selectedMonths from request
        $selectedMonths = $request->query('selectedMonths', []);
        if (is_string($selectedMonths)) {
            $this->selectedMonths = explode(',', $selectedMonths);
        } else {
            $this->selectedMonths = (array)$selectedMonths;
        }
        
        $this->tempSelectedTimeFrame = $this->selectedTimeFrame;
        $this->tempSelectedMonths = $this->selectedMonths;
        $this->startDate = $request->query('startDate');
        $this->endDate = $request->query('endDate');
        $this->tempStartDate = $this->startDate;
        $this->tempEndDate = $this->endDate;
        $this->showDetail = $request->query('showDetail', false) == '1';
        $this->showQue = $request->query('showQue', false) == '1';
        $this->showChat = $request->query('showChat', false) == '1';
        
        $this->determineUserRole();
        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();
    }

    private function determineUserRole()
    {
        $user = Auth::user();
        $userName = $user->name;
        $userRoles = $user->roles->pluck('name')->toArray();
        
        // Set default values
        $this->roleInfo = [
            'isSuperAdmin' => false,
            'isChecker' => false,
            'isMujeeb' => false,
            'userName' => $userName,
            'checkerName' => null,
            'mujeebName' => null,
            'darulName' => null,
            'assignedMujeebs' => [],
        ];
        
        // Check for SuperAdmin role (highest priority)
        if (in_array('SuperAdmin', $userRoles)) {
            $this->roleInfo['isSuperAdmin'] = true;
            return;
        }
        
        // Check for Checker role (second priority)
        if (in_array('Checker', $userRoles)) {
            $checker = DB::table('checker')
                ->where('checker_name', $userName)
                ->first();
                
            if ($checker) {
                $this->roleInfo['isChecker'] = true;
                $this->roleInfo['checkerName'] = $userName;
                $this->roleInfo['checkerFolderId'] = $checker->folder_id ?? null;
                return;
            }
        }
        
        // Check for Mujeeb role (lowest priority)
        if (in_array('mujeeb', $userRoles)) {
            $mujeeb = DB::table('mujeebs')
                ->where('mujeeb_name', $userName)
                ->first();

            if ($mujeeb) {
                $this->roleInfo['isMujeeb'] = true;
                $this->roleInfo['mujeebName'] = $userName;
                $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
                return;
            }
        }
        
        // Check if user is in mujeebs table but not assigned the mujeeb role
        $mujeeb = DB::table('mujeebs')
            ->where('mujeeb_name', $userName)
            ->first();

        if ($mujeeb) {
            $this->roleInfo['isMujeeb'] = true;
            $this->roleInfo['mujeebName'] = $userName;
            $this->roleInfo['darulName'] = $mujeeb->darul_name ?? null;
        }
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);

        // Initialize checkers with default value
        $this->checkers = DB::table('checker')
            ->select('folder_id')
            ->distinct()
            ->pluck('folder_id')
            ->toArray();

        if ($this->roleInfo['isMujeeb']) {
            // If user is mujeeb, only show their darul's data
            $this->daruliftaNames = [$this->roleInfo['darulName']];
            $this->mujeebs = [$this->roleInfo['userName']];
        } else if ($this->darulifta === null) {
            if(count(Auth::user()->roles) > 1){
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('uploaded_files.darulifta_name')
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->pluck('uploaded_files.darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
            } else {
                $this->daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', $firstRoleName)
                    ->pluck('darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
            }
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('darulifta_name');
            $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('sender');
        }
    }

    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')
        
        ->distinct()
        
        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()
    
    ->pluck('mail_folder_date');
    }
}
public function sendMessage($iftaCode)
{
    
    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.
    

    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}    
public function render()
    {
        
        // $this->obtain = DB::table('uploaded_files')
        // ->where('selected', 0)
        // // Add any additional conditions here
        // ->select('mail_folder_date', DB::raw('SUM(total_score) as total_score_sum'))
        // ->groupBy('mail_folder_date')
        // ->havingRaw('COUNT(mail_folder_date) > 1')
        // ->get();
        $this->daruliftalist = DB::table('uploaded_files')
        ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('uploaded_files.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('uploaded_files.darulifta_name');
        $this->checkerlist = DB::table('checker')
            ->get();
        $this->que_day_r = DB::table('questions')
        ->whereIn('question_branch', $this->daruliftaNames)
        ->get();
        $this->allfatawa = DB::table('uploaded_files')
    ->orderBy('id', 'asc') // Sorting by 'id' in ascending order
    ->get();
    $this->datefilter= DB::table('uploaded_files')
    ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
    ->orderBy('year', 'asc')
    ->orderBy('month', 'asc')
    ->get();

    $this->codebylower = DB::table('uploaded_files as uf1')
    ->select('uf1.*')
    ->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files GROUP BY file_code) as uf2'), function($join) {
        $join->on('uf1.id', '=', 'uf2.id');
    })
    ->orderBy('uf1.id', 'asc')
    ->get();
    
        $this->mahlenazar_null = DB::table('uploaded_files as u1')
        ->whereIn('u1.darulifta_name', $this->daruliftaNames)
        ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
        
        ->leftJoin('uploaded_files as u2', function ($join) {
            $join->on('u1.file_code', '=', 'u2.file_code')
                ->where(function ($query) {
                    $query->where('u2.checked_folder', 'ok')
                    
                        ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere
                        
                });
        })
        ->whereNull('u2.file_code')
        
        ->select('u1.*')
        ->get();
        $this->remainingFatawa = $this->remainingFatawadata();
        $this->sendingFatawa = $this->sendingFatawadata();

        $messages = Chat::latest()->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.sent-for-checking', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'checkers' => $this->checkers,
            'remainingFatawa' => $this->remainingFatawa,
            'sendingFatawa' => $this->sendingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmufti' => $this->selectedmufti,
            'selectedexclude' => $this->selectedexclude,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'tempSelectedTimeFrame' => $this->tempSelectedTimeFrame,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'showDetail' => $this->showDetail,
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'userName' => $this->userName,
            'checkerlist' => $this->checkerlist,
            'munsab' => $this->munsab,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'tempStartDate' => $this->tempStartDate,
            'tempEndDate' => $this->tempEndDate,
            
            // ... (other data to be passed to the view)
        ]
        
        )
        ->layout('layouts.app');
        
    }
    public function remainingFatawadata()
    {
        $remainingFatawas = [];

        // Format selected months for date comparison
        $formattedSelectedMonths = [];
        if (!empty($this->selectedMonths)) {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, (array)$this->selectedMonths);
        }

        foreach ($this->mailfolderDate as $mailfolderDates) {
            $data = [];

            foreach ($this->daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDates);

                // If user is mujeeb, only show their own data
                if ($this->roleInfo['isMujeeb']) {
                    $query->where('sender', $this->roleInfo['userName']);
                } else {
                    if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
                        $query->where('sender', $this->selectedmujeeb);
                    }
                }

                if ($this->selectedmufti && $this->selectedmufti != 'all') {
                    if ($this->selectedmufti == 'mufti_ali_asghar') {
                        $query->where(function($query) {
                            $query->where('checker', $this->selectedmufti)
                                  ->orWhereNull('checker');
                        });
                    } else {
                        $query->where('checker', $this->selectedmufti);
                    }
                }

                if ($this->selectedexclude == 'exclude_checked') {
                    $query->where('selected', 0);
                }

                // Apply date filtering
                if ($this->tempSelectedTimeFrame == 'this_month') {
                    $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                        now()->startOfMonth(),
                        now()->endOfMonth()
                    ]);
                } elseif ($this->tempSelectedTimeFrame == 'last_month') {
                    $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                        now()->subMonth()->startOfMonth(),
                        now()->subMonth()->endOfMonth()
                    ]);
                } elseif ($this->selectedTimeFrame == 'other' && !empty($formattedSelectedMonths)) {
                    $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
                } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
                    $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                        Carbon::parse($this->startDate)->format('Y-m-d'),
                        Carbon::parse($this->endDate)->format('Y-m-d')
                    ]);
                }

                if ($query->exists()) {
                    $remainingFatawas[$daruliftaName][$mailfolderDates] = $query->get();
                }
            }
        }

        return $remainingFatawas;
    }
    public function sendingFatawadata()
    {
        $sendingFatawas = [];

        // Ensure checkers is initialized
        if (empty($this->checkers)) {
            $this->checkers = DB::table('checker')
                ->select('folder_id')
                ->distinct()
                ->pluck('folder_id')
                ->toArray();
        }

        // Format selected months for date comparison
        $formattedSelectedMonths = [];
        if (!empty($this->selectedMonths)) {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, (array)$this->selectedMonths);
        }

        foreach ($this->mailfolderDate as $mailfolderDates) {
            foreach ($this->checkers as $checked) {
                foreach ($this->daruliftaNames as $daruliftaName) {
                    $query = DB::table('uploaded_files')
                        ->where('darulifta_name', $daruliftaName)
                        ->where(function($query) use ($checked) {
                            $query->where('checker', $checked)
                                  ->orWhere(function($query) use ($checked) {
                                      if ($checked === 'mufti_ali_asghar') {
                                          $query->whereNull('checker');
                                      }
                                  });
                        })
                        ->where('mail_folder_date', $mailfolderDates);

                    // If user is mujeeb, only show their own data
                    if ($this->roleInfo['isMujeeb']) {
                        $query->where('sender', $this->roleInfo['userName']);
                    } else {
                        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
                            $query->where('sender', $this->selectedmujeeb);
                        }
                    }

                    if ($this->selectedexclude == 'exclude_checked') {
                        $query->where('selected', 0);
                    }

                    // Apply date filtering
                    if ($this->tempSelectedTimeFrame == 'this_month') {
                        $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                            now()->startOfMonth(),
                            now()->endOfMonth()
                        ]);
                    } elseif ($this->tempSelectedTimeFrame == 'last_month') {
                        $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth()
                        ]);
                    } elseif ($this->selectedTimeFrame == 'other' && !empty($formattedSelectedMonths)) {
                        $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
                    } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
                        $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                            Carbon::parse($this->startDate)->format('Y-m-d'),
                            Carbon::parse($this->endDate)->format('Y-m-d')
                        ]);
                    }

                    if ($query->exists()) {
                        $sendingFatawas[$daruliftaName][$checked][$mailfolderDates] = $query->get();
                    }
                }
            }
        }

        // Reorder $sendingFatawas based on the order of checkers
        foreach ($sendingFatawas as $daruliftaName => $checkers) {
            $orderedCheckers = [];
            foreach ($this->checkers as $checker) {
                if (isset($checkers[$checker])) {
                    $orderedCheckers[$checker] = $checkers[$checker];
                }
            }
            $sendingFatawas[$daruliftaName] = $orderedCheckers;
        }

        return $sendingFatawas;
    }

}

