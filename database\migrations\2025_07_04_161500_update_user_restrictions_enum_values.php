<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to modify the enum column to include new values
        DB::statement("ALTER TABLE user_restrictions MODIFY COLUMN restriction_type ENUM(
            'performance_not_submitted',
            'fatawa_limit_exceeded', 
            'manual_lock',
            'mahl_e_nazar_default_restricted',
            'mahl_e_nazar_manual_allow'
        ) NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the new enum values (this will fail if there are records with these values)
        DB::statement("ALTER TABLE user_restrictions MODIFY COLUMN restriction_type ENUM(
            'performance_not_submitted',
            'fatawa_limit_exceeded', 
            'manual_lock'
        ) NOT NULL");
    }
};
