
<div>
    
        
      
<!-- <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
        
<script src="https://rawgit.com/eKoopmans/html2pdf/master/dist/html2pdf.bundle.js"></script> -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2/dist/alpine.min.js" defer></script>        
<script src="//unpkg.com/alpinejs" defer></script>

            <!-- Navbar -->
            <style>

.table-responsive {
    width: 100%;
    overflow-x: auto;
}

.table-responsive table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: collapse;
}

.table-responsive th,
.table-responsive td {
    white-space: nowrap; /* Prevents wrapping by default */
    overflow: hidden;
    text-overflow: ellipsis; /* Adds ellipsis to overflowed content */
}

.table-bordered th,
  .table-bordered td {
    border: 1px solid #dee2e6; /* Border color */
  }

  .table-bordered {
    border-collapse: collapse; /* Ensures borders are not doubled */
  }
  .title-cell {
        max-width: 150px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }
.question-cell {
        background-color: #ffffff;
        max-width: 1000px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }

    .question-text {
        white-space: normal; /* Allow text to wrap */
        word-wrap: break-word; /* Ensure long words break onto the next line */
        text-align: right; /* Right-align text */
        max-width: 100%; /* Ensure text doesn't overflow beyond the cell */
    }
    @media (max-width: 768px) {
        .question-text {
            white-space: normal;
            word-wrap: break-word;
        }
    }
.folder-entries {
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
    }
    .folder-entry {
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 5px;
        border: 1px solid #ccc; /* Border for each entry */
        border-radius: 4px;
        background-color: #f9f9f9; /* Background color for better contrast */
        transition: background-color 0.3s; /* Smooth transition for hover effect */
    }
    .folder-entry:hover {
        background-color: #e9ecef; /* Highlight color on hover */
    }
    .folder-date, .folder-status {
        white-space: nowrap;
        margin: 2px 0;
    }
    .date-link {
        text-decoration: none;
        color: #007bff; /* Link color */
    }
    .date-link:hover {
        text-decoration: underline; /* Underline on hover */
    }
                .arrow {
            transition: transform 0.3s;
        }
        .rotate-180 {
            transform: rotate(180deg);
        }
                .clickable-link {
        cursor: pointer;
        color: blue;
        text-decoration: underline;
    }
    .clickable-link:hover {
        color: darkblue;
    }
                .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
.main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }
                .view a i {
    color: #4CAF50; /* Green color for view icon */
}

.download a i {
    color: #FF5733; /* Orange color for download icon */
}
td {
    white-space: nowrap; /* Prevents wrapping by default */
    overflow: hidden;    /* Hides overflow */
    text-overflow: ellipsis; /* Adds ellipsis to overflowed content */
}

/* Responsive adjustments */
@media screen and (max-width: 768px) { /* Adjust for tablets */
    td {
        white-space: normal; /* Allows wrapping */
        overflow: visible;
    }
}

@media screen and (max-width: 480px) { /* Adjust for mobile phones */
    td {
        font-size: 0.8rem; /* Smaller font size for smaller screens */
    }
}
                .calendar-style {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 items per row, adjust as needed */
    gap: 5px; /* smaller gap */
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.month-year {
    display: flex;
    align-items: center;
    padding: 3px; /* reduced padding */
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.month-year input[type="checkbox"] {
    margin-right: 5px; /* smaller margin */
}

.month-year label {
    margin: 0;
    font-size: 12px; /* smaller font size */
}


                 .right-aligned {
        text-align: right;
    }
    .card-title-row {
        display: flex;          /* Enable flexbox for the container */
        justify-content: space-between; /* Spread children to each end */
        align-items: center;    /* Center items vertically */
        width: 100%;            /* Ensure the container spans the full width */
    }

    h2.card-title {
        margin: 0;             /* Remove margin to avoid unnecessary spacing */
        flex-grow: 1;          /* Allow the title to take up necessary space */
        white-space: nowrap;   /* Keep the title in a single line */
    }

    select {
        margin-left: auto;     /* Push the select box to the end of the container */
    }
    /* .question-section,
.chat-section {
    display: none;
} */
        
                    .custom-bg-light-red {
                    background-color: #FFDDDD; /* Lightest shade of red */
        }
        
        .custom-bg-light-blue {
            background-color: #DDDDFF; /* Lightest shade of blue */
        }
        
        .custom-bg-light-green {
            background-color: #DDFFDD; /* Lightest shade of green */
        }
        
        
        .custom-bg-light-yellow {
            background-color: #FFFFCC; /* Lightest shade of yellow */
        }
        .custom-text-dark-black {
            color: #000; /* Dark black color */
        }
                    .table{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 30px
                    
                  }
                  .card{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 30px
                    
                  }
                  .table2{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 30px
                    
                  }
                    .not-assigned {
            color: red;
            /* Add any other styles for not assigned here */
        }
        .future-date {
            color: red !important;
            border: 1px solid red;
        }
        
        .past-date {
            border: 1px solid green;
        }
        .increased-font {
                font-size: 20px; /* Change the font size as needed */
            }
            table {
            table-layout: auto;
            font-size: 2030px; /* Adjust the font size to your preference */
            
        }
        th, td {
                font-size: 20px; /* Adjust the font size for table headers and table data cells */
            }

</style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
            <?php if (isset($component)) { $__componentOriginal778d3beb0063990dd56df93abee65235 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal778d3beb0063990dd56df93abee65235 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.navs.auth','data' => ['titlePage' => 'Sent Fatawa']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.navs.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['titlePage' => 'Sent Fatawa']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $attributes = $__attributesOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__attributesOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $component = $__componentOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__componentOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
            
            <?php
    
            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        ?>
        <div wire:loading>
        <div style="display: flex; justify-content: center; align-items: center; background-color: black; 
        position: fixed; top: 0px; left: 0px; z-index: 9999; width: 100%; height: 100%;
        opacity: .75;">
                
                <div class="la-ball-spin la-2x">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                </div>   
        </div>
        
        
            
            <div class="card z-index-2 mb-3" id="big-card-1" style="background-color: #FFFFCC;">
                <div class="card-header pb-0">
                    <h2>Fatwa Detail of <?php echo e($fatwa); ?> </h2>
                    
                </div>
                
        <td colspan="1" class="align-middle text-center cursor-pointer" style="background-color: #FFDDCC;">
            Question 
        </td>
        
        <td colspan="12" class="question-cell align-right text-center">
        <!-- <td x-show="openQuestion" @click.outside="openQuestion = false" colspan="9" class="question-cell align-right text-center"> -->
    <!-- Dynamic Content Here -->
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $que_day_r; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!--[if BLOCK]><![endif]--><?php if(strtolower($fatwa) == strtolower($day->ifta_code)): ?>
            <div class="question-text" style="color: black;">سوال: <?php echo e($day->question); ?></div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
</td>

        
                
                <div class="card-body px-0 pb-2">
    <?php
        $serialNumber_fl = 1; // Initialize the serial number    
        $colors = ['#F0FFF0', '#F0F8FF', '#FFFFE0', '#FFE4E1', '#F0F8FF', '#F5FFFA', '#E6E6FA', '#FAEBD7', '#ADD8E6']; // Add more colors as needed
    ?>


        <div class="card z-index-2 mb-3" style="background-color: #F0FFF0 ">
        <div class="table-responsive">
                    <table class="table1 table-bordered align-items-center mb-0">
                        <thead>
                            <tr >
                                <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 3%; white-space: normal; color: black;">S.No</th>
                                <th class="text-uppercase font-weight-bolder opacity-7" style="width: 3%; white-space: normal; color: black;">Fatwa No & D/E</th>
                                <th class="text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Sayel Info</th>
                                <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Category & Title</th>
                                <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 10%; white-space: normal; color: black;">Rec Date</th>
                                <th class="text-uppercase font-weight-bolder opacity-7 ps-2" style="width: 5%; white-space: normal; color: black;">Mujeeb</th>
                                
                                <th class="text-uppercase font-weight-bolder opacity-7 ps-2" style="width: 5%; white-space: normal; color: black;">Mail Folder</th>
                                <th class="text-uppercase font-weight-bolder opacity-7 ps-2" style="width: 5%; white-space: normal; color: black;">Checked Folder Date</th>
                                <th class="text-uppercase font-weight-bolder opacity-7 ps-2" style="width: 5%; white-space: normal; color: black;">Checked Folder</th>
                                <th class="text-uppercase font-weight-bolder opacity-7 ps-2" style="width: 5%; white-space: normal; color: black;">Fatwa Final Status</th>
                                <th class="text-uppercase font-weight-bolder opacity-7 ps-2" style="width: 5%; white-space: normal; color: black;">Expected Date</th>
                                
                                <th class="text-center text-uppercase font-weight-bolder  opacity-7 ps-2" style="width: 5%; color: black;">View</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $serialNumber_re = 1; ?>
                            <?php
                                $file = $remainingFatawa->last();
                            ?>
                                                        <tr>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="font-weight-bold"><?php echo e($serialNumber_re++); ?></span>
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <div class="d-flex px-2 py-1">
                                                                <div class="d-flex flex-column justify-content-center">
                                                                    <h6 style="color: blue;"><?php echo e($file->file_code); ?></h6>
                                                                    <span class=" font-weight-bold" style="color: green;"><?php echo e($file->question_type); ?></span>
                                                                </div>
                                                            </div>
                                                            
                                                        </td>
                                                        <td class="text-left">
    <span class="font-weight-bold" style="width: 5%; white-space: normal; color: black;"><?php echo e($file->sayel); ?></span>
    <div class="font-weight-bold" style="color: blue;">Email: <span style="width: 5%; white-space: normal;"><?php echo e($file->email); ?></span></div>
    <div class="font-weight-bold" style="color: green;">Address: <span style="width: 5%; white-space: normal;"><?php echo e($file->address); ?></span></div>
    <div class="font-weight-bold" style="color: red;">Phone:<br> <span style="width: 5%; white-space: normal;"><?php echo e($file->phone); ?></span></div>
</td>
                                                        <td class="align-middle text-center title-cell">
                                                            <span class="font-weight-bold" style="color: blue;">موضوع: <?php echo e($file->category); ?></span>
                                                            <br>
                                                            <span class="font-weight-bold" style="color: green; width: 5%; white-space: normal;">عنوان: <?php echo e($file->question_title); ?></span>
                                                        </td>
                                                        <td class="align-middle text-center ">
    <?php
        $recDate = new \DateTime($file->rec_date);
        if ($file->checked_folder === 'ok' && !is_null($file->checked_date)) {
            $checkedDate = new \DateTime($file->checked_date);
            $daysDifference = $checkedDate->diff($recDate)->days;
        } else {
            $currentDate = now();
            $daysDifference = $currentDate->diff($recDate)->days;
        }
    ?>
    <span class="font-weight-bold" style="color: blue;">
        <?php echo e($file->rec_date); ?>

    </span>
    <br> 
    <span class="font-weight-bold" style="color: green;">
        <?php echo e($daysDifference); ?> days
    </span>
</td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                <span class="mujeeb-column<?php echo e($file->assign_id === null ? ' not-assigned' : ''); ?>" style="color: blue;">
                                                    <?php echo e($file->assign_id === null ? 'Fatwa not assign' : $file->assign_id); ?>

                                                </span>
                                            </td>
                                            <?php
    use Carbon\Carbon;
    $serialNumber_ma = 1;
    $mailFolderDates = [];
    $checkedDates = [];
    $checkedFolders = [];
    $viewLinks = [];
    $fileCodes = [];

    foreach ($remainingFatawa as $files) {
        $mailFolderDates[] = $files->mail_folder_date;
        $checkedDates[] = $files->checked_date; 
        $checkedFolders[] = $files->checked_folder;
        $fileCodes[] = $files->file_code;

        if (!is_null($files->file_code)) {
            if (!is_null($files->checked_folder)) {
                $viewLinks[] = route('viewCheck', [
                    'date' => $files->mail_folder_date . $files->darulifta_name . 'Checked',
                    'folder' => $files->checked_folder,
                    'filename' => $files->file_name
                ]);
            } else {
                $viewLinks[] = route('viewRemain', [
                    'date' => $files->mail_folder_date . $files->darulifta_name . $files->checker,
                    'filename' => $files->file_name
                ]);
            }
        } else {
            $viewLinks[] = null;
        }
    }
?>

<!-- Displaying Data -->
<td class="align-middle text-center title-cell">
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mailFolderDates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!--[if BLOCK]><![endif]--><?php if(is_null($fileCodes[$index])): ?>
            <span class="text-warning" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Not Available">
                <i class="fas fa-ban"></i>
            </span>
        <?php else: ?>
            <!--[if BLOCK]><![endif]--><?php if(!empty($viewLinks[$index])): ?>
                <a href="<?php echo e($viewLinks[$index]); ?>" target="_blank" style="color: green;">
                    <i class="fas fa-eye"></i>
                </a>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <span class="font-weight-bold" style="color: blue;"><?php echo e(Carbon::parse($date)->format('d-m-y')); ?>(<span style="color: red;"><?php echo e($serialNumber_ma++); ?></span>)</span><br>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
</td>
<td class="align-middle text-center title-cell">
    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $checkedDates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!--[if BLOCK]><![endif]--><?php if(!is_null($date)): ?>
            <span class="font-weight-bold" style="color: green;"><?php echo e(Carbon::parse($date)->format('d-m-y')); ?></span><br>
        <?php else: ?>
            <span class="font-weight-bold" style="color: red;">Null</span><br>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
</td>
    <td class="align-middle text-center title-cell">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $checkedFolders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <span class="font-weight-bold" style="color: blue;"><?php echo e($folder); ?></span><br>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </td>                                                        <td class="align-middle text-center status-column" style="white-space: normal;">
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $notsentiftacode; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notsent): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <!--[if BLOCK]><![endif]--><?php if($file->ifta_code != $notsent->ifta_code): ?>
                                                    
                                                        <?php $innerLoopBreak = false; ?>
                                                        
                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $resultstatus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <!--[if BLOCK]><![endif]--><?php if(Str::lower($status->file_code) == Str::lower($file->ifta_code)): ?>
                                                                <!--[if BLOCK]><![endif]--><?php if($status->checked_folder != null): ?>
                                                                    <span class="font-weight-bold" style="color: blue;">
                                                                        <?php echo e($status->checked_folder); ?>

                                                                    </span>
                                                                <?php else: ?>
                                                                    <span class="font-weight-bold" style="color: green;">
                                                                        Sent Fatwa For Checking at <?php echo e($status->mail_folder_date); ?>

                                                                    </span>
                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            
                                                                <?php $innerLoopBreak = true; break; ?>
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                            
                                                        <!--[if BLOCK]><![endif]--><?php if(!$innerLoopBreak): ?>
                                                            <span class="font-weight-bold not-assigned">
                                                                Not Sent For Checking
                                                            </span>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <?php break; ?>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                
                                            </td>
                                            <td class="align-middle text-center">
                                                <?php
                                                    // Assuming $file->expected_date is a valid date string in the format 'Y-m-d'
                                                    $expectedDate = \Carbon\Carbon::parse($file->expected_date);
                                                    $currentDate = \Carbon\Carbon::now();
                                                    $color = ($currentDate->greaterThan($expectedDate)) ? ' future-date' : '';
                                                ?>
                                                <span class="font-weight-bold <?php echo e($color); ?>">
                                                    <?php echo e($file->expected_date); ?>

                                                </span>
                                            </td>        

                                            
                                                     


                                            <td class="text-center">
                                            <!--[if BLOCK]><![endif]--><?php if(is_null($file->file_code)): ?>
        <span class="text-warning" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Not Available">
            <i class="fas fa-ban"></i>
        </span>
        <?php else: ?>
    <!--[if BLOCK]><![endif]--><?php if(!is_null($file->checked_folder)): ?>
        <span class="view">
            <a href="<?php echo e(route('viewCheck',
                ['date' => $file->mail_folder_date . $file->darulifta_name .'Checked',
                'folder' => $file->checked_folder,
                'filename' => $file->file_name])); ?>" target="_blank">
                <i class="fas fa-eye"></i> 
            </a>
        </span>
        <span class="download">
            <a href="<?php echo e(route('downloadCheck',
                ['date' => $file->mail_folder_date . $file->darulifta_name .'Checked',
                'filename' => $file->file_name,
                'folder' => $file->checked_folder])); ?>">
                <i class="fas fa-download"></i> 
            </a>
        </span>
        <?php
            $deleteFile = route('deleteCheckedFile', [
                'mailfolderDates' => $file->mail_folder_date,
                'daruliftaName' => $file->darulifta_name,
                'checker' => $file->checker ?? ''
            ]);

            $deleteFileFormId = 'delete-file-form-' . $file->id;
            $downloadfileByadmin = $file->downloaded_by_admin;
            $userRoles = Auth::user()->roles->pluck('name')->toArray();
            $isAdmin = in_array('Admin', $userRoles);
        ?>

        <span class="delete">
            <!--[if BLOCK]><![endif]--><?php if($isAdmin): ?>
                <a href="#" onclick="event.preventDefault(); if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('<?php echo e($deleteFileFormId); ?>').submit(); }" class="text-danger">
                    <i class="fas fa-trash"></i>
                </a>
                <form id="<?php echo e($deleteFileFormId); ?>" action="<?php echo e($deleteFile); ?>" method="POST" class="d-none">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <input type="hidden" name="id" value="<?php echo e($file->id); ?>">
                    <input type="hidden" name="file_name" value="<?php echo e($file->file_name); ?>">
                    <input type="hidden" name="file_code" value="<?php echo e($file->file_code); ?>">
                    <input type="hidden" name="checked_folder" value="<?php echo e($file->checked_folder); ?>">
                </form>
            <?php else: ?>
                <span class="text-danger" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Cannot delete, only Admins can delete.">
                    <i class="fas fa-trash mx-1"></i>
                </span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </span>
    <?php else: ?>
        <span class="view">
            <a href="<?php echo e(route('viewRemain',
                ['date' => $file->mail_folder_date . $file->darulifta_name .$file->checker,
                'filename' => $file->file_name])); ?>" target="_blank">
                <i class="fas fa-eye"></i> 
            </a>
        </span>
        <span class="download">
            <a href="<?php echo e(route('downloadFile',
                ['date' => $file->mail_folder_date . $file->darulifta_name .$file->checker,
                'filename' => $file->file_name,
                'id' => $file->id])); ?>">
                <i class="fas fa-download"></i> 
            </a>
        </span>
        <?php
            $deleteFile = route('deleteFile', [
                'mailfolderDates' => $file->mail_folder_date,
                'daruliftaName' => $file->darulifta_name,
                'checker' => $file->checker ?? ''
            ]);

            $deleteFileFormId = 'delete-file-form-' . $file->id;
            $downloadfileByadmin = $file->downloaded_by_admin;
            $canDeletefile = is_null($downloadfileByadmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
        ?>

        <span class="delete">
            <!--[if BLOCK]><![endif]--><?php if($canDeletefile): ?>
                <a href="#" onclick="event.preventDefault(); if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('<?php echo e($deleteFileFormId); ?>').submit(); }" class="text-danger">
                    <i class="fas fa-trash"></i>
                </a>
                <form id="<?php echo e($deleteFileFormId); ?>" action="<?php echo e($deleteFile); ?>" method="POST" class="d-none">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <input type="hidden" name="id" value="<?php echo e($file->id); ?>">
                    <input type="hidden" name="file_name" value="<?php echo e($file->file_name); ?>">
                    <input type="hidden" name="file_code" value="<?php echo e($file->file_code); ?>">
                </form>
            <?php else: ?>
                <span class="text-danger" style="cursor: not-allowed;" data-bs-toggle="tooltip" title="Cannot delete, downloaded by admin on <?php echo e($downloadfileByadmin); ?>">
                    <i class="fas fa-trash mx-1"></i>
                </span>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </span>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</td>

                                                    </tr>
                                                    <tr>
                                                    <td colspan="13" class="text-center">
<?php
        // Use fully qualified name to avoid conflicts
        $Qr = 1;
        $Qc = 1;
        $totalDays = 0;
        $gapTotal = 0;
        $previousCheckedDate = null;
        $previousMailFolderDate = null;
        $finalGap = 0;
        $lastCheckedFolder = null;
        $isFirstIteration = true;
    ?>

    <div class="border p-3 bg-light rounded">
        <h2 class="mb-3">Fatwa Trail Analyse</h2>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Received Folder Date</th>
                    <th>Checked Date</th>
                    <th>Days Difference<br> (Between Rec & Checked)</th>
                    <th>Gap Days<br> (Held By Mujeeb)</th>
                </tr>
            </thead>
            <tbody>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $allfatawa; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fatawa): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <!--[if BLOCK]><![endif]--><?php if($file->file_code == $fatawa->file_code): ?>
                        <?php
                            $mailFolderDate = \Carbon\Carbon::parse($fatawa->mail_folder_date);
                            $checkedDate = $fatawa->checked_date ? \Carbon\Carbon::parse($fatawa->checked_date) : null;
                            $daysDifference1 = $checkedDate ? $mailFolderDate->diffInDays($checkedDate) : '-';
                            if ($checkedDate) {
                                $totalDays += $daysDifference1;
                            }

                            $gap = $previousCheckedDate ? $previousCheckedDate->diffInDays($mailFolderDate) : 0;
                            if ($previousCheckedDate && !$isFirstIteration) {
                                $gapTotal += $gap;
                            }

                            $previousCheckedDate = $checkedDate;
                            $lastCheckedFolder = $fatawa->checked_folder;
                            $previousMailFolderDate = $mailFolderDate;
                        ?>
                        <tr>
                            <td><?php echo e($Qr++); ?></td>
                            <td><?php echo e($mailFolderDate->format('d-m-Y')); ?></td>
                            <td><?php echo e($checkedDate ? $checkedDate->format('d-m-Y') : 'N/A'); ?></td>
                            <td><?php echo e($daysDifference1); ?></td>
                            <td><?php echo e(!$isFirstIteration ? $gap : '-'); ?></td>
                        </tr>
                        <?php
                            $isFirstIteration = false;
                        ?>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                <?php
                    // Calculate final gap if necessary
                    if ($previousCheckedDate && $lastCheckedFolder !== 'ok') {
                        $currentDate = \Carbon\Carbon::now();
                        $finalGap = $previousCheckedDate->diffInDays($currentDate);
                    }
                ?>

                <tr class="alert alert-info">
                    <td colspan="2" class="text-right"><strong>Totals:</strong></td>
                    <td>
                        <!--[if BLOCK]><![endif]--><?php if($lastCheckedFolder !== 'ok'): ?>
                            Last Gap <?php echo e($finalGap); ?> days
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </td>
                    <td><?php echo e($totalDays); ?> days</td>
                    <td><?php echo e($gapTotal); ?> days</td>
                </tr>
            </tbody>
        </table>

        <div class="mt-3">
            <!--[if BLOCK]><![endif]--><?php if($finalGap && $lastCheckedFolder !== 'ok'): ?>
                <strong>Last Gap Held By Mujeeb:</strong> <?php echo e($finalGap); ?><br>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            <strong>Total Days Between Rec & Checked:</strong> <?php echo e($totalDays); ?><br>
            <strong>Gap Days Held By Mujeeb:</strong> <?php echo e($gapTotal); ?><br>
            <strong>Total (Days + Gap Days <!--[if BLOCK]><![endif]--><?php if($lastCheckedFolder !== 'ok'): ?> + Last Gap <?php endif; ?><!--[if ENDBLOCK]><![endif]-->):</strong> 
            <?php echo e($totalDays + $gapTotal + ($lastCheckedFolder !== 'ok' ? intval($finalGap) : 0)); ?>

        </div>
    </div>
</td>




                                                        </tr>
                                                    
    <!-- Question Toggle and Content -->
            
    
    
    </tr>
    
    
        <!-- Chat Toggle and Content -->
        <tr x-data="{ openChat: false }">
    <td colspan="1" @click="openChat = !openChat" class="align-middle text-center cursor-pointer toggle-chat right-aligned" data-section="chat" style="background-color: #FFDDCC;">
        Chat <span x-text="openChat ? '◀' : '▶'"></span>
    </td>

    <td x-show="openChat" colspan="8" class="align-middle text-center">
        <div class="d-flex justify-content-center align-items-center">
            <div class="col-md-6 col-lg-7 col-xl-8">
                <ul class="list-unstyled">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $messages->sortBy('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <!--[if BLOCK]><![endif]--><?php if($message->ifta_code == $file->file_code): ?>
                            <!--[if BLOCK]><![endif]--><?php if($message->user_id == auth()->user()->id): ?>
                                <li class="d-flex justify-content-between mb-4">
                                    <span class="badge rounded-circle bg-success d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                        <?php echo e(strtoupper(substr($message->user_name, 0, 1))); ?>

                                    </span>
                                    <div class="card w-auto">
                                        <div class="card-header d-flex justify-content-between p-3">
                                            <p class="fw-bold mb-0">
                                                <?php echo e($message->user_name); ?>

                                            </p>
                                            <p class="text-muted small mb-0">
                                                <i class="far fa-clock"></i> <?php echo e($message->created_at); ?>

                                            </p>
                                        </div>
                                        <div class="card-body" style="background-color:#ADD8E6;">
                                            <p class="mb-0">
                                                <?php echo e($message->message); ?>

                                            </p>
                                        </div>
                                    </div>
                                </li>
                            <?php else: ?>
                                <!-- Unauthenticated / Other users messages. -->
                                <li class="d-flex justify-content-between mb-4">
                                    <div class="card w-auto">
                                        <div class="card-header d-flex justify-content-between p-3">
                                            <p class="fw-bold mb-0">
                                                <?php echo e($message->user_name); ?>

                                            </p>
                                            <p class="text-muted small mb-0">
                                                <i class="far fa-clock"></i> <?php echo e($message->created_at); ?>

                                            </p>
                                        </div>
                                        <div class="card-body">
                                            <p class="mb-0">
                                                <?php echo e($message->message); ?>

                                            </p>
                                        </div>
                                    </div>
                                    <span class="badge rounded-circle bg-primary d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                        <?php echo e(strtoupper(substr($message->user_name, 0, 1))); ?>

                                    </span>
                                </li>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </ul>
                <!-- Message input should be outside the loop -->
                <div class="bg-light">
                    <div class="input-group">
                        <input wire:model="message" type="text" placeholder="Type a message" aria-describedby="button-addon2" class="form-control rounded-0 border-0 py-4 bg-light text-end">
                        <div class="input-group-append">
                            <button id="button-addon2" class="btn btn-link" wire:click="sendMessage('<?php echo e($file->file_code); ?>')"> 
                                <i class="fa fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </td>
</tr>
                            
                        </tbody>
                    </table>
                    </div>
        </div>
    

            </div>
        
   
   

   <script>
    document.addEventListener('DOMContentLoaded', function () {
    const checkboxes = document.querySelectorAll('.calendar-style input[type="checkbox"]');

    // Example: Adding an event listener to each checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if(this.checked) {
                console.log(this.value + ' is checked');
            } else {
                console.log(this.value + ' is unchecked');
            }
        });
    });
});

   </script>
   <div wire:ignore>    
           <?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show  bg-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show  bg-gray-200']); ?>
        <?php if (isset($component)) { $__componentOriginaleeb4de73933bf6c97cffe74e5846276e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.sidebar','data' => ['activePage' => ''.e(request()->route('role') ?? 'sent-fatawa').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => ''.e(request()->route('role') ?? 'sent-fatawa').'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $attributes = $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $component = $__componentOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
</div>   
    <?php if (isset($component)) { $__componentOriginalf30276552b63aa6c9559a1667ce359f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf30276552b63aa6c9559a1667ce359f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footers.auth','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('footers.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $attributes = $__attributesOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $component = $__componentOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__componentOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>

</main>
<?php if (isset($component)) { $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.plugins','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('plugins'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $attributes = $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $component = $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>



</div>

<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/fatwa-detail.blade.php ENDPATH**/ ?>