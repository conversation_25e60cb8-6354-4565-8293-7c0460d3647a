<!-- Sending Fatawa Summary Table Component -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Dar<PERSON>fta and Mail Folder Summary
        </h5>
        <p class="mb-0 opacity-75">Detailed breakdown of sending fatawa by Darulifta and folders</p>
    </div>
    <div class="modern-card-body">
        <div class="table-responsive">
            <table class="table modern-table table-hover">
                <thead>
                    <tr>
                        <th class="text-center">
                            <i class="fas fa-building me-1"></i>
                            Darulifta
                        </th>
                        @if ($selectedmufti == 'all')
                            <th class="text-center">
                                <i class="fas fa-user-check me-1"></i>
                                Checker
                            </th>
                        @endif
                        <th class="text-center">
                            <i class="fas fa-calendar-alt me-1"></i>
                            Sent Fatawa Date
                        </th>
                        <th class="text-center">
                            <i class="fas fa-chart-pie me-1"></i>
                            Total
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $totalCounts = 0;
                        $overallFolderCount = 0;
                    @endphp

                    @if ($selectedmufti == 'all')
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($sendingFatawa[$daruliftaName]) && is_array($sendingFatawa[$daruliftaName]))
                                @foreach($sendingFatawa[$daruliftaName] as $checked => $dates)
                                    @php
                                        $daruliftaTotalCounts = 0;
                                        $folderCounts = [];
                                        $transferByCounts = [];
                                        $folderEntries = [];
                                    @endphp

                                    @foreach($dates as $mailfolderDates => $files)
                                        @foreach($files as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy] = isset($transferByCounts[$folder][$transferBy]) ? $transferByCounts[$folder][$transferBy] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                                $folderEntries[$folder] = $folderEntries[$folder] ?? ['count' => 0, 'transfer_by' => []];
                                                $folderEntries[$folder]['count']++;
                                                $folderEntries[$folder]['transfer_by'][$transferBy] = ($folderEntries[$folder]['transfer_by'][$transferBy] ?? 0) + 1;
                                            @endphp
                                        @endforeach
                                    @endforeach

                                    <tr class="table-row-hover">
                                        <td class="text-center">
                                            @if ($loop->first || $loop->parent->first)
                                                @php
                                                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                                @endphp
                                                <a href="{{ route('sending-fatawa', [
                                                    'darulifta' => $daruliftaName,
                                                    'selectedmujeeb' => $this->selectedmujeeb,
                                                    'selectedmufti' => $this->selectedmufti,
                                                    'selectedTimeFrame' => $this->selectedTimeFrame,
                                                    'startDate' => $this->startDate,
                                                    'endDate' => $this->endDate,
                                                ]) }}&{{ $selectedMonthsQuery }}" 
                                                   class="text-decoration-none fw-bold text-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i>
                                                    {{ $daruliftaName }}
                                                </a>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">{{ $checked }}</span>
                                        </td>
                                        <td class="text-center">
                                            @php
                                                $foldercount = 0;
                                            @endphp
                                            <div class="folder-entries-modern">
                                                @foreach ($folderEntries as $folder => $data)
                                                    <div class="folder-entry-modern">
                                                        <div class="folder-date">
                                                            <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder]) }}" 
                                                               class="folder-link-modern">
                                                                <i class="fas fa-calendar-day me-1"></i>
                                                                {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                            </a>
                                                            <span class="badge bg-primary ms-1">T-{{ $data['count'] }}</span>
                                                        </div>
                                                        
                                                        @if(isset($data['transfer_by']))
                                                            @foreach($data['transfer_by'] as $transferBy => $transferByCount)
                                                                <div class="folder-transfer-by mt-1">
                                                                    <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder, 'transfer_by' => $transferBy]) }}" 
                                                                       class="folder-link-modern">
                                                                        <i class="fas fa-user me-1"></i>
                                                                        {{ $transferBy }}
                                                                    </a>
                                                                    <span class="badge bg-success ms-1">{{ $transferByCount }}</span>
                                                                </div>
                                                            @endforeach
                                                        @endif
                                                    </div>
                                                    @php
                                                        $foldercount++;
                                                        $overallFolderCount++;
                                                    @endphp
                                                @endforeach
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="stats-summary-professional">
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Fatawa:</span>
                                                    <span class="stat-value-professional text-primary">{{ $daruliftaTotalCounts }}</span>
                                                </div>
                                                <div class="stat-item-professional">
                                                    <span class="stat-label-professional">Folders:</span>
                                                    <span class="stat-value-professional text-success">{{ $foldercount }}</span>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        @endforeach
                    @else
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($remainingFatawa[$daruliftaName]) && is_array($remainingFatawa[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0;
                                    $folderCounts = [];
                                    $transferByCounts = [];
                                @endphp

                                @foreach($mailfolderDate as $mailfolderDates)
                                    @if(isset($remainingFatawa[$daruliftaName][$mailfolderDates]))
                                        @foreach($remainingFatawa[$daruliftaName][$mailfolderDates] as $file)
                                            @php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy] = isset($transferByCounts[$folder][$transferBy]) ? $transferByCounts[$folder][$transferBy] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach

                                <tr class="table-row-hover">
                                    <td class="text-center">
                                        @php
                                            $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                        @endphp
                                        <a href="{{ route('sending-fatawa', [
                                            'darulifta' => $daruliftaName,
                                            'selectedmujeeb' => $this->selectedmujeeb,
                                            'selectedmufti' => $this->selectedmufti,
                                            'selectedTimeFrame' => $this->selectedTimeFrame,
                                            'startDate' => $tempStartDate,
                                            'endDate' => $tempEndDate,
                                        ]) }}&{{ $selectedMonthsQuery }}" 
                                           class="text-decoration-none fw-bold text-primary">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            {{ $daruliftaName }}
                                        </a>
                                    </td>
                                    <td class="text-center">
                                        <div class="folder-entries-modern">
                                            @php
                                                $foldercount = 0;
                                            @endphp
                                            @foreach ($folderCounts as $folder => $count)
                                                <div class="folder-entry-modern">
                                                    <div class="folder-date">
                                                        <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder])}}"
                                                           class="folder-link-modern">
                                                            <i class="fas fa-calendar-day me-1"></i>
                                                            {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                                                        </a>
                                                        <span class="badge bg-primary ms-1">T-{{ $count }}</span>
                                                    </div>
                                                    @if(isset($transferByCounts[$folder]))
                                                        @foreach($transferByCounts[$folder] as $transferBy => $transferByCount)
                                                            <div class="folder-transfer-by mt-1">
                                                                <a href="{{ route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder, 'transfer_by' => $transferBy])}}"
                                                                   class="folder-link-modern">
                                                                    <i class="fas fa-user me-1"></i>
                                                                    {{ $transferBy }}
                                                                </a>
                                                                <span class="badge bg-success ms-1">{{ $transferByCount }}</span>
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                                @php
                                                    $foldercount++;
                                                    $overallFolderCount++;
                                                @endphp
                                            @endforeach
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="stats-summary-professional">
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Fatawa:</span>
                                                <span class="stat-value-professional text-primary">{{ $daruliftaTotalCounts }}</span>
                                            </div>
                                            <div class="stat-item-professional">
                                                <span class="stat-label-professional">Folders:</span>
                                                <span class="stat-value-professional text-success">{{ $foldercount }}</span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>

        <!-- Overall Summary -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success d-flex align-items-center">
                    <i class="fas fa-chart-line me-2"></i>
                    <strong>Overall Total:</strong>
                    <span class="ms-2">
                        <span class="badge bg-primary me-2">{{ number_format($totalCounts) }} Fatawa</span>
                        <span class="badge bg-success">{{ number_format($overallFolderCount) }} Folders</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- Performance Notice -->
        <div class="row mt-2">
            <div class="col-12">
                <div class="alert alert-info d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <small>
                        <strong>Note:</strong> For optimal performance, data is limited to recent entries.
                        Use filters to narrow down results for specific searches.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
