<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="{{ request()->route('role') ?? 'mahlenazar' }}"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Dashboard"></x-navbars.navs.auth>

    <div>
        <h1>Your Dashboard</h1>

        {{-- Livewire Component --}}
        @livewire('dash-board')

        {{-- Rest of your content --}}
        <!-- ... -->
    </div>

<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Mail Received Date</th>
            <th>Darulifta</th>
            <th>Mail Folder Date</th>
            <th>Selected</th>
        </tr>
    </thead>
    <tbody>
        @foreach($daruliftaData as $id => $data )
        <tr>
            <td>{{ $data->id }}</td>
            <td>{{ $data->darul_code }}</td>
            <td>{{ $data->darul_name }}</td>
            {{-- add folder date --}}
            {{-- <td>{{ $data->folder_date }}</td> --}}
            

        </tr>
        @endforeach
    </tbody>
</table>
</x-layout>