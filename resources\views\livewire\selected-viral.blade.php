<div>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Example of including Bootstrap 5 -->

    <!-- <script src="//unpkg.com/alpinejs" defer></script> -->


    <style>
         .custom-select {
        display: block;
        width: 100%;
        padding: 0.375rem 1.75rem 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .custom-select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
         .result-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;
        margin-bottom: 30px;
    }
         /* Styling each box (for each column like Viral Total, Ready to Print, etc.) */
    .result-box {
        flex: 1 1 22%; /* Each box will take 22% of the row, adjust for spacing */
        background-color: #f9f9f9;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        font-family: Arial, sans-serif;
    }

    /* Styling for box titles */
    .result-box h3 {
        font-size: 18px;
        margin-bottom: 10px;
        color: #007bff;
        text-transform: uppercase;
        font-weight: bold;
    }

    /* Styling for list inside each box */
    .result-box ul {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    /* List item style (Darulifta and counts) */
    .result-box ul li {
        font-size: 16px;
        margin: 5px 0;
        color: #333;
    }

    /* Total row at the bottom */
    .result-box .total {
        font-weight: bold;
        margin-top: 15px;
    }

    /* Responsive layout: for smaller screens, adjust the box width */
    @media (max-width: 768px) {
        .result-box {
            flex: 1 1 48%; /* Two boxes per row on smaller screens */
        }
    }

    @media (max-width: 480px) {
        .result-box {
            flex: 1 1 100%; /* One box per row on very small screens */
        }
    }
    .pagination-links {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding: 10px 0;
}

/* Pagination structure */
.pagination {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

/* Style for individual page items (previous/next buttons and page numbers) */
.pagination .page-item {
    margin: 0 5px;
}

/* Page link styling */
.pagination .page-link {
    display: inline-block;
    padding: 8px 16px;
    border: 1px solid #ccc;
    border-radius: 4px;
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    background-color: #fff;
}

/* Hover effect on page links */
.pagination .page-link:hover {
    background-color: #f1f1f1;
    color: #0056b3;
}

/* Active page link style */
.pagination .page-item.active .page-link {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

/* Disabled page link style (e.g., "Previous" and "Next" when disabled) */
.pagination .page-item.disabled .page-link {
    background-color: #e0e0e0;
    color: #b0b0b0;
    cursor: not-allowed;
}

/* First and last item rounding */
.pagination .page-item:first-child .page-link {
    border-radius: 4px 0 0 4px;
}

.pagination .page-item:last-child .page-link {
    border-radius: 0 4px 4px 0;
}
table, th, td {
    color: #333; /* Dark black font for table text */
}

h1 {
    color: #333; /* Dark black color for headings */
}

.search-bar input {
    color: #333; /* Dark black font for search bar */
}

/* Ensure icons have the blue color */
i.fas {
    color: #007bff; /* Blue color for font awesome icons */
}
@media (max-width: 576px) {
    .pagination .page-link {
        padding: 0.4rem 0.8rem; /* Slightly smaller padding for mobile */
        font-size: 0.9rem;
    }
}
        .main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }

    .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
    table {
            font-family: Jameel Noori Nastaleeq;
            width: 100%;
            margin: 10px 0;
            border-collapse: collapse;
            background-color: #fff;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #333;
        }
        .search-bar {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .search-bar input {
            padding: 8px;
            width: 100%;
            max-width: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination-links {
            margin-top: 40px;
        }

    </style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
@livewire('navbar', ['titlePage' => 'Selected Viral'])


            @php

            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        @endphp
        <div id="loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.7); z-index:9999; text-align:center;">
            <div style="position:relative; top:50%; transform:translateY(-50%);">
                <h2>Loading...</h2>
                <!-- You can replace this with a spinner or any loader image -->
            </div>
        </div>

        <div class="month-container">

    <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
       <div class="container">
       @php
    // Check if the user is authenticated before checking roles
    $Admin = Auth::check() && (
        in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
        in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
    );

    // Determine which button should be highlighted based on the current route.
    $isSelectedActive     = request()->routeIs('selected-viral');
    $isUnselectedActive   = request()->routeIs('select-viral');
    $isViralFatawaActive  = request()->routeIs('shoba-viral');
@endphp

<!-- Buttons for Selected, Unselected, and Viral Fatawa Detail -->
<div style="display: flex; align-items: center; margin-bottom: 20px;">
    <a href="{{ route('selected-viral') }}"
       class="apply-filters-button {{ $isSelectedActive ? 'apply-filters-button-active' : '' }}"
       style="margin-right: 10px;">
        Selected Fatawa
    </a>
    <a href="{{ route('select-viral') }}"
       class="apply-filters-button {{ $isUnselectedActive ? 'apply-filters-button-active' : '' }}"
       style="margin-right: 10px;">
        Unselected Fatawa
    </a>

    @if($Admin)
        <a href="{{ route('shoba-viral', 'all') }}"
           class="apply-filters-button {{ $isViralFatawaActive ? 'apply-filters-button-active' : '' }}">
            Shoba Viral
        </a>
    @endif
</div>
<div class="card mb-4">
    <div class="card-body">
        <div>
            <h2 class="card-title">Darulifta And Mail Folder Summary of Viral Fatawa</h2>
            <form method="GET" wire:submit.prevent>
                <div class="row">
                    <!-- Mujeeb Filter -->
                    <div class="col-md-2 form-group">
                        <label for="mujeebframe">Select Mujeeb:</label>
                        <select class="custom-select" id="mujeebframe" wire:model="selectedmujeeb">
                            <option value="all" {{ $selectedmujeeb === 'all' ? 'selected' : '' }}>All Mujeeb</option>
                            @foreach ($mujeebs as $sender)
                                <option value="{{ $sender }}" {{ $selectedmujeeb === $sender ? 'selected' : '' }}>
                                    {{ $sender }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Time Frame Filter -->
                    <div class="col-md-2 form-group">
                        <label for="timeframe">Time Frame:</label>
                        <select class="custom-select" id="timeframe" wire:model="selectedTimeFrame">
                            <option value="all" {{ $selectedTimeFrame === 'all' ? 'selected' : '' }}>All</option>
                            <option value="this_month" {{ $selectedTimeFrame === 'this_month' ? 'selected' : '' }}>This Month</option>
                            <option value="last_month" {{ $selectedTimeFrame === 'last_month' ? 'selected' : '' }}>Last Month</option>
                            @if ($selectedTimeFrame === 'custom')
                                <option value="custom" selected>Selected Date</option>
                            @endif
                        </select>
                    </div>

                    <!-- Checked Status Filter -->
                    <div class="col-md-2 form-group">
                        <label for="checked">Checked Status:</label>
                        <select class="custom-select" id="checked" wire:model="selectedchecked">
                        <option value="all" {{ $selectedchecked === 'all' ? 'selected' : '' }}>All</option>                        <option value="selected_viral" {{ $selectedchecked === 'selected_viral' ? 'selected' : '' }}>Not Ready to Print</option>
                            <option value="ready_print" {{ $selectedchecked === 'ready_print' ? 'selected' : '' }}>Ready To Print</option>
                            <option value="web_link" {{ $selectedchecked === 'web_link' ? 'selected' : '' }}>Web Link</option>
                            <option value="viral_link" {{ $selectedchecked === 'viral_link' ? 'selected' : '' }}>Viral Link</option>

                        </select>
                    </div>

                    <div class="col-md-2 form-group custom-width">
                        <label for="start_date">Start Date:</label>
                        <input type="date" class="date-input" id="start_date" wire:model="startDate">
                    </div>
                    <div class="col-md-2 form-group custom-width">
                        <label for="end_date">End Date:</label>
                        <input type="date" class="date-input" id="end_date" wire:model="endDate">
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
    <div class="result-container">
<!-- First box for "Total Viral" -->
<div class="result-box">
    <h3>New Fatawa For Viral</h3>
    <ul>
        @if($counts['groupedData']->isNotEmpty())
            @foreach($counts['groupedData'] as $daruliftaName => $fatawaData)
                <li>{{ $daruliftaName }}: {{ $fatawaData->count() }}</li>
            @endforeach
        @else
            <li>No data available</li>
        @endif
    </ul>
    <div class="total">Total: {{ $counts['totalViral'] }}</div>
</div>

<!-- Second box for "Total Ready to Print" -->
<div class="result-box">
    <h3>Total Ready to Print</h3>
    <ul>
        @if($counts['groupedData']->isNotEmpty())
            @foreach($counts['groupedData'] as $daruliftaName => $fatawaData)
                <li>{{ $daruliftaName }}: {{ $fatawaData->where('viral_upload', '!=', '')->count() }}</li>
            @endforeach
        @else
            <li>No data available</li>
        @endif
    </ul>
    <div class="total">Total: {{ $counts['totalReadyToPrint'] }}</div>
</div>

<!-- Third box for "Not Ready to Print" -->
<div class="result-box">
    <h3>Not Ready to Print</h3>
    <ul>
        @if($counts['groupedData']->isNotEmpty())
            @foreach($counts['groupedData'] as $daruliftaName => $fatawaData)
                <li>{{ $daruliftaName }}: {{ $fatawaData->where('viral_upload', '')->count() }}</li>
            @endforeach
        @else
            <li>No data available</li>
        @endif
    </ul>
    <div class="total">Total: {{ $counts['totalNotReady'] }}</div>
</div>

<!-- Fourth box for "Viral & Web Upload" -->
<div class="result-box">
    <h3>Viral & Web Upload</h3>
    <ul>
        @if($counts['groupedData']->isNotEmpty())
            @foreach($counts['groupedData'] as $daruliftaName => $fatawaData)
                <li>
                    <strong>{{ $daruliftaName }}:</strong>
                    Viral: {{ $fatawaData->where('viral_link', '!=', '')->count() }}, 
                    Web: {{ $fatawaData->where('web_link', '!=', '')->count() }}
                </li>
            @endforeach
        @else
            <li>No data available</li>
        @endif
    </ul>
    <div class="total">
        <strong>Total Viral:</strong> {{ $counts['totalViralLink'] }}<br>
        <strong>Total Web:</strong> {{ $counts['totalWebUpload'] }}
    </div>
</div>

<!-- Fifth box for "Not Viral and Not Uploaded to Web" -->
<div class="result-box">
    <h3>Not Viral and Not Uploaded to Web</h3>
    <ul>
        @if($counts['groupedData']->isNotEmpty())
            @foreach($counts['groupedData'] as $daruliftaName => $fatawaData)
                @php
                    $readyCount = $fatawaData->where('viral_upload', '!=', '')->count();
                    $viralLinkCount = $fatawaData->where('viral_link', '!=', '')->count();
                    $webCount = $fatawaData->where('web_link', '!=', '')->count();

                    $notViral = max($readyCount - $viralLinkCount, 0);
                    $notWeb = max($readyCount - $webCount, 0);
                @endphp
                <li>
                    <strong>{{ $daruliftaName }}:</strong>
                    Not Viral: {{ $notViral }}, Not Uploaded to Web: {{ $notWeb }}
                </li>
            @endforeach
        @else
            <li>No data available</li>
        @endif
    </ul>
    <div class="total">
        <strong>Total Not Viral:</strong> {{ $counts['totalReadyToPrint'] - $counts['totalViralLink'] }}<br>
        <strong>Total Not Uploaded to Web:</strong> {{ $counts['totalReadyToPrint'] - $counts['totalWebUpload'] }}
    </div>
</div>
    </div>
    <!-- Existing content -->
    <h1>Selected Fatawa For Viral</h1>
    @if ($Admin)
    <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <!-- All Ifta Button -->
        @php
            $isAllIftaActive = request()->route('darulifta') == null;
            $queryParams = [
                'selectedmujeeb' => request('selectedmujeeb'),
                'selectedTimeFrame' => request('selectedTimeFrame'),
                'selectedchecked' => request('selectedchecked'),
                'startDate' => request('startDate'),
                'endDate' => request('endDate')
            ];
        @endphp
        <a href="{{ route('selected-viral', array_merge($queryParams)) }}"
           class="apply-filters-button {{ $isAllIftaActive ? 'apply-filters-button-active' : '' }}">
           All Ifta
        </a>

        <!-- Darulifta Buttons -->
        @foreach($daruliftalist as $daruliftalistn)
            @php
                $isActive = request()->route('darulifta') == $daruliftalistn;
            @endphp
            <a href="{{ route('selected-viral', array_merge(['darulifta' => $daruliftalistn], $queryParams)) }}"
               class="apply-filters-button {{ $isActive ? 'apply-filters-button-active' : '' }}">
                {{ $daruliftalistn }}
            </a>
        @endforeach
    </div>
@endif
        <!-- Search Bar -->
        <div class="search-bar">
        <input
    type="text"
    wire:model.live.debounce.300ms="search"
    placeholder="Search by title, fatwa no, or category..."
>
</div>



<table class="table">
    <thead>
        <tr>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">S.No</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Fatwa No</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Mujeeb</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Darulifta</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Selected Viral Date</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Category</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Ready To Print File</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Web Link</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Viral Link</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Fatwa Detail</th>
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Viral</th> <!-- New column for the viral checkbox -->
        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Actions</th> <!-- Updated column for actions -->
        </tr>
    </thead>
    <tbody>
        @php $serialNumber = ($question_b->currentPage() - 1) * $question_b->perPage() + 1; @endphp
        @foreach($question_b as $question)
            <tr wire:key="question-row-{{ $question->id }}">
                <td>{{ $serialNumber++ }}</td>
                <td class="align-middle text-center" style="position: relative;">
                    <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                            <h6 style="position: relative; display: flex; align-items: center;">
                                @if ($question->viral != 0 && $question->user_name)
                                    @php
                                        $nameParts = explode(' ', $question->user_name);
                                        $initials = count($nameParts) > 1
                                            ? strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1))
                                            : strtoupper(substr($nameParts[0], 0, 2));
                                    @endphp
                                    <!-- Styled pencil-shaped badge -->
                                    <span class="badge pencil-badge bg-secondary text-white me-2" title="{{ $question->user_name }}">
                                        {{ $initials }}
                                    </span>
                                @endif
                                <span style="color: blue;">{{ $question->file_code }}</span>
                            </h6>
                        </div>
                    </div>
                </td>

                <td>{{ $question->sender }}</td>
                <td>{{ $question->darulifta_name }}</td>
                <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="font-weight-bold" style="color: green; width: 5%; white-space: normal;">{{ $question->checked_date }}</span>
                                                        </td>
                <td>{{ $question->category }}</td>
                <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            @if (empty($question->viral_upload))
                                                                @php
                                                                    // Calculate the days difference between the checked date and today
                                                                    $checkedDate = \Carbon\Carbon::parse($question->checked_date);
                                                                    $currentDate = \Carbon\Carbon::now('Asia/Karachi'); // Use Karachi timezone
                                                                    $daysDifference = $checkedDate->diffInDays($currentDate);
                                                                @endphp

                                                                    <!-- Other condition if you want to add something after 7 days -->
                                                                    <span class="font-weight-bold" style="color: red; width: 5%; white-space: normal;">
                                                                        Not Ready ({{ $daysDifference }} days)
                                                                    </span>

                                                            @else
                                                                <a href="{{ route('viewRemain', [
                                                                        'date' => 'viral',  // Use only the base folder without subfolders
                                                                        'filename' => $question->checked_file_name
                                                                    ]) }}" target="_blank">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            @endif
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            @if(empty($question->viral_upload))
                                                                <!-- Condition 1: viral_upload is empty or null -->
                                                                <span class="font-weight-bold" style="color: red; width: 5%; white-space: normal;">Waiting for Ready to Print file</span>
                                                            @else
                                                                <!-- Condition 2: viral_upload is not empty -->
                                                                @if(empty($question->web_link))
                                                                    <!-- Condition 2.1: web_link is empty or null -->
                                                                    @php
                                                                        // Calculate the number of days between viral_upload and current date
                                                                        $currentDate = \Carbon\Carbon::now()->timezone('Asia/Karachi');
                                                                        $viralUploadDate = \Carbon\Carbon::parse($question->viral_upload);
                                                                        $daysDifference = $viralUploadDate->diffInDays($currentDate);
                                                                    @endphp
                                                                    <span class="font-weight-bold" style="color: orange; width: 5%; white-space: normal;">
                                                                        Not uploaded on web — {{ $daysDifference }} days
                                                                    </span>
                                                                @else
                                                                    <!-- Condition 2.2: web_link is not empty -->
                                                                    <a href="{{ $question->web_link }}" target="_blank" class="font-weight-bold" style="color: green;">
                                                                        View On Web
                                                                    </a>
                                                                @endif
                                                            @endif
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            @if(empty($question->viral_upload))
                                                                <!-- Condition 1: viral_upload is empty or null -->
                                                                <span class="font-weight-bold" style="color: red; width: 5%; white-space: normal;">Waiting for Ready to Print file</span>
                                                            @else
                                                                <!-- Condition 2: viral_upload is not empty -->
                                                                @if(empty($question->viral_link))
                                                                    <!-- Condition 2.1: web_link is empty or null -->
                                                                    @php
                                                                        // Calculate the number of days between viral_upload and current date
                                                                        $currentDate = \Carbon\Carbon::now()->timezone('Asia/Karachi');
                                                                        $viralUploadDate = \Carbon\Carbon::parse($question->viral_upload);
                                                                        $daysDifference = $viralUploadDate->diffInDays($currentDate);
                                                                    @endphp
                                                                    <span class="font-weight-bold" style="color: orange; width: 5%; white-space: normal;">
                                                                        Not Viral — {{ $daysDifference }} days
                                                                    </span>
                                                                @else
                                                                    <!-- Condition 2.2: web_link is not empty -->
                                                                    <a href="{{ $question->viral_link }}" target="_blank" class="font-weight-bold" style="color: green;">
                                                                        View Viral Fatwa
                                                                    </a>
                                                                @endif
                                                            @endif
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                        <span class="view">
                                                                <a href="{{ route('viral-fatwa-detail',
                                                                    ['fatwa' => $question->file_code]) }}" target="_blank">
                                                                    <button class="btn btn-outline-primary">
                                                                        <i class="fas fa-eye"></i> View Details
                                                                    </button>




                                                                </a>
                                                            </span>
                                                        </td>
                @php
                    // Check if the user is authenticated and is an Admin.
                    $Admin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                @endphp

                <td>
                    <input
                        type="checkbox"
                        wire:click="toggleViral({{ $question->id }})"
                        {{ $question->viral != 0 ? 'checked' : '' }}
                        @if($question->viral == 22 && !$Admin)
                            disabled
                            title="This Fatwa is selected by Mufi Ali Asghar Sahib; only admin can unselect it"
                        @endif
                    >
                </td>
                <td>
                    <!-- Determine the date parameter -->
                    @php
                        if (empty($question->by_mufti)) {
                            if (empty($question->checker)) {
                                $dateParam = $question->mail_folder_date . $question->darulifta_name . 'Checked';
                            } else {
                                $dateParam = $question->mail_folder_date . $question->darulifta_name . 'Checked_by_' . $question->checker;
                            }
                        } else {
                            $dateParam = $question->mail_folder_date . $question->darulifta_name . 'Checked_by_' . $question->checker . '_' . $question->by_mufti;
                        }
                    @endphp

                    <span class="view">
                        <a href="{{ route('viewCheck', [
                            'date' => $dateParam,
                            'folder' => $question->checked_folder,
                            'filename' => $question->file_name
                        ]) }}" target="_blank">
                            <i class="fas fa-eye"></i>
                        </a>
                    </span>

                    <span class="download">
                        <a href="{{ route('downloadCheck', [
                            'date' => $dateParam,
                            'filename' => $question->file_name,
                            'folder' => $question->checked_folder
                        ]) }}">
                            <i class="fas fa-download"></i>
                        </a>
                    </span>
                </td>
<tr>
<tr wire:key="viral-uploaded-{{ $question->id }}">

                <livewire:viral-upload
                    wire:key="viral-upload-{{ $question->id }}"
                    :fileCode="$question->file_code"
                    :viralUpload="$question->viral_upload"
                    :webLink="$question->web_link"
                    :viralLink="$question->viral_link"
                    :fileName="$question->checked_file_name"
                    :title="$question->title"
                    :id="$question->id"
                    :viral="$question->viral"
      />

            </tr>
        @endforeach
    </tbody>
</table>

<!-- Display error message if the file does not exist -->


<!-- Pagination Links -->
<div class="pagination-links d-flex justify-content-center">
    {{ $question_b->links() }}
</div>



    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
            let startDateFilled = false;
            let endDateFilled = false;

            function updateUrl(reload = false) {
                const url = new URL(window.location.href);
                const params = url.searchParams;

                const selectedMujeeb = document.getElementById('mujeebframe').value;
                const selectedchecked = document.getElementById('checked').value;
                let selectedTimeFrame = document.getElementById('timeframe').value;
                const startDate = document.getElementById('start_date').value;
                const endDate = document.getElementById('end_date').value;

                if (startDateFilled && endDateFilled) {
                    selectedTimeFrame = 'custom';
                    document.getElementById('timeframe').value = 'custom';
                }

                params.set('selectedmujeeb', selectedMujeeb);
                params.set('selectedchecked', selectedchecked);
                params.set('selectedTimeFrame', selectedTimeFrame);

                if (selectedTimeFrame === 'custom') {
                    if (startDate) params.set('startDate', startDate);
                    if (endDate) params.set('endDate', endDate);
                } else {
                    params.delete('startDate');
                    params.delete('endDate');
                }

                const newUrl = `${url.pathname}?${params.toString()}`;
                window.history.replaceState({}, '', newUrl);

                if (reload) {
                    window.location.href = newUrl;
                }
            }

            function checkDatesAndUpdateUrl() {
                const startDate = document.getElementById('start_date').value;
                const endDate = document.getElementById('end_date').value;

                startDateFilled = !!startDate;
                endDateFilled = !!endDate;

                if (startDateFilled && endDateFilled) {
                    updateUrl(true);
                }
            }

            document.getElementById('mujeebframe').addEventListener('change', () => updateUrl(true));
            document.getElementById('timeframe').addEventListener('change', () => updateUrl(true));
            document.getElementById('checked').addEventListener('change', () => updateUrl(true));
            document.getElementById('start_date').addEventListener('change', checkDatesAndUpdateUrl);
            document.getElementById('end_date').addEventListener('change', checkDatesAndUpdateUrl);
        </script>
<div wire:ignore>
           <x-layout bodyClass="g-sidenav-show  bg-gray-200">
           @php

$ViralNazim = Auth::check() && in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray());

@endphp
@if ($ViralNazim)
    <x-navbars.vsidebar activePage="{{ request()->route('darulifta') ?? 'viral-fatawa' }}"></x-navbars.vsidebar>
@else
<x-navbars.sidebar activePage="{{ request()->route('darulifta') ?? 'viral-fatawa' }}"></x-navbars.sidebar>
@endif
</x-layout>
</div>
    <x-footers.auth></x-footers.auth>

</main>



</div>
