
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلاق فتاوی مینجمنٹ</title>
    <style>


        .openbtn {
  font-size: 16px;
  cursor: pointer;
  background-color: #2e3a59;
  color: white;
  padding: 10px 15px;
  border: none;
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 20;
  border-radius: 5px;
}

.openbtn:hover {
  background-color: #3b4a6b;
}
 .sidebar.hidden {
        display: none;
    }
    .editor-expanded {
        flex-grow: 1;
        width: 100%; /* Full width when sidebar is hidden */
    }
    .highlight {
    background-color: yellow;
    color: black;
    font-weight: bold;
    padding: 2px;
    border-radius: 3px;
}

/* General Body Styles */
body {
    font-family: 'Jameel Noori Nastaleeq', serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
    color: #333;
    direction: rtl;
    line-height: 1.8;
}

/* Main Editor */
.main-editor {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    max-width: 100%;
}

/* Alert Message */
.alert {
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    font-size: 16px;
    text-align: center;
}
.alert-danger {
    border: 1px solid red;
    background-color: #f8d7da;
    color: #721c24;
}

/* Editor Container */
.editor-container {
    width: 100%;
    max-width: 21cm; /* A4 width */
    height: auto;
    min-height: 29.7cm; /* A4 height */
    background-color: white;
    padding: 20px;
    border: 1px solid #ddd;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    margin-bottom: 20px;
    box-sizing: border-box;
}

textarea#editor {
    width: 100%;
    height: 100%;
    font-family: 'Jameel Noori Nastaleeq', serif;
    font-size: 18px;
    line-height: 1.8;
    border: none;
    outline: none;
    resize: none;
    padding: 10px;
    box-sizing: border-box;
    direction: rtl;
}

/* Form Controls */
form {
    width: 100%;
    max-width: 21cm; /* Match editor width */
    display: flex;
    flex-direction: column;
    gap: 15px;
}

label {
    font-size: 16px;
    color: #444;
}

input.form-control, select.form-control {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    font-family: inherit;
}

/* button {
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button.btn-primary {
    background-color: #007bff;
    color: white;
}

button.btn-primary:hover {
    background-color: #0056b3;
}

button.btn-secondary {
    background-color: #6c757d;
    color: white;
}

button.btn-secondary:hover {
    background-color: #5a6268;
} */

/* Responsive Design */
@media (max-width: 768px) {
    .editor-container {
        width: 100%;
        min-height: auto;
        height: auto;
    }

    form {
        max-width: 100%;
    }

    button {
        width: auto;
    }
}

/* Font-Face Definitions */
@font-face {
    font-family: 'Jameel Noori Nastaleeq';
    src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');
}
@font-face {
    font-family: 'Naskh Unicode';
    src: url('/storage/fonts/UrduNaskhUnicode.ttf');
}
@font-face {
    font-family: 'Al_Mushaf';
    src: url('/storage/fonts/Al_Mushaf.ttf');
}

      /* Container for Sidebar and Editor */
.containe {
    display: flex;
    flex-direction: row; /* Align sidebar and editor in a single row */
    height: 100vh; /* Take full viewport height */
    width: 100%; /* Full width */

    overflow: hidden; /* Prevent overflow issues */
}

/* Sidebar Styles */
.sidebar {
    width: 25%; /* Fixed width for the sidebar */
    background-color: #2e3a59; /* Deep blue-gray background */
    color: #ffffff; /* White text for contrast */
    padding: 20px; /* Add spacing inside the sidebar */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Subtle shadow for depth */
    overflow-y: auto; /* Enable vertical scrolling */
    position: sticky; /* Keep sidebar fixed when scrolling */
    top: 0; /* Sticky positioning starts at the top */
    max-height: 100vh; /* Prevent overflow beyond the viewport height */
    z-index: 10; /* Ensure it appears above other elements if needed */
}

/* Sidebar Scrollbar Styling */
.sidebar::-webkit-scrollbar {
    width: 8px; /* Width of the scrollbar */
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.3); /* Light scrollbar thumb */
    border-radius: 4px; /* Rounded corners for the thumb */
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.5); /* Slightly brighter on hover */
}

.sidebar::-webkit-scrollbar-track {
    background-color: transparent; /* Transparent scrollbar track */
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%; /* Sidebar takes full width on smaller screens */
        position: relative; /* Normal flow positioning for mobile */
        box-shadow: none; /* Remove shadow for simplicity */
    }

    .containe {
        flex-direction: column; /* Stack sidebar and editor vertically */
    }
}




        .add-to-editor-btn {
            display: block;
            width: 100%;
            background-color: #007bff;
            color: #fff;
            padding: 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }

        .add-to-editor-btn:hover {
            background-color: #0056b3;
        }

        /* Main Editor Styles */
        .main-editor {
            flex-grow: 1;
            padding: 20px;
            background-color: #fff;
            overflow-y: auto;
            order: 2; /* Editor comes second */
        }

        .main-editor h1 {
            text-align: center;
            color: #2e3a59;
            margin-bottom: 20px;
        }

        .details-card {
            background-color: #f9f9f9;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .card:hover {
        background-color: #f0f0f0;
    }

    .insert-btn {
        background-color: #007bff;
        color: #ffffff;
        border: none;
        border-radius: 3px;
        padding: 5px 10px;
        cursor: pointer;
    }

    .insert-btn:hover {
        background-color: #0056b3;
    }
        .juziyaat {
            font-family: 'Noto Naskh Arabic', serif;
            font-size: 18px;
            line-height: 1.8;
            color: #333;
        }
        .note-editor .dropdown-toggle::after {
    display: none !important;
}
    </style>
<!-- Bootstrap CSS -->
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<!-- jQuery and Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- FontAwesome CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">

</head>
<body>
<div class="containe">

<aside class="sidebar" id="sidebar">


@livewire('talaq-sidebar')



</aside>

 <!-- Main Editor -->
 <main class="main-editor main-content position-relative max-height-vh-100 h-100 border-radius-lg">
 <button id="toggleSidebarBtn" class="openbtn" style="position: absolute; top: 10px; right: 10px; z-index: 20;">

        </button>
    @if (session('error'))
    <div class="alert alert-danger" style="padding: 10px; border: 1px solid red; border-radius: 5px; background-color: #f8d7da; color: #721c24;">
        {{ session('error') }}
    </div>
@endif

<div class="card-header d-flex justify-content-between align-items-center">

       <a href="{{ $nextId ? route('talaq-fatwa-edit', ['id' => $nextId]) : '#' }}"
       class="btn btn-secondary"
       @if(!$nextId) disabled @endif>Next</a>

   <!-- File Codes Section -->
   @if(isset($fileCodes) && count($fileCodes) > 0)
        <div>
            @foreach($fileCodes as $fileId => $fileCode)
                <a href="{{ route('talaq-fatwa-edit', ['id' => $fileId]) }}"
                   class="badge {{ $fileId == $currentId ? 'bg-primary text-white' : 'bg-light text-dark' }} mx-1"
                   style="text-decoration: none;">
                    {{ $fileCode }}
                </a>
            @endforeach
        </div>
    @endif
    <a href="{{ $previousId ? route('talaq-fatwa-edit', ['id' => $previousId]) : '#' }}"
       class="btn btn-secondary"
       @if(!$previousId) disabled @endif>Previous</a>

</div>

            @if ($record)
    <h5 class="card-title">Record ID: {{ $record->id }}</h5>
    <form action="{{ route('mark-as-checked', ['id' => $record->id]) }}" method="POST" id="mainForm">
        @csrf
        <div class="editor-container">
            <textarea name="content" id="editor">{{ $record->content }}</textarea>
        </div>

        <table class="table">
    <thead>
        <tr>
            <th>Grade</th>
            <th>Tasurat</th>
            <th>Instructions</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td class="align-middle text-center" style="width: 10%; white-space: normal;">
                <select name="checked_grade_{{ $record->id }}" data-id="{{ $record->id }}">
                    <option value="">Select Grade</option>
                    <option value="Munasib">Munasib</option>
                    <option value="Bhetar">Bhetar</option>
                    <option value="Mumtaz">Mumtaz</option>
                </select>
            </td>
            <td class="align-middle text-center" style="width: 15%; white-space: normal;">
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <livewire:tasurat-dropdown />
                    <i class="fas fa-edit" data-toggle="modal" data-target="#myModal" style="cursor: pointer; margin-left: 10px;"></i>
                </div>
            </td>
            <td class="align-middle text-center" style="width: 10%; white-space: normal;">
                <textarea class="checked_instructions_textarea" name="checked_instructions_{{ $record->id }}" data-id="{{ $record->id }}" style="width: 100%;"></textarea>
                <div style="display: flex; align-items: center; justify-content: space-between;">
                    <livewire:instruction-dropdown />
                    <i class="fas fa-edit" data-toggle="modal" data-target="#instructionModal" style="cursor: pointer; margin-left: 10px;"></i>
                </div>
            </td>
        </tr>
    </tbody>
</table>

        <input type="hidden" name="folder_label" id="folderLabel">
        <button type="button" class="btn btn-success mt-3 action-btn" data-label="ok">Ok</button>
        <button type="button" class="btn btn-warning mt-3 action-btn" data-label="mahl-e-nazar">Mahl-e-Nazar</button>
    </form>
@else
    <div class="card-body">
        <p>No record found.</p>
    </div>
@endif
    </div>


  <!-- The Modal -->
  <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="max-width: 800px;">
      <div class="modal-content">

        <!-- Modal Header -->
        <div class="modal-header">
          <h4 class="modal-title">Tasurat Editor</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

        <!-- Modal body -->
        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
    <!-- Livewire Component -->
    <livewire:tasurat-editor/>
</div>

        <!-- Modal footer -->
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>

      </div>
    </div>
  </div>
</div>

<div class="container">
  <!-- The Modal -->
  <div class="modal fade" id="instructionModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable" role="document" style="max-width: 800px;">
      <div class="modal-content">

        <!-- Modal Header -->
        <div class="modal-header">
          <h4 class="modal-title">Instruction Editor</h4>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

        <!-- Modal body -->
        <div class="modal-body" style="max-height: 400px; overflow-y: auto;">
          <!-- Livewire Component -->
          <livewire:instruction-editor/>
        </div>

        <!-- Modal footer -->
        <div class="modal-footer">
          <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
        </div>

      </div>
    </div>
  </div>
</div>
    <script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.9.0/dist/summernote-lite.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/summernote@0.9.0/dist/summernote-lite.min.js"></script>
    <script>
$(document).ready(function () {
    $('#editor').summernote({
      height: 1000, // Set the editor height
      toolbar: [
        ['style', ['style']],
        ['font', ['bold', 'italic', 'underline', 'clear', 'fontname']],
        ['fontstyle', ['fontsize', 'fontfamily']],
        ['color', ['color']],
        ['para', ['ul', 'ol', 'paragraph']],
        ['insert', ['link', 'picture', 'video']],
        ['view', ['fullscreen', 'codeview', 'help']],
        // ['custom', ['removeWhitespace', 'increaseGap', 'decreaseGap']]
      ],

      fontNames: [
        'Jameel Noori Nastaleeq',
        'Naskh Unicode',
        'Al Mushaf',
        'Arial',
        'Times New Roman',
        'Courier New'
      ],
      fontNamesIgnoreCheck: [
        'Jameel Noori Nastaleeq',
        'Naskh Unicode',
        'Al Mushaf'
      ],
      callbacks: {

        onInit: function () {
          $('head').append(`
            <style>
              @font-face {
                  font-family: 'Jameel Noori Nastaleeq';
                  src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');
              }
              @font-face {
                  font-family: 'Naskh Unicode';
                  src: url('/storage/fonts/UrduNaskhUnicode.ttf');
              }
              @font-face {
                  font-family: 'Al Mushaf';
                  src: url('/storage/fonts/Al_Mushaf.ttf');
              }
              .sidebar-content {
                  background-color: #f0f8ff; /* Light blue background */
                  border-left: 4px solid #007BFF; /* Blue border on the left */
                  padding: 5px;
                  margin: 5px 0;
              }
                  .note-editor .note-editable { /* Target the editable area */
                            background-color: white; /* Add white background color */

                        }
                        .note-editor .note-editable p { /* Target paragraph within the editable area */
                          text-align: right;
                        }
                          /* Fix for double dropdown arrows in Summernote with Bootstrap */
                    .note-editor .dropdown-toggle::after {
                    display: none !important;

            </style>
          `);


        },
            onFullscreen: function () {
                $('.editor-container').hide(); // Hide the editor container
                $('#sidebar').addClass('hidden'); // Hide the sidebar
            },
            onBlur: function () {
                $('.editor-container').show(); // Show the editor container
                $('#sidebar').removeClass('hidden'); // Show the sidebar
            }
        }
    });
    // Add shortcut key (e.g., Ctrl + Alt + N) for Jameel Noori Nastaleeq
    $(document).on('keydown', function (e) {
        if (e.ctrlKey && e.altKey && e.key === 'n') { // Ctrl + Alt + N
            const contentToInsert = `
                <p dir="rtl" style="text-align: right; font-family: 'Jameel Noori Nastaleeq'; font-size: 21px;">
                    <br>
                </p>
            `;

            // Focus on the editor and insert content
            const summernoteEditor = $('#editor');
            summernoteEditor.summernote('focus');
            summernoteEditor.summernote('pasteHTML', contentToInsert);

            // Prevent default browser behavior
            e.preventDefault();
        }
    });

    const toggleButton = document.getElementById('toggleSidebarBtn');
    const sidebar = document.getElementById('sidebar');
    const mainEditor = document.getElementById('mainEditor');

    toggleButton.addEventListener('click', () => {
        sidebar.classList.toggle('hidden');
        mainEditor.classList.toggle('editor-expanded');
    });

    $(document).on('click', '.copy-btn', function (e) {
    e.stopPropagation(); // Prevent other click events from triggering
    const cardName = $(this).closest('.card').attr('data-name');

    // Construct the HTML to insert
    const contentToInsert = `
        <div class="sidebar-wrapper">
            <div class="sidebar-content" contenteditable="false">${cardName}</div>

         <p dir="RTL" style="text-align:right; font-family:'Jameel Noori Nastaleeq'; font-size:21px; direction: rtl;">
              <br>
            </p>
    `;

    // Get the Summernote editor instance
    const summernoteEditor = $('#editor');

    // Ensure the editor is focused before inserting content
    summernoteEditor.summernote('focus');

    // Insert the HTML content at the current cursor position
    summernoteEditor.summernote('pasteHTML', contentToInsert);
});
});
document.addEventListener('DOMContentLoaded', function () {
    const actionButtons = document.querySelectorAll('.action-btn');
    const folderLabelInput = document.getElementById('folderLabel');
    const mainForm = document.getElementById('mainForm');

    actionButtons.forEach(button => {
        button.addEventListener('click', function () {
            // Sync Summernote content with the hidden textarea
            const summernoteEditor = $('#editor');
            const summernoteContent = summernoteEditor.summernote('code');
            $('#editor').val(summernoteContent);

            // Set the folder_label value based on the clicked button
            folderLabelInput.value = this.dataset.label;

            // Submit the form
            mainForm.submit();
        });
    });
});
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
        // Select all dropdowns
        const dropdowns = document.querySelectorAll('.tasurat_dropdown');

        // Add event listeners to all dropdowns
        dropdowns.forEach((dropdown) => {
            dropdown.addEventListener('change', function() {
                // Find the corresponding textarea using data-id
                const dataId = dropdown.closest('td').querySelector('.checked_instructions_textarea').dataset.id;
                const textarea = document.querySelector(`.checked_instructions_textarea[data-id="${dataId}"]`);
                if (textarea) {
                    // Get the selected option text
                    const selectedText = dropdown.options[dropdown.selectedIndex].text;

                    // Append the selected text to the textarea with a line break
                    textarea.value += (textarea.value ? '\n' : '') + selectedText;
                }
            });
        });
    });
    </script>

      <!-- <script>
        // JavaScript for toggling sidebar visibility
        const toggleButton = document.getElementById('toggleSidebarBtn');
        const sidebar = document.getElementById('sidebar');
        const mainEditor = document.getElementById('mainEditor');

        toggleButton.addEventListener('click', () => {
            sidebar.classList.toggle('hidden'); // Toggle sidebar visibility
            mainEditor.classList.toggle('editor-expanded'); // Adjust editor width
        });
    </script> -->
</body>
</html>

