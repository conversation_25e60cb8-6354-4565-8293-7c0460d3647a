
<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Checking Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>

        <!-- Create Fatawa Entry Form -->
        <style>
            /* ... existing styles ... */
            .hidden-cell {
                display: none;
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
         
            /* ... remaining styles ... */
        </style>
        
        <div class="container">
            <div class="container">
                <h1 class="mb-4">Update Mail Folder</h1>
                <title>Onclick increase Table Rows</title>
                <script type="text/javascript">
                    function addRows() {
                        var table = document.getElementById('emptbl');
                        var newRow = table.insertRow();
                        var firstRow = table.rows[1]; // Get the second row (first data row)

                        // Clone the second row and its cells
                        for (var i = 0; i < firstRow.cells.length; i++) {
                            var newCell = newRow.insertCell(i);
                            newCell.innerHTML = firstRow.cells[i].innerHTML;
                        }

                        // Clear input values
                        var inputs = newRow.getElementsByTagName('input');
                        for (var i = 0; i < inputs.length; i++) {
                            inputs[i].value = '';
                        }
                    }

                    function deleteRows() {
                        var table = document.getElementById('emptbl');
                        var rowCount = table.rows.length;
                        if (rowCount > 2) {
                            table.deleteRow(rowCount - 1);
                        } else {
                            alert('There should be at least one row');
                        }
                    }
                </script>
                </head>
                <body>
                    <form action="{{ route('scmail', ['id' => $ecmail->id]) }}" method="POST">
                        @csrf 
                        <table id="emptbl">
                            <thead>
                                <tr>
                                    <th class="hidden-cell"></th>
                                    <th>Mail Received Date</th>
                                    <th>Darulifta</th> 
                                    <th>Mail Folder Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}" />
                                    <td id="col1"><input type="Date" class="form-control" name="date[]" value="{{$ecmail->mail_recived_date}}" /></td> 
                                    <td id="col2" class="form-control"> 
                                        <select name="darulifta[]" id="ifta"> 
                                            <option value="0" {{ $ecmail->darulifta == 0 ? 'selected' : '' }}>Select Ifta</option> 
                        <option value="1" {{ $ecmail->darulifta == 'Noorulirfan' ? 'selected' : '' }}>Noorulirfan</option>
                        <option value="2" {{ $ecmail->darulifta == 'Faizan e Ajmair' ? 'selected' : '' }}>Faizan e Ajmair</option>
                        <option value="3" {{ $ecmail->darulifta == 'Gulzare Taiba' ? 'selected' : '' }}>Gulzare Taiba</option>
                        <option value="4" {{ $ecmail->darulifta == 'Iqtisaad' ? 'selected' : '' }}>Iqtisaad</option>
                                        </select> 
                                    </td> 
                                    <td id="col3"><input type="date" class="form-control" name="fdate[]" value="{{$ecmail->mail_folder_date}}" /></td> 
                                </tr>
                            </tbody>
                        </table>
                        
                        <div style="text-align: center; margin-top: 20px;">
                            <input type="button" class="btn btn-primary" value="Add Row" onclick="addRows()" />
                            <input type="button" class="btn btn-danger" value="Delete Row" onclick="deleteRows()" />
                            <input type="submit" class="btn btn-success" value="Update" />
                        </div>
                    </form> 
                </body> 
            </div>
        </div>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
