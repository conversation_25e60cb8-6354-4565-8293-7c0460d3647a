<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Header extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'serial_number',
        'click_count',
    ];

    /**
     * Get the parents associated with the header.
     */
    public function parents()
    {
        return $this->hasMany(ParentModel::class,'header_id');
    }
}
