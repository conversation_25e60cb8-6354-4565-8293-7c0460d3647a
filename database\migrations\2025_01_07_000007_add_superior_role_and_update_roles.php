<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new roles to the roles table
        $roles = [
            'Superior', // <PERSON><PERSON><PERSON>
            'Nazim',    // Admin role for system management
            'Muawin',   // Assistant role (alternative name for Mujeeb assistants)
        ];

        foreach ($roles as $roleName) {
            Role::firstOrCreate(['name' => $roleName]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the roles we added
        Role::whereIn('name', ['Superior', '<PERSON><PERSON>', '<PERSON>aw<PERSON>'])->delete();
    }
};
