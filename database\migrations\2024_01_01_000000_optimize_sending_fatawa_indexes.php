<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations for optimizing Sending Fatawa page performance.
     */
    public function up(): void
    {
        // Add indexes for uploaded_files table to optimize queries
        Schema::table('uploaded_files', function (Blueprint $table) {
            // Composite index for main filtering
            $table->index(['selected', 'darulifta_name', 'mail_folder_date'], 'idx_selected_darulifta_date');
            
            // Index for checker filtering
            $table->index(['checker', 'selected'], 'idx_checker_selected');
            
            // Index for sender filtering
            $table->index(['sender', 'selected'], 'idx_sender_selected');
            
            // Index for transfer_by filtering
            $table->index(['transfer_by', 'selected'], 'idx_transfer_selected');
            
            // Index for date filtering
            $table->index(['mail_folder_date', 'selected'], 'idx_date_selected');
            
            // Composite index for time-based queries
            $table->index(['selected', 'mail_folder_date', 'darulifta_name', 'checker'], 'idx_time_queries');
        });

        // Add indexes for checker table
        Schema::table('checker', function (Blueprint $table) {
            // Index for checker_name lookups
            if (!Schema::hasIndex('checker', 'idx_checker_name')) {
                $table->index('checker_name', 'idx_checker_name');
            }
            
            // Index for folder_id lookups
            if (!Schema::hasIndex('checker', 'idx_folder_id')) {
                $table->index('folder_id', 'idx_folder_id');
            }
        });

        // Add indexes for daruliftas table
        Schema::table('daruliftas', function (Blueprint $table) {
            // Index for darul_name lookups
            if (!Schema::hasIndex('daruliftas', 'idx_darul_name')) {
                $table->index('darul_name', 'idx_darul_name');
            }
        });

        // Add indexes for questions table (used in que_day_r)
        Schema::table('questions', function (Blueprint $table) {
            // Index for question_branch filtering
            if (!Schema::hasIndex('questions', 'idx_question_branch')) {
                $table->index('question_branch', 'idx_question_branch');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('uploaded_files', function (Blueprint $table) {
            $table->dropIndex('idx_selected_darulifta_date');
            $table->dropIndex('idx_checker_selected');
            $table->dropIndex('idx_sender_selected');
            $table->dropIndex('idx_transfer_selected');
            $table->dropIndex('idx_date_selected');
            $table->dropIndex('idx_time_queries');
        });

        Schema::table('checker', function (Blueprint $table) {
            if (Schema::hasIndex('checker', 'idx_checker_name')) {
                $table->dropIndex('idx_checker_name');
            }
            if (Schema::hasIndex('checker', 'idx_folder_id')) {
                $table->dropIndex('idx_folder_id');
            }
        });

        Schema::table('daruliftas', function (Blueprint $table) {
            if (Schema::hasIndex('daruliftas', 'idx_darul_name')) {
                $table->dropIndex('idx_darul_name');
            }
        });

        Schema::table('questions', function (Blueprint $table) {
            if (Schema::hasIndex('questions', 'idx_question_branch')) {
                $table->dropIndex('idx_question_branch');
            }
        });
    }
};
