@if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <h5 class="mb-0">
                <i class="fas fa-sitemap me-2"></i>
                Darulifta Navigation
            </h5>
        </div>
        <div class="modern-card-body">
            <div class="d-flex flex-wrap gap-2 align-items-center">
                @php
                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                    $isAllIftaActive = request()->route('darulifta') == null;
                    $tempStartDate = $this->tempStartDate;
                    $tempEndDate = $this->tempEndDate;
                @endphp
                
                <a href="{{ route('recived-fatawa', [
                    'selectedmufti' => $this->selectedmufti,
                    'selectedTimeFrame' => $this->selectedTimeFrame,
                    'startDate' => $tempStartDate,
                    'endDate' => $tempEndDate,
                    'selectedexclude' => $this->selectedexclude,
                    'showDetail' => $showDetail ? '1' : '0',
                    'showQue' => $showQue ? '1' : '0',
                    'showChat' => $showChat ? '1' : '0',
                    'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                ]) }}" 
                   class="btn-modern {{ $isAllIftaActive ? 'btn-primary-modern' : 'btn-outline-modern' }}">
                    <i class="fas fa-globe me-2"></i>
                    All Ifta
                </a>

                @foreach($daruliftalist as $daruliftalistn)
                    @php
                        $isActive = request()->route('darulifta') == $daruliftalistn;
                    @endphp
                    <a href="{{ route('recived-fatawa', [
                        'darulifta' => $daruliftalistn,
                        'selectedmufti' => $this->selectedmufti,
                        'selectedTimeFrame' => $this->selectedTimeFrame,
                        'startDate' => $tempStartDate,
                        'endDate' => $tempEndDate,
                        'selectedexclude' => $this->selectedexclude,
                        'showDetail' => $showDetail ? '1' : '0',
                        'showQue' => $showQue ? '1' : '0',
                        'showChat' => $showChat ? '1' : '0',
                        'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                    ]) }}" 
                       class="btn-modern {{ $isActive ? 'btn-primary-modern' : 'btn-outline-modern' }}">
                        <i class="fas fa-building me-2"></i>
                        {{ $daruliftalistn }}
                    </a>
                @endforeach
            </div>
        </div>
    </div>
@endif
