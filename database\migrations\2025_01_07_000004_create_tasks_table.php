<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->enum('type', ['daily', 'weekly'])->default('daily');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade');
            $table->foreignId('assigned_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('department_id')->nullable()->constrained()->onDelete('set null');
            $table->date('due_date');
            $table->date('completed_at')->nullable();
            $table->text('completion_notes')->nullable();
            $table->integer('priority')->default(1); // 1=low, 2=medium, 3=high
            $table->timestamps();
            
            // Indexes for efficient queries
            $table->index(['assigned_to', 'status', 'due_date']);
            $table->index(['assigned_by', 'status']);
            $table->index(['department_id', 'status']);
            $table->index(['type', 'due_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
