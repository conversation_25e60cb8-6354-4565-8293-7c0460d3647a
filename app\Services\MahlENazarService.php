<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserRestriction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MahlENazarService
{
    const MAHL_E_NAZAR_LIMIT = 12;

    /**
     * Get the count of Mahl-e-Nazar fatawa for a user.
     * This matches the exact logic used in mahlenazar-fatawa page.
     */
    public function getMahlENazarCount($userName): int
    {
        // Get only the latest Mahl-e-Nazar entry for each file code
        $latestIds = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        // Count fatawa in Mahl-e-Nazar folder that are still pending (not moved to ok/Tahqiqi)
        return DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->where('u1.selected', 1)
            ->where('u1.sender', $userName)
            ->whereIn('u1.id', $latestIds) // Only count latest entries
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code') // Only count those not yet moved to ok/Tahqiqi
            ->count();
    }

    /**
     * Check if user has exceeded the Mahl-e-Nazar limit.
     */
    public function hasExceededLimit(User $user): bool
    {
        $count = $this->getMahlENazarCount($user->name);
        return $count >= self::MAHL_E_NAZAR_LIMIT;
    }

    /**
     * Get users who have exceeded the limit.
     */
    public function getUsersExceedingLimit(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $exceedingUsers = [];
        
        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            if ($count >= self::MAHL_E_NAZAR_LIMIT) {
                $exceedingUsers[] = [
                    'user' => $user,
                    'count' => $count,
                    'excess' => $count - self::MAHL_E_NAZAR_LIMIT,
                ];
            }
        }

        return $exceedingUsers;
    }

    /**
     * Apply restriction to user who exceeded limit.
     */
    public function applyLimitRestriction(User $user): void
    {
        $count = $this->getMahlENazarCount($user->name);
        
        if ($count >= self::MAHL_E_NAZAR_LIMIT) {
            UserRestriction::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'restriction_type' => UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED,
                    'is_active' => true,
                ],
                [
                    'reason' => "User has {$count} fatawa in Mahl-e-Nazar status (limit: " . self::MAHL_E_NAZAR_LIMIT . ")",
                    'restricted_by' => $user->id, // System restriction
                    'restricted_at' => now(),
                ]
            );
        }
    }

    /**
     * Remove restriction if user is below limit.
     */
    public function removeLimitRestriction(User $user): void
    {
        $count = $this->getMahlENazarCount($user->name);
        
        if ($count < self::MAHL_E_NAZAR_LIMIT) {
            UserRestriction::where('user_id', $user->id)
                ->where('restriction_type', UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED)
                ->where('is_active', true)
                ->update([
                    'is_active' => false,
                    'lifted_by' => $user->id,
                    'lifted_at' => now(),
                    'lift_reason' => 'Mahl-e-Nazar count below limit',
                ]);
        }
    }

    /**
     * Check and update all users' limit restrictions.
     */
    public function updateAllUserRestrictions(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $updated = [
            'restricted' => 0,
            'unrestricted' => 0,
            'total_checked' => 0,
        ];

        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            $updated['total_checked']++;

            if ($count >= self::MAHL_E_NAZAR_LIMIT) {
                $this->applyLimitRestriction($user);
                $updated['restricted']++;
            } else {
                $this->removeLimitRestriction($user);
                $updated['unrestricted']++;
            }
        }

        return $updated;
    }

    /**
     * Get Mahl-e-Nazar statistics.
     */
    public function getStatistics(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $stats = [
            'total_users' => $users->count(),
            'users_at_limit' => 0,
            'users_over_limit' => 0,
            'users_near_limit' => 0, // 10-11 fatawa
            'total_mahl_e_nazar' => 0,
            'average_per_user' => 0,
            'users_with_restrictions' => 0,
        ];

        $totalCount = 0;
        
        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            $totalCount += $count;

            if ($count >= self::MAHL_E_NAZAR_LIMIT) {
                if ($count == self::MAHL_E_NAZAR_LIMIT) {
                    $stats['users_at_limit']++;
                } else {
                    $stats['users_over_limit']++;
                }
            } elseif ($count >= 10) {
                $stats['users_near_limit']++;
            }
        }

        $stats['total_mahl_e_nazar'] = $totalCount;
        $stats['average_per_user'] = $users->count() > 0 ? round($totalCount / $users->count(), 1) : 0;
        $stats['users_with_restrictions'] = UserRestriction::where('restriction_type', UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED)
            ->where('is_active', true)
            ->count();

        return $stats;
    }

    /**
     * Get detailed user report with Mahl-e-Nazar counts.
     */
    public function getUserReport(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->with(['activeRestrictions' => function ($query) {
            $query->where('restriction_type', UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED);
        }])->get();

        $report = [];
        
        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            $hasRestriction = $user->activeRestrictions->isNotEmpty();
            
            $report[] = [
                'user' => $user,
                'mahl_e_nazar_count' => $count,
                'remaining_capacity' => max(0, self::MAHL_E_NAZAR_LIMIT - $count),
                'is_at_limit' => $count >= self::MAHL_E_NAZAR_LIMIT,
                'is_near_limit' => $count >= 10 && $count < self::MAHL_E_NAZAR_LIMIT,
                'has_restriction' => $hasRestriction,
                'status' => $this->getUserStatus($count, $hasRestriction),
            ];
        }

        // Sort by count descending
        usort($report, function ($a, $b) {
            return $b['mahl_e_nazar_count'] <=> $a['mahl_e_nazar_count'];
        });

        return $report;
    }

    /**
     * Get user status based on count and restrictions.
     */
    private function getUserStatus(int $count, bool $hasRestriction): string
    {
        if ($hasRestriction) {
            return 'restricted';
        }

        if ($count >= self::MAHL_E_NAZAR_LIMIT) {
            return 'at_limit';
        }

        if ($count >= 10) {
            return 'near_limit';
        }

        return 'normal';
    }

    /**
     * Check if user is manually allowed by Nazim/Admin.
     */
    public function isManuallyAllowed(User $user): bool
    {
        return UserRestriction::where('user_id', $user->id)
            ->where('restriction_type', UserRestriction::TYPE_MAHL_E_NAZAR_MANUAL_ALLOW)
            ->where('is_active', true)
            ->exists();
    }

    /**
     * Manually allow user to submit fatawa (Nazim/Admin only).
     */
    public function manuallyAllowUser(User $user, User $allowedBy): void
    {
        // Remove any existing manual restrictions
        UserRestriction::where('user_id', $user->id)
            ->where('restriction_type', UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $allowedBy->id,
                'lifted_at' => now(),
                'lift_reason' => 'Manually allowed by ' . $allowedBy->name,
            ]);

        // Create manual allow record
        UserRestriction::updateOrCreate(
            [
                'user_id' => $user->id,
                'restriction_type' => UserRestriction::TYPE_MAHL_E_NAZAR_MANUAL_ALLOW,
                'is_active' => true,
            ],
            [
                'reason' => 'Manually allowed to submit fatawa by ' . $allowedBy->name,
                'restricted_by' => $allowedBy->id,
                'restricted_at' => now(),
            ]
        );
    }

    /**
     * Remove manual allow and apply default restriction.
     */
    public function removeManualAllow(User $user, User $restrictedBy): void
    {
        // Remove manual allow
        UserRestriction::where('user_id', $user->id)
            ->where('restriction_type', UserRestriction::TYPE_MAHL_E_NAZAR_MANUAL_ALLOW)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $restrictedBy->id,
                'lifted_at' => now(),
                'lift_reason' => 'Manual allow removed by ' . $restrictedBy->name,
            ]);

        // Apply default restriction
        UserRestriction::updateOrCreate(
            [
                'user_id' => $user->id,
                'restriction_type' => UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED,
                'is_active' => true,
            ],
            [
                'reason' => 'Default restriction - requires manual approval to submit fatawa',
                'restricted_by' => $restrictedBy->id,
                'restricted_at' => now(),
            ]
        );
    }

    /**
     * Can user submit new fatawa?
     */
    public function canSubmitFatawa(User $user): bool
    {
        // Nazim/Admin can always submit
        if ($user->isNazim()) {
            return true;
        }

        // Check if user is manually allowed - this overrides limit checks
        if ($this->isManuallyAllowed($user)) {
            return true;
        }

        // Default: all users are restricted unless manually allowed
        return false;
    }

    /**
     * Get restriction message for user.
     */
    public function getRestrictionMessage(User $user): ?string
    {
        // Nazim/Admin have no restrictions
        if ($user->isNazim()) {
            return null;
        }

        // Check if user is manually allowed first - this overrides limit checks
        if ($this->isManuallyAllowed($user)) {
            // User is manually allowed, no restrictions
            return null;
        }

        $count = $this->getMahlENazarCount($user->name);

        // Check if user exceeded the 12 fatawa limit
        if ($count >= self::MAHL_E_NAZAR_LIMIT) {
            return "You have {$count} fatawa in Mahl-e-Nazar status. The limit is " . self::MAHL_E_NAZAR_LIMIT . ". Please resolve existing fatawa before submitting new ones.";
        }

        // Default: user is restricted unless manually allowed
        return "You are currently restricted from submitting fatawa. Please contact your Nazim or Admin for approval.";
    }

    /**
     * Get detailed Mahl-e-Nazar breakdown by Darulifta and Mujeeb.
     * This matches the exact summary data shown in mahlenazar-fatawa page.
     */
    public function getDetailedBreakdown(): array
    {
        // Get only the latest Mahl-e-Nazar entry for each file code
        $latestIds = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        // Get all Mahl-e-Nazar fatawa with Darulifta breakdown
        $query = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->where('u1.selected', 1)
            ->whereIn('u1.id', $latestIds) // Only get latest entries
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.darulifta_name', 'u1.sender')
            ->get();

        $summaryData = [];
        $totalCounts = 0;

        foreach ($query as $record) {
            $daruliftaName = $record->darulifta_name;
            $sender = $record->sender;

            if (!isset($summaryData[$daruliftaName])) {
                $summaryData[$daruliftaName] = [
                    'senders' => [],
                    'total' => 0
                ];
            }

            if (!isset($summaryData[$daruliftaName]['senders'][$sender])) {
                $summaryData[$daruliftaName]['senders'][$sender] = 0;
            }

            $summaryData[$daruliftaName]['senders'][$sender]++;
            $summaryData[$daruliftaName]['total']++;
            $totalCounts++;
        }

        return [
            'summaryData' => $summaryData,
            'totalCounts' => $totalCounts
        ];
    }

    /**
     * Get Mahl-e-Nazar count for a specific user and darulifta.
     */
    public function getMahlENazarCountByDarulifta($userName, $daruliftaName = null): int
    {
        // Get only the latest Mahl-e-Nazar entry for each file code
        $latestIds = DB::table('uploaded_files')
            ->where('checked_folder', 'Mahl-e-Nazar')
            ->select(DB::raw('MAX(id) as id'))
            ->groupBy('file_code')
            ->pluck('id')
            ->toArray();

        $query = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->where('u1.selected', 1)
            ->where('u1.sender', $userName)
            ->whereIn('u1.id', $latestIds) // Only count latest entries
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code');

        if ($daruliftaName) {
            $query->where('u1.darulifta_name', $daruliftaName);
        }

        return $query->count();
    }

    /**
     * Initialize default restrictions for all users who don't have manual allow.
     */
    public function initializeDefaultRestrictions(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $initialized = [
            'restricted' => 0,
            'already_allowed' => 0,
            'total_checked' => 0,
        ];

        foreach ($users as $user) {
            $initialized['total_checked']++;

            // Skip if user is already manually allowed
            if ($this->isManuallyAllowed($user)) {
                $initialized['already_allowed']++;
                continue;
            }

            // Apply default restriction if not already restricted
            $hasDefaultRestriction = UserRestriction::where('user_id', $user->id)
                ->where('restriction_type', UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED)
                ->where('is_active', true)
                ->exists();

            if (!$hasDefaultRestriction) {
                // Get first admin user for system restrictions
                $adminUser = User::whereHas('roles', function ($query) {
                    $query->where('name', 'Admin');
                })->first();

                UserRestriction::create([
                    'user_id' => $user->id,
                    'restriction_type' => UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED,
                    'reason' => 'Default restriction - requires manual approval to submit fatawa',
                    'restricted_by' => $adminUser ? $adminUser->id : $user->id, // Use admin or self if no admin exists
                    'restricted_at' => now(),
                    'is_active' => true,
                ]);
                $initialized['restricted']++;
            }
        }

        return $initialized;
    }
}
