<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserRestriction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MahlENazarService
{
    const MAHL_E_NAZAR_LIMIT = 12;

    /**
     * Get the count of Mahl-e-Nazar fatawa for a user.
     */
    public function getMahlENazarCount($userName): int
    {
        return DB::table('uploaded_files')
            ->where('ftype', 'Mahl e Nazar')
            ->where(function ($query) use ($userName) {
                $query->where('checker', $userName)
                      ->orWhere('transfer_by', $userName);
            })
            ->count();
    }

    /**
     * Check if user has exceeded the Mahl-e-Nazar limit.
     */
    public function hasExceededLimit(User $user): bool
    {
        $count = $this->getMahlENazarCount($user->name);
        return $count >= self::MAHL_E_NAZAR_LIMIT;
    }

    /**
     * Get users who have exceeded the limit.
     */
    public function getUsersExceedingLimit(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $exceedingUsers = [];
        
        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            if ($count >= self::MAHL_E_NAZAR_LIMIT) {
                $exceedingUsers[] = [
                    'user' => $user,
                    'count' => $count,
                    'excess' => $count - self::MAHL_E_NAZAR_LIMIT,
                ];
            }
        }

        return $exceedingUsers;
    }

    /**
     * Apply restriction to user who exceeded limit.
     */
    public function applyLimitRestriction(User $user): void
    {
        $count = $this->getMahlENazarCount($user->name);
        
        if ($count >= self::MAHL_E_NAZAR_LIMIT) {
            UserRestriction::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'restriction_type' => 'fatawa_limit_exceeded',
                    'is_active' => true,
                ],
                [
                    'reason' => "User has {$count} fatawa in Mahl-e-Nazar status (limit: " . self::MAHL_E_NAZAR_LIMIT . ")",
                    'restricted_by' => $user->id, // System restriction
                    'restricted_at' => now(),
                ]
            );
        }
    }

    /**
     * Remove restriction if user is below limit.
     */
    public function removeLimitRestriction(User $user): void
    {
        $count = $this->getMahlENazarCount($user->name);
        
        if ($count < self::MAHL_E_NAZAR_LIMIT) {
            UserRestriction::where('user_id', $user->id)
                ->where('restriction_type', 'fatawa_limit_exceeded')
                ->where('is_active', true)
                ->update([
                    'is_active' => false,
                    'lifted_by' => $user->id,
                    'lifted_at' => now(),
                    'lift_reason' => 'Mahl-e-Nazar count below limit',
                ]);
        }
    }

    /**
     * Check and update all users' limit restrictions.
     */
    public function updateAllUserRestrictions(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $updated = [
            'restricted' => 0,
            'unrestricted' => 0,
            'total_checked' => 0,
        ];

        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            $updated['total_checked']++;

            if ($count >= self::MAHL_E_NAZAR_LIMIT) {
                $this->applyLimitRestriction($user);
                $updated['restricted']++;
            } else {
                $this->removeLimitRestriction($user);
                $updated['unrestricted']++;
            }
        }

        return $updated;
    }

    /**
     * Get Mahl-e-Nazar statistics.
     */
    public function getStatistics(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->get();

        $stats = [
            'total_users' => $users->count(),
            'users_at_limit' => 0,
            'users_over_limit' => 0,
            'users_near_limit' => 0, // 10-11 fatawa
            'total_mahl_e_nazar' => 0,
            'average_per_user' => 0,
            'users_with_restrictions' => 0,
        ];

        $totalCount = 0;
        
        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            $totalCount += $count;

            if ($count >= self::MAHL_E_NAZAR_LIMIT) {
                if ($count == self::MAHL_E_NAZAR_LIMIT) {
                    $stats['users_at_limit']++;
                } else {
                    $stats['users_over_limit']++;
                }
            } elseif ($count >= 10) {
                $stats['users_near_limit']++;
            }
        }

        $stats['total_mahl_e_nazar'] = $totalCount;
        $stats['average_per_user'] = $users->count() > 0 ? round($totalCount / $users->count(), 1) : 0;
        $stats['users_with_restrictions'] = UserRestriction::where('restriction_type', 'fatawa_limit_exceeded')
            ->where('is_active', true)
            ->count();

        return $stats;
    }

    /**
     * Get detailed user report with Mahl-e-Nazar counts.
     */
    public function getUserReport(): array
    {
        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        })->with(['activeRestrictions' => function ($query) {
            $query->where('restriction_type', 'fatawa_limit_exceeded');
        }])->get();

        $report = [];
        
        foreach ($users as $user) {
            $count = $this->getMahlENazarCount($user->name);
            $hasRestriction = $user->activeRestrictions->isNotEmpty();
            
            $report[] = [
                'user' => $user,
                'mahl_e_nazar_count' => $count,
                'remaining_capacity' => max(0, self::MAHL_E_NAZAR_LIMIT - $count),
                'is_at_limit' => $count >= self::MAHL_E_NAZAR_LIMIT,
                'is_near_limit' => $count >= 10 && $count < self::MAHL_E_NAZAR_LIMIT,
                'has_restriction' => $hasRestriction,
                'status' => $this->getUserStatus($count, $hasRestriction),
            ];
        }

        // Sort by count descending
        usort($report, function ($a, $b) {
            return $b['mahl_e_nazar_count'] <=> $a['mahl_e_nazar_count'];
        });

        return $report;
    }

    /**
     * Get user status based on count and restrictions.
     */
    private function getUserStatus(int $count, bool $hasRestriction): string
    {
        if ($hasRestriction) {
            return 'restricted';
        }
        
        if ($count >= self::MAHL_E_NAZAR_LIMIT) {
            return 'at_limit';
        }
        
        if ($count >= 10) {
            return 'near_limit';
        }
        
        return 'normal';
    }

    /**
     * Can user submit new fatawa?
     */
    public function canSubmitFatawa(User $user): bool
    {
        // Check if user has active restrictions
        if ($user->hasActiveRestrictions()) {
            return false;
        }

        // Check Mahl-e-Nazar limit
        if ($this->hasExceededLimit($user)) {
            return false;
        }

        return true;
    }

    /**
     * Get restriction message for user.
     */
    public function getRestrictionMessage(User $user): ?string
    {
        $count = $this->getMahlENazarCount($user->name);
        
        if ($count >= self::MAHL_E_NAZAR_LIMIT) {
            return "You have {$count} fatawa in Mahl-e-Nazar status. The limit is " . self::MAHL_E_NAZAR_LIMIT . ". Please resolve existing fatawa before submitting new ones.";
        }

        if ($count >= 10) {
            $remaining = self::MAHL_E_NAZAR_LIMIT - $count;
            return "Warning: You have {$count} fatawa in Mahl-e-Nazar status. Only {$remaining} more allowed before reaching the limit.";
        }

        return null;
    }
}
