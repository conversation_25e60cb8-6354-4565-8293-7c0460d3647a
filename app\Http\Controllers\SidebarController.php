<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SidebarController extends Controller
{
    public function index()
    {
        // Fetch data from the database (adjust table name as per your database structure)
        $checkerlist1 = DB::table('checkers')->get();

        // Pass the data to the view
        return view('components.navbars.sidebar', [
            'checkerlist1' => $checkerlist1,
        ]);
    }
}