<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="schedule"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Set Schedule"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <!-- Include jQuery library -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>
   

    <!-- Create Appointment Entry Form -->
    <style>
        #emptbl th,
        #emptbl td {
            padding: 8px;
            border: 1px solid #ccc;
            text-align: center;
        }
        
        #emptbl {
            width: 100%;
            border-collapse: collapse;
        }
        
        /* Define alternate row colors */
        #emptbl tr:nth-child(odd) {
            background-color: #f2f2f2;
        }
        
        #emptbl tr:nth-child(even) {
            background-color: #ffffff;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            #emptbl th, #emptbl td {
                display: block;
                text-align: left;
                width: 100%;
            }
            
            #emptbl th {
                font-weight: bold;
            }
            
            #emptbl td:before {
                content: attr(data-label);
                float: left;
                font-weight: normal;
            }
            
            #col5 textarea {
                width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
    
    <div class="container">
<h1 class="mb-4">Add Schedule</h1>
<form action="{{ route('schedules.store') }}" method="POST">
    @csrf
            <table id="emptbl">
                    <tr>
                        <th>Sehdule Date</th>
                        <th>Schedule Day</th>
                        <th>Schedule Time</th>
                    </tr>
                    <tr>
                        <td id="col0"><input type="Date" class="form-control text-center" name="date[]" value="" /></td>
                        <td id="col1">
                            <select name="day[]" class="form-control text-center"></select>
                        </td>
                        <td id="col2"><input type="time"  class="form-control text-center" name="time[]" value="" /></td>
                    </tr>
                    <tr>
                        <th>Event Name</th>
                        <th>Location</th>
                        <th>Description</th>
                    </tr>
                    <tr>
                        <td id="col3"><input type="name"  class="form-control text-center" name="event[]" value="" /></td>
                        <td id="col4"><input type="location"  class="form-control text-center" name="location[]" value="" min="1" max="24" step="1" /></td>
                        <td id="col5" style="text-align: right;"><textarea class="form-control" name="description[]" rows="4" cols="50"></textarea></td>
                    </tr>
                </table> 
                <div style="text-align: center; margin-top: 20px;">
                        <input type="submit" class="btn btn-success" value="Submit" />
                </div>
            </form> 
        </div>
        <script>
            $(document).ready(function() {
        $('.bootstrap-timepicker').timepicker({
            showMeridian: false, // Use 24-hour format
            showSeconds: false, // Remove seconds from the interface
            defaultTime: false // Don't set a default time
        });
    });
            const dateInput = document.querySelector('input[name="date[]"]');
            const daySelect = document.querySelector('select[name="day[]"]');
    
            dateInput.addEventListener('change', () => {
                const selectedDate = new Date(dateInput.value);
                const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                const selectedDay = daysOfWeek[selectedDate.getDay()];
    
                // Clear existing options and add the calculated day as the only option
                daySelect.innerHTML = '';
                const option = document.createElement('option');
                option.value = selectedDay;
                option.textContent = selectedDay;
                daySelect.appendChild(option);
            });
        </script>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
