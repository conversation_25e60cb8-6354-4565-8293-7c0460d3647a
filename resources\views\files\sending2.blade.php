<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Recived Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- Create Appointment Entry Form -->
        <style>
                        .year {
                background-color: #7971ec; /* Year section color */
            }

            .month {
                background-color: #96c47d; /* Month section color */
            }

            .date {
                background-color: #c8d053; /* Date section color */
            }

            .file {
                background-color: #c6d2f4; /* Date section color */
            }
            .download-button {
                display: inline-block;
                padding: 10px 20px; /* Adjust the padding as needed */
                background-color: #007bff; /* Button background color */
                color: #fff; /* Text color */
                text-decoration: none;
                border: none;
                border-radius: 5px; /* Rounded corners */
                cursor: pointer;
            }

            .download-button:hover {
                background-color: #0056b3; /* Change color on hover */
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
           /* Style the file input container as a button */
    .file-input-container {
        display: inline-block;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        background-color: #007bff;
        color: #fff;
        border: none; /* Remove border to keep it consistent in size */
        border-radius: 5px;
        text-align: center; /* Center text horizontally */
    }

    /* Style the file input to be transparent and overlay it on the button */
    .file-input-container input[type="file"] {
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
        cursor: pointer;
    }

    /* Style the chosen file name to appear on the right side */
    .file-input-name {
        display: inline-block;
        padding: 0 10px; /* Add some spacing between the button and file name */
        vertical-align: middle; /* Vertically align the text with the button */
        font-size: inherit; /* Inherit font size from the button */
        white-space: nowrap; /* Prevent long file names from wrapping */
        overflow: hidden;
        text-overflow: ellipsis; /* Add ellipsis (...) for long file names */
    }
    .button-container {
        text-align: center;
    }
        </style>
        <script>
        function updatefolderdateOptions() {
    const selectedDarul = document.getElementById('darulifta_name').value;
    const folderSelect = document.getElementById('folder_select'); // Use the correct id here

    // Clear existing options
    folderSelect.innerHTML = '<option value="0">Select Folder</option>';

    if (selectedDarul !== '0') {
        fetch(`/get-folder/${selectedDarul}`)
            .then(response => response.json())
            .then(data => {
                // Use a Set to store unique mail_folder_date values
                const uniqueMailFolderDates = new Set();

                // Iterate through the data and add mail_folder_date values to the Set
                data.forEach(folder => {
                    uniqueMailFolderDates.add(folder.mail_folder_date);
                });

                // Convert the Set back to an array
                const uniqueMailFolderDatesArray = [...uniqueMailFolderDates];

                // Sort the unique values if needed
                uniqueMailFolderDatesArray.sort();

                // Populate the dropdown with unique values
                uniqueMailFolderDatesArray.forEach(mailFolderDate => {
                    const option = document.createElement('option');
                    option.value = mailFolderDate;
                    option.textContent = mailFolderDate;
                    folderSelect.appendChild(option);
                });
            });
    }
}

// Add event listeners to file input elements to update the subfolder_name field
const dataLabelFiles = {};

// Add event listeners to file input elements to update the dataLabelFiles object
const fileInputs = document.querySelectorAll('input[type="file"]');
fileInputs.forEach(input => {
    input.addEventListener('change', function () {
        const dataLabel = this.getAttribute('data-label');
        dataLabelFiles[dataLabel] = this.files; // Store files in the object using data-label as the key
    });
});

// Add a submit event listener to the form
document.querySelector('form').addEventListener('submit', function (e) {
        e.preventDefault(); // Prevent the form from submitting

        const formData = new FormData(this); // Create a FormData object from the form

        fetch(this.action, {
            method: this.method,
            body: formData,
        })
        .then(response => {
            if (response.ok) {
                // Files uploaded successfully
                alert('Files uploaded successfully');
            } else {
                // Handle errors here
                alert('An error occurred while uploading files');
            }
        })
        .catch(error => {
            // Handle network or other errors here
            alert('An error occurred while uploading files');
        });
    });
        </script>

<form action="{{ route('upload') }}" method="POST" enctype="multipart/form-data">
    @csrf
    
    <select name="darulifta_name" id="darulifta_name" onchange="updatefolderdateOptions()">
        <option value="0">Select Darulifta</option>
        @foreach($daruliftaData as $data)
            <option value="{{ $data }}">{{ $data }}</option>
        @endforeach
    </select>

    <select name="folder_select" id="folder_select">
        <option value="0">Select Folder</option>
        <!-- Options will be dynamically populated here using JavaScript -->
    </select>

    <div class="form-group">
        
        <div class="file-input-container">
            Choose Files for 'ok'
            <input type="file" class="form-control" id="ok1" name="ok[]" multiple data-label="ok">
        </div>
    
    
    
        
        <div class="file-input-container">
            Choose Files for 'Mahl-e-Nazar'
            <input type="file" class="form-control" id="mahl-e-nazar" name="Mahl-e-Nazar[]" multiple data-label="Mahl-e-Nazar">
        </div>
    
    
    
        
        <div class="file-input-container">
            Choose Files for 'Tahqiqi'
            <input type="file" class="form-control" id="tahqiqi" name="Tahqiqi[]" multiple data-label="Tahqiqi">
        </div>
    </div>

    <button type="submit" class="btn btn-primary">Upload</button>
</form>

        
        
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
