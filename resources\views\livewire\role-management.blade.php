<div>
    <!-- Success/Error Messages -->
    @if($successMessage)
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">check_circle</i>
            {{ $successMessage }}
            <button type="button" class="btn-close" wire:click="clearMessages"></button>
        </div>
    @endif

    @if($errorMessage)
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="material-icons me-2">error</i>
            {{ $errorMessage }}
            <button type="button" class="btn-close" wire:click="clearMessages"></button>
        </div>
    @endif

    <!-- Search and Actions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="input-group input-group-outline">
                <label class="form-label">Search users...</label>
                <input type="text" class="form-control" wire:model.live="search">
            </div>
        </div>
        <div class="col-md-6 text-end">
            <button class="btn bg-gradient-primary" wire:click="refreshData">
                <i class="material-icons me-1">refresh</i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Users Table -->
    <div class="table-responsive">
        <table class="table align-items-center mb-0">
            <thead>
                <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Current Roles</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Superior Status</th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                </tr>
            </thead>
            <tbody>
                @forelse($users as $user)
                <tr>
                    <td>
                        <div class="d-flex px-2 py-1">
                            <div class="d-flex flex-column justify-content-center">
                                <h6 class="mb-0 text-sm">{{ $user->name }}</h6>
                                <p class="text-xs text-secondary mb-0">{{ $user->email }}</p>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="d-flex flex-wrap gap-1">
                            @foreach($user->roles as $role)
                                <span class="badge badge-sm bg-gradient-{{ $this->getRoleColor($role->name) }}">
                                    {{ $role->name }}
                                    @if($user->roles->count() > 1)
                                        <button type="button" class="btn-close btn-close-white ms-1" 
                                                wire:click="removeRole({{ $user->id }}, {{ $role->id }})"
                                                wire:confirm="Are you sure you want to remove the '{{ $role->name }}' role from {{ $user->name }}?"
                                                style="font-size: 0.6rem;"></button>
                                    @endif
                                </span>
                            @endforeach
                        </div>
                    </td>
                    <td class="align-middle text-center text-sm">
                        @if($this->isUserSuperior($user))
                            <span class="badge badge-sm bg-gradient-warning">
                                <i class="material-icons me-1" style="font-size: 12px;">star</i>
                                Superior
                            </span>
                            <button class="btn btn-sm btn-outline-warning ms-1" 
                                    wire:click="removeSuperiorRole({{ $user->id }})"
                                    wire:confirm="Are you sure you want to remove Superior role from {{ $user->name }}?">
                                <i class="material-icons" style="font-size: 14px;">remove</i>
                            </button>
                        @else
                            <button class="btn btn-sm bg-gradient-warning" 
                                    wire:click="addSuperiorRole({{ $user->id }})"
                                    wire:confirm="Are you sure you want to assign Superior role to {{ $user->name }}?">
                                <i class="material-icons me-1" style="font-size: 14px;">star</i>
                                Make Superior
                            </button>
                        @endif
                    </td>
                    <td class="align-middle text-center">
                        @if($this->canManageUser($user))
                            <button class="btn btn-sm bg-gradient-info" wire:click="openRoleModal({{ $user->id }})">
                                <i class="material-icons me-1" style="font-size: 14px;">edit</i>
                                Manage Roles
                            </button>
                        @else
                            <span class="text-secondary text-xs">No Access</span>
                        @endif
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="4" class="text-center py-4">
                        <div class="text-center">
                            <i class="material-icons text-secondary" style="font-size: 48px;">people</i>
                            <p class="text-secondary mb-0">No users found</p>
                        </div>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <!-- Role Management Modal -->
    @if($showModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ $modalTitle }}</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    @if($selectedUser)
                        <p class="text-sm text-secondary mb-3">
                            Select roles for <strong>{{ $selectedUser->name }}</strong>:
                        </p>
                        
                        <div class="row">
                            @foreach($roles as $role)
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               wire:model="selectedRoles" 
                                               value="{{ $role->id }}" 
                                               id="role_{{ $role->id }}">
                                        <label class="form-check-label" for="role_{{ $role->id }}">
                                            <span class="badge bg-gradient-{{ $this->getRoleColor($role->name) }} me-1">
                                                {{ $role->name }}
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        @if(empty($selectedRoles))
                            <div class="alert alert-warning mt-3">
                                <i class="material-icons me-2">warning</i>
                                Please select at least one role.
                            </div>
                        @endif
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Cancel</button>
                    <button type="button" class="btn bg-gradient-primary" 
                            wire:click="updateUserRoles" 
                            @if(empty($selectedRoles)) disabled @endif>
                        <i class="material-icons me-1">save</i>
                        Update Roles
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
    
<style>
    .table th {
        border-bottom: 1px solid #e9ecef;
        padding: 0.75rem;
    }

    .table td {
        padding: 0.75rem;
        border-bottom: 1px solid #f1f3f4;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: 0.5rem;
    }

    .btn-close-white {
        filter: invert(1) grayscale(100%) brightness(200%);
    }

    .alert {
        border-radius: 0.75rem;
        border: none;
        padding: 1rem 1.25rem;
    }

    .alert-success {
        background-color: rgba(45, 206, 137, 0.1);
        color: #2dce89;
        border-left: 4px solid #2dce89;
    }

    .alert-danger {
        background-color: rgba(245, 54, 92, 0.1);
        color: #f5365c;
        border-left: 4px solid #f5365c;
    }

    .alert-warning {
        background-color: rgba(251, 99, 64, 0.1);
        color: #fb6340;
        border-left: 4px solid #fb6340;
    }

    .input-group-outline {
        position: relative;
    }

    .input-group-outline .form-label {
        position: absolute;
        top: 50%;
        left: 0.75rem;
        transform: translateY(-50%);
        transition: all 0.2s ease;
        pointer-events: none;
        color: #6c757d;
        font-size: 0.875rem;
    }

    .input-group-outline .form-control:focus + .form-label,
    .input-group-outline .form-control:not(:placeholder-shown) + .form-label {
        top: 0;
        font-size: 0.75rem;
        color: #5e72e4;
        background: white;
        padding: 0 0.25rem;
    }

    .modal.show {
        display: block !important;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.375rem;
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
    }

    .bg-gradient-success {
        background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(87deg, #8392ab 0, #96a2b2 100%);
    }

    .table-responsive {
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .form-check-input:checked {
        background-color: #5e72e4;
        border-color: #5e72e4;
    }

    /* Animation for smooth transitions */
    .badge, .btn {
        transition: all 0.2s ease;
    }

    .badge:hover {
        transform: translateY(-1px);
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
</style>
</div>

