<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="appointment"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="appointment"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
            <!-- Link to create Fatawa entry -->
            <div class="container">
                
                <!-- Link to view Fatawa entries -->
                <a href="{{ route('aform') }}" class="btn btn-primary">Add Appointment</a>
                     <!-- You can add more content here as needed -->
                     <div class="container">
                        <h1>Appointments</h1>
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Day</th>
                                    <th>Time</th>
                                    <th>Name</th>
                                    <th>Contact</th>
                                    <th>Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($appointments as $appointment)
                                <tr>
                                    <td>{{ $appointment->appt_date }}</td>
                                    <td>{{ $appointment->appt_day }}</td>
                                    <td>{{ $appointment->appt_time }}</td>
                                    <td>{{ $appointment->appt_name }}</td>
                                    <td>{{ $appointment->appt_cont }}</td>
                                    <td>{{ $appointment->appt_dec }}</td>
                                    <td>
                                        <a href="{{ route('appointments.edit', ['id' => $appointment->id]) }}" class="btn btn-sm btn-warning">Edit</a>
                                        <form action="{{ route('appointments.destroy', ['id' => $appointment->id]) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this appointment?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
            </div>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
            <script>
                $(document).ready(function () {
                  $('#myTable').DataTable();
            
                });
              </script>
            <x-footers.auth></x-footers.auth>
        </div>
    </main>
    <x-plugins></x-plugins>

</x-layout>
