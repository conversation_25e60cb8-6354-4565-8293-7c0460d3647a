<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="mujeeb"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->


        <x-navbars.navs.auth titlePage="question_list"></x-navbars.navs.auth>
        <style>
        table{
            font-family: <PERSON>eel <PERSON>eeq;
            
          }
          </style>
        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
            <!-- Link to create Fatawa entry -->
            <div class="container">
                
                <!-- Link to view Fatawa entries -->
                <meta name="csrf-token" content="{{ csrf_token() }}">
                <a href="{{ route('reciptque.index') }}" class="btn btn-primary">Add Question</a>
                     <!-- You can add more content here as needed -->
                     <div class="container">
    <h1 class="text-center">Viral Fatawa by Darulifta</h1>

    @if(!empty($dataByDaruliftaName))
        @foreach($dataByDaruliftaName as $daruliftaName => $fatawa)
            <div class="card mt-4">
                <div class="card-header">
                    <h2>{{ $daruliftaName }}</h2>
                </div>
                <div class="card-body">
                <table class="table" id="myTable">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Fatwa No</th>
                                <th>Mujeeb</th>
                                <th>Fatwa Folder</th>
                                <th>Category</th>
                                <th>View</th>
                            </tr>
                        </thead>
                        <tbody>
                        @php
                                $serialNumber = 1; // Initialize the serial number
                                
                                @endphp
                            @foreach($fatawa as $item)
                                <tr>
                                <td>{{ $serialNumber++ }}</td> {{-- Increment the serial number --}}
                                <td>{{ $item->file_code }}</td>
                                    <td>{{ $item->sender }}</td>
                                    <td>{{ $item->mail_folder_date }}</td>
                                    <td>{{ $item->category }}</td>
                                    <td>View</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endforeach
    @else
        <p>No viral fatawa found.</p>
    @endif
</div>
            </div>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
            <script>
                $(document).ready(function () {
                  $('#myTable').DataTable();
            
                });
              </script>
              <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

              <script>
                 document.addEventListener("DOMContentLoaded", function() {
    // Your JavaScript code here
    const mujeebSelects = document.querySelectorAll('.mujeeb-select');
    
    mujeebSelects.forEach(select => {
        select.addEventListener('change', async function() {
            const questionId = this.getAttribute('data-question-id');
            const selectedMujeebId = this.value;
            
            try {
                // Include the CSRF token in the request headers
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                
                const response = await axios.post(`/update-assign-id/${questionId}`, { selectedMujeebId }, {
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                    },
                });
                
                if (response.data.success) {
                    // You can update the view to reflect the change if needed.
                    // For example, show the selected Mujeeb's name next to the dropdown.
                }
            } catch (error) {
                console.error(error);
            }
        });
    });
});
              </script>
            <x-footers.auth></x-footers.auth>
        </div>
    </main>
    <x-plugins></x-plugins>

</x-layout>
