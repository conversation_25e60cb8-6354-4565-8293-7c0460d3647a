<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="{{ request()->route('role') ?? 'mahlenazar' }}"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Dashboard"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Add this to the head of your HTML file -->
        <div>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
            <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
        <script src="https://rawgit.com/eKoopmans/html2pdf/master/dist/html2pdf.bundle.js"></script>


                <style>
                    .question-section,
.chat-section {
    display: none;
}

                    .custom-bg-light-red {
                    background-color: #FFDDDD; /* Lightest shade of red */
        }

        .custom-bg-light-blue {
            background-color: #DDDDFF; /* Lightest shade of blue */
        }

        .custom-bg-light-green {
            background-color: #DDFFDD; /* Lightest shade of green */
        }


        .custom-bg-light-yellow {
            background-color: #FFFFCC; /* Lightest shade of yellow */
        }
        .custom-text-dark-black {
            color: #000; /* Dark black color */
        }
                    .table{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px

                  }
                  .card{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px

                  }
                  .table2{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px

                  }
                    .not-assigned {
            color: red;
            /* Add any other styles for not assigned here */
        }
        .future-date {
            color: red !important;
            border: 1px solid red;
        }

        .past-date {
            border: 1px solid green;
        }
        .increased-font {
                font-size: 20px; /* Change the font size as needed */
            }
            table {
            table-layout: auto;
            font-size: 20px; /* Adjust the font size to your preference */

        }
        th, td {
                font-size: 20px; /* Adjust the font size for table headers and table data cells */
            }
        </style>
        @php
        $userRoles = Auth::user()->roles->pluck('name')->toArray();
        @endphp


                        <div class="col-lg-12 col-md-6 mt-0 mb-md-0 mb-4">

                            <div class="card z-index-2 mb-4" id="big-card-9" style="background-color: #FFDDDD;" >

                                    <div class="card-header pb-0">

                                        <h3>Still In Mahl-e-Nazar</h3>

@php
    $daruliftaCounts = []; // Initialize an array to store Darulifta counts
    $senderCounts = []; // Initialize an array to store sender counts
    $totalCounts = 0; // Initialize a variable to store the overall total count
@endphp

{{-- Display sender counts at the top of the page --}}
<h4>Darulifta And Mujeeb Summary of Mahl-e-Nazar</h4>

<table>
    <thead>
        <tr>
            <h5><th>Darulifta</th></h5>
                <h5><th>Mujeeb Mahl-e-Nazar Toltal</th></h5>
                    <h5><th>Total</th></h5>
        </tr>
    </thead>
    <tbody>
        @foreach($daruliftaNames as $daruliftaName)
            @if(isset($mahl_e_nazarfataw[$daruliftaName]))
                @php
                    $daruliftaTotalCounts = 0; // Initialize a variable to store the total count for the current $daruliftaName
                @endphp

                @foreach($mailfolderDates as $mailfolderDate)
                    @if(isset($mahl_e_nazarfataw[$daruliftaName][$mailfolderDate]))
                        @foreach($mahl_e_nazarfataw[$daruliftaName][$mailfolderDate] as $file)
                            @php
                                $sender = $file->sender;
                                $senderCounts[$daruliftaName][$sender] = isset($senderCounts[$daruliftaName][$sender]) ? $senderCounts[$daruliftaName][$sender] + 1 : 1;
                                $daruliftaTotalCounts++;
                                $totalCounts++;
                            @endphp
                        @endforeach
                    @endif
                @endforeach

                <tr>
                    <td>
                        <h5><a href="{{ route('mahlenazar', ['role' => $daruliftaName]) }}" target="_blank">{{ $daruliftaName }}
                        </a></h5>
                    </td>
                    <td>
                        @foreach ($senderCounts[$daruliftaName] as $sender => $count)
                        <a href="{{ route('mahlenazar', ['role' => $daruliftaName, 'fatwa_no' => $sender]) }}" target="_blank">
                         {{ $sender }}</a>: {{ $count }} |
                        @endforeach
                    </td>
                    <td>
                        Total: {{ $daruliftaTotalCounts }}
                    </td>
                </tr>
            @endif
        @endforeach
    </tbody>
</table>

<h5><p>Overall Total: {{ $totalCounts }}</p></h5>
                                    </div>
                                    <div class="card-body">




                    </div>

                <div class="card-body px-0 pb-2">


            @foreach($daruliftaNames as $daruliftaName)

            @if(isset($mahl_e_nazarfataw[$daruliftaName]))
            <h5 class="toggle-header toggle-parent"><span class="toggle-arrow">▼</span>{{ $daruliftaName }}</h5>
            <div class="toggle-content toggle-parent">
                <div class="table-responsive5">
                    @php
                            $serialNumber_fl1 = 1; // Initialize the serial number

                            $mailfolderDateCount1 = 0;
                            $colors = ['#F0FFF0', '#F0F8FF', '#FFFFE0', '#FFE4E1', '#F0F8FF', '#F5FFFA', '#E6E6FA', '#FAEBD7', '#ADD8E6']; // Add more colors as needed
                            @endphp
                    @foreach($mailfolderDates as $mailfolderDate)
                    @if(isset($mahl_e_nazarfataw[$daruliftaName][$mailfolderDate]))
                    @php
                        $mailfolderDateCount1 = count($mahl_e_nazarfataw[$daruliftaName][$mailfolderDate]); // Count the occurrences of $mailfolderDate
                    @endphp

                    <div class="card z-index-2 mb-3" style="background-color: {{ $colors[$loop->index % count($colors)] }};">
                        {{-- <div  class="data-row" data-rec-date="{{ $mailfolderDate }}" > --}}

                    <h6 class="toggle-header toggle-child d-flex">
                        ({{ $serialNumber_fl1++ }})<span class="toggle-arrow">▼</span>{{ $mailfolderDate }}<span class="ms-auto pr-2">Fatawa:({{ $mailfolderDateCount1 }})</span></h6>

                    <div class="toggle-content toggle-child">
                                            <table class="table5 align-items-center text-center mb-0">
                                                <thead>
                                                    <tr>
                                                        <th class="text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%;">
                                                            S.No</th>
                                                        <th
                                                            class="text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%;">
                                                            Fatwa No</th>
                                                        <th
                                                            class="text-uppercase text-secondary font-weight-bolder opacity-7 ps-2" style="width: 5%;">
                                                            Mujeeb</th>
                                                            <th
                                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7"  style="width: 5%; white-space: normal;">
                                                            Q.Rec. Days</th>
                                                            <th
                                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7"  style="width: 5%; white-space: normal;">
                                                            Mahl-e-Nazar Pending Days</th>
                                                            <th
                                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7"  style="width: 5%; white-space: normal;">
                                                            Mahl-e-Nazar Folder</th>
                                                            <th
                                                            class="text-center text-uppercase text-secondary font-weight-bolder" style="width: 5%;">
                                                            Category</th>
                                                            <th
                                                            class="text-center text-uppercase text-secondary font-weight-bolder" style="width: 5%;">
                                                            View</th>

                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @php
                                                $serialNumber_mahle = 1; // Initialize the serial number
                                                @endphp
                                               @foreach($mahl_e_nazarfataw[$daruliftaName][$mailfolderDate] as $file)


            <tr>
                                                  <tr style="background-color: #FFDDF0;">
                                                    <td>
                                                        <div class="d-flex px-2 py-1">

                                                            <span class=" font-weight-bold">{{ $serialNumber_mahle++ }}</span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                            <div class="d-flex px-2 py-1">

                                                                <div class="d-flex flex-column justify-content-center">
                                                                    <h6 class="mb-0 ">{{$file->file_code}}</h6>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="mujeeb-column4"> {{$file->sender}} </span>
                                                                   @php
                $sender = $file->sender;
                $senderCounts[$sender] = isset($senderCounts[$sender]) ? $senderCounts[$sender] + 1 : 1;
            @endphp

                                                        </td>
                                                        <td class="align-middle text-center ">
                                                            @foreach ($que_day_r as $day)
                                                            @if (Str::lower($file->file_code) == Str::lower($day->ifta_code))
                                                                @php
                                                                    $recDate = new \DateTime($day->rec_date);
                                                                    $currentDate = now();
                                                                    $daysDifference = $currentDate->diff($recDate)->days;
                                                                @endphp
                                                                {{ $daysDifference }} days
                                                                <br>
                                                            @endif
                                                        @endforeach

                                                        </td>
                                                        <td class="align-middle text-center">
                                                            @php
                                                                $mahlDate = \Carbon\Carbon::createFromFormat('Y-m-d', $file->mail_folder_date);
                                                                $checkedDate = $file->checked_date ?? $file->mail_folder_date; // Use checked_date if not null, otherwise use mail_folder_date
                                                                $currentDate = \Carbon\Carbon::now();
                                                                $mahle_today = $currentDate->diffInDays(\Carbon\Carbon::createFromFormat('Y-m-d', $checkedDate));
                                                            @endphp

                                                            <span class="font-weight-bold">{{ $mahle_today }}</span>
                                                        </td>

                                                        <td class="align-middle text-center ">
                                                            @foreach ($mahlenazar_null as $mahle)

                                                                @if ($file->file_code == $mahle->file_code)

                                                                    {{ $mahle->mail_folder_date }}
                                                                    <br>
                                                                    {{-- @if($mahle->mail_folder_date == null) --}}



                                                                @endif

                                                        @endforeach


                                                        </td>
                                                        <td class="align-middle text-center ">
                                                            <span class=" font-weight-bold">{{$file->category}} </span>
                                                        </td>
                                                        <td>
                                                            <span class="view">
                                                            @php
        // Determine the date parameter based on conditions
        if (empty($file->by_mufti)) {
            if (empty($file->checker)) {
                $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked';
            } else {
                $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked_by_' . $file->checker;
            }
        } else {
            $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked_by_' . $file->checker . '_' . $file->by_mufti;
        }
    @endphp

    <a href="{{ route('viewCheck', [
        'date' => $dateParam,
        'folder' => $file->checked_folder,
        'filename' => $file->file_name
    ]) }}" target="_blank">
        <i class="fas fa-eye"></i>View
    </a>
</span>

<span class="download">
    <a href="{{ route('downloadCheck', [
        'date' => $dateParam,
        'filename' => $file->file_name,
        'folder' => $file->checked_folder
    ]) }}">
        <i class="fas fa-download"></i>Download
    </a>
</span>
                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td colspan="1" class="align-middle text-center toggle-section" data-section="question" style="background-color: #FFDDCC;">
                                                            Question
                                                            <i class="toggle-icon fas fa-chevron-down"></i>
                                                        </td>
                                                        <td colspan="1" class="align-middle text-center toggle-section" data-section="chat" style="background-color: #FFDDCC;">
                                                            Chat
                                                            <i class="toggle-icon fas fa-chevron-down"></i>
                                                        </td>
                                                    </tr>
                                                    <tr class="hidden-section question-section">
                                                        <td colspan="8" class="align-middle text-center" style="background-color: #ffffff;">
                                                            @foreach ($que_day_r as $day)
                                                                @if (Str::lower($file->file_code) == Str::lower($day->ifta_code))
                                                                سوال:{{  $day->question }}
                                                                    <br>
                                                                @endif
                                                            @endforeach
                                                        </td>
                                                    </tr>
                                                    <tr >
                                                        <td colspan="8" class="align-middle text-center"  >
                                                        @livewire('chat-box', ['ifta_code' => $file->file_code, 'user_name' => auth()->user()->name, 'user_id' => auth()->user()->id])

                                                        </td>
                                                    </tr>

                                                    </tr>


                                                @endforeach
                                            </tbody>
                                        </table>

                                    </div>
                                </div>
                            {{-- </div> --}}
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                                @endif
                            @endforeach

                        </div>
                    </div>


                    <x-footers.auth></x-footers.auth>
                </div>
            </main>
            <x-plugins></x-plugins>
            </div>
            @php
            // Get the user's roles
            $userRoles = auth()->user()->roles ?? [];

            // Extract only the role names
            $roleNames = $userRoles->pluck('name')->toArray();

            // Set the default role to null
            $defaultRole = null;

            // Check if the user has less than two roles
            if (count($roleNames) < 2) {
                // Use the first role as the default role
                $defaultRole = $roleNames[0] ?? null;
            }
        @endphp

        <script>
            // Define a JavaScript variable with user roles and default role
            const userRoles = @json($roleNames);
            const defaultRole = @json($defaultRole); // Make sure $defaultRole is defined

            // Use defaultRole in your JavaScript logic
            console.log('Default Role:', defaultRole);
        </script>
        <script>
        $(document).ready(function () {
    // Initially hide question sections
    $(".question-section").hide();

    // Initially hide chat sections
    $(".chat-section").hide();

    // Question section toggle
    $(".toggle-section[data-section='question']").click(function () {
        var $parentCard = $(this).closest('.card');
        $parentCard.find(".question-section").toggle();
        $(this).toggleClass("active");
    });

    // Chat section toggle
    $(".toggle-section[data-section='chat']").click(function () {
        var $parentCard = $(this).closest('.card');
        $parentCard.find(".chat-section").toggle();
        $(this).toggleClass("active");
    });
});
            $(document).on('click', '.toggle-header', function() {
            // Find the next element with the toggle-content class within the same parent
            var toggleElement = $(this).next('.toggle-content');

            // Toggle the visibility of the found element
            toggleElement.toggle();

            // Change the arrow symbol based on visibility
            var arrowElement = $(this).find('span.toggle-arrow');
            if (toggleElement.is(':visible')) {
                arrowElement.text('▲');
            } else {
                arrowElement.text('▼');
            }

            // If the clicked element is a child, hide/show the content before the next child
            if ($(this).hasClass('toggle-child')) {
                $(this).nextUntil('.toggle-child').toggle();
            }
        });


                    </script>


            {{-- @endpush --}}
        </div>
</x-layout>
