<div class="container">
    <div class="card">
            @php
        // Check if the user is authenticated before checking roles
        $Admin = Auth::check() && (
            in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
            in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
        );
        @endphp
        @if ($Admin)

        <div class="card-header d-flex justify-content-between">
            <button wire:click="previous" class="btn btn-secondary" @if(!$record) disabled @endif>Previous</button>
            <button wire:click="next" class="btn btn-secondary" @if(!$record) disabled @endif>Next</button>
        </div>
         @endif

        @if($record)
            <div class="card-body">

                <p dir="RTL" style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:"<PERSON><PERSON>ri",sans-serif;margin-bottom:0in;line-height:27.0pt;'><span style="font-size:19px;font-family:Al_Mushaf;color:black;">بسمِ اللہ الرَّحمنِ ا لرَّ حِیم</span></p>
<p dir="RTL" style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:"Calibri",sans-serif;margin-bottom:0in;'><span style='font-size:17px;font-family:"Dubai Unicode";color:black;'>الصلوۃ والسلام علیک یارسول اللہ&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;وعلی آلک واصحبک یا حبیب اللہ</span></p>
<p dir="RTL" style='margin-top:0in;margin-right:0in;margin-left:0in;text-align:center;text-indent:0in;font-size:15px;font-family:"Calibri",sans-serif;margin-bottom:0in;line-height:80%;'><span style='font-size:80px;line-height:80%;font-family:"Makkah Contour Unicode";color:black;'>دارالافتاء اہلسنت</span></p>
                <p><strong>Content:</strong> {!! $record->content !!}</p>

                <form action="{{ route('download-word') }}" method="POST">
                    @csrf
                    <input type="hidden" name="content" value="{{ $record->content }}">
                    <input type="hidden" name="ifta_code" value="{{ $record->ifta_code }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-download"></i> Download as Word
                    </button>
                </form>
            </div>



        @else
            <div class="card-body">
                <p>No record found.</p>
            </div>
        @endif
    </div>
    <style>
       .mufti-comment {
    font-family: 'Jameel Noori Nastaleeq';
    font-size: 16px;
    background-color: yellow;
    margin: 0;
}
       .sidebar-content {
                background-color: #f0f8ff; /* Light blue background */
                border-left: 4px solid #007BFF; /* Blue border on the left */
                padding: 5px;
                margin: 5px 0;
            }
        .card {
    margin: 20px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 10px;
}
        @font-face {
            font-family: 'Jameel Noori Nastaleeq';
            src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');

        }
        @font-face {
            font-family: 'Naskh Unicode';
            src: url('/storage/fonts/UrduNaskhUnicode.ttf');
        }
        @font-face {
            font-family: 'Al_Mushaf';
            src: url('/storage/fonts/Al_Mushaf.ttf');
        }
        .btn-primary {
        font-family: 'Jameel Noori Nastaleeq'; /* Apply custom font */
        font-size: 16px;
        color: white; /* Text color */
        background-color: #007BFF; /* Blue background */
        border: none; /* Remove border */
        border-radius: 5px; /* Rounded corners */
        padding: 10px 15px; /* Add padding */
        transition: background-color 0.3s ease, color 0.3s ease; /* Smooth hover effect */
    }

    .btn-primary:hover {
        background-color: #0056b3; /* Darker blue on hover */
        color: #ffffff; /* Keep text white */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Add shadow effect on hover */
    }

    .btn-primary i {
        margin-right: 5px; /* Add some space between the icon and the text */
    }
    </style>
</div>
