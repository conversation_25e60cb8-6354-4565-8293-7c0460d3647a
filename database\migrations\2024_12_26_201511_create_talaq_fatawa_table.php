<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('talaq_fatawa', function (Blueprint $table) {
            $table->id();
            $table->string('ifta_code')->nullable();
            $table->date('rec_date')->nullable();
            $table->string('assign_id')->nullable();
            $table->text('content')->nullable(); // For TinyMCE data
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('talaq_fatawa');
    }
};
