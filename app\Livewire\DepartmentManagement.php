<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Department;
use App\Models\User;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class DepartmentManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $showCreateModal = false;
    public $showEditModal = false;
    public $showAssignModal = false;
    
    public $departmentId;
    public $name = '';
    public $description = '';
    public $is_active = true;
    
    public $selectedUsers = [];
    public $availableUsers = [];
    public $assignedUsers = [];
    
    public $search = '';
    public $filterActive = 'all';

    protected $rules = [
        'name' => 'required|string|max:255|unique:departments,name',
        'description' => 'nullable|string',
        'is_active' => 'boolean',
    ];

    public function mount()
    {
        $this->authorize('manage-departments');
        $this->loadAvailableUsers();
    }

    public function render()
    {
        $departments = Department::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%');
            })
            ->when($this->filterActive !== 'all', function ($query) {
                $query->where('is_active', $this->filterActive === 'active');
            })
            ->withCount('users')
            ->paginate(10);

        return view('livewire.department-management', [
            'departments' => $departments,
        ]);
    }

    public function openCreateModal()
    {
        $this->authorize('create', Department::class);
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function openEditModal($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('update', $department);
        
        $this->departmentId = $department->id;
        $this->name = $department->name;
        $this->description = $department->description;
        $this->is_active = $department->is_active;
        
        $this->showEditModal = true;
    }

    public function openAssignModal($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('assignUsers', $department);
        
        $this->departmentId = $departmentId;
        $this->loadAssignedUsers($departmentId);
        $this->selectedUsers = $this->assignedUsers->pluck('id')->toArray();
        
        $this->showAssignModal = true;
    }

    public function createDepartment()
    {
        $this->authorize('create', Department::class);
        $this->validate();

        Department::create([
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showCreateModal = false;
        session()->flash('message', 'Department created successfully.');
    }

    public function updateDepartment()
    {
        $department = Department::findOrFail($this->departmentId);
        $this->authorize('update', $department);

        $this->rules['name'] = 'required|string|max:255|unique:departments,name,' . $this->departmentId;
        $this->validate();

        $department->update([
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->is_active,
        ]);

        $this->resetForm();
        $this->showEditModal = false;
        session()->flash('message', 'Department updated successfully.');
    }

    public function assignUsers()
    {
        $department = Department::findOrFail($this->departmentId);
        $this->authorize('assignUsers', $department);

        // Sync users with the department
        $department->users()->sync($this->selectedUsers, [
            'assigned_at' => now(),
            'assigned_by' => auth()->id(),
        ]);

        $this->showAssignModal = false;
        session()->flash('message', 'Users assigned to department successfully.');
    }

    public function deleteDepartment($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('delete', $department);

        // Check if department has users assigned
        if ($department->users()->count() > 0) {
            session()->flash('error', 'Cannot delete department with assigned users.');
            return;
        }

        $department->delete();
        session()->flash('message', 'Department deleted successfully.');
    }

    public function toggleStatus($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $this->authorize('update', $department);

        $department->update(['is_active' => !$department->is_active]);
        
        $status = $department->is_active ? 'activated' : 'deactivated';
        session()->flash('message', "Department {$status} successfully.");
    }

    private function resetForm()
    {
        $this->departmentId = null;
        $this->name = '';
        $this->description = '';
        $this->is_active = true;
        $this->selectedUsers = [];
        $this->resetValidation();
    }

    private function loadAvailableUsers()
    {
        $this->availableUsers = User::select('id', 'name', 'email')
            ->orderBy('name')
            ->get();
    }

    private function loadAssignedUsers($departmentId)
    {
        $this->assignedUsers = Department::findOrFail($departmentId)
            ->users()
            ->select('users.id', 'users.name', 'users.email')
            ->get();
    }

    public function closeModals()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showAssignModal = false;
        $this->resetForm();
    }
}
