<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class PerformanceHoliday extends Model
{
    use HasFactory;

    protected $fillable = [
        'holiday_date',
        'name',
        'description',
        'type',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'holiday_date' => 'date',
        'is_active' => 'boolean',
    ];

    // Holiday type constants
    const TYPE_RELIGIOUS = 'religious';
    const TYPE_NATIONAL = 'national';
    const TYPE_CUSTOM = 'custom';

    /**
     * Get the user who created this holiday.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get active holidays.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get holidays for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('holiday_date', $date);
    }

    /**
     * Check if a given date is a holiday.
     */
    public static function isHoliday($date): bool
    {
        $date = Carbon::parse($date)->format('Y-m-d');
        
        return static::active()
            ->where('holiday_date', $date)
            ->exists();
    }

    /**
     * Check if a given date requires performance submission.
     * Returns false if it's Sunday or a holiday.
     */
    public static function requiresPerformance($date): bool
    {
        $carbon = Carbon::parse($date);
        
        // Skip Sundays
        if ($carbon->isSunday()) {
            return false;
        }
        
        // Skip holidays
        if (static::isHoliday($date)) {
            return false;
        }
        
        return true;
    }

    /**
     * Get all non-working days (Sundays + holidays) for a date range.
     */
    public static function getNonWorkingDays($startDate, $endDate): array
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $nonWorkingDays = [];
        
        // Get all holidays in the range
        $holidays = static::active()
            ->whereBetween('holiday_date', [$start->format('Y-m-d'), $end->format('Y-m-d')])
            ->pluck('holiday_date')
            ->map(function ($date) {
                return Carbon::parse($date)->format('Y-m-d');
            })
            ->toArray();
        
        // Check each day in the range
        $current = $start->copy();
        while ($current->lte($end)) {
            if ($current->isSunday() || in_array($current->format('Y-m-d'), $holidays)) {
                $nonWorkingDays[] = $current->format('Y-m-d');
            }
            $current->addDay();
        }
        
        return $nonWorkingDays;
    }

    /**
     * Get working days count between two dates.
     */
    public static function getWorkingDaysCount($startDate, $endDate): int
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        $totalDays = $start->diffInDays($end) + 1;
        $nonWorkingDays = count(static::getNonWorkingDays($startDate, $endDate));
        
        return $totalDays - $nonWorkingDays;
    }
}
