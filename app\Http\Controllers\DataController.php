<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;


class DataController extends Controller
{
    public function getData()
{
    // Fetch data with unique darulifta_name and mail_folder_date values
    $data = DB::table('uploaded_files')
    ->select('darulifta_name', 'mail_folder_date', 'file_name', 'sender', 'checked_file_name', 'checked_folder', 'checked_grade', 'checked_tasurat', 'selected', 'id')
    ->groupBy('darulifta_name', 'mail_folder_date', 'file_name', 'sender', 'checked_file_name', 'checked_folder', 'checked_grade', 'checked_tasurat', 'selected', 'id')
    ->get();
    $uniqueDaruliftaNames = $data->pluck('darulifta_name')->unique();
    $uniqueMailFolderDates = $data->pluck('mail_folder_date')->unique();

    return view('files.sending', compact('data', 'uniqueDaruliftaNames', 'uniqueMailFolderDates'));
}
public function getMailFolderDates($daruliftaName)
{
    // Fetch mail folder dates based on the selected Darulifta's name
    $mailFolderDates = DB::table('uploaded_files')
        ->where('darulifta_name', $daruliftaName)
        ->distinct()
        ->pluck('mail_folder_date')
        ->toArray();

    return response()->json($mailFolderDates);
}
public function getUniqueValues()
{
    // Fetch unique darulifta_name and mail_folder_date values
    $daruliftaNames = DB::table('uploaded_files')->distinct()->pluck('darulifta_name')->toArray();
    $mailFolderDates = DB::table('uploaded_files')->distinct()->pluck('mail_folder_date')->toArray();

    return response()->json([
        'daruliftaNames' => array_values(array_unique($daruliftaNames)), // Remove duplicates
        'mailFolderDates' => array_values(array_unique($mailFolderDates)), // Remove duplicates
    ]);
}
// public function storeSelectedValues(Request $request) {
//     // Get the selectedDarul and selectedFolder values from the request
//     $selectedDarul = $request->input('selectedDarul');
//     $selectedFolder = $request->input('selectedFolder');

//     // Query the database to get matching records
//     $fatwaData = DB::table('uploaded_files')
//         ->where('darulifta_name', '=', $selectedDarul)
//         ->where('mail_folder_date', '=', $selectedFolder)
//         ->get();

//     // Check if there is no data
//     if ($fatwaData->isEmpty()) {
//         $message = 'No data found for the selected folder.';
        
//         return response()->json(['message' => $message]);
//     }

//     // Fetch Darulifta data
//     $daruliftaData = DB::table('uploaded_files')->distinct()->pluck('darulifta_name');

//     // Fetch Mujeeb data
//     $folderData = DB::table('uploaded_files')->distinct()->pluck('mail_folder_date');

//     // Pass the filtered data and additional data as JSON
//     return response()->json([
//         'fatwaData' => $fatwaData,
//         'daruliftaData' => $daruliftaData,
//         'folderData' => $folderData,
//     ]);
// }

public function storeSelectedValues(Request $request, $selectedDarul = null, $selectedFolder = null, $transfer_by = null) {

    $username = Auth::user()->name;
    // dd($selectedDarul,$selectedFolder);
    // Get the selectedDarul and selectedFolder values from the request
    // $selectedDarul = $request->input('darulifta1');
    // $selectedFolder = $request->input('folder_select');
    // $selectedDate = $request->input('folder_select');
    // $checkername = $request->input('username');
    
    $checker = DB::table('checker')
    ->where('checker_name', $username)
    ->first()
    ->folder_id;
    // $checker_d = DB::table('checker')
    // ->where('checker_name', $username)
    // ->first()
    // ->folder_id;
    // dd($checker);
    // Log the received data for debugging
    \Log::info("Selected Darul: $selectedDarul, Selected Folder: $selectedFolder");
    $que_day_r = DB::table('questions')
            ->where('question_branch', $selectedDarul)
            ->get();
    $mahlenazar_null = DB::table('uploaded_files as u1')
    ->where('u1.darulifta_name', $selectedDarul)
    ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
    
    ->leftJoin('uploaded_files as u2', function ($join) {
        $join->on('u1.file_code', '=', 'u2.file_code')
            ->where(function ($query) {
                $query->where('u2.checked_folder', 'ok')
                
                    ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere
                    
            });
    })
    ->whereNull('u2.file_code')
    
    ->select('u1.*')
    ->get();

    $daruliftaData = DB::table('uploaded_files')
        ->distinct()
        ->where('darulifta_name', $selectedDarul)
        ->where('selected', 0)
        ->where(function($query) use ($checker) {
            if ($checker == 'mufti_ali_asghar') {
                $query->whereNull('checker')
                      ->orWhere('checker', $checker);
            } else {
                $query->where('checker', $checker);
            }
        })
        ->pluck('darulifta_name');

    $folderData = DB::table('uploaded_files')
        ->distinct()
        ->where('darulifta_name', $selectedDarul)
        ->where('selected', 0)
        ->where(function($query) use ($checker) {
            if ($checker == 'mufti_ali_asghar') {
                $query->whereNull('checker')
                      ->orWhere('checker', $checker);
            } else {
                $query->where('checker', $checker);
            }
        })
        ->pluck('mail_folder_date');
        $remainingFatawas = [];

        foreach ($folderData as $mailfolderDates) {
            foreach ($daruliftaData as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDates)
                    ->where('selected', 0)
                    ->where(function($query) use ($checker) {
                        if ($checker == 'mufti_ali_asghar') {
                            $query->whereNull('checker')
                                  ->orWhere('checker', $checker);
                        } else {
                            $query->where('checker', $checker);
                        }
                    });
    
                if ($query->exists()) {
                    $remainingFatawas[$daruliftaName][$mailfolderDates] = $query->get();
                }
            }
        }
        // dd($remainingFatawas,$folderData,$daruliftaData);
    // Query the database to get matching records
    $fatwaData = DB::table('uploaded_files')
        ->where('darulifta_name', $selectedDarul)
        ->where('mail_folder_date', $selectedFolder)
        ->where('selected', 0)
        ->when($transfer_by, function ($query, $transfer_by) {
            if ($transfer_by === 'Mujeeb') {
                // If transfer_by is 'Mujeeb', get data where transfer_by is null or empty
                $query->where(function ($query) {
                    $query->whereNull('transfer_by')
                          ->orWhere('transfer_by', '');
                });
            } else {
                // Otherwise, get data where transfer_by matches the provided value
                $query->where('transfer_by', $transfer_by);
            }
        })
        ->where(function($query) use ($checker) {
            if ($checker == 'mufti_ali_asghar') {
                $query->whereNull('checker')
                      ->orWhere('checker', $checker);
            } else {
                $query->where('checker', $checker);
            }
        })
        ->get();

        $quest = DB::table('questions')
        ->where('send_to_mufti', 1)
       ->get();
    //    dd($username,$checker,$daruliftaData,$folderData,$fatwaData,$quest);        
   // First, pluck the 'transfer_by' values
   
    $transferByQuery = DB::table('uploaded_files')
    ->distinct()
    ->where('selected', 0)
    ->where('darulifta_name', $selectedDarul);

// Add the condition for `selectedFolder` if it's not null or empty
if (!is_null($selectedFolder) && $selectedFolder !== '') {
    $transferByQuery->where('mail_folder_date', '>', $selectedFolder);
}

$transferByValues = $transferByQuery
    ->pluck('transfer_by')
    ->map(function($value) {
        return empty($value) ? 'Mujeeb' : $value; // Replace null/empty with 'Mujeeb'
    })
    ->unique();
    
// Step 2: Initialize a collection to store the results
$nextFolderData = collect();

foreach ($transferByValues as $transferBy) {
    $nextFolderQuery = DB::table('uploaded_files')
        ->distinct()
        ->where('selected', 0)
        ->where('darulifta_name', $selectedDarul);

    // Handle 'checker' condition
    if ($checker == 'mufti_ali_asghar') {
        $nextFolderQuery->where(function ($query) {
            $query->whereNull('checker')
                  ->orWhere('checker', 'mufti_ali_asghar');
        });
    } else {
        $nextFolderQuery->where('checker', $checker);
    }

    // Handle 'mail_folder_date' condition
    if (!is_null($selectedFolder) && $selectedFolder !== '') {
        $nextFolderQuery->whereRaw("STR_TO_DATE(mail_folder_date, '%Y-%m-%d') > STR_TO_DATE(?, '%Y-%m-%d')", [$selectedFolder]);
    }

    // Handle 'transfer_by' condition
    if ($transferBy === 'Mujeeb') {
        $nextFolderQuery->where(function ($query) {
            $query->whereNull('transfer_by')
                  ->orWhere('transfer_by', '');
        });
    } else {
        $nextFolderQuery->where('transfer_by', $transferBy);
    }

    $nextFolder = $nextFolderQuery
        ->orderBy('mail_folder_date', 'asc')
        ->first();

    if ($nextFolder) {
        $nextFolderData->push($nextFolder);
    }
}


// Step 1: Fetch transfer_by values for back method
$transferbyback = DB::table('uploaded_files')
    ->distinct()
    ->where('selected', 0)
    ->where('darulifta_name', $selectedDarul);

// Add the condition for `selectedFolder` if it's not null or empty
if (!is_null($selectedFolder) && $selectedFolder !== '') {
    $transferbyback->where('mail_folder_date', '<', $selectedFolder);
}

$transferByback = $transferbyback
    ->pluck('transfer_by')
    ->map(function($value) {
        return empty($value) ? 'Mujeeb' : $value; // Replace null/empty with 'Mujeeb'
    })
    ->unique();
    // dd($transferByValues,$transferByback);
// Step 2: Initialize a collection to store the results
$backFolderData = collect();

foreach ($transferByback as $transferback) {
    $backFolderQuery = DB::table('uploaded_files')
        ->distinct()
        ->where('selected', 0)
        ->where('darulifta_name', $selectedDarul);

    // Handle 'checker' condition
    if ($checker == 'mufti_ali_asghar') {
        $backFolderQuery->where(function ($query) {
            $query->whereNull('checker')
                  ->orWhere('checker', 'mufti_ali_asghar');
        });
    } else {
        $backFolderQuery->where('checker', $checker);
    }

    // Handle 'mail_folder_date' condition
    if (!is_null($selectedFolder) && $selectedFolder !== '') {
        $backFolderQuery->whereDate('mail_folder_date', '<', $selectedFolder);
    }

    // Handle 'transfer_by' condition
    if ($transferback === 'Mujeeb') {
        $backFolderQuery->where(function ($query) {
            $query->whereNull('transfer_by')
                  ->orWhere('transfer_by', '');
        });
    } else {
        $backFolderQuery->where('transfer_by', $transferback);
    }

    $backFolder = $backFolderQuery
        ->orderBy('mail_folder_date', 'desc')
        ->first();

    if ($backFolder) {
        $backFolderData->push($backFolder);
    }
}
$daruliftalist = DB::table('uploaded_files')
            ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
            ->select('uploaded_files.darulifta_name')
            ->distinct()
            ->orderBy('daruliftas.id')
            ->pluck('uploaded_files.darulifta_name');



           
            
// dd($nextFolderData,$backFolderData,$selectedDarul,$fatwaData,$selectedFolder);
    // Check if there is no data
    if ($fatwaData->isEmpty()) {
        return redirect()->route('sending-fatawa');
        // $message = 'No data found for the selected folder.';
        // return view('files.sending1', compact('message', 'daruliftaData', 'selectedFolder','quest','checker','selectedDarul','daruliftalist','backFolderData','nextFolderData'));
    }
    
        $tasurats = DB::table('tasurats')->get();
    

    // Pass the filtered data to the view
    return view('files.sending1', compact('folderData','fatwaData', 'daruliftaData', 'selectedFolder','selectedDarul','quest','checker','nextFolderData','backFolderData','transfer_by','daruliftalist','que_day_r','mahlenazar_null','remainingFatawas','tasurats'));
}
//     public function index()
//     {
//         // Fetch unique values for darulifta_name and mail_folder_date
//         $daruliftaNames = DB::table('uploaded_files')->distinct()->pluck('darulifta_name');
//         $mailFolderDates = DB::table('uploaded_files')->distinct()->pluck('mail_folder_date');

//         // Fetch initial data (without filters)
//         $data = DB::table('uploaded_files')->get();

//         return view('files.sending', compact('data', 'daruliftaNames', 'mailFolderDates'));
//     }

//     public function fetchData(Request $request)
// {
//     // Get the selected darulifta_name and mail_folder_date from the request
//     $daruliftaName = $request->input('daruliftaName');
//     $mailFolderDate = $request->input('mailFolderDate');

//     // Fetch unique values for darulifta_name and mail_folder_date
//     $daruliftaNames = DB::table('uploaded_files')->distinct()->pluck('darulifta_name');
//     $mailFolderDates = DB::table('uploaded_files')->distinct()->pluck('mail_folder_date')->unique();

//     // Query the database with the selected filters
//     $data = DB::table('uploaded_files')
//         ->when($daruliftaName, function ($query) use ($daruliftaName) {
//             return $query->where('darulifta_name', $daruliftaName);
//         })
//         ->when($mailFolderDate, function ($query) use ($mailFolderDate) {
//             return $query->where('mail_folder_date', $mailFolderDate);
//         })
//         ->get();
//           // Render the same view ('data.blade.php') with filtered data
//         return view('files.sending', compact('data', 'daruliftaNames', 'mailFolderDates'));
//     }
// public function create()
// {
//     $daruliftaData = DB::table('uploaded_files')->distinct()->pluck('darulifta_name'); // Fetch Darulifta data
//     $folderData = DB::table('uploaded_files')->distinct()->get();//pluck('mail_folder_date'); // Fetch Mujeeb data
//     // $fatwaData = DB::table('uploaded_files')->get();
    

//     // Pass both sets of data to the view
//     return view('files.sending1', compact('daruliftaData', 'folderData'));
// }
public function createSending2()
{
    $daruliftaData = DB::table('uploaded_files')->distinct()->pluck('darulifta_name'); // Fetch Darulifta data
    $folderData = DB::table('uploaded_files')->distinct()->get(); // Fetch Mujeeb data
    

    // Pass both sets of data to the "sending2" view
    return view('files.sending2', compact('daruliftaData', 'folderData'));
}
// public function index()
// {
//     $daruliftaNames = DB::table('uploaded_files')->distinct('darulifta_name')->pluck('darulifta_name');
//     $mailFolderDates = DB::table('uploaded_files')->distinct('mail_folder_date')->pluck('mail_folder_date');

//     // Fetch initial data (you can change this query as needed)
//     $data = DB::table('uploaded_files')->limit(10)->get();

//     return view('files.sending1', compact('daruliftaNames', 'mailFolderDates', 'data'));
// }
// public function getData1(Request $request)
// {
//     $selectedDaruliftaName = $request->input('darulifta_name');
//     $selectedMailFolderDate = $request->input('selected_mail_folder_date');

//     $query = DB::table('your_table_name');

//     if ($selectedDaruliftaName) {
//         $query->where('darulifta_name', $selectedDaruliftaName);
//     }

//     if ($selectedMailFolderDate) {
//         $query->where('mail_folder_date', $selectedMailFolderDate);
//     }

//     $data = $query->get();

//     return view('files.sending1', compact('data'));
// }
public function getfolder($daruliftaName)
    {
        // Fetch Mujeebs based on the selected Darulifta's name
        $username = Auth::user()->name;
        $checker = DB::table('checker')
    ->where('checker_name', $username)
    ->first()
    ->folder_id;
    $folder = DB::table('uploaded_files')
    ->where('selected', 0)
    ->where('darulifta_name', $daruliftaName)
    ->where(function($query) use ($checker) {
        if ($checker == 'mufti_ali_asghar') {
            $query->whereNull('checker')
                  ->orWhere('checker', $checker);
        } else {
            $query->where('checker', $checker);
        }
    })
    ->get();

        return response()->json($folder);
    }  
}

