<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB; // Import the DB facade

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot()
    {
        
        View::share('mahl_e_mujeeb', $this->getMahlEMujeebData());
        // View::share('daruliftaNames', $this->getDaruliftaNames());
        View::share('mahlenazar_mujeeb', $this->getMahlenazarMujeebData());
    }
    
    private function getMahlEMujeebData()
    {
        
    
        // Fetch darulifta names from the database
       

        $mahl_e_mujeeb = [];

        $daruliftaNames = DB::table('uploaded_files')
        ->select('darulifta_name')
        ->where('darulifta_name', 'NOT LIKE', '%3btn%')
        ->distinct()
        ->orderByRaw('
            CASE
                WHEN darulifta_name = "Noorulirfan" THEN 1
                WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
                WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
                WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
                ELSE 5
            END
        ')
        ->pluck('darulifta_name');
        // Log the retrieved darulifta names for debugging
        // Log::info('Darulifta Names:', $daruliftaNames);

        foreach ($daruliftaNames as $daruliftaName) {
            $senders = DB::table('uploaded_files as u1')
                ->leftJoin('uploaded_files as u2', function ($join) {
                    $join->on('u1.file_code', '=', 'u2.file_code')
                        ->where(function ($query) {
                            $query->where('u2.checked_folder', 'ok')
                                ->orWhere('u2.checked_folder', 'Tahqiqi');
                        });
                })
                ->whereNull('u2.file_code')
                ->where('u1.checked_folder', 'Mahl-e-Nazar')
                ->where('u1.darulifta_name', $daruliftaName)
                ->distinct()
                ->pluck('u1.sender');

            foreach ($senders as $sender) {
                $m_d_mujeeeb = DB::table('uploaded_files as u1')
                    ->where('u1.checked_folder', 'Mahl-e-Nazar')
                    ->where('u1.darulifta_name', $daruliftaName)
                    ->where('u1.sender', $sender)
                    ->orderByRaw('
                        CASE 
                            WHEN u1.darulifta_name = "Noorulirfan" THEN 1
                            WHEN u1.darulifta_name = "Faizan-e-Ajmair" THEN 2
                            WHEN u1.darulifta_name = "Gulzar-e-Taiba" THEN 3
                            WHEN u1.darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
                            ELSE 5
                        END
                    ')
                    ->select('u1.darulifta_name', 'u1.sender')
                    ->distinct()
                    ->get();

                if ($m_d_mujeeeb->isNotEmpty()) {
                    $mahl_e_mujeeb[$daruliftaName][$sender] = $m_d_mujeeeb;
                }
            }
        }

        // // Log the final $mahl_e_mujeeb data for debugging
        // Log::info('Mahl-e-Mujeeb Data:', $mahl_e_mujeeb);
   
        return $mahl_e_mujeeb;
    

       
    // dd($mahl_e_mujeeb);
    }

    // private function getDaruliftaNames()
    // {
    //     // Your logic to fetch and return the data
    // }

    private function getMahlenazarMujeebData()
    {
        // Your logic to fetch and return the data
    }
}
