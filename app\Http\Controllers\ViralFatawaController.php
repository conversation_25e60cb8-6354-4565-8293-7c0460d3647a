<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ViralFatawaController extends Controller
{
    public function allViralFatawa(Request $request)
    {
        // Retrieve the authenticated user's roles
        $userRoles = Auth::user()->roles;
        $roleNames = $userRoles->pluck('name')->toArray(); // Get role names as an array

        // Get the first role name if the user has multiple roles
        $firstRoleName = reset($roleNames);
        $role = $request->input('role'); // Assume you may pass a 'role' parameter in the request

        // Get username
        $username = Auth::user()->name;

        // Logic for retrieving darulifta names
        if ($role == null) {
            if (count($userRoles) > 1) {
                $daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->pluck('darulifta_name');
            } else {
                $daruliftaNames = DB::table('uploaded_files')
                    ->select('darulifta_name')
                    ->distinct()
                    ->where('darulifta_name', $firstRoleName)
                    ->pluck('darulifta_name');
            }
        } else {
            $daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name', $role)
                ->pluck('darulifta_name');
        }

        // Prepare an array to store fatawa data by darulifta name
        $dataByDaruliftaName = [];

        foreach ($daruliftaNames as $daruliftaName) {
            $query = DB::table('uploaded_files')
                ->where('darulifta_name', $daruliftaName)
                ->where('viral', 1); // Only select viral fatawa

            $result = $query->get();

            // Store the fatawa for the current darulifta name if any are found
            if ($result->isNotEmpty()) {
                $dataByDaruliftaName[$daruliftaName] = $result;
            }
        }

        // Pass the fatawa data to the view
        return view('files.viralfatawa', compact('dataByDaruliftaName'));
    }
    public function index(Request $request, $filter = null)
    {
        $userRoles = auth()->user()->roles;

        // Extract the names of the roles into an array
        $roleNames = $userRoles->pluck('name')->toArray();

        // Now you can use the $roleNames array in your controller method
        // For example, if you need to access the first role name:
        $firstRoleName = reset($roleNames);

        $question_b = DB::table('uploaded_files');

        // Apply additional conditions based on $all, $viral, and $web values
        if ($filter === 'all') {
            // Case when 'all' is set to 'all'
            $question_b = $question_b->whereNotNull('shoba_viral');
        } elseif ($filter === 'viral') {
            // Case when 'viral' is set to 'viral'
            $question_b = $question_b->whereNotNull('viral_link');
        } elseif ($filter === 'web') {
            // Case when 'web' is set to 'web'
            $question_b = $question_b->whereNotNull('web_link');
        } else {
            // Case when all, viral, and web are null
            $question_b = $question_b->whereNotNull('shoba_viral')
                ->where(function ($query) {
                    $query->whereNull('viral_link')
                          ->orWhereNull('web_link');
                });
        }

        // Order by 'shoba_viral' and retrieve results
        $question_b = $question_b->orderBy('shoba_viral', 'asc')->paginate(10);

        $questions = DB::table('questions')
        ->whereNull('assign_id')
        ->get();
        $mujeebs = DB::table('mujeebs')
        ->get();

        return view('viral.shoba_viral', compact('question_b','mujeebs','questions'));
    }
}
