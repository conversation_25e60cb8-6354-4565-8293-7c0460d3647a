<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\DailyPerformance;
use App\Models\UserRestriction;

class CheckDailyPerformance
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Skip check for Nazim/Admin users
        if ($user->isNazim()) {
            return $next($request);
        }

        // Only check performance for users with assigned tasks
        if (!$user->hasAssignedTasks()) {
            return $next($request);
        }

        // Check if user has submitted today's performance
        if (!$user->hasSubmittedTodaysPerformance()) {
            // Create or update restriction
            UserRestriction::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'restriction_type' => 'performance_not_submitted',
                    'is_active' => true,
                ],
                [
                    'reason' => 'Daily performance not submitted for ' . now()->format('Y-m-d'),
                    'restricted_by' => $user->id, // System restriction
                    'restricted_at' => now(),
                ]
            );

            // Check if this is a performance-related route
            $performanceRoutes = [
                'daily-performance.create',
                'daily-performance.store',
                'logout',
            ];

            if (in_array($request->route()->getName(), $performanceRoutes)) {
                return $next($request);
            }

            // For AJAX requests, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'error' => 'Access denied. Please submit your daily performance report.',
                    'redirect' => route('daily-performance.create')
                ], 403);
            }

            // Show access denied page with blur effect
            return response()->view('access-denied', [
                'user' => $user,
                'todayDate' => now()->format('F d, Y')
            ]);
        }

        // Remove performance restriction if it exists and performance is submitted
        UserRestriction::where('user_id', $user->id)
            ->where('restriction_type', 'performance_not_submitted')
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $user->id,
                'lifted_at' => now(),
                'lift_reason' => 'Daily performance submitted',
            ]);

        return $next($request);
    }
}
