<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\DailyPerformance;
use App\Models\UserRestriction;
use App\Models\PerformanceHoliday;
use Carbon\Carbon;

class CheckDailyPerformance
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return $next($request);
        }

        // Skip check for Nazim/Admin users
        if ($user->isNazim()) {
            return $next($request);
        }

        // Only check performance for users with assigned tasks
        if (!$user->hasAssignedTasks()) {
            return $next($request);
        }

        // Check if today requires performance submission (exclude Sundays and holidays)
        if (!PerformanceHoliday::requiresPerformance(now())) {
            return $next($request);
        }

        // Check for missed performance submissions
        $missedDays = $this->getMissedPerformanceDays($user);

        if (!empty($missedDays)) {
            // Get first admin user for system restrictions
            $adminUser = \App\Models\User::whereHas('roles', function ($query) {
                $query->where('name', 'Admin');
            })->first();

            // Create or update restriction for missed performance
            UserRestriction::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'restriction_type' => UserRestriction::TYPE_PERFORMANCE_NOT_SUBMITTED,
                    'is_active' => true,
                ],
                [
                    'reason' => 'Daily performance not submitted for ' . count($missedDays) . ' working day(s). Last missed: ' . end($missedDays),
                    'restricted_by' => $adminUser ? $adminUser->id : $user->id, // Use admin or self if no admin exists
                    'restricted_at' => now(),
                ]
            );

            // Check if this is a performance-related route or logout
            $allowedRoutes = [
                'daily-performance.create',
                'daily-performance.store',
                'logout',
                'dashboard', // Allow access to dashboard to see notifications
            ];

            if (in_array($request->route()->getName(), $allowedRoutes)) {
                return $next($request);
            }

            // For AJAX requests, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'error' => 'Account locked. Please submit your missed daily performance reports.',
                    'redirect' => route('daily-performance.create')
                ], 403);
            }

            // Show account locked page with blur effect
            return response()->view('account-locked', [
                'user' => $user,
                'missedDays' => $missedDays,
                'reason' => 'performance_not_submitted'
            ]);
        }

        // Remove performance restriction if no missed days and restriction exists
        UserRestriction::where('user_id', $user->id)
            ->where('restriction_type', UserRestriction::TYPE_PERFORMANCE_NOT_SUBMITTED)
            ->where('is_active', true)
            ->update([
                'is_active' => false,
                'lifted_by' => $user->id,
                'lifted_at' => now(),
                'lift_reason' => 'All required daily performance reports submitted',
            ]);

        return $next($request);
    }

    /**
     * Get missed performance days for a user (excluding Sundays and holidays).
     */
    private function getMissedPerformanceDays($user): array
    {
        $missedDays = [];
        $today = Carbon::now();

        // Check last 7 working days (excluding today)
        for ($i = 1; $i <= 7; $i++) {
            $checkDate = $today->copy()->subDays($i);

            // Skip if it's not a working day (Sunday or holiday)
            if (!PerformanceHoliday::requiresPerformance($checkDate)) {
                continue;
            }

            // Check if user submitted performance for this date
            $hasSubmitted = DailyPerformance::where('user_id', $user->id)
                ->whereDate('created_at', $checkDate->format('Y-m-d'))
                ->exists();

            if (!$hasSubmitted) {
                $missedDays[] = $checkDate->format('Y-m-d');
            }
        }

        return $missedDays;
    }
}
