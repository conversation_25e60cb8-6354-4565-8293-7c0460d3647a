@php
    $totalCounts = 0;
    $overallFolderCount = 0;

    // Calculate statistics from remainingFatawa data
    if (isset($remainingFatawa)) {
        foreach ($remainingFatawa as $daruliftaName => $folders) {
            foreach ($folders as $folderId => $files) {
                // Handle both arrays and collections
                $fileCount = is_array($files) ? count($files) : $files->count();
                $totalCounts += $fileCount;
                $overallFolderCount++;
            }
        }
    }

    // Calculate statistics from sendingFatawa data if selectedmufti is 'all'
    if ($selectedmufti == 'all' && isset($sendingFatawa)) {
        $totalCounts = 0;
        $overallFolderCount = 0;

        foreach ($sendingFatawa as $daruliftaName => $checkers) {
            foreach ($checkers as $checker => $dates) {
                foreach ($dates as $date => $files) {
                    // Handle both arrays and collections
                    $fileCount = is_array($files) ? count($files) : $files->count();
                    $totalCounts += $fileCount;
                    $overallFolderCount++;
                }
            }
        }
    }
@endphp

<!-- Summary Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value" id="totalFatawaCount">{{ $totalCounts }}</div>
        <div class="stat-label">
            <i class="fas fa-file-alt me-1"></i>
            Total Received Fatawa
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="totalFolderCount">{{ $overallFolderCount }}</div>
        <div class="stat-label">
            <i class="fas fa-folder me-1"></i>
            Total Folders
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ count($daruliftaNames) }}</div>
        <div class="stat-label">
            <i class="fas fa-building me-1"></i>
            Active Daruliftaas
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value">{{ count($mujeebs) }}</div>
        <div class="stat-label">
            <i class="fas fa-users me-1"></i>
            Active Mujeebs
        </div>
    </div>
</div>
