<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\DailyPerformance;
use App\Models\Task;
use App\Models\UserRestriction;
use App\Services\MahlENazarService;
use Carbon\Carbon;

class DashboardNotifications extends Component
{
    public $notifications = [];
    public $sortedNotifications = [];
    public $showNotifications = false;

    protected $mahlENazarService;

    public function __construct()
    {
        $this->mahlENazarService = app(MahlENazarService::class);
    }

    public function mount()
    {
        $this->notifications = [];
        $this->sortedNotifications = [];
        $this->showNotifications = false;
        $this->loadNotifications();
    }

    public function render()
    {
        return view('livewire.dashboard-notifications');
    }

    public function loadNotifications()
    {
        $user = auth()->user();

        if (!$user) {
            $this->notifications = [];
            $this->sortedNotifications = [];
            $this->showNotifications = false;
            return;
        }

        $this->notifications = [];

        try {
            // Check daily performance submission
            $this->checkDailyPerformance($user);

            // Check Mahl-e-Nazar limit
            $this->checkMahlENazarLimit($user);

            // Check pending tasks
            $this->checkPendingTasks($user);

            // Check active restrictions
            $this->checkActiveRestrictions($user);

            // Check team notifications (for Superiors)
            if (method_exists($user, 'isSuperior') && method_exists($user, 'isNazim')) {
                if ($user->isSuperior() || $user->isNazim()) {
                    $this->checkTeamNotifications($user);
                }
            }
        } catch (\Exception $e) {
            // Log error but don't break the page
            \Log::error('Error loading notifications: ' . $e->getMessage());
        }

        $this->showNotifications = count($this->notifications) > 0;
        $this->sortedNotifications = $this->getNotificationsByPriority();
    }

    private function checkDailyPerformance($user)
    {
        try {
            // Only check performance for users with assigned tasks
            if (!$user->hasAssignedTasks()) {
                return;
            }

            $hasSubmitted = DailyPerformance::where('user_id', $user->id)
                ->where('performance_date', Carbon::today())
                ->where('is_submitted', true)
                ->exists();

            if (!$hasSubmitted && !Carbon::today()->isWeekend()) {
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'assignment_turned_in',
                    'title' => 'Daily Performance Pending',
                    'message' => 'Please submit your daily performance report.',
                    'action_url' => route('daily-performance.create'),
                    'action_text' => 'Submit Now',
                    'priority' => 'high',
                ];
            }
        } catch (\Exception $e) {
            // Skip this notification if there's an error
        }
    }

    private function checkMahlENazarLimit($user)
    {
        try {
            if (!$this->mahlENazarService) {
                return;
            }

            $count = $this->mahlENazarService->getMahlENazarCount($user->name);
            $limit = MahlENazarService::MAHL_E_NAZAR_LIMIT;

            if ($count >= $limit) {
                $this->notifications[] = [
                    'type' => 'danger',
                    'icon' => 'warning',
                    'title' => 'Fatawa Limit Reached',
                    'message' => "You have {$count} fatawa in Mahl-e-Nazar status (limit: {$limit}). Please resolve existing fatawa.",
                    'action_url' => route('mahlenazar-fatawa'),
                    'action_text' => 'View Fatawa',
                    'priority' => 'critical',
                ];
            } elseif ($count >= 10) {
                $remaining = $limit - $count;
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'info',
                    'title' => 'Approaching Fatawa Limit',
                    'message' => "You have {$count} fatawa in Mahl-e-Nazar status. Only {$remaining} more allowed.",
                    'action_url' => route('mahlenazar-fatawa'),
                    'action_text' => 'View Status',
                    'priority' => 'medium',
                ];
            }
        } catch (\Exception $e) {
            // Skip this notification if there's an error
        }
    }

    private function checkPendingTasks($user)
    {
        $pendingTasks = Task::where('assigned_to', $user->id)
            ->where('status', 'pending')
            ->where('due_date', '<=', Carbon::today()->addDays(2))
            ->count();

        if ($pendingTasks > 0) {
            $this->notifications[] = [
                'type' => 'info',
                'icon' => 'task',
                'title' => 'Pending Tasks',
                'message' => "You have {$pendingTasks} pending tasks due soon.",
                'action_url' => route('my-tasks'),
                'action_text' => 'View Tasks',
                'priority' => 'medium',
            ];
        }

        $overdueTasks = Task::where('assigned_to', $user->id)
            ->where('status', 'pending')
            ->where('due_date', '<', Carbon::today())
            ->count();

        if ($overdueTasks > 0) {
            $this->notifications[] = [
                'type' => 'danger',
                'icon' => 'schedule',
                'title' => 'Overdue Tasks',
                'message' => "You have {$overdueTasks} overdue tasks.",
                'action_url' => route('my-tasks'),
                'action_text' => 'View Tasks',
                'priority' => 'high',
            ];
        }
    }

    private function checkActiveRestrictions($user)
    {
        $restrictions = UserRestriction::where('user_id', $user->id)
            ->where('is_active', true)
            ->get();

        foreach ($restrictions as $restriction) {
            $this->notifications[] = [
                'type' => 'danger',
                'icon' => 'block',
                'title' => 'Account Restriction',
                'message' => $restriction->reason,
                'action_url' => null,
                'action_text' => null,
                'priority' => 'critical',
            ];
        }
    }

    private function checkTeamNotifications($user)
    {
        if ($user->isSuperior()) {
            // Check team performance submissions
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->departments->flatMap->users->pluck('id');
            $teamUserIds = $assistantIds->merge($departmentUserIds)->unique();

            // Only count users who have assigned tasks and haven't submitted performance
            $usersWithTasks = \App\Models\User::whereIn('id', $teamUserIds)
                ->whereHas('assignedTasks', function ($query) {
                    $query->whereNotIn('status', ['completed', 'cancelled']);
                })
                ->pluck('id');

            // Count users with tasks who haven't submitted today's performance
            $submittedUserIds = DailyPerformance::whereIn('user_id', $usersWithTasks)
                ->where('performance_date', Carbon::today())
                ->where('is_submitted', true)
                ->pluck('user_id');

            $pendingReports = $usersWithTasks->diff($submittedUserIds)->count();

            if ($pendingReports > 0) {
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'group',
                    'title' => 'Team Performance Pending',
                    'message' => "{$pendingReports} team members haven't submitted today's performance.",
                    'action_url' => route('performance-management'),
                    'action_text' => 'View Reports',
                    'priority' => 'medium',
                ];
            }

            // Check team task completion
            $pendingTeamTasks = Task::whereIn('assigned_to', $teamUserIds)
                ->where('status', 'pending')
                ->where('due_date', '<=', Carbon::today())
                ->count();

            if ($pendingTeamTasks > 0) {
                $this->notifications[] = [
                    'type' => 'info',
                    'icon' => 'assignment',
                    'title' => 'Team Tasks Pending',
                    'message' => "{$pendingTeamTasks} team tasks are pending or overdue.",
                    'action_url' => route('workflow-tasks.index'),
                    'action_text' => 'Manage Tasks',
                    'priority' => 'medium',
                ];
            }
        }

        if ($user->isNazim()) {
            // Check system-wide issues
            $usersAtLimit = $this->mahlENazarService->getStatistics()['users_at_limit'] ?? 0;
            $usersOverLimit = $this->mahlENazarService->getStatistics()['users_over_limit'] ?? 0;

            if ($usersOverLimit > 0) {
                $this->notifications[] = [
                    'type' => 'danger',
                    'icon' => 'warning',
                    'title' => 'Users Over Limit',
                    'message' => "{$usersOverLimit} users have exceeded the Mahl-e-Nazar limit.",
                    'action_url' => route('mahl-e-nazar-limits'),
                    'action_text' => 'Manage Limits',
                    'priority' => 'high',
                ];
            } elseif ($usersAtLimit > 0) {
                $this->notifications[] = [
                    'type' => 'warning',
                    'icon' => 'info',
                    'title' => 'Users At Limit',
                    'message' => "{$usersAtLimit} users are at the Mahl-e-Nazar limit.",
                    'action_url' => route('mahl-e-nazar-limits'),
                    'action_text' => 'View Status',
                    'priority' => 'medium',
                ];
            }
        }
    }

    public function dismissNotification($index)
    {
        if (isset($this->notifications[$index])) {
            unset($this->notifications[$index]);
            $this->notifications = array_values($this->notifications); // Re-index array
            $this->showNotifications = count($this->notifications) > 0;
            $this->sortedNotifications = $this->getNotificationsByPriority();
        }
    }

    public function dismissAllNotifications()
    {
        $this->notifications = [];
        $this->sortedNotifications = [];
        $this->showNotifications = false;
    }

    public function refreshNotifications()
    {
        $this->loadNotifications();
    }

    public function getNotificationsByPriority()
    {
        if (empty($this->notifications)) {
            return [];
        }

        $priorityOrder = ['critical' => 0, 'high' => 1, 'medium' => 2, 'low' => 3];

        $sortedNotifications = $this->notifications;
        usort($sortedNotifications, function ($a, $b) use ($priorityOrder) {
            $aPriority = $priorityOrder[$a['priority']] ?? 999;
            $bPriority = $priorityOrder[$b['priority']] ?? 999;
            return $aPriority <=> $bPriority;
        });

        return $sortedNotifications;
    }
}
