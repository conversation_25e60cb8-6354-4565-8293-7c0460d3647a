<div style="direction: rtl;">
    <div class="mt-4">
        <h3 class="font-bold mb-2">Add New Instruction</h3>
        <input type="text" wire:model="newInstructionText" placeholder="Instruction" class="border border-gray-300 p-3 mb-2 text-lg" style="width: 100%; max-width: 600px;">


        <button wire:click="save" class="btn btn-success text-white px-4 py-2">Add</button>
    </div>

    <!-- Display Instruction List -->
    <table class="table-auto w-full">
        <thead>
            <tr>
                <th>ID</th>
                <th>Instruction</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($instructions as $instruction)
                <tr>
                    @if ($editId === $instruction->id)
                        <!-- Edit Mode -->
                        <td>{{ $instruction->id }}</td>
                        <td>
                            <input type="text" wire:model="instruction_text" class="form-control w-full">
                        </td>
                        <td>
                            <button wire:click="save" class="btn btn-success px-4 py-2">Save</button>
                            <button wire:click="resetInput" class="btn btn-danger px-4 py-2">Cancel</button>
                        </td>
                    @else
                        <!-- View Mode -->
                        <td>{{ $instruction->id }}</td>
                        <td style="text-align: right;">{{ $instruction->instruction }}</td>

                        <td>
                            <button wire:click="edit({{ $instruction->id }})" class="btn btn-primary px-4 py-2">Edit</button>
                            <button wire:click="delete({{ $instruction->id }})" class="btn btn-danger px-4 py-2">Delete</button>
                        </td>
                    @endif
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
