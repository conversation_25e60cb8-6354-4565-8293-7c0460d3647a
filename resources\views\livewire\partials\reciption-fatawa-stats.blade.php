<!-- Statistics Section -->
{{-- Use total statistics calculated in backend for summary cards --}}

<!-- Summary Note -->
<div class="alert alert-info mb-3" style="border-left: 4px solid #17a2b8;">
    <div class="d-flex align-items-center">
        <i class="fas fa-info-circle me-2"></i>
        <small class="mb-0">
            <strong>Summary Statistics:</strong> The cards below show counts filtered by your selected time frame and displayed month-wise for better performance.
        </small>
    </div>
</div>

<div class="row mb-4">
    <!-- Total Fatawa -->
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="stat-card">
            <div class="stat-number text-primary">{{ number_format($totalStats['totalFatawa']) }}</div>
            <div class="stat-label">
                <i class="fas fa-file-alt me-1"></i>
                Total Fatawa
            </div>
        </div>
    </div>

    <!-- Total Folders -->
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="stat-card">
            <div class="stat-number text-info">{{ number_format($totalStats['totalFolders']) }}</div>
            <div class="stat-label">
                <i class="fas fa-folder me-1"></i>
                <span data-bs-toggle="tooltip" data-bs-placement="top" title="Unique Ifta Codes from received questions">
                    Total Folders
                </span>
            </div>
        </div>
    </div>

    <!-- Email Fatawa -->
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="stat-card">
            <div class="stat-number text-success">{{ number_format($totalStats['emailFatawa']) }}</div>
            <div class="stat-label">
                <i class="fas fa-envelope me-1"></i>
                Email Fatawa
            </div>
        </div>
    </div>

    <!-- Daily Fatawa -->
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
        <div class="stat-card">
            <div class="stat-number text-warning">{{ number_format($totalStats['dailyFatawa']) }}</div>
            <div class="stat-label">
                <i class="fas fa-calendar-day me-1"></i>
                Daily Fatawa
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="professional-stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <span><i class="fas fa-user-times me-2 text-danger"></i>Not Assigned to Mujeeb</span>
                <strong class="text-danger">{{ number_format($totalStats['notAssignedToMujeeb']) }}</strong>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="professional-stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <span><i class="fas fa-exclamation-triangle me-2 text-warning"></i>Not Sent for Checking</span>
                <strong class="text-warning">{{ number_format($totalStats['notSentForChecking']) }}</strong>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="professional-stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <span><i class="fas fa-calendar-week me-2 text-info"></i>
                    @if($tempSelectedTimeFrame === 'all')
                        Time Period
                    @else
                        Current Month
                    @endif
                </span>
                <strong class="text-info">
                    @if($tempSelectedTimeFrame === 'all')
                        All Time
                    @elseif(isset($currentMonth))
                        {{ \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->format('M Y') }}
                        @if(isset($totalMonths) && $totalMonths > 1)
                            ({{ $currentMonthIndex + 1 }}/{{ $totalMonths }})
                        @endif
                    @else
                        {{ count($recdate) }} days
                    @endif
                </strong>
            </div>
        </div>
    </div>
</div>
