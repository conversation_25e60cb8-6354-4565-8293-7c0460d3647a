<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['activePage']) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['activePage']); ?>
<?php foreach (array_filter((['activePage']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<style>
    .custom-active {
        color: #fff;
        background-color: #3490dc;
    }
    
</style>
<aside
    class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3   bg-gradient-dark"
    id="sidenav-main">
    <div class="sidenav-header">
        <i class="fas fa-times p-3 cursor-pointer text-white opacity-5 position-absolute end-0 top-0 d-none d-xl-none"
            aria-hidden="true" id="iconSidenav"></i>
        <a class="navbar-brand m-0 d-flex text-wrap align-items-center" href=" <?php echo e(route('dashboard')); ?> ">
            <img src="<?php echo e(asset('assets')); ?>/img/logo-ct.png" class="navbar-brand-img h-100" alt="main_logo">
            <span class="ms-3 font-weight-bold text-white" style="display: inline-block; width: 150px; white-space: normal; font-size: 13px;">
    Fatawa Checking Management System
</span>
        </a>
    </div>
     
    
    <hr class="horizontal light mt-0 mb-2">
    <div class="  w-auto  max-height-vh-100" id="sidenav-collapse-main">
        <ul class="navbar-nav">
            <?php
                    $userRoles = Auth::user()->roles->pluck('name')->toArray();
                    $currentRoute = request()->route()->getName();
                    $navigationItems = \App\Helpers\NavigationHelper::getMenuWithActiveState($currentRoute);
                    // dd($userRoles);
                    ?>
            <?php if(!in_array('Mahlenazar', $userRoles)): ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'dashboard' ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('dashboard')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">dashboard</i>
                    </div>
                    <span class="nav-link-text ms-1">Dashboard</span>
                </a>
            </li>
            <?php if(request()->is('remaining-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('remaining-fatawa') || request()->route('darulifta') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('remaining-fatawa')); ?>">
                <span class="nav-link-text ms-1">Remaining Fatawa</span>
            </a> 
        </li>
        <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('remaining-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(request()->is('recived-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('recived-fatawa') || request()->route('darulifta') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('recived-fatawa')); ?>">
                <span class="nav-link-text ms-1">Recived Fatawa</span>
            </a> 
        </li>
        <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('recived-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(request()->is('mahlenazar-fatawa*') || request()->is('transferred-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->

<li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('mahlenazar-fatawa') && !request()->route('darulifta') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('mahlenazar-fatawa')); ?>">
                <span class="nav-link-text ms-1">Mahlenazar Fatawa</span>
            </a> 
        </li>
        
        <!-- Link to the Transferred Fatawa page -->
        <li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('transferred-fatawa') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('transferred-fatawa')); ?>">
                <span class="nav-link-text ms-1">Transferred Fatawa</span>
            </a> 
        </li>
        
        <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('mahlenazar-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(request()->is('sending-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->

<li class="nav-item">

                                                             
                                                             <a class="nav-link text-white <?php echo e(request()->routeIs('sending-fatawa') || request()->route('darulifta') ? 'custom-active' : ''); ?>"
                                                                 href="<?php echo e(route('sending-fatawa')); ?>">
                                                             
                                 
                                                                 <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                                     <i class="material-icons opacity-10">receipt_long</i>
                                                                 </div>
                                                                 <span class="nav-link-text ms-1">Sending Fatawa</span>
                                                             </a>
            </li>
            <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('sending-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(request()->is('reciption-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('reciption-fatawa') || request()->route('darulifta') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('reciption-fatawa')); ?>">
                <span class="nav-link-text ms-1">Reciption Fatawa</span>
            </a> 
        </li>
        <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('reciption-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(request()->is('sent-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('sent-fatawa') || request()->route('darulifta') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('sent-fatawa')); ?>">
                <span class="nav-link-text ms-1">Checked Fatawa</span>
            </a> 
        </li>
        <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('sent-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(request()->is('ok-fatawa*')): ?>     
                <?php if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())): ?>
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white <?php echo e(request()->routeIs('ok-fatawa') || request()->route('darulifta') ? 'custom-active' : ''); ?>"
               href="<?php echo e(route('ok-fatawa')); ?>">
                <span class="nav-link-text ms-1">Ok Fatawa</span>
            </a> 
        </li>
        <ul>
            <?php $__currentLoopData = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $darulifta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $darulifta ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('ok-fatawa', $darulifta)); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($darulifta); ?></span>
                </a>
            </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endif; ?>
<?php endif; ?>
<?php if(Auth::check() && in_array('Checker', Auth::user()->roles->pluck('name')->toArray())): ?>
    <li class="nav-item">
        <a class="nav-link text-white <?php echo e($activePage == 'mutakhassis' ? ' active bg-gradient-primary' : ''); ?>"
           href="<?php echo e(route('mutakhassis')); ?>">
            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                <i class="material-icons opacity-10">receipt_long</i>
            </div>
            <span class="nav-link-text ms-1">Transfer To Mufti Sahib</span>
        </a>
    </li>
<?php endif; ?>

                <?php if(count(Auth::user()->roles) < 2): ?>
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white <?php echo e($activePage == 'reciption' ? ' active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('question.index')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">group</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Reciption Que</span>
                                </a>
    </li>
    <li class="nav-item">
                                <a class="nav-link text-white <?php echo e($activePage == 'mujeeb' ? ' active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('m_question.index')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">account_circle</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Mujeeb Assign Que</span>
                                </a>
    </li>
                            <?php endif; ?>  
                <?php if(count(Auth::user()->roles) > 1): ?>
                            
                <?php
    $urlrecived = explode('/', request()->path());
    $lasturlrec = last($urlrecived);

    // Check if $lasturlrec is not in the specified values, set it to null
    $validValues = ['noorulirfan', 'faizaneajmair', 'gulzahretaiba', 'iqtisad'];
    $lasturlrec = (!in_array($lasturlrec, $validValues)) ? null : $lasturlrec;
?>
                <!-- <li class="nav-item">
                                                             
                                <a class="nav-link text-white <?php echo e($activePage == $lasturlrec ? 'custom-active' : ''); ?>"
                                    href="<?php echo e(route('recivedNor')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Recived Fatawa</span>
                                </a>
                            </li>
                <?php endif; ?>                              
                <?php if(count(Auth::user()->roles) > 1): ?>
                    <?php if(request()->is('noorulirfan','faizaneajmair','gulzahretaiba','iqtisad')): ?>
                <ul>
                
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white <?php echo e($activePage == 'noorulirfan' ? ' active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('recivedNor')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10"></i>
                                    </div>
                                    <span class="nav-link-text ms-1">Noorulirfan</span>
                                </a>
                            </li>
                                <li class="nav-item">
                                                             
                                    <a class="nav-link text-white <?php echo e($activePage == 'faizaneajmair' ? ' active bg-gradient-primary' : ''); ?>"
                                        href="<?php echo e(route('recivedFaj')); ?>">
                                    
        
                                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                            <i class="material-icons opacity-10"></i>
                                        </div>
                                        <span class="nav-link-text ms-1">Fazian-e-Ajmair</span>
                                    </a>
                                </li>
                                    <li class="nav-item">
                                                             
                                        <a class="nav-link text-white <?php echo e($activePage == 'gulzahretaiba' ? ' active bg-gradient-primary' : ''); ?>"
                                            href="<?php echo e(route('recivedGul')); ?>">
                                        
            
                                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                <i class="material-icons opacity-10"></i>
                                            </div>
                                            <span class="nav-link-text ms-1">Gulzahr-e-Taiba</span>
                                        </a>
                                    </li>
                                        <li class="nav-item">
                                                             
                                            <a class="nav-link text-white <?php echo e($activePage == 'iqtisad' ? ' active bg-gradient-primary' : ''); ?>"
                                                href="<?php echo e(route('recivedIec')); ?>">
                                            
                
                                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                    <i class="material-icons opacity-10"></i>
                                                </div>
                                                <span class="nav-link-text ms-1">Markaz-ul-Iqtsiaat</span>
                                            </a>
                                        </li>
                </ul> -->
                    <?php endif; ?>
                <?php else: ?>
                
                            

                
                    

                    
                       
                        <!-- Checking Fatawa Link -->
                        <li class="nav-item">
    <a class="nav-link text-white <?php echo e(request()->checker == 'mufti_ali_asghar' ? ' active bg-gradient-primary' : ''); ?>"
        href="<?php echo e(route('create', ['checker' => 'mufti_ali_asghar'])); ?>">
        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
            <i class="material-icons opacity-10">receipt_long</i>
        </div>
        <span class="nav-link-text ms-1">Sending Fatawa</span>
    </a>
</li>
    <ul class="nav flex-column ms-3">
        <?php $__currentLoopData = $checkerlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $checker): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->checker == $checker->folder_id ? ' active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('create', ['checker' => $checker->folder_id])); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="fas fa-arrow-right opacity-10"></i>
                    </div>
                    <span class="nav-link-text ms-1"><?php echo e($checker->checker_name); ?></span>
                </a>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>

                        <?php endif; ?>
                        
                        

                            
                <?php
    $urlrecived = explode('/', request()->path());
    $lasturlrec = last($urlrecived);

    // Check if $lasturlrec is not in the specified values, set it to null
    $validValues = ['noorulirfan/mufti_ali_asghar', 'faizaneajmair/mufti_ali_asghar', 'gulzahretaiba/mufti_ali_asghar', 'iqtisad/mufti_ali_asghar'];
    $lasturlrec = (!in_array($lasturlrec, $validValues)) ? null : $lasturlrec;
?>
                
                
                <?php if(count(Auth::user()->roles) > 1): ?>
                    <?php if(request()->is('noorulirfan/mufti_ali_asghar','faizaneajmair/mufti_ali_asghar','gulzahretaiba/mufti_ali_asghar','iqtisad/mufti_ali_asghar')): ?>
                <ul>
                
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white <?php echo e($activePage == 'noorulirfan' ? ' active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('recivedNor','mufti_ali_asghar')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10"></i>
                                    </div>
                                    <span class="nav-link-text ms-1">Noorulirfan</span>
                                </a>
                            </li>
                                <li class="nav-item">
                                                             
                                    <a class="nav-link text-white <?php echo e($activePage == 'faizaneajmair' ? ' active bg-gradient-primary' : ''); ?>"
                                        href="<?php echo e(route('recivedFaj','mufti_ali_asghar')); ?>">
                                    
        
                                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                            <i class="material-icons opacity-10"></i>
                                        </div>
                                        <span class="nav-link-text ms-1">Fazian-e-Ajmair</span>
                                    </a>
                                </li>
                                    <li class="nav-item">
                                                             
                                        <a class="nav-link text-white <?php echo e($activePage == 'gulzahretaiba' ? ' active bg-gradient-primary' : ''); ?>"
                                            href="<?php echo e(route('recivedGul','mufti_ali_asghar')); ?>">
                                        
            
                                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                <i class="material-icons opacity-10"></i>
                                            </div>
                                            <span class="nav-link-text ms-1">Gulzahr-e-Taiba</span>
                                        </a>
                                    </li>
                                        <li class="nav-item">
                                                             
                                            <a class="nav-link text-white <?php echo e($activePage == 'iqtisad' ? ' active bg-gradient-primary' : ''); ?>"
                                                href="<?php echo e(route('recivedIec','mufti_ali_asghar')); ?>">
                                            
                
                                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                    <i class="material-icons opacity-10"></i>
                                                </div>
                                                <span class="nav-link-text ms-1">Markaz-ul-Iqtsiaat</span>
                                            </a>
                                        </li>
                </ul>
                    <?php endif; ?>
                        <?php endif; ?>         
                        <?php if(count(Auth::user()->roles) < 2): ?>
                            <li class="nav-item">
                                                             
                            <a class="nav-link text-white <?php echo e(request()->routeIs('sent-for-checking')  ? 'bg-gradient-primary' : ''); ?>"
                            href="<?php echo e(route('sent-for-checking')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Sent Fatawa</span>
                                </a>
                            <?php endif; ?>                        
                        <?php if(count(Auth::user()->roles) > 1): ?>

                        <?php
                $urlchecked = explode('/', request()->path());
                $lasturlcheck = last($urlchecked);
                $validValuescheck = ['checkednor','checkedfaj','checkedgul','checkedIec'];
                $lasturlcheck = (!in_array($lasturlcheck, $validValuescheck)) ? null : $lasturlcheck;

                ?>
                        <!-- <li class="nav-item">
                        <a class="nav-link text-white <?php echo e($activePage == $lasturlcheck ? 'custom-active' : ''); ?>"
                                href="<?php echo e(route('checkedNor')); ?>">
                            

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    <i class="material-icons opacity-10">verified_user</i>
                                </div>
                                <span class="nav-link-text ms-1">Checked Fatawa</span>
                            </a>
                        </li>
                        <?php if(request()->is('checkednor','checkedfaj','checkedgul','checkedIec')): ?>
                        <ul>
                        <li class="nav-item">
                                                             
                            <a class="nav-link text-white <?php echo e($activePage == 'checkednor' ? ' active bg-gradient-primary' : ''); ?>"
                                href="<?php echo e(route('checkedNor')); ?>">
                            

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    
                                </div>
                                <span class="nav-link-text ms-1">Noorulirfan</span>
                            </a>
                        </li>
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white <?php echo e($activePage == 'checkedfaj' ? ' active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('checkedFaj')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        
                                    </div>
                                    <span class="nav-link-text ms-1">Faizan-e-Ajmair</span>
                                </a>
                            </li>
                                <li class="nav-item">
                                                             
                                    <a class="nav-link text-white <?php echo e($activePage == 'checkedgul' ? ' active bg-gradient-primary' : ''); ?>"
                                        href="<?php echo e(route('checkedGul')); ?>">
                                    
        
                                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                            
                                        </div>
                                        <span class="nav-link-text ms-1">Gulzahr-e-Taiba</span>
                                    </a>
                                </li>
                                    <li class="nav-item">
                                                             
                                        <a class="nav-link text-white <?php echo e($activePage == 'checkedIec' ? ' active bg-gradient-primary' : ''); ?>"
                                            href="<?php echo e(route('checkedIec')); ?>">
                                        
            
                                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                
                                            </div>
                                            <span class="nav-link-text ms-1">Markaz-ul-Iqtisad</span>
                                        </a>
                                    </li>
                            
                        </ul> -->
                           <?php endif; ?>
                           <?php else: ?>
                            <!-- Checking Fatawa Link -->
                            <li class="nav-item">
                            <a class="nav-link text-white <?php echo e(request()->routeIs('sent-fatawa') ? 'bg-gradient-primary' : ''); ?>"
                            href="<?php echo e(route('sent-fatawa')); ?>">
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">verified_user</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Checked Fatawa</span>
                                </a>
                            </li>
                            <?php endif; ?>
                            
                            <?php if(count(Auth::user()->roles) < 2): ?>
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white <?php echo e($activePage == 'deliver' ? ' active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('not_deliver')); ?>">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Not Deliver To Sayel</span>
                                </a>
                            <?php endif; ?>    
                            </li> 
                            <?php endif; ?>
                            <?php if(count(Auth::user()->roles) < 2): ?>
                            <li class="nav-item">
                                <a class="nav-link text-white <?php echo e($activePage == 'mahlenazar-fatawa' ? 'active bg-gradient-primary' : ''); ?>"
                                    href="<?php echo e(route('mahlenazar-fatawa')); ?>">
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">dashboard</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Mahl-e-Nazar</span>
                                </a>
                            </li>
                           
                                    
                                   
                                    <ul>
                                        <?php
                                            $urlPath = request()->path();
                                            $urlPathWithoutSlash = explode('/', $urlPath, 2)[0];
                                            // echo "$urlPathWithoutSlash";
                                        ?>


                                        <?php if($urlPathWithoutSlash == 'mahlenazar-fatawa'): ?>

                                        
                                        <?php if(is_array($mahl_e_mujeeb)): ?>
    <?php
        $allowedRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
        $userRoles = Auth::user()->roles->pluck('name')->toArray();
        
    ?>

    <?php $__currentLoopData = $mahl_e_mujeeb; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName => $senders): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    
    
        
        <?php if(in_array($daruliftaName, $userRoles)): ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == $daruliftaName ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('mahlenazar-fatawa', ['role' => $daruliftaName])); ?>">
                    <span class="nav-link-text ms-1"><?php echo e($daruliftaName); ?></span>
                </a>
                <?php if(isset($senders) && is_array($senders)): ?>
                    <ul class="nav-item">
                        <?php $__currentLoopData = $senders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sender => $m_d_mujeeeb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="nav-item">
                                
                                <?php if(in_array($daruliftaName, $allowedRoles)): ?>
                                    <a class="nav-link text-white <?php echo e($activePage == $sender ? 'active bg-gradient-primary' : ''); ?>"
                                        href="<?php echo e(route('mahlenazar-fatawa', ['role' => $daruliftaName, 'fatwa_no' => $sender])); ?>">
                                        <span class="nav-link-text ms-1"><?php echo e($sender); ?></span>
                                    </a>
                                <?php endif; ?>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                <?php endif; ?>
            </li>
        <?php endif; ?>
        
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>   


<?php endif; ?>
                                    </ul>
     
                                <?php endif; ?>
                            
                            



                 
                    <!-- Other sidebar links go here -->
                
                
            
            <?php if(in_array('Manager ', $userRoles)
                            ): ?>  
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'appointment' ? ' active bg-gradient-primary' : ''); ?> "
                    href="<?php echo e(route('appointment')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">receipt_long</i>
                    </div>
                    <span class="nav-link-text ms-1">Appointment</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'schedule' ? ' active bg-gradient-primary' : ''); ?> "
                    href="<?php echo e(route('schedule')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">speaker_notes</i>
                    </div>
                    <span class="nav-link-text ms-1">Schedule</span>
                </a>
            </li>
        </li>
        
        <li class="nav-item">
            <a class="nav-link text-white <?php echo e($activePage == 'notice' ? ' active bg-gradient-primary' : ''); ?> "
                href="<?php echo e(route('notice')); ?>">
                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="material-icons opacity-10">sticky_note_2</i>
                </div>
                <span class="nav-link-text ms-1">Notice Board</span>
            </a>
        </li>
        

        
        <li class="nav-item">
            <a class="nav-link text-white <?php echo e($activePage == 'otheri' ? ' active bg-gradient-primary' : ''); ?> "
                href="<?php echo e(route('otheri')); ?>">
                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="material-icons opacity-10">attach_file</i>
                </div>
                <span class="nav-link-text ms-1">Other Ifta</span>
            </a>
        </li>
        
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'virtual-reality' ? ' active bg-gradient-primary' : ''); ?>  "
                    href="<?php echo e(route('virtual-reality')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">view_in_ar</i>
                    </div>
                    <span class="nav-link-text ms-1">Virtual Reality</span>
                </a>
            </li>
            <?php endif; ?>
            <?php if(in_array('Admin', $userRoles) || in_array('Superior', $userRoles)): ?>
            <li class="nav-item mt-3">
                <h6 class="ps-4 ms-2 text-uppercase text-xs text-white font-weight-bolder opacity-8">System Management</h6>
            </li>

            <?php if(in_array('Admin', $userRoles)): ?>
            <!-- Department Management -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('departments.*') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('departments.index')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">business</i>
                    </div>
                    <span class="nav-link-text ms-1">Department Management</span>
                </a>
            </li>

            <!-- Role Management -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('role-management') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('role-management')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">admin_panel_settings</i>
                    </div>
                    <span class="nav-link-text ms-1">Role Management</span>
                </a>
            </li>

            <!-- Supervisor-Assistant Mapping -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('supervisor-assistant-mapping') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('supervisor-assistant-mapping')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">group</i>
                    </div>
                    <span class="nav-link-text ms-1">Team Management</span>
                </a>
            </li>

            <!-- Mahl-e-Nazar Limit Management -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('mahl-e-nazar-limits') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('mahl-e-nazar-limits')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">shield</i>
                    </div>
                    <span class="nav-link-text ms-1">Limit Control</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- Task Management (Admin and Superior) -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('workflow-tasks.*') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('workflow-tasks.index')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">task</i>
                    </div>
                    <span class="nav-link-text ms-1">Task Management</span>
                </a>
            </li>

            <!-- Performance Management (Admin and Superior) -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('performance-management') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('performance-management')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">analytics</i>
                    </div>
                    <span class="nav-link-text ms-1">Performance Reports</span>
                </a>
            </li>
            <?php endif; ?>

            <?php if(in_array('Admin', $userRoles)): ?>
            <li class="nav-item mt-3">
                <h6 class="ps-4 ms-2 text-uppercase text-xs text-white font-weight-bolder opacity-8">User Management</h6>
            </li>

            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'darulifta' ? 'active bg-gradient-primary' : ''); ?> "
                    href="<?php echo e(route('darulifta')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1.2rem;" class="fas fa-user-circle ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Darulifta</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'mujeeb' ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('mujeeb.store')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1.2rem;" class="fas fa-user-circle ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Mujeeb</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'user-profile' ? 'active bg-gradient-primary' : ''); ?> "
                    href="<?php echo e(route('user-profile')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1.2rem;" class="fas fa-user-circle ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">User Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'user-management' ? ' active bg-gradient-primary' : ''); ?> "
                    href="<?php echo e(route('user-management')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1rem;" class="fas fa-lg fa-list-ul ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">User Management</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- Personal Management Section (All authenticated users) -->
            <li class="nav-item mt-3">
                <h6 class="ps-4 ms-2 text-uppercase text-xs text-white font-weight-bolder opacity-8">Personal</h6>
            </li>

            <!-- Daily Performance Report -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('daily-performance.create') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('daily-performance.create')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">assignment_turned_in</i>
                    </div>
                    <span class="nav-link-text ms-1">Daily Performance</span>
                </a>
            </li>

            <!-- My Tasks -->
            <?php if(in_array('Superior', $userRoles) || in_array('mujeeb', $userRoles) || in_array('Muawin', $userRoles)): ?>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('my-tasks') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('my-tasks')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">checklist</i>
                    </div>
                    <span class="nav-link-text ms-1">My Tasks</span>
                </a>
            </li>
            <?php endif; ?>

            <!-- My Performance History -->
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e(request()->routeIs('my-performance') ? 'active bg-gradient-primary' : ''); ?>"
                    href="<?php echo e(route('my-performance')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">trending_up</i>
                    </div>
                    <span class="nav-link-text ms-1">My Performance</span>
                </a>
            </li>
            <li class="nav-item mt-3">
                <h6 class="ps-4 ms-2 text-uppercase text-xs text-white font-weight-bolder opacity-8">Account pages</h6>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white <?php echo e($activePage == 'profile' ? ' active bg-gradient-primary' : ''); ?>  "
                    href="<?php echo e(route('profile')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">person</i>
                    </div>
                    <span class="nav-link-text ms-1">Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white " href="<?php echo e(route('static-sign-in')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">login</i>
                    </div>
                    <span class="nav-link-text ms-1">Sign In</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white " href="<?php echo e(route('static-sign-up')); ?>">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">assignment</i>
                    </div>
                    <span class="nav-link-text ms-1">Sign Up</span>
                </a>
            </li>
            <?php endif; ?>
        </ul>
    </div>
    <div class="sidenav-footer position-absolute w-0 bottom-0 ">
        
    </div>
</aside>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/components/navbars/sidebar.blade.php ENDPATH**/ ?>