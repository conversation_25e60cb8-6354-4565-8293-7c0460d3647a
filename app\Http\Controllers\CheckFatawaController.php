<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CheckFatawaController extends Controller
{
    public function create()
    {
        return view('checkf.create');
    }

    public function store(Request $request)
    {
        /*bilal commit*/
        $data = $request->validate([
            'enter_date' => 'required|date',
            'ifta_name' => 'required',
            'ifta_number' => 'required|string|max:10',
            'details' => 'nullable|string',
        ]);

        $data['ifta_code'] = $this->generateIftaCode($data['ifta_name']);

        CheckFatawa::create($data);

        return redirect()->route('checkf.create')->with('success', 'Fatawa entry created successfully.');
    }

    public function index()
    {
        $data = CheckFatawa::orderBy('enter_date', 'desc')->get();

        return view('checkf.inde', compact('data'));
    }

    private function generateIftaCode($iftaName)
    {
        $code = '';

        // Implement your logic to generate the ifta_code based on the ifta_name
        if ($iftaName === 'Norulirfan') {
            $code = 'Nor';
        } elseif ($iftaName === 'Faizan e Ajmair') {
            $code = 'FAj';
        } elseif ($iftaName === 'Gulzare Taiba') {
            $code = 'Gul';
        } elseif ($iftaName === 'Iqtisaad') {
            $code = 'Iec';
        }

        return $code;
    }
}
