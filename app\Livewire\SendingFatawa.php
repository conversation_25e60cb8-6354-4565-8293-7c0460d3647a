<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Livewire\WithPagination; // Correct import for WithPagination trait
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;
use Illuminate\Http\Request; // Add this line



// #[Layout('layouts.app')]
class SendingFatawa extends Component
{
    use WithPagination;

    // Performance optimization properties
    public $isDataLoaded = false;
    public $loadingProgress = 0;
    public $currentLoadingStep = '';

    // Original properties
    public $selectedMonths = [];
    public $checkerlist;
    public $munsab;
    public $datefilter;
    public $showTrail = false;
    public $showQue = false;
    public $showChat = false;
    public $codebylower;
    public $message;
    public $allfatawa;
    Public $obtain;
    public $userName;
    public $checker;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $remainingFatawa = [];
    Public $sendingFatawa = [];
    public $daruliftaNames;
    public $checkers;
    public $mailfolderDate;
    public $darulifta;
    public $mailfolder;
    public $mujeebs;
    public $selectedmujeeb = 'all';
    public $selectedmufti = 'mufti_ali_asghar';
    public $selectedTimeFrame;
    protected $listeners = ['selectedmuftiUpdated', 'selectedTimeFrameUpdated','fetchFatawaData', 'loadData'];
    public $tempSelectedMonths = [];
    public $tempSelectedTimeFrame;
    public $daruliftalist;
    public $startDate;
    public $endDate;
    public $tempStartDate;
    public $tempEndDate;
    public $transferBy;
    public $transferFatawa = [];
    // protected $listeners = ['updateSelectedTimeFrame'];
    
    // protected $listeners = ['fetchFatawaData'];
    
    // public function loadMore()
    // {
    //     $this->perPage += $this->loadMoreCount;
    // }
    public function mount(Request $request, $darulifta = null, $mailfolder = null)
        {
            $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);
            $this->selectedmufti = $request->query('selectedmufti', $this->selectedmufti);
            $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);
            $this->selectedMonths = $request->query('selectedMonths', $this->selectedMonths);
            $this->tempSelectedTimeFrame = $this->selectedTimeFrame;
            $this->tempSelectedMonths = $this->selectedMonths;
            $this->startDate = $request->query('startDate');
            $this->endDate = $request->query('endDate');
            $this->tempStartDate = $this->startDate;
            $this->tempEndDate = $this->endDate;
            $this->showTrail = $request->query('showTrail', false) == '1';
            $this->showQue = $request->query('showQue', false) == '1';
            $this->showChat = $request->query('showChat', false) == '1';

        
        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;
        
        $this->userName = Auth::user()->name; // Assuming you have the user's name here
        if ($this->userName == 'Sayed Masood Sahib') {
            $this->selectedmufti = 'sayed_masood';
        }
        $this->checker = DB::table('checker')
            ->where('checker_name', $this->userName)
            ->first()
            ->folder_id ?? null;
            $this->munsab = DB::table('checker')
            ->where('checker_name', $this->userName)
            ->first()
            ->munsab ?? null;
            
        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();

        // Automatically load data on mount
        $this->loadData();
    }

    private function loadBasicData()
    {
        // Load only essential data for initial page load
        $this->transferBy = DB::table('uploaded_files')
            ->select('transfer_by')
            ->whereNotNull('transfer_by')
            ->where('transfer_by', '!=', '')
            ->distinct()
            ->limit(20) // Limit for performance
            ->pluck('transfer_by');

        $this->daruliftalist = DB::table('uploaded_files')
            ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
            ->select('uploaded_files.darulifta_name')
            ->distinct()
            ->orderBy('daruliftas.id')
            ->limit(10) // Limit for performance
            ->pluck('uploaded_files.darulifta_name');

        $this->checkerlist = DB::table('checker')
            ->select('id', 'checker_name', 'folder_id', 'munsab')
            ->get();

        $this->datefilter = DB::table('uploaded_files')
            ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
            ->whereNotNull('mail_folder_date')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12) // Only last year
            ->get();
    }
    public function loadData()
    {
        $this->currentLoadingStep = 'Loading basic data...';
        $this->loadingProgress = 20;
        $this->dispatch('updateProgress', $this->loadingProgress);

        // Load essential data first
        $this->loadBasicData();

        $this->currentLoadingStep = 'Loading fatawa data...';
        $this->loadingProgress = 60;
        $this->dispatch('updateProgress', $this->loadingProgress);

        // Load main data
        $this->remainingFatawa = $this->remainingFatawadata();
        $this->sendingFatawa = $this->sendingFatawadata();
        $this->transferFatawa = $this->transferFatawadata();

        $this->currentLoadingStep = 'Finalizing...';
        $this->loadingProgress = 100;
        $this->dispatch('updateProgress', $this->loadingProgress);

        $this->isDataLoaded = true;
    }

    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->startDate && $this->endDate) {
            $this->selectedTimeFrame = 'custom';
        }

        // Reload data with new filters
        $this->remainingFatawa = $this->remainingFatawadata();
        $this->sendingFatawa = $this->sendingFatawadata();
        $this->transferFatawa = $this->transferFatawadata();
    }
//     public function fetchFatawaData($daruliftaName)
// {
//     $fatawa = DB::table('uploaded_files')
//     ->where('darulifta_name', $daruliftaName)->get();
//     $this->dispatch('fatawaFetched', $fatawa);
// }

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->selectedTimeFrame = $timeFrame;
    }
    public function updateSelectedMujeeb($mujeebframe)
    {
        $this->selectedmujeeb = $mujeebframe;
    }
    public function updateSelectedMufti($muftiFrame)
    {
        $this->selectedmufti = $muftiFrame;
    }

    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);
        
        if ($this->darulifta === null) {
            if(count(Auth::user()->roles) > 1){
                $this->daruliftaNames = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                ->select('uploaded_files.darulifta_name')
                ->distinct()
                ->orderBy('daruliftas.id')
                ->pluck('uploaded_files.darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 0)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                    $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->orderByRaw("CASE WHEN folder_id = 'mufti_ali_asghar' THEN 0 ELSE 1 END")
                    ->pluck('folder_id');
            } 
            else {
                $this->daruliftaNames = DB::table('uploaded_files')
                
                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name',$firstRoleName)
                ->pluck('darulifta_name');

                $this->mujeebs = DB::table('uploaded_files')
                    ->select('sender')
                    ->where('selected', 0)
                    ->whereIn('darulifta_name', $this->daruliftaNames)
                    ->distinct()
                    ->pluck('sender');
                    $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
                
            }

        } else {
            // $this->daruliftaNames = $this->darulifta;
            $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name',$this->darulifta)
                
                ->distinct()
                ->pluck('darulifta_name');
                $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('selected', 1)
                ->where('darulifta_name', $this->darulifta)
                ->distinct()
                ->pluck('sender');
                $this->checkers = DB::table('checker')
                    ->select('folder_id')
                    ->distinct()
                    ->pluck('folder_id');
        }
    }
    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')
        
        ->distinct()
        
        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()
    
    ->pluck('mail_folder_date');
    }
}
public function sendMessage($iftaCode)
{
    
    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.
    

    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}    
public function render()
    {
        try {
            // Load basic data and messages
            $this->loadBasicData();
            $messages = Chat::latest()->limit(50)->get();

            return view('livewire.sending-fatawa', [
                'mailfolderDate' => $this->mailfolderDate,
                'daruliftaNames' => $this->daruliftaNames,
                'checkers' => $this->checkers,
                'remainingFatawa' => $this->remainingFatawa,
                'sendingFatawa' => $this->sendingFatawa,
                'transferFatawa' => $this->transferFatawa,
                'obtain' => $this->obtain ?? [],
                'mahlenazar_null' => $this->mahlenazar_null ?? [],
                'que_day_r' => $this->que_day_r ?? [],
                'messages' => $messages,
                'selectedmufti' => $this->selectedmufti,
                'selectedTimeFrame' => $this->selectedTimeFrame,
                'allfatawa' => $this->allfatawa ?? [],
                'codebylower' => $this->codebylower ?? [],
                'datefilter' => $this->datefilter,
                'showTrail' => $this->showTrail,
                'showChat' => $this->showChat,
                'showQue' => $this->showQue,
                'userName' => $this->userName,
                'checkerlist' => $this->checkerlist,
                'munsab' => $this->munsab,
                'daruliftalist' => $this->daruliftalist,
                'mujeebs' => $this->mujeebs,
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ])
            ->layout('layouts.app');

        } catch (\Exception $e) {
            // Handle any database errors gracefully
            \Log::error('SendingFatawa render error: ' . $e->getMessage());

            return view('livewire.sending-fatawa', [
                'mailfolderDate' => collect([]),
                'daruliftaNames' => collect([]),
                'checkers' => collect([]),
                'remainingFatawa' => [],
                'sendingFatawa' => [],
                'transferFatawa' => [],
                'obtain' => [],
                'mahlenazar_null' => collect([]),
                'que_day_r' => collect([]),
                'messages' => collect([]),
                'selectedmufti' => $this->selectedmufti,
                'selectedTimeFrame' => $this->selectedTimeFrame,
                'allfatawa' => collect([]),
                'codebylower' => collect([]),
                'datefilter' => collect([]),
                'showTrail' => $this->showTrail,
                'showChat' => $this->showChat,
                'showQue' => $this->showQue,
                'userName' => $this->userName,
                'checkerlist' => collect([]),
                'munsab' => $this->munsab,
                'daruliftalist' => collect([]),
                'mujeebs' => collect([]),
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ])
            ->layout('layouts.app');
        }
    }
    public function remainingFatawadata()
{
        // Create cache key based on filters
        $cacheKey = 'remaining_fatawa_' . md5(serialize([
            $this->selectedmujeeb,
            $this->selectedmufti,
            $this->selectedTimeFrame,
            $this->selectedMonths,
            $this->startDate,
            $this->endDate
        ]));

        // Try to get from cache first (5 minutes cache)
        $cached = cache()->remember($cacheKey, 300, function() {
            // Use aggregated query instead of nested loops for better performance
            $query = DB::table('uploaded_files')
                ->select([
                    'darulifta_name',
                    'mail_folder_date',
                    'sender',
                    'checker',
                    'transfer_by',
                    DB::raw('COUNT(*) as count')
                ])
                ->where('selected', 0)
                ->whereIn('darulifta_name', $this->daruliftaNames)
                ->whereIn('mail_folder_date', $this->mailfolderDate)
                ->groupBy(['darulifta_name', 'mail_folder_date', 'sender', 'checker', 'transfer_by']);

        // Apply filters
        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }

        if ($this->selectedmufti && $this->selectedmufti != 'all') {
            if ($this->selectedmufti == 'mufti_ali_asghar') {
                $query->where(function($query) {
                    $query->where('checker', $this->selectedmufti)
                          ->orWhereNull('checker');
                });
            } else {
                $query->where('checker', $this->selectedmufti);
            }
        }

        // Apply time filters
        $formattedSelectedMonths = array_map(function ($date) {
            return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
        }, $this->selectedMonths);

        if ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
            $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }

            // Get aggregated results and transform to expected format
            $results = $query->limit(500)->get(); // Limit results for performance

            $remainingFatawas = [];
            foreach ($results as $result) {
                $remainingFatawas[$result->darulifta_name][$result->mail_folder_date][] = $result;
            }

            return $remainingFatawas;
        });

        return $cached;
}

public function sendingFatawadata()
{
        // Use single optimized query instead of nested loops
        $query = DB::table('uploaded_files')
            ->select([
                'darulifta_name',
                'checker',
                'mail_folder_date',
                'sender',
                'transfer_by',
                DB::raw('COUNT(*) as count')
            ])
            ->where('selected', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->whereIn('mail_folder_date', $this->mailfolderDate)
            ->groupBy(['darulifta_name', 'checker', 'mail_folder_date', 'sender', 'transfer_by']);

        // Apply filters
        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }

        // Apply time filters
        $formattedSelectedMonths = array_map(function ($date) {
            return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
        }, $this->selectedMonths);

        if ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
            $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }

        // Get results with limit for performance
        $results = $query->limit(1000)->get();

        $sendingFatawas = [];
        foreach ($results as $result) {
            $checker = $result->checker ?: 'mufti_ali_asghar';
            $sendingFatawas[$result->darulifta_name][$checker][$result->mail_folder_date][] = $result;
        }

        return $sendingFatawas;
}
public function transferFatawadata()
{
        // Use single optimized query
        $query = DB::table('uploaded_files')
            ->select([
                'transfer_by',
                'mail_folder_date',
                'darulifta_name',
                'sender',
                'checker',
                DB::raw('COUNT(*) as count')
            ])
            ->where('selected', 0)
            ->whereNotNull('transfer_by')
            ->where('transfer_by', '!=', '')
            ->whereIn('transfer_by', $this->transferBy)
            ->whereIn('mail_folder_date', $this->mailfolderDate)
            ->groupBy(['transfer_by', 'mail_folder_date', 'darulifta_name', 'sender', 'checker']);

        // Apply filters
        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $query->where('sender', $this->selectedmujeeb);
        }

        if ($this->selectedmufti && $this->selectedmufti != 'all') {
            if ($this->selectedmufti == 'mufti_ali_asghar') {
                $query->where(function($query) {
                    $query->where('checker', $this->selectedmufti)
                          ->orWhereNull('checker');
                });
            } else {
                $query->where('checker', $this->selectedmufti);
            }
        }

        // Apply time filters
        $formattedSelectedMonths = array_map(function ($date) {
            return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
        }, $this->selectedMonths);

        if ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
            $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
        } elseif ($this->selectedTimeFrame == 'custom' && $this->startDate && $this->endDate) {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                Carbon::parse($this->startDate)->format('Y-m-d'),
                Carbon::parse($this->endDate)->format('Y-m-d')
            ]);
        }

        // Get results with limit
        $results = $query->limit(500)->get();

        $transferFatawas = [];
        foreach ($results as $result) {
            $transferFatawas[$result->transfer_by][$result->mail_folder_date][] = $result;
        }

        return $transferFatawas;
}


}
