<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Tasurat;

class TasuratEditor extends Component
{
    public $tasurats;
    public $option, $tasurat_no, $editId = null;
    public $newOption = '', $newTasuratNo = null;

    public function mount()
    {
        $this->loadTasurats();
    }

    public function loadTasurats()
    {
        $this->tasurats = Tasurat::all();
    }

    public function edit($id)
    {
        $tasurat = Tasurat::find($id);
        $this->editId = $id;
        $this->option = $tasurat->option;
        $this->tasurat_no = $tasurat->tasurat_no;
    }

    public function save()
    {
        if ($this->editId) {
            // Update existing tasurat
            $tasurat = Tasurat::find($this->editId);
            $tasurat->option = $this->option;
            $tasurat->tasurat_no = $this->tasurat_no;
            $tasurat->save();
        } else {
            // Create new tasurat
            Tasurat::create([
                'option' => $this->newOption,
                'tasurat_no' => $this->newTasuratNo,
            ]);
            $this->newOption = '';
            $this->newTasuratNo = null;
        }

        // Reload tasurats list
        $this->loadTasurats();
        $this->resetInput();

        // Dispatch the event to refresh the dropdown
        $this->dispatch('tasurat-updated');
    }

    public function delete($id)
    {
        Tasurat::find($id)->delete();
        $this->loadTasurats();
        $this->dispatch('tasurat-updated'); // Dispatch event to refresh the dropdown
    }

    public function resetInput()
    {
        $this->option = '';
        $this->tasurat_no = null;
        $this->editId = null;
    }

    public function render()
    {
        return view('livewire.tasurat-editor');
    }
}
