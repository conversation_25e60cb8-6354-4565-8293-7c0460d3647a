<x-layout bodyClass="g-sidenav-show bg-gray-200">

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <div class="container-fluid py-4">

            <!-- Loader (No changes, logic is preserved) -->
            <div id="loader-overlay" class="loader-overlay" style="display: none;">
                <div id="loader" class="loader"></div>
            </div>

            <!-- Modern UI Styles -->
            <style>
                :root {
                    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
                    --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
                    --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
                }

                .modern-card {
                    background: white;
                    border-radius: 16px;
                    box-shadow: var(--card-shadow);
                    border: none;
                    transition: all 0.3s ease;
                    overflow: hidden;
                }

                .modern-card:hover {
                    transform: translateY(-5px);
                    box-shadow: var(--card-shadow-hover);
                }

                .modern-card-header {
                    background: var(--primary-gradient);
                    color: white;
                    border: none;
                    padding: 1.5rem;
                    border-radius: 16px 16px 0 0;
                }

                .folder-entry {
                    background: white;
                    border: 2px solid #e9ecef;
                    border-radius: 12px;
                    padding: 1rem;
                    transition: all 0.3s ease;
                    position: relative;
                    overflow: hidden;
                }

                .folder-entry::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: var(--primary-gradient);
                    transform: scaleX(0);
                    transition: transform 0.3s ease;
                }

                .folder-entry:hover::before,
                .folder-entry.highlight::before {
                    transform: scaleX(1);
                }

                .folder-entry.highlight {
                    background: linear-gradient(135deg, #e7f3ff 0%, #ffffff 100%);
                    border-color: #0d6efd;
                    transform: translateY(-2px);
                    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
                }

                .folder-transfer-by {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 0.5rem 0.75rem;
                    transition: all 0.3s ease;
                    font-size: 0.875rem;
                }

                .folder-transfer-by:hover,
                .folder-transfer-by.highlight {
                    background: linear-gradient(135deg, #f0f0f0 0%, #ffffff 100%);
                    border-color: #6c757d;
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.15);
                }

                .question-cell {
                    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                    direction: rtl;
                    border-radius: 8px;
                    padding: 1rem;
                    margin: 0.25rem 0;
                }

                .question-text {
                    white-space: normal;
                    word-wrap: break-word;
                    text-align: right;
                    color: #2c3e50;
                    line-height: 1.6;
                    font-weight: 500;
                }

                .fatwa-group-start > td {
                    border-top: 3px solid #0d6efd !important;
                    position: relative;
                }

                .fatwa-group-start > td::before {
                    content: '';
                    position: absolute;
                    top: -3px;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: var(--primary-gradient);
                }

                .modern-table {
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: var(--card-shadow);
                }

                .modern-table thead th {
                    background: var(--primary-gradient);
                    color: white;
                    border: none;
                    padding: 1rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    font-size: 0.875rem;
                }

                .modern-table tbody tr {
                    transition: all 0.3s ease;
                }

                .modern-table tbody tr:hover {
                    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
                    transform: scale(1.01);
                }

                .modern-btn {
                    border-radius: 10px;
                    padding: 0.75rem 1.5rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    transition: all 0.3s ease;
                    border: none;
                    position: relative;
                    overflow: hidden;
                }

                .modern-btn::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.5s;
                }

                .modern-btn:hover::before {
                    left: 100%;
                }

                .modern-btn-primary {
                    background: var(--primary-gradient);
                    color: white;
                }

                .modern-btn-success {
                    background: var(--success-gradient);
                    color: white;
                }

                .modern-btn-warning {
                    background: var(--warning-gradient);
                    color: white;
                }

                .modern-btn-info {
                    background: var(--info-gradient);
                    color: #2c3e50;
                }

                .hidden {
                    display: none;
                }

                .loader-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(255, 255, 255, 0.9);
                    backdrop-filter: blur(5px);
                    z-index: 9999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .loader {
                    width: 50px;
                    height: 50px;
                    border: 5px solid #f3f3f3;
                    border-top: 5px solid #0d6efd;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                .navigation-pills {
                    background: white;
                    border-radius: 50px;
                    padding: 0.5rem;
                    box-shadow: var(--card-shadow);
                }

                .navigation-pills .btn {
                    border-radius: 25px;
                    margin: 0 0.25rem;
                    transition: all 0.3s ease;
                }

                .status-badge {
                    border-radius: 20px;
                    padding: 0.5rem 1rem;
                    font-weight: 600;
                    font-size: 0.875rem;
                }

                .animate-fade-in {
                    animation: fadeIn 0.5s ease-in;
                }

                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(20px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                /* Modern Input Styling */
                .modern-input {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 0.5rem 0.75rem;
                    transition: all 0.3s ease;
                    background: white;
                    font-size: 0.875rem;
                }

                .modern-input:focus {
                    border-color: #0d6efd;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
                    background: #f8f9ff;
                    outline: none;
                }

                .modern-select {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 0.5rem 0.75rem;
                    transition: all 0.3s ease;
                    background: white;
                    font-size: 0.875rem;
                    cursor: pointer;
                }

                .modern-select:focus {
                    border-color: #0d6efd;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
                    background: #f8f9ff;
                    outline: none;
                }

                .modern-textarea {
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 0.5rem 0.75rem;
                    transition: all 0.3s ease;
                    background: white;
                    font-size: 0.875rem;
                    resize: vertical;
                    min-height: 38px;
                }

                .modern-textarea:focus {
                    border-color: #0d6efd;
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
                    background: #f8f9ff;
                    outline: none;
                }

                .modern-checkbox {
                    width: 1.25rem;
                    height: 1.25rem;
                    border: 2px solid #e9ecef;
                    border-radius: 4px;
                    transition: all 0.3s ease;
                    cursor: pointer;
                }

                .modern-checkbox:checked {
                    background-color: #0d6efd;
                    border-color: #0d6efd;
                }

                .modern-checkbox:focus {
                    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.15);
                    outline: none;
                }

                /* Modern Action Buttons */
                .action-btn {
                    border-radius: 6px;
                    padding: 0.375rem 0.75rem;
                    font-weight: 500;
                    font-size: 0.8rem;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    transition: all 0.3s ease;
                    border: none;
                    position: relative;
                    overflow: hidden;
                    min-width: 80px;
                }

                .action-btn::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.5s;
                }

                .action-btn:hover::before {
                    left: 100%;
                }

                .action-btn:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                .btn-details {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }

                .btn-view {
                    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                    color: white;
                }

                .btn-download {
                    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
                    color: white;
                }

                .btn-delete {
                    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
                    color: white;
                }

                .btn-clear {
                    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
                    color: #8b4513;
                }

                .btn-edit {
                    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
                    color: #2c3e50;
                }

                /* Input Group Styling */
                .modern-input-group {
                    display: flex;
                    align-items: stretch;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                .modern-input-group .modern-input,
                .modern-input-group .modern-select {
                    border-radius: 0;
                    border-right: none;
                    margin-bottom: 0;
                }

                .modern-input-group .action-btn {
                    border-radius: 0;
                    border-left: 2px solid #e9ecef;
                }
            </style>

            <!-- Modern Page Header Navigation -->
            <div class="modern-card mb-4 animate-fade-in">
                <div class="card-body p-4">
                    <div class="navigation-pills d-flex flex-wrap justify-content-center gap-2">
                        <a href="{{ route('dashboard') }}" class="btn btn-outline-primary modern-btn">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a href="{{ route('recived-fatawa') }}" class="btn btn-outline-info modern-btn">
                            <i class="fas fa-inbox me-2"></i>Received Fatawa
                        </a>
                        <a href="{{ route('store.selected.values') }}" class="btn modern-btn modern-btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Sending Fatawa
                        </a>
                        <a href="{{ route('sent-fatawa') }}" class="btn btn-outline-success modern-btn">
                            <i class="fas fa-check-double me-2"></i>Checked Fatawa
                        </a>
                    </div>
                </div>
            </div>

            <!-- FIX: Pre-calculate all folder and count data before displaying -->
            @php
                $totalCounts = 0;
                $overallFolderCount = 0;
                $folderCounts = [];
                $transferByCounts = [];
                $daruliftaNameForNav = null; // To store the relevant darulifta name for nav links
                $closestBackFolder = null;
                $closestNextFolder = null;

                foreach($daruliftaData as $daruliftaName) {
                    if(isset($remainingFatawas[$daruliftaName])) {
                        $daruliftaNameForNav = $daruliftaName; // Set the name for later use
                        $currentDate = \Carbon\Carbon::parse($selectedFolder);
                        
                        // Calculate next/back folders (original logic)
                        $closestBackFolder = $backFolderData->sortBy(fn($folder) => $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date)))->first();
                        $closestNextFolder = $nextFolderData->sortBy(fn($folder) => $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date)))->first();

                        // Calculate counts (original logic)
                        foreach($folderData as $mailfolderDates) {
                            if(isset($remainingFatawas[$daruliftaName][$mailfolderDates])) {
                                foreach($remainingFatawas[$daruliftaName][$mailfolderDates] as $file) {
                                    $folder = $file->mail_folder_date;
                                    $folderCounts[$folder] = ($folderCounts[$folder] ?? 0) + 1;
                                    $transferBy = $file->transfer_by ?: 'Mujeeb';
                                    $transferByCounts[$folder][$transferBy] = ($transferByCounts[$folder][$transferBy] ?? 0) + 1;
                                    $totalCounts++;
                                }
                            }
                        }
                    }
                }
                $overallFolderCount = count($folderCounts);
            @endphp


            <!-- Modern Folder Navigation -->
            <div class="modern-card mb-4 animate-fade-in">
                <div class="modern-card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0">
                            <i class="fas fa-folder-open me-2"></i>Folder Navigation
                        </h5>
                        <div class="d-flex gap-3">
                            <div class="status-badge bg-light text-dark">
                                <i class="fas fa-file-alt me-1"></i>{{ $totalCounts }} Fatawa
                            </div>
                            <div class="status-badge bg-light text-dark">
                                <i class="fas fa-folder me-1"></i>{{ $overallFolderCount }} Folders
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                     @if($daruliftaNameForNav)
                        <div class="d-flex align-items-center justify-content-between">
                            <!-- Back Button -->
                            <div>
                                @if ($closestBackFolder)
                                    @php $transferByName = empty($closestBackFolder->transfer_by) ? 'Mujeeb' : $closestBackFolder->transfer_by; @endphp
                                    <a href="{{ url("/sending1/{$selectedDarul}/{$closestBackFolder->mail_folder_date}/{$transferByName}") }}" class="btn btn-icon btn-outline-primary mb-0"><i class="fas fa-arrow-left"></i></a>
                                @endif
                            </div>

                            <!-- Folder Entries -->
                            <div class="d-flex flex-wrap justify-content-center gap-3">
                                @foreach ($folderCounts as $folder => $count)
                                    @php $isActiveFolder = ($selectedFolder === $folder); @endphp
                                    <div class="folder-entry border rounded p-2 text-center {{ $isActiveFolder ? 'highlight' : '' }}">
                                        <a href="{{ url('sending1/' . $daruliftaNameForNav . '/' . urlencode($folder)) }}" class="text-dark fw-bold text-decoration-none">
                                            {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }} <span class="badge bg-primary rounded-pill ms-1">{{ $count }}</span>
                                        </a>
                                        @if(isset($transferByCounts[$folder]))
                                            <div class="d-flex flex-wrap justify-content-center gap-1 mt-2">
                                                @foreach($transferByCounts[$folder] as $transferBy => $transferByCount)
                                                    @php $isActiveTransferBy = $isActiveFolder && ($transfer_by === $transferBy); @endphp
                                                    <div class="folder-transfer-by border rounded px-2 py-1 {{ $isActiveTransferBy ? 'highlight' : '' }}">
                                                        <a href="{{ url('sending1/' . $daruliftaNameForNav . '/' . urlencode($folder) . '/' . urlencode($transferBy)) }}" class="text-secondary text-decoration-none small">
                                                            {{ $transferBy }} <span class="badge bg-secondary rounded-pill ms-1">{{ $transferByCount }}</span>
                                                        </a>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>

                            <!-- Next Button -->
                            <div>
                                @if ($closestNextFolder)
                                    @php $transferByName = empty($closestNextFolder->transfer_by) ? 'Mujeeb' : $closestNextFolder->transfer_by; @endphp
                                    <a href="{{ url("/sending1/{$selectedDarul}/{$closestNextFolder->mail_folder_date}/{$transferByName}") }}" class="btn btn-icon btn-outline-primary mb-0"><i class="fas fa-arrow-right"></i></a>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Modern Darulifta Filters -->
            <div class="modern-card mb-4 animate-fade-in">
                <div class="card-body p-4">
                    <h6 class="mb-3">
                        <i class="fas fa-filter me-2 text-primary"></i>Filter by Darulifta
                    </h6>
                    <div class="navigation-pills d-flex flex-wrap gap-2">
                        <a href="{{ route('sending-fatawa')}}"
                           class="btn modern-btn {{ !$selectedDarul ? 'modern-btn-primary' : 'btn-outline-primary' }}">
                            <i class="fas fa-globe me-2"></i>All Ifta
                        </a>
                        @foreach($daruliftalist as $daruliftalistn)
                            <a href="{{ route('sending-fatawa', ['darulifta' => $daruliftalistn])}}"
                               class="btn modern-btn {{ request()->route('selectedDarul') == $daruliftalistn ? 'modern-btn-primary' : 'btn-outline-primary' }}">
                                <i class="fas fa-university me-2"></i>{{ $daruliftalistn }}
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Main Content Form -->
            <form action="{{ route('upload') }}" method="POST" enctype="multipart/form-data" id="submission-form">
                @csrf
                @if (!empty($fatwaData) && !empty($selectedFolder) && !empty($selectedDarul))
                
                <!-- Modern File Upload Component -->
                <div class="modern-card mb-4 animate-fade-in">
                    <div class="modern-card-header text-center">
                        <h4 class="mb-0">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            {{$selectedDarul}} - {{ \Carbon\Carbon::parse($selectedFolder)->format('d/m/Y') }}
                            @if($transfer_by)
                                <div class="mt-2">
                                    <span class="status-badge bg-light text-dark">
                                        <i class="fas fa-user me-1"></i>by {{ ucwords(str_replace('_', ' ', $transfer_by)) }}
                                    </span>
                                </div>
                            @endif
                        </h4>
                    </div>
                    <div class="card-body p-4">
                         @livewire('file-upload', ['selectedDarul' => $selectedDarul, 'selectedFolder' => $selectedFolder, 'checker' => $checker])
                    </div>
                </div>

                <!-- Hidden inputs (Logic preserved) -->
                <input type="hidden" name="folder_select" id="folder_select" value="{{ $selectedFolder }}">
                <input type="hidden" name="darulifta_select" id="darulifta_select" value="{{ $selectedDarul }}">
                <input type="hidden" name="checker_id" id="checker_id" value="{{ $checker }}">

                <!-- Modern Fatawa Table -->
                <div class="modern-card animate-fade-in">
                    <div class="modern-card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>Fatawa for Sending
                        </h5>
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show mb-0" role="alert">
                                <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @elseif(session('fileErrors'))
                            <div class="alert alert-danger alert-dismissible fade show mb-0" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @foreach(session('fileErrors') as $error)<div>{{ $error }}</div>@endforeach
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @elseif(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show mb-0" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>{{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif
                    </div>
                    <div class="table-responsive">
                        <table id="table1" class="table modern-table align-items-center mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Fatwa No</th><th>Sender</th><th>Checked File Name</th><th>Checked Folder Name</th><th>Grade</th><th>Tasurat</th><th>Send To</th><th>Viral</th><th>Instructions</th><th>Select</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse ($fatwaData as $row)
                                    <tr class="fatwa-group-start" data-id="{{ $row->id }}">
                                        <td class="hidden">{{ $row->id }}</td>
                                        <td class="align-middle text-center text-sm">{{ $row->file_code }}</td>
                                        <td class="hidden">{{ $row->file_name }}</td>
                                        <td class="align-middle text-center text-sm">{{ $row->sender }}</td>
                                        <td class="hidden">{{ $row->darulifta_name }}</td>
                                        <td class="hidden">{{ $row->mail_folder_date }}</td>
                                        <td class="align-middle">
                                            <input type="text" name="checked_file_name[]" data-id="{{ $row->id }}" class="modern-input" placeholder="Enter file name">
                                        </td>
                                        <td class="align-middle skip-clear">
                                            <input type="text" name="checked_folder[]" data-id="{{ $row->id }}" class="modern-input" placeholder="Enter folder name">
                                        </td>
                                        <td class="align-middle">
                                            <select name="checked_grade[]" data-id="{{ $row->id }}" class="modern-select">
                                                <option value="">Select Grade</option>
                                                <option value="Munasib">Munasib</option>
                                                <option value="Bhetar">Bhetar</option>
                                                <option value="Mumtaz">Mumtaz</option>
                                            </select>
                                        </td>
                                        <td class="align-middle">
                                            <div class="modern-input-group">
                                                <livewire:tasurat-dropdown />
                                                <button class="action-btn btn-edit" type="button" data-bs-toggle="modal" data-bs-target="#myModal">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td class="align-middle">
                                            @php $isMujeeb = request()->segment(4) === 'Mujeeb'; @endphp
                                            <select name="by_mufti[]" class="by_mufti modern-select" data-id="{{ $row->id }}">
                                                <option value="to_mujeeb">Mujeeb</option>
                                                @if(!$isMujeeb)<option value="to_checker">Checker</option>@endif
                                            </select>
                                        </td>
                                        <td class="align-middle text-center">
                                            <div class="form-check">
                                                <input type="hidden" name="viral[{{ $row->id }}]" value="0">
                                                <input class="modern-checkbox" type="checkbox" name="viral[{{ $row->id }}]" value="{{ Auth::id() }}" data-id="{{ $row->id }}" {{ $row->viral ? 'checked' : '' }}>
                                            </div>
                                        </td>
                                        <td class="align-middle">
                                            <textarea class="checked_instructions_textarea modern-textarea mb-2" name="checked_Instructions[]" data-id="{{ $row->id }}" rows="1" placeholder="Enter instructions"></textarea>
                                            <div class="modern-input-group">
                                                <livewire:instruction-dropdown />
                                                <button class="action-btn btn-edit" type="button" data-bs-toggle="modal" data-bs-target="#instructionModal">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <td class="align-middle text-center">
                                            <div class="form-check">
                                                <input type="checkbox" name="selected[]" data-id="{{ $row->id }}" onchange="updateCheckboxValue(this)" class="modern-checkbox">
                                                <input type="hidden" name="row_id[]" value="{{ $row->id }}">
                                            </div>
                                        </td>
                                    </tr>
                                    <tr><td colspan="10" class="question-cell p-2"><div class="custom-textarea" readonly>@foreach ($quest as $que)@if ($que->ifta_code == $row->file_code)<div class="question-text">سوال: {{ $que->question }}</div>@endif @endforeach</div></td></tr>
                                    <tr>
                                        <td colspan="10" class="p-2">
                                            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                                                <span class="badge bg-info">موضوع: {{ $row->category }}</span>
                                                <span class="badge bg-success">Received: {{ \Carbon\Carbon::parse($row->mail_recived_date)->format('d-M-y h:i A') }}</span>
                                                <span>@foreach ($que_day_r as $day)@if (Str::lower($row->file_code) == Str::lower($day->ifta_code))<span class="badge bg-warning text-dark">Total Days: {{ now()->diffInDays(new \DateTime($day->rec_date)) }}</span>@endif @endforeach</span>
                                                <div><strong class="text-xs text-secondary">Mahl-e-Nazar:</strong>@foreach ($mahlenazar_null as $mahle)@if ($row->file_code == $mahle->file_code && $mahle->mail_folder_date < $row->mail_folder_date)<span class="badge bg-light text-dark me-1">{{ $mahle->mail_folder_date }}</span>@endif @endforeach</div>
                                                <div class="d-flex gap-2 flex-wrap">
                                                    @php
                                                        $fullPath = ($row->mail_folder_date . $row->darulifta_name . $row->checker) . (empty($row->transfer_by) ? '' : '_by_' . $row->transfer_by);
                                                        $deleteFileUrl = route('deleteFile', ['mailfolderDates' => $row->mail_folder_date, 'daruliftaName' => $row->darulifta_name, 'checker' => $row->checker ?? '', 'transferby' => $row->transfer_by ?? '']);
                                                        $canDeletefile = is_null($row->downloaded_by_admin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                                                    @endphp
                                                    <a href="{{ route('fatwa-detail', ['fatwa' => $row->file_code]) }}" target="_blank" class="action-btn btn-details">
                                                        <i class="fas fa-eye me-1"></i>Details
                                                    </a>
                                                    <a href="{{ route('viewRemain', ['date' => $fullPath, 'filename' => $row->file_name]) }}" target="_blank" class="action-btn btn-view">
                                                        <i class="fas fa-file-alt me-1"></i>View
                                                    </a>
                                                    <a href="{{ route('downloadFile', ['date' => $fullPath, 'filename' => $row->file_name, 'id' => $row->id]) }}" class="action-btn btn-download">
                                                        <i class="fas fa-download me-1"></i>Download
                                                    </a>
                                                    @if ($canDeletefile)
                                                        <a href="#" onclick="deleteFile('{{ $row->id }}', '{{ $row->file_name }}', '{{ $row->file_code }}', '{{ $deleteFileUrl }}')" class="action-btn btn-delete">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </a>
                                                    @else
                                                        <button class="action-btn btn-delete" disabled data-bs-toggle="tooltip" title="Downloaded by admin on {{ $row->downloaded_by_admin }}" style="opacity: 0.6; cursor: not-allowed;">
                                                            <i class="fas fa-trash me-1"></i>Delete
                                                        </button>
                                                    @endif
                                                    <button type="button" onclick="clearRow({{ $row->id }})" class="action-btn btn-clear">
                                                        <i class="fas fa-undo me-1"></i>Clear
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    @if(is_null($row->checker) || $row->checker === '')<input type="hidden" name="checker[]" value="mufti_ali_asghar" data-id="{{ $row->id }}">@else<input type="hidden" name="checker[]" value="{{ $row->checker }}" data-id="{{ $row->id }}">@endif
                                @empty
                                    <tr><td colspan="10" class="text-center py-5"><p class="text-muted">{{ $message ?? 'No Fatawa found for the selected criteria.' }}</p></td></tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer text-center p-4" style="background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); border-radius: 0 0 16px 16px;">
                        <button type="submit" class="btn btn-lg modern-btn modern-btn-success px-5 py-3">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Upload Selected Fatawa
                            <i class="fas fa-arrow-right ms-2"></i>
                        </button>
                    </div>
                </div>
                @endif
            </form>

            @if(empty($fatwaData) || empty($selectedFolder) || empty($selectedDarul))
                <div class="modern-card animate-fade-in">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-folder-open text-muted" style="font-size: 4rem;"></i>
                        </div>
                        <h5 class="text-muted mb-3">No Data Available</h5>
                        <p class="text-muted">Please select a Darulifta and folder to view fatawa.</p>
                        <div class="mt-4">
                            <a href="{{ route('sending-fatawa') }}" class="btn modern-btn modern-btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>Go Back to Selection
                            </a>
                        </div>
                    </div>
                </div>
            @endif

            <table id="table2" class="hidden"><thead><tr><th>Checked File Name</th><th>Checked Folder Name</th><th>Selected</th><th>ID</th></tr></thead><tbody id="file-details"></tbody></table>

            <!-- Modals (Updated for Bootstrap 5) -->
            <div class="modal fade" id="myModal" tabindex="-1" aria-labelledby="myModalLabel" aria-hidden="true"><div class="modal-dialog modal-dialog-centered modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="myModalLabel">Tasurat Editor</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><livewire:tasurat-editor/></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button></div></div></div></div>
            <div class="modal fade" id="instructionModal" tabindex="-1" aria-labelledby="instructionModalLabel" aria-hidden="true"><div class="modal-dialog modal-dialog-centered modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="instructionModalLabel">Instruction Editor</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><livewire:instruction-editor/></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button></div></div></div></div>
        </div>

        <!-- All JavaScript logic is preserved without any changes -->
        <script>
            // All original JS is preserved to maintain functionality
            document.addEventListener('DOMContentLoaded', function () {
                function addFileDetails(file, label) {
                    var tr = document.createElement('tr');
                    var tdCheckedFileName = document.createElement('td');
                    tdCheckedFileName.textContent = file.name;
                    tr.appendChild(tdCheckedFileName);
                    var tdCheckedFolderName = document.createElement('td');
                    tdCheckedFolderName.textContent = label;
                    tr.appendChild(tdCheckedFolderName);
                    var tdID = document.createElement('td');
                    var correspondingID = findCorrespondingID(file.name);
                    tdID.textContent = correspondingID;
                    tr.appendChild(tdID);
                    document.getElementById('file-details').appendChild(tr);
                    populateTable1FromTable2();
                }

                function findCorrespondingID(fileName) {
                    var firstTableRows = document.querySelectorAll('#table1 tbody tr[data-id]');
                    for (var i = 0; i < firstTableRows.length; i++) {
                        var firstTableFileName = firstTableRows[i].querySelector('td:nth-child(3)');
                        var firstTableID = firstTableRows[i].querySelector('td:nth-child(1)');
                        if (firstTableFileName && firstTableID && firstTableFileName.textContent.trim() === fileName.trim()) {
                            return firstTableID.textContent.trim();
                        }
                    }
                    return 'Not Found';
                }
                
                var fileInputs = document.querySelectorAll('input[type="file"]');
                fileInputs.forEach(function (input) {
                    input.addEventListener('change', function (e) {
                        var files = e.target.files;
                        var label = e.target.getAttribute('data-label');
                        for (var i = 0; i < files.length; i++) { addFileDetails(files[i], label); }
                    });
                });

                function populateTable1FromTable2() {
                    let table1Rows = document.querySelectorAll('#table1 tbody tr[data-id]');
                    let table2Rows = document.querySelectorAll('#table2 tbody tr');
                    table1Rows.forEach(function (table1Row) {
                        let table1FileNameElement = table1Row.querySelector('td:nth-child(3)');
                        if (!table1FileNameElement) return;
                        let table1FileName = table1FileNameElement.textContent.trim();
                        table2Rows.forEach(function (table2Row) {
                            let table2FileNameElement = table2Row.querySelector('td:nth-child(1)');
                            if (!table2FileNameElement) return;
                            let table2FileName = table2FileNameElement.textContent.trim();
                            if (table1FileName === table2FileName) {
                                let checkedFileNameInput = table1Row.querySelector('input[name="checked_file_name[]"]');
                                let checkedFolderInput = table1Row.querySelector('input[name="checked_folder[]"]');
                                let checkbox = table1Row.querySelector('input[name="selected[]"]');
                                if (checkedFileNameInput && checkedFolderInput && checkbox) {
                                    checkedFileNameInput.value = table2Row.querySelector('td:nth-child(1)').textContent.trim();
                                    checkedFolderInput.value = table2Row.querySelector('td:nth-child(2)').textContent.trim();
                                    checkbox.checked = true;
                                }
                            }
                        });
                    });
                }
            });

            function updateCheckboxValue(checkbox) { }

            function clearRow(rowId) {
                document.querySelectorAll(`tr[data-id="${rowId}"] select, tr[data-id="${rowId}"] textarea`).forEach(input => {
                    if (input.tagName === 'SELECT') input.selectedIndex = 0; else input.value = '';
                });
                document.querySelectorAll(`tr[data-id="${rowId}"] input[type="checkbox"]`).forEach(checkbox => { checkbox.checked = false; });
                Livewire.dispatch('resetTasuratDropdown');
                Livewire.dispatch('resetInstructionDropdown');
            }
        </script>
    </main>
    <x-plugins></x-plugins>
</x-layout>