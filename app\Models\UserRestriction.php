<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserRestriction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'restriction_type',
        'reason',
        'is_active',
        'restricted_by',
        'restricted_at',
        'lifted_by',
        'lifted_at',
        'lift_reason',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'restricted_at' => 'datetime',
        'lifted_at' => 'datetime',
    ];

    /**
     * Get the user this restriction applies to.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who applied this restriction.
     */
    public function restrictedBy()
    {
        return $this->belongsTo(User::class, 'restricted_by');
    }

    /**
     * Get the user who lifted this restriction.
     */
    public function liftedBy()
    {
        return $this->belongsTo(User::class, 'lifted_by');
    }

    /**
     * Scope to get active restrictions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get restrictions for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get restrictions by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('restriction_type', $type);
    }

    /**
     * Lift the restriction.
     */
    public function lift($liftedBy, $reason = null)
    {
        $this->update([
            'is_active' => false,
            'lifted_by' => $liftedBy,
            'lifted_at' => Carbon::now(),
            'lift_reason' => $reason,
        ]);
    }

    /**
     * Check if a user has any active restrictions.
     */
    public static function hasActiveRestrictions($userId)
    {
        return static::where('user_id', $userId)
                    ->where('is_active', true)
                    ->exists();
    }

    /**
     * Get active restrictions for a user.
     */
    public static function getActiveRestrictions($userId)
    {
        return static::where('user_id', $userId)
                    ->where('is_active', true)
                    ->get();
    }
}
