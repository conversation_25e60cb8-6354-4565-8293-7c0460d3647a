@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">My Tasks</h5>
                            <p class="text-sm mb-0">Tasks assigned to you</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                @if($tasks->where('status', '!=', 'completed')->count() > 0)
                                    <a href="{{ route('daily-performance.create') }}" class="btn bg-gradient-primary btn-sm mb-0">
                                        <i class="fas fa-clipboard-check"></i>&nbsp;&nbsp;Submit Performance
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="row mt-4">
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Tasks</p>
                                <h5 class="font-weight-bolder mb-0">
                                    {{ $tasks->total() }}
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="ni ni-collection text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Pending</p>
                                <h5 class="font-weight-bolder mb-0">
                                    {{ $tasks->where('status', 'pending')->count() }}
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="ni ni-time-alarm text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">In Progress</p>
                                <h5 class="font-weight-bolder mb-0">
                                    {{ $tasks->where('status', 'in_progress')->count() }}
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                <i class="ni ni-settings text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Completed</p>
                                <h5 class="font-weight-bolder mb-0">
                                    {{ $tasks->where('status', 'completed')->count() }}
                                </h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                <i class="ni ni-check-bold text-lg opacity-10" aria-hidden="true"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">My Tasks</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Department</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Type</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tasks as $task)
                                <tr class="{{ $task->isOverdue() ? 'table-warning' : '' }}">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                                @if($task->isOverdue())
                                                    <span class="badge badge-sm bg-gradient-danger">Overdue</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $task->department->name ?? 'No Department' }}</h6>
                                            @if($task->assignedBy)
                                                <p class="text-xs text-secondary mb-0">Assigned by: {{ $task->assignedBy->name }}</p>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary') }}">
                                            {{ ucfirst($task->type) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($task->due_date)
                                            <span class="text-secondary text-xs font-weight-bold">
                                                {{ $task->due_date->format('M d, Y') }}
                                            </span>
                                            @if($task->isOverdue())
                                                <br><small class="text-danger">{{ $task->due_date->diffForHumans() }}</small>
                                            @else
                                                <br><small class="text-muted">{{ $task->due_date->diffForHumans() }}</small>
                                            @endif
                                        @else
                                            <span class="text-secondary text-xs">No due date</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning')) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button class="btn btn-sm btn-outline-info mb-0" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#viewModal{{ $task->id }}" 
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if($task->status !== 'completed' && $task->status !== 'cancelled')
                                                <form action="{{ route('workflow-tasks.change-status', $task) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('PATCH')
                                                    <input type="hidden" name="status" value="{{ $task->status === 'pending' ? 'in_progress' : 'completed' }}">
                                                    <button type="submit" class="btn btn-sm btn-outline-{{ $task->status === 'pending' ? 'primary' : 'success' }} mb-0"
                                                            title="{{ $task->status === 'pending' ? 'Start Task' : 'Complete Task' }}">
                                                        <i class="fas fa-{{ $task->status === 'pending' ? 'play' : 'check' }}"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>

                                <!-- View Modal -->
                                <div class="modal fade" id="viewModal{{ $task->id }}" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-tasks me-2"></i>
                                                    Task Details
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Title:</strong> {{ $task->title }}</p>
                                                        <p class="mb-1"><strong>Department:</strong> {{ $task->department->name ?? 'No Department' }}</p>
                                                        <p class="mb-1"><strong>Assigned By:</strong> {{ $task->assignedBy->name ?? 'System' }}</p>
                                                        <p class="mb-1"><strong>Type:</strong> 
                                                            <span class="badge bg-{{ $task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary') }}">
                                                                {{ ucfirst($task->type) }}
                                                            </span>
                                                        </p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Priority:</strong> 
                                                            <span class="badge bg-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                                                {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                                            </span>
                                                        </p>
                                                        <p class="mb-1"><strong>Status:</strong> 
                                                            <span class="badge bg-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning')) }}">
                                                                {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                                            </span>
                                                        </p>
                                                        <p class="mb-1"><strong>Due Date:</strong> 
                                                            {{ $task->due_date ? $task->due_date->format('M d, Y') : 'No due date' }}
                                                        </p>
                                                        <p class="mb-1"><strong>Created:</strong> {{ $task->created_at->format('M d, Y') }}</p>
                                                    </div>
                                                </div>
                                                
                                                @if($task->description)
                                                    <div class="mt-3">
                                                        <strong>Description:</strong>
                                                        <p class="mb-0 mt-1">{{ $task->description }}</p>
                                                    </div>
                                                @endif
                                                
                                                @if($task->completion_notes && $task->status === 'completed')
                                                    <div class="mt-3">
                                                        <strong>Completion Notes:</strong>
                                                        <div class="alert alert-success mt-1">
                                                            {{ $task->completion_notes }}
                                                        </div>
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                @if($task->status !== 'completed' && $task->status !== 'cancelled')
                                                    <form action="{{ route('workflow-tasks.change-status', $task) }}" method="POST" class="d-inline">
                                                        @csrf
                                                        @method('PATCH')
                                                        <input type="hidden" name="status" value="{{ $task->status === 'pending' ? 'in_progress' : 'completed' }}">
                                                        <button type="submit" class="btn btn-{{ $task->status === 'pending' ? 'primary' : 'success' }}">
                                                            <i class="fas fa-{{ $task->status === 'pending' ? 'play' : 'check' }} me-2"></i>
                                                            {{ $task->status === 'pending' ? 'Start Task' : 'Complete Task' }}
                                                        </button>
                                                    </form>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-tasks text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted">No Tasks Assigned</h6>
                                            <p class="text-sm text-muted mb-0">You don't have any tasks assigned to you yet.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    @if($tasks->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $tasks->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
