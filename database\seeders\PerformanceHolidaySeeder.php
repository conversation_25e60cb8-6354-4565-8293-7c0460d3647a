<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PerformanceHoliday;
use Carbon\Carbon;

class PerformanceHolidaySeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $holidays = [
            [
                'holiday_date' => '2025-01-01',
                'name' => 'New Year\'s Day',
                'description' => 'New Year celebration',
                'type' => 'national',
            ],
            [
                'holiday_date' => '2025-03-23',
                'name' => 'Pakistan Day',
                'description' => 'National holiday of Pakistan',
                'type' => 'national',
            ],
            [
                'holiday_date' => '2025-08-14',
                'name' => 'Independence Day',
                'description' => 'Pakistan Independence Day',
                'type' => 'national',
            ],
            [
                'holiday_date' => '2025-11-09',
                'name' => 'Iqbal Day',
                'description' => 'Birthday of Allama Iqbal',
                'type' => 'national',
            ],
            [
                'holiday_date' => '2025-12-25',
                'name' => 'Quaid-e-Azam Day',
                'description' => 'Birthday of Muhammad Ali <PERSON>',
                'type' => 'national',
            ],
            // Add some religious holidays (approximate dates)
            [
                'holiday_date' => '2025-04-10',
                'name' => 'Eid ul-Fitr (Day 1)',
                'description' => 'End of Ramadan celebration',
                'type' => 'religious',
            ],
            [
                'holiday_date' => '2025-04-11',
                'name' => 'Eid ul-Fitr (Day 2)',
                'description' => 'End of Ramadan celebration',
                'type' => 'religious',
            ],
            [
                'holiday_date' => '2025-06-17',
                'name' => 'Eid ul-Adha (Day 1)',
                'description' => 'Festival of Sacrifice',
                'type' => 'religious',
            ],
            [
                'holiday_date' => '2025-06-18',
                'name' => 'Eid ul-Adha (Day 2)',
                'description' => 'Festival of Sacrifice',
                'type' => 'religious',
            ],
            [
                'holiday_date' => '2025-07-07',
                'name' => 'Muharram',
                'description' => 'Islamic New Year',
                'type' => 'religious',
            ],
        ];

        $firstUserId = \App\Models\User::first()->id ?? 13;

        foreach ($holidays as $holiday) {
            PerformanceHoliday::create([
                'holiday_date' => $holiday['holiday_date'],
                'name' => $holiday['name'],
                'description' => $holiday['description'],
                'type' => $holiday['type'],
                'is_active' => true,
                'created_by' => $firstUserId,
            ]);
        }
    }
}
