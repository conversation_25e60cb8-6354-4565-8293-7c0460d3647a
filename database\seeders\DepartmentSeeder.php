<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Department;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Research',
                'description' => 'Research and documentation of Islamic rulings and fatawa',
                'is_active' => true,
            ],
            [
                'name' => 'Documentation',
                'description' => 'Documentation and archival of fatawa and related materials',
                'is_active' => true,
            ],
            [
                'name' => 'Quality Control',
                'description' => 'Quality assurance and checking of fatawa before publication',
                'is_active' => true,
            ],
            [
                'name' => 'Translation',
                'description' => 'Translation of fatawa and related content',
                'is_active' => true,
            ],
            [
                'name' => 'Digital Media',
                'description' => 'Digital publishing and online content management',
                'is_active' => true,
            ],
            [
                'name' => 'Administration',
                'description' => 'Administrative tasks and system management',
                'is_active' => true,
            ],
        ];

        foreach ($departments as $department) {
            Department::firstOrCreate(
                ['name' => $department['name']],
                $department
            );
        }
    }
}
