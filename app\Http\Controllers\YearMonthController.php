<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Settings;
use PhpOffice\PhpWord\Writer\HTML;
use Barryvdh\DomPDF\Facade\Pdf;
// use PDF;
use Dompdf\Options;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;


class YearMonthController extends Controller
{
//     public function index()
// {
//     $data = $this->fetchDataFromDatabase();
//     return view('files.year_month', compact('data'));
// }
public function index()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 0) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Noorulirfan') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
            ])
            ->groupBy(function ($item) {
                return date('Y', strtotime($item->mail_folder_date)); // Group by year
            })
            ->map(function ($yearData) {
                return $yearData->groupBy(function ($item) {
                    return date('F', strtotime($item->mail_folder_date)); // Group by month
                })
                ->map(function ($monthData) {
                    return $monthData->groupBy(function ($item) {
                        return date('d-m-Y', strtotime($item->mail_folder_date)); // Group by date
                    })
                    ->map(function ($dateData) {
                        return $dateData->map(function ($file) {
                            return [
                                'file_name' => $file->file_name,
                                'file_code' => $file->file_code,
                                'sender' => $file->sender,
                                'mail_folder_date' => $file->mail_folder_date,
                                'mail_recived_date' => $file->mail_recived_date,
                                'darulifta_name' => $file->darulifta_name,
                                'selected' => $file->selected,
                                'checked_folder' => $file->checked_folder,
                                'checked_file_name' => $file->checked_file_name,
                                'checked_grade' => $file->checked_grade,
                                'checked_tasurat' => $file->checked_tasurat,
                                'checked_Instructions' => $file->checked_Instructions,
                            ];
                        });
                    });
                });
            });
    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.year_month', compact('data', 'datesInfo', 'userRoles'));
}
public function indexNor($checker = null)
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();
    $userName = auth()->user()->name; // Assuming 'name' is the username field
    // Fetch data from the database
    if (is_null($checker)) {
        $checker = DB::table('checker')
            ->where('checker_name', $userName)
            ->first()
            ->folder_id ?? null; // Use folder_id from matched checker, or null if not found
    }
    $data = DB::table('uploaded_files')
    ->orderBy('mail_folder_date', 'desc')
    ->where('selected', '=', 0) // Filter data where 'selected' column is 0
    ->where('darulifta_name', 'Noorulirfan') // Filter data where 'darulifta_name' is 'Noorulirfan'
    ->when($checker, function ($query, $checker) {
        if ($checker == 'mufti_ali_asghar') {
            // Fetch records where checker is 'mufti_ali_asghar' or null
            return $query->where(function ($query) use ($checker) {
                $query->where('checker', $checker)
                      ->orWhereNull('checker');
            });
        } elseif ($checker == 'sayed_masood') {
            // Fetch records where checker is 'sayed_masood'
            return $query->where('checker', $checker);
        }
    })
    ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.noorulirfan', compact('data', 'datesInfo', 'userRoles','checker'));
}
public function checkedNor()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->orderByRaw('
        CASE
            WHEN checked_folder = "ok" THEN 1
            WHEN checked_folder = "Mahl-e-Nazar" THEN 2
            WHEN checked_folder = "Tahqiqi" THEN 3
            ELSE 4
        END
    ')
        ->where('selected', '=', 1) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Noorulirfan') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.checkednor', compact('data', 'datesInfo', 'userRoles'));
}
public function checkedFaj()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 1) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Faizan-e-Ajmair') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.checkedfaj', compact('data', 'datesInfo', 'userRoles'));
}
public function checkedGul()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 1) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Gulzar-e-Taiba') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.checkedgul', compact('data', 'datesInfo', 'userRoles'));
}
public function checkedIec()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 1) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Markaz-ul-Iqtisaad') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.checkediec', compact('data', 'datesInfo', 'userRoles'));
}
public function indexFaj()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 0) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Faizan-e-Ajmair') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.faizaneajmair', compact('data', 'datesInfo', 'userRoles'));
}
public function indexGul()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 0) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Gulzar-e-Taiba') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.gulzahretaiba', compact('data', 'datesInfo', 'userRoles'));
}
public function indexIec()
{
    // Get the user's roles from the authenticated user
    $userRoles = auth()->user()->roles->pluck('name')->toArray();

    // Fetch data from the database
    $data = DB::table('uploaded_files')
        ->orderBy('mail_folder_date')
        ->where('selected', '=', 0) // Filter data where 'selected' column is 0
        ->where('darulifta_name', 'Markaz-ul-Iqtisaad') // Filter data where 'darulifta_name' is 'Noorulirfan'
        ->get([
            'id',
            'file_name',
            'file_code',
            'sender',
            'mail_folder_date',
            'mail_recived_date',
            'darulifta_name',
            'selected',
            'checked_folder',
            'checked_file_name',
            'checked_grade',
            'checked_tasurat',
            'checked_Instructions',
        ])
        ->groupBy(function ($item) {
            return date('Y', strtotime($item->mail_folder_date)); // Group by year
        })
        ->map(function ($yearData) {
            return $yearData->groupBy(function ($item) {
                return date('F', strtotime($item->mail_folder_date)); // Group by month
            })
            ->map(function ($monthData) {
                return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                    return $dateData->map(function ($file) {
                        return [
                            'id' => $file->id,
                            'file_name' => $file->file_name,
                            'file_code' => $file->file_code,
                            'sender' => $file->sender,
                            'mail_folder_date' => $file->mail_folder_date,
                            'mail_recived_date' => $file->mail_recived_date,
                            'darulifta_name' => $file->darulifta_name,
                            'selected' => $file->selected,
                            'checked_folder' => $file->checked_folder,
                            'checked_file_name' => $file->checked_file_name,
                            'checked_grade' => $file->checked_grade,
                            'checked_tasurat' => $file->checked_tasurat,
                            'checked_Instructions' => $file->checked_Instructions,
                        ];
                    });
                });
            });
        });

    // Initialize an empty array to store the dates
    $datesInfo = [];

    // Loop through the data and extract 'date' and 'darulifta_name'
    foreach ($data as $year => $months) {
        foreach ($months as $month => $dates) {
            foreach ($dates as $date => $files) {
                // Extract darulifta_name
                $daruliftaName = $files->first()['darulifta_name'];

                // Create an array with 'date' and 'darulifta_name' and add it to $datesInfo
                $datesInfo[] = [
                    'date' => $date,
                    'darulifta_name' => $daruliftaName,
                ];
            }
        }
    }

    return view('files.iqtisad', compact('data', 'datesInfo', 'userRoles'));
}
// public function indexSu()
// {
//     $data = $this->fetchDataFromDatabase();
//     return view('files.summary', compact('data'));
// }
public function downloadAll($date)
{
    $zipFileName = Str::slug($date) . '.zip';

    // Create a temporary file to store the zip archive
    $tempZipPath = tempnam(sys_get_temp_dir(), 'zip_');
    $zip = new \ZipArchive();

    if ($zip->open($tempZipPath, \ZipArchive::CREATE)) {
        $files = $this->getFilesInFolder("public/$date");

        foreach ($files as $file) {
            $filePath = storage_path("app/$file");
            if (file_exists($filePath)) {
                $relativePath = $this->getRelativePath($file, "public/$date");
                $zip->addFile($filePath, $relativePath);
            } else {
                // Create empty directory
                $relativePath = $this->getRelativePath($file, "public/$date");
                if (substr($relativePath, -1) === '/') {
                    $zip->addEmptyDir($relativePath);
                }
            }
        }
        $zip->close();
    } else {
        abort(500, 'Failed to create zip file');
    }

    // Create a download response from the temporary zip file
    return response()->download($tempZipPath, $zipFileName, [
        'Content-Type' => 'application/zip',
    ])->deleteFileAfterSend(); // This deletes the temporary file after sending the response.
}

public function downloadFolder(Request $request, $mailfolderDates, $daruliftaName)
{

    $currentroute = $request->input('currentRoute');
    $transferBy = $request->input('transferBy');
    $checker = $request->input('checker');
    $checkedFolder = $request->input('checkedFolder');
    $byMufti = $request->input('byMufti');
    if ($checker == null){
        $date = $mailfolderDates . $daruliftaName;
    }

    if ($checkedFolder == null) {
        if ($transferBy == 'Mujeeb') {
            $date = $mailfolderDates . $daruliftaName . ($checker ?? '');
        } else {
            $date = $mailfolderDates . $daruliftaName . ($checker ?? '') . '_by_' . ($transferBy ?? '');
        }
    } else {
        if (empty($byMufti)) {  // This will handle both null and empty string cases
            if (empty($checker)) {  // Check for both null and empty string
                $date = $mailfolderDates . $daruliftaName . 'Checked';
            } else {
                $date = $mailfolderDates . $daruliftaName . 'Checked_by_' . $checker;
            }
        } else {
            $date = $mailfolderDates . $daruliftaName . 'Checked_by_' . $checker . '_' . $byMufti;
        }
    }
// dd($request,$date);
    $zipFileName = Str::slug($date) . '.zip';

    // Create a temporary file to store the zip archive
    $tempZipPath = tempnam(sys_get_temp_dir(), 'zip_');
    $zip = new \ZipArchive();

    if ($zip->open($tempZipPath, \ZipArchive::CREATE) === TRUE) {
        // Get files and directories
        $this->addFolderToZip($zip, storage_path("app/public/$date"), '', $date);

        $zip->close();
    } else {
        abort(500, 'Failed to create zip file');
    }

    // Check if the user is an admin
    if (in_array('Admin', Auth::user()->roles->pluck('name')->toArray())) {
        // Get current date and time in Pakistan timezone
        $nowInPakistan = Carbon::now('Asia/Karachi');

        // Update the database to mark the folder as downloaded by admin
        $query = DB::table('uploaded_files')
            ->where('mail_folder_date', $mailfolderDates)
            ->where('darulifta_name', $daruliftaName);

            if (empty($checker)) {
            $query->whereNull('checker');
        } else {
            $query->where('checker', $checker);
        }

        $query->update(['downloaded_by_admin' => $nowInPakistan]);
    }

    // Ensure the temporary file exists before attempting to download it
    if (file_exists($tempZipPath)) {
        return response()->download($tempZipPath, $zipFileName, [
            'Content-Type' => 'application/zip',
        ])->deleteFileAfterSend(); // This deletes the temporary file after sending the response.
    } else {
        abort(500, 'Temporary zip file does not exist');
    }
}

/**
 * Recursively add a folder to the ZIP file.
 */
private function addFolderToZip($zip, $folderPath, $relativePath = '', $baseFolder = '')
{
    $folderPath = rtrim($folderPath, '/');
    $files = array_diff(scandir($folderPath), ['.', '..']);

    foreach ($files as $file) {
        $filePath = $folderPath . '/' . $file;
        $relativeFilePath = $relativePath . '/' . $file;

        if (is_dir($filePath)) {
            // Add directory (create an empty directory entry in the ZIP file)
            $zip->addEmptyDir($relativeFilePath);
            // Recursively add the directory contents
            $this->addFolderToZip($zip, $filePath, $relativeFilePath, $baseFolder);
        } else {
            // Add file
            $zip->addFile($filePath, $relativeFilePath);
        }
    }
}

public function deleteFolder(Request $request, $mailfolderDates, $daruliftaName)
{

      // Extract additional request inputs


      $currentroute = $request->input('currentRoute');
    $transferBy = $request->input('transferBy');
    $checker = $request->input('checker');
    $checkedFolder = $request->input('checkedFolder');
    $byMufti = $request->input('byMufti');
    if (empty($checker)) {
        $folderpath = $mailfolderDates . $daruliftaName;

    }

      // Build folder path based on conditions
      if ($checkedFolder == null) {
          if ($transferBy == 'Mujeeb') {
              $folderpath = $mailfolderDates . $daruliftaName . ($checker ?? '');
          } else {
              $folderpath = $mailfolderDates . $daruliftaName . ($checker ?? '') . '_by_' . ($transferBy ?? '');
          }
      } else {
        if (empty($byMufti)) {  // This will handle both null and empty string cases
            if (empty($checker)) {  // Check for both null and empty string
                $folderpath = $mailfolderDates . $daruliftaName . 'Checked';
            } else {
                $folderpath = $mailfolderDates . $daruliftaName . 'Checked_by_' . $checker;
            }
        } else {
            $folderpath = $mailfolderDates . $daruliftaName . 'Checked_by_' . $checker . '_' . $byMufti;
        }
      }
        // dd($request,$folderpath);


      // Delete or update logic based on `checkedFolder`
      if (empty($checkedFolder)) {
        // Delete the folder from the storage
        Storage::deleteDirectory("public/$folderpath");

       // Prepare the delete query
$query = DB::table('uploaded_files')
->where('mail_folder_date', $mailfolderDates)
->where('darulifta_name', $daruliftaName);

// Add conditions for `checker`
$query->where(function ($subQuery) use ($checker) {
if (is_null($checker) || $checker === '') {
    // If checker is null or empty, check for both NULL and empty string
    $subQuery->whereNull('checker')
             ->orWhere('checker', '');
} else {
    // Otherwise, filter by the actual checker value
    $subQuery->where('checker', $checker);
}
});

// Add conditions for `transfer_by`
$query->where(function ($subQuery) use ($transferBy) {
if (!is_null($transferBy)) {
    if ($transferBy === 'Mujeeb') {
        // If $transferBy is 'Mujeeb', filter for null or empty 'transfer_by' entries
        $subQuery->whereNull('transfer_by')
                 ->orWhere('transfer_by', '');
    } else {
        // Otherwise, filter by the actual 'transfer_by' value
        $subQuery->where('transfer_by', $transferBy);
    }
} else {
    // If transferBy is null, still check for NULL or empty string
    $subQuery->whereNull('transfer_by')
             ->orWhere('transfer_by', '');
}
});

// Get results for debugging
// $results = $query->get();
// dd($results, $query->toSql(), $query->getBindings());

    // dd($query);
        // Delete the matching records from the database
        $query->delete();
    } else {
        // Update the folder details in the database
        $query = DB::table('uploaded_files')
            ->where('mail_folder_date', $mailfolderDates)
            ->where('darulifta_name', $daruliftaName)
            ->where('by_mufti', $byMufti);

        if (is_null($checker)) {
            $query->whereNull('checker');
        } else {
            $query->where('checker', $checker);
        }

        // Update the checked folder details
        $query->update([
            'selected' => 0,
            'viral' => 0,
            'checked_file_name' => null,
            'checked_folder' => null,
            'checked_grade' => null,
            'checked_tasurat' => null,
            'checked_instructions' => null,
            'by_mufti' => null,
            'downloaded_by_admin' => null,
            // Conditionally set the checker to null if `mail_recived_date` is older than '2024-09-23'
    'checker' => DB::raw("CASE
    WHEN mail_recived_date < '2024-09-23' THEN NULL
    ELSE checker
  END"),
]);

        // Delete the folder from the storage
        Storage::deleteDirectory("public/$folderpath");
    }
    // dd($request,$daruliftaName,$mailfolderDates,$checker,$transferBy,$currentroute,$checkedFolder,$byMufti,$folderpath,$query);
      // Extract query parameters from the request for redirection
      $selectedmufti = $request->query('selectedmufti');
      $selectedexclude = $request->query('selectedexclude');
      $selectedTimeFrame = $request->query('selectedTimeFrame');
      $selectedMonths = $request->query('selectedMonths');
      $showTrail = $request->query('showTrail');
      $showQue = $request->query('showQue');
      $showChat = $request->query('showChat');
      $allOpen = $request->query('allOpen');

      // Redirect back to the Livewire component with query parameters
      return redirect()->route($currentroute, [
          'selectedmufti' => $selectedmufti,
          'selectedexclude' => $selectedexclude,
          'selectedTimeFrame' => $selectedTimeFrame,
          'selectedMonths' => $selectedMonths,
          'showTrail' => $showTrail,
          'showQue' => $showQue,
          'showChat' => $showChat,
          'allOpen' => $allOpen,
      ])->with('success', 'Folder operation completed successfully.');
  }

public function deleteCheckedFolder(Request $request, $mailfolderDates, $daruliftaName, $checker = null)
{

    $folderpath = $mailfolderDates . $daruliftaName .'Checked';

    // Proceed with the deletion
    Storage::deleteDirectory("public/$folderpath");

    // Delete the file entry from the database
    $query = DB::table('uploaded_files')
    ->where('mail_folder_date', $mailfolderDates)
    ->where('darulifta_name', $daruliftaName);

if (is_null($checker)) {
    $query->whereNull('checker');
} else {
    $query->where('checker', $checker);
}

$query->update([
    'selected' => 0,  // An integer 0
    'checked_file_name' => null,  // Null for other columns
    'checked_folder' => null,
    'checked_grade' => null,
    'checked_tasurat' => null,
    'checked_instructions' => null,
]);


    // Extract query parameters from the request
    $selectedmufti = $request->query('selectedmufti');
    $selectedchecked = $request->query('selectedchecked');
    $selectedTimeFrame = $request->query('selectedTimeFrame');
    $selectedMonths = $request->query('selectedMonths');
    $routeName = $request->input('currentRoute');
    $showTrail = $request->query('showTrail');
    $showQue = $request->query('showQue');
    $showChat = $request->query('showChat');
    $allOpen = $request->query('allOpen');

    // Redirect back to the Livewire component with query parameters
    return redirect()->route($routeName, [
            'selectedmufti' => $selectedmufti,
            'selectedchecked' => $selectedchecked,
            'selectedTimeFrame' => $selectedTimeFrame,
            'selectedMonths' => $selectedMonths,
            'showTrail' => $showTrail, // Add showTrail to query params
            'showQue' => $showQue,     // Add showQue to query params
            'showChat' => $showChat,    // Add showChat to query params
            'allOpen' => $allOpen,    // Add showChat to query params
        ])->with('success', 'Folder deleted successfully.');
}
public function deleteFile(Request $request, $mailfolderDates, $daruliftaName, $checker = null, $transferby = null)
{
    if (is_null($transferby) || empty($transferby)) {
        $path = $mailfolderDates . $daruliftaName . ($checker ?? '');
    } else {
        $path = $mailfolderDates . $daruliftaName . ($checker ?? '') . '_by_' . ($transferby ?? '');
    }

    $id = $request->input('id');
    $filename = $request->input('file_name');
    $filcode = $request->input('file_code');

    // Proceed with the deletion
    Storage::delete("public/$path/$filename");

    // Delete the file entry from the database
    $query = DB::table('uploaded_files')
    ->where('id', $id);

    $query->delete();

    // Extract query parameters from the request
    $selectedmufti = $request->query('selectedmufti');
    $selectedexclude = $request->query('selectedexclude');
    $selectedTimeFrame = $request->query('selectedTimeFrame');
    $selectedMonths = $request->query('selectedMonths');
    $routeName = $request->input('currentRoute');
    $showTrail = $request->query('showTrail');
    $showQue = $request->query('showQue');
    $showChat = $request->query('showChat');


    // Redirect back to the Livewire component with query parameters
    return redirect()->route($routeName, [
            'selectedmufti' => $selectedmufti,
            'selectedexclude' => $selectedexclude,
            'selectedTimeFrame' => $selectedTimeFrame,
            'selectedMonths' => $selectedMonths,
            'showTrail' => $showTrail, // Add showTrail to query params
            'showQue' => $showQue,     // Add showQue to query params
            'showChat' => $showChat,    // Add showChat to query params

        ])->with('success', 'File deleted successfully.');
}
public function deleteCheckedFile(Request $request, $mailfolderDates, $daruliftaName, $checker = null)
{

    $path = $mailfolderDates . $daruliftaName .'Checked';
    $id = $request->input('id');
    $filename = $request->input('file_name');
    $filcode = $request->input('file_code');
    $checkedfolder = $request->input('checked_folder');

    // Proceed with the deletion
    Storage::delete("public/$path/$checkedfolder/$filename");

    // Delete the file entry from the database
    $query = DB::table('uploaded_files')
    ->where('id', $id);

$query->update([
    'selected' => 0,  // An integer 0
    'checked_file_name' => null,  // Null for other columns
    'checked_folder' => null,
    'checked_grade' => null,
    'checked_tasurat' => null,
    'checked_instructions' => null,
]);

    // Extract query parameters from the request
    $selectedmufti = $request->query('selectedmufti');
    $selectedchecked = $request->query('selectedchecked');
    $selectedTimeFrame = $request->query('selectedTimeFrame');
    $selectedMonths = $request->query('selectedMonths');
    $routeName = $request->input('currentRoute');
    $showTrail = $request->query('showTrail');
    $showQue = $request->query('showQue');
    $showChat = $request->query('showChat');


    // Redirect back to the Livewire component with query parameters
    return redirect()->route($routeName, [
            'selectedmufti' => $selectedmufti,
            'selectedchecked' => $selectedchecked,
            'selectedTimeFrame' => $selectedTimeFrame,
            'selectedMonths' => $selectedMonths,
            'showTrail' => $showTrail, // Add showTrail to query params
            'showQue' => $showQue,     // Add showQue to query params
            'showChat' => $showChat,    // Add showChat to query params

        ])->with('success', 'File deleted successfully.');
}
// public function downloadAll($date)
// {
//     $zipFileName = Str::slug($date) . '.zip';
//     $zipPath = storage_path("app/public/$date/$zipFileName");

//     $files = $this->getFilesInFolder("public/$date");

//     if (count($files) === 0) {
//         abort(404, 'No files found for this date');
//     }

//     $zip = new \ZipArchive();
//     if ($zip->open($zipPath, \ZipArchive::CREATE)) {
//         foreach ($files as $file) {
//             $filePath = storage_path("app/$file");
//             $relativePath = $this->getRelativePath($file, "public/$date");
//             $zip->addFile($filePath, $relativePath);
//         }
//         $zip->close();
//     } else {
//         abort(500, 'Failed to create zip file');
//     }

//     return response()->download($zipPath);
// }

private function getFilesInFolder($folder)
{
    $files = [];

    $items = Storage::allFiles($folder);

    return $items;
}

private function getRelativePath($path, $folder)
{
    return str_replace("$folder/", '', $path);
}

// public function downloadAll($date)
// {

//     $zipFileName = Str::slug($date) . '.zip';

//     $zipPath = storage_path("app/public/$date/$zipFileName");

//     $files = Storage::files("public/$date");

//     if (count($files) === 0) {
//         abort(404, 'No files found for this date');
//     }

//     $zip = new \ZipArchive();
//     if ($zip->open($zipPath, \ZipArchive::CREATE)) {
//         foreach ($files as $file) {
//             $fileName = pathinfo($file, PATHINFO_FILENAME);
//             $zip->addFile(storage_path("app/$file"), "$fileName.docx");
//         }
//         $zip->close();
//     } else {
//         abort(500, 'Failed to create zip file');
//     }

//     return response()->download($zipPath);
// }
private function fetchDataFromDatabase($columns = [])
    {
        if (empty($columns)) {
            // If columns are not specified, fetch all columns
            $columns = [
                'file_name',
                'file_code',
                'sender',
                'mail_folder_date',
                'mail_recived_date',
                'darulifta_name',
                'selected',
                'checked_folder',
                'checked_file_name',
                'checked_grade',
                'checked_tasurat',
                'checked_Instructions',
            ];
        }

        // Fetch data from the database, organized by mail_folder_date
        $data = DB::table('uploaded_files')
            ->orderBy('mail_folder_date')
            ->get($columns)
            ->groupBy(function ($item) {
                return date('Y', strtotime($item->mail_folder_date)); // Group by year
            })
            ->map(function ($yearData) {
                return $yearData->groupBy(function ($item) {
                    return date('F', strtotime($item->mail_folder_date)); // Group by month
                })
                ->map(function ($monthData) {
                    return $monthData->groupBy('mail_folder_date')->map(function ($dateData) {
                        return $dateData->map(function ($file) {
                            return [
                                'file_name' => $file->file_name,
                                'file_code' => $file->file_code,
                                'sender' => $file->sender,
                                'mail_folder_date' => $file->mail_folder_date,
                                'mail_recived_date' => $file->mail_recived_date,
                                'darulifta_name' => $file->darulifta_name,
                                'selected' => $file->selected,
                                'checked_folder' => $file->checked_folder,
                                'checked_file_name' => $file->checked_file_name,
                                'checked_grade' => $file->checked_grade,
                                'checked_tasurat' => $file->checked_tasurat,
                                'checked_Instructions' => $file->checked_Instructions,
                            ];
                        });
                    });
                });
            });

        return $data;
    }
    // public function generateSummaryReport()
    // {
    //     // Get unique darulifta_name values
    //     $daruliftaNames = DB::table('uploaded_files')
    //         ->select('darulifta_name')
    //         ->distinct()
    //         ->pluck('darulifta_name');

    //     // Initialize an array to store the summary report
    //     $summaryReport = [];

    //     foreach ($daruliftaNames as $daruliftaName) {
    //         // Get unique mail_folder_date values related to the current darulifta_name
    //         $mailFolderDates = DB::table('uploaded_files')
    //             ->select('mail_folder_date')
    //             ->orderBy('mail_folder_date')
    //             ->where('selected', 0)
    //             ->where('darulifta_name', $daruliftaName)
    //             ->distinct()
    //             ->pluck('mail_folder_date');

    //         $summaryItem = [
    //             'Darulifta' => $daruliftaName,
    //             'Mail Recived' => $mailFolderDates->implode('  |  ')
    //         ];

    //         $summaryReport[] = $summaryItem;
    //     }

    //     $latestMailFolderDate = DB::table('uploaded_files')
    //     ->max('mail_folder_date');

    //     $dataByDaruliftaName = [];

    //     foreach ($daruliftaNames as $daruliftaName) {
    //         $data = DB::table('uploaded_files')
    //             ->select('file_code', 'sender', 'ftype', 'category')
    //             ->where('darulifta_name', $daruliftaName)
    //             ->where('mail_folder_date', $latestMailFolderDate)
    //             ->get();

    //         $dataByDaruliftaName[$daruliftaName] = $data;
    //     }

    //     return view('files.summary', compact('summaryReport', 'dataByDaruliftaName','latestMailFolderDate'));
    // }
    //------------------------------
        public function download($date, $filename)
    {
        // Define the path to the file based on the date and filename
        $filePath = storage_path("app/public/$date/$filename");

        // Check if the file exists
        if (Storage::exists("public/$date/$filename")) {
            // Generate a downloadable response for the file
            return response()->download($filePath);
        } else {
            // File not found, return a 404 error
            abort(404, 'File not found');
        }
    }
    public function downloadFile($date, $filename, $id)
    {
        // Define the path to the file based on the date and filename
        $filePath = storage_path("app/public/$date/$filename");

        // Check if the file exists
        if (!Storage::exists("public/$date/$filename")) {
            // File not found, return a 404 error
            abort(404, 'File not found');
        }

        // Check if the user is an admin
        if (in_array('Admin', Auth::user()->roles->pluck('name')->toArray())) {
            // Get current date and time in Pakistan timezone
            $nowInPakistan = Carbon::now('Asia/Karachi');

            // Update the database to mark the folder as downloaded by admin
            DB::table('uploaded_files')
                ->where('id', $id)
                ->update(['downloaded_by_admin' => $nowInPakistan]);
        }

        // Generate a downloadable response for the file
        return response()->download($filePath);
    }

    public function downloadCheck($date, $folder, $filename)
    {
        // Define the path to the file based on the folder, subfolder, and filename
        $filePath = storage_path("app/public/$date/$folder/$filename");

        // Check if the file exists
        if (Storage::exists("public/$date/$folder/$filename")) {
            // Generate a downloadable response for the file
            return response()->download($filePath);
        } else {
            // File not found, return a 404 error
            abort(404, 'File not found');
        }
    }
//     public function viewCheck($date, $folder, $filename)
// {
//     $filePath = storage_path("app/public/$date/$folder/$filename");

//     if (Storage::exists("public/$date/$folder/$filename")) {
//         $phpWord = IOFactory::load($filePath);

//         // Create a memory stream to capture the HTML content
//         $htmlWriter = IOFactory::createWriter($phpWord, 'HTML');
//         $htmlContent = $htmlWriter->save('php://output');

//         return view('documents.view', compact('htmlContent'));
//     } else {
//         abort(404, 'File not found');
//     }
// }
public function viewCheck($date, $folder, $filename)
{
    $filePath = storage_path("app/public/$date/$folder/$filename");

    if (Storage::exists("public/$date/$folder/$filename")) {
        $phpWord = IOFactory::load($filePath);

        // Modify the PHPWord document to adjust alignment
        foreach ($phpWord->getSections() as $section) {
            foreach ($section->getElements() as $element) {
                if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                    $paragraphStyle = $element->getParagraphStyle();
                    if ($paragraphStyle && $paragraphStyle->getAlignment() === 'both') {
                        $element->setParagraphStyle(['align' => 'right', 'spaceAfter' => 0, 'spaceBefore' => 0]);
                    }
                }
            }
        }

        // Generate HTML content
        $htmlWriter = IOFactory::createWriter($phpWord, 'HTML');
        ob_start();
        $htmlWriter->save('php://output');
        $htmlContent = ob_get_clean();

        // Remove any existing <title> tag in the generated HTML
        $htmlContent = preg_replace('/<title>.*?<\/title>/is', '', $htmlContent);

        // Add a tab before each paragraph in the HTML content
        $htmlContent = preg_replace('/<p>/', '<p>&nbsp;&nbsp;&nbsp;&nbsp;', $htmlContent);

        return view('documents.view', compact('htmlContent', 'filename'));
    } else {
        abort(404, 'File not found');
    }
}

public function viewPdf(Request $request)
{
    // Retrieve request parameters
    $daruliftaNames = $request->query('darulifta', []);
    $mailfolderDates = $request->query('mailfolder', []);
    $checkers = $request->query('checker', []);
    $selectedMujeeb = $request->query('selectedmujeeb', null);
    $selectedMufti = $request->query('selectedmufti', null);
    $selectedChecked = $request->query('selectedchecked', null);
    $selectedTimeFrame = $request->query('selectedTimeFrame', null);
    $startDate = $request->query('startDate', null);
    $endDate = $request->query('endDate', null);
    $selectedMonths = $request->query('selectedMonths', null);

    // Initialize an empty array to store fatawa data
    $fatawaData = [];

    // Format selected months if available

    // Ensure daruliftaNames and mailfolderDates are arrays before looping
    if (is_array($daruliftaNames) && is_array($mailfolderDates)) {
        // Condition when $selectedMufti is 'all'
        if ($selectedMufti == 'all') {
            foreach ($mailfolderDates as $mailfolderDate) {
                foreach ($daruliftaNames as $daruliftaName) {
                    foreach ($checkers as $checked) { // Loop over the checkers (checked values)
                        $query = DB::table('uploaded_files')
                            ->where('darulifta_name', $daruliftaName)
                            ->where(function($query) use ($checked) {
                                $query->where('checker', $checked)
                                      ->orWhere(function($query) use ($checked) {
                                          if ($checked === 'mufti_ali_asghar') {
                                              $query->whereNull('checker'); // Handle null checkers for Mufti Ali Asghar
                                          }
                                      });
                            })
                            ->whereDate('mail_folder_date', $mailfolderDate)
                            ->where('selected', 1);

                        // Apply selected Mujeeb filter
                        if ($selectedMujeeb != 'all') {
                            $query->where('sender', $selectedMujeeb);
                        }

                        // Apply checked folder filter
                        if ($selectedChecked == 'mahle_nazar') {
                            $query->where('checked_folder', 'Mahl-e-Nazar');
                        } elseif ($selectedChecked == 'ok_fatawa') {
                            $query->where('checked_folder', 'ok');
                        }

                        // Fetch fatawa data for the current daruliftaName, mailfolderDate, and checked combination
                        $results = $query->get();

                        if ($results->isNotEmpty()) {
                            $fatawaData[$daruliftaName][$checked][$mailfolderDate] = $results->filter(function ($item) {
                                return isset($item->mail_folder_date); // Ensure the expected property exists
                            });
                        }
                    }
                }
            }
        }
        // Condition when $selectedMufti is not 'all'
        else {
            foreach ($mailfolderDates as $mailfolderDate) {
                foreach ($daruliftaNames as $daruliftaName) {
                    $query = DB::table('uploaded_files')
                        ->where('darulifta_name', $daruliftaName)
                        ->whereDate('mail_folder_date', $mailfolderDate) // Ensure mail_folder_date is treated as a date
                        ->where('selected', 1);

                    // Apply selected Mujeeb filter
                    if ($selectedMujeeb != 'all') {
                        $query->where('sender', $selectedMujeeb);
                    }

                    // Apply selected Mufti filter
                    if ($selectedMufti != 'all') {
                        if ($selectedMufti == 'mufti_ali_asghar') {
                            $query->where(function ($query) {
                                $query->where('checker', 'mufti_ali_asghar')
                                      ->orWhereNull('checker');
                            });
                        } elseif ($selectedMufti == 'transfer_checked') {
                            $checkerFolderId = DB::table('checker')
                                ->where('checker_name', $selectedMufti)
                                ->first()
                                ->folder_id ?? null;
                            $query->where('transfer_by', $checkerFolderId);
                        } else {
                            $query->where('checker', $selectedMufti);
                        }
                    }

                    // Apply checked folder filter
                    if ($selectedChecked == 'mahle_nazar') {
                        $query->where('checked_folder', 'Mahl-e-Nazar');
                    } elseif ($selectedChecked == 'ok_fatawa') {
                        $query->where('checked_folder', 'ok');
                    }

                    // Fetch fatawa data for the current daruliftaName and mailfolderDate combination
                    $results = $query->get();

                    // Check if the results are valid and contain the correct structure
                    if ($results->isNotEmpty()) {
                        $fatawaData[$daruliftaName][$mailfolderDate] = $results->filter(function ($item) {
                            return isset($item->mail_folder_date); // Ensure the expected property exists
                        });
                    }
                }
            }
        }
    } else {
        // Handle the case where daruliftaNames or mailfolderDates are not arrays
        return response()->json(['error' => 'Invalid data for daruliftaNames or mailfolderDates'], 400);
    }

    // Compile necessary fatawa data for the view
    $sendingFatawa = $fatawaData;  // Match the variable expected by the HTML
    $totalCounts = array_reduce($sendingFatawa, function ($carry, $item) {
        return $carry + count($item);
    }, 0);
    $overallFolderCount = count($mailfolderDates);

    // Prepare the PDF using the compiled fatawa data
    $pdf = Pdf::loadView('pdf.fatawadata', compact(
        'daruliftaNames', 'sendingFatawa', 'selectedMujeeb', 'selectedMufti',
        'selectedChecked', 'totalCounts', 'overallFolderCount', 'mailfolderDates'
    ))
    ->setPaper('a4', 'landscape');  // Set the paper size to A4 and orientation to landscape

    // Return PDF stream or download
    return $pdf->stream('fatawa.pdf');
}

public function viewRemain($date, $filename)
{
    // dd($date,$filename);
    $filePath = storage_path("app/public/$date/$filename");

    if (Storage::exists("public/$date/$filename")) {
        $phpWord = IOFactory::load($filePath);

        // Modify the PHPWord document to adjust alignment
        foreach ($phpWord->getSections() as $section) {
            foreach ($section->getElements() as $element) {
                if ($element instanceof \PhpOffice\PhpWord\Element\TextRun) {
                    $paragraphStyle = $element->getParagraphStyle();
                    if ($paragraphStyle && $paragraphStyle->getAlignment() === 'both') {
                        $element->setParagraphStyle(['align' => 'right', 'spaceAfter' => 0, 'spaceBefore' => 0]);
                    }
                }
            }
        }

        // Generate HTML content
        $htmlWriter = IOFactory::createWriter($phpWord, 'HTML');
        ob_start();
        $htmlWriter->save('php://output');
        $htmlContent = ob_get_clean();

        // Remove any existing <title> tag in the generated HTML
        $htmlContent = preg_replace('/<title>.*?<\/title>/is', '', $htmlContent);

        // Add a tab before each paragraph in the HTML content
        $htmlContent = preg_replace('/<p>/', '<p>&nbsp;&nbsp;&nbsp;&nbsp;', $htmlContent);

        return view('documents.view', compact('htmlContent', 'filename'));
    } else {
        abort(404, 'File not found');
    }
}


// public function viewCheck($date, $folder, $filename)
// {
//     // Define the file path for the Word document
//     $filePath = storage_path("app/public/$date/$folder/$filename");

//     // Check if the Word file exists in the specified path
//     if (Storage::exists("public/$date/$folder/$filename")) {

//         // Set PDF renderer to TCPDF for PHPWord
//         \PhpOffice\PhpWord\Settings::setPdfRendererName(\PhpOffice\PhpWord\Settings::PDF_RENDERER_TCPDF);
//         \PhpOffice\PhpWord\Settings::setPdfRendererPath(base_path('vendor/tecnickcom/tcpdf'));

//         // Load the Word file using IOFactory
//         $phpWord = \PhpOffice\PhpWord\IOFactory::load($filePath);

//         // Apply the desired font (Jameel Noori Nastaleeq) to ensure proper rendering
//         foreach ($phpWord->getSections() as $section) {
//             foreach ($section->getElements() as $element) {
//                 if (method_exists($element, 'getFontStyle')) {
//                     $fontStyle = $element->getFontStyle();
//                     if ($fontStyle) {
//                         $fontStyle->setName('JameelNooriNastaleeqNew');
//                     }
//                 }
//             }
//         }

//         // Create a PDF writer using PHPWord
//         $pdfWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'PDF');

//         // Stream the PDF file for download
//         return response()->streamDownload(function () use ($pdfWriter) {
//             $pdfWriter->save('php://output');
//         }, 'generated.pdf');

//     } else {
//         // If the file doesn't exist, return a 404 error
//         return response()->json(['error' => 'File not found'], 404);
//     }
// }


        // Convert HTML content to a string
//         $htmlString = (string) $htmlContent;

//         // Load HTML content into the PDF wrapper
//         $pdf = PDF::loadHtml($htmlString);

//         // Set options, including fonts
//         $pdf->setOptions([
//             'isHtml5ParserEnabled' => true,
//             'isPhpEnabled' => true,
//             'isFontSubsettingEnabled' => true,
//             'fontDir' => storage_path('app/fonts/'),
//             'fontCache' => storage_path('app/fonts/'),
//         ]);

//         // Load custom font
//         $font = 'Makkah Contour.ttf';
//         $fontPath = storage_path("app/fonts/{$font}");

//         // Add the font directory to DomPDF
//         $pdf->getDomPDF()->getOptions()->setFontDir($fontPath);

//         // Set the default font
//         // $pdf->getDomPDF()->getOptions()->setDefaultFont('your-custom-font-family');

//         // Optionally set PDF options
//         // $pdf->setPaper('letter', 'portrait');

//         // Return PDF as stream
//         return $pdf->stream();
//     } else {
//         abort(404, 'File not found');
//     }
// }
//     public function viewCheck($date, $folder, $filename)
// {
//     // Define the path to the file based on the folder, subfolder, and filename
//     $filePath = storage_path("app/public/$date/$folder/$filename");

//     // Check if the file exists
//     if (Storage::exists("public/$date/$folder/$filename")) {
//         // Load the DOC file
//         $phpWord = IOFactory::load($filePath);

//         // Get plain text content
//         $textContent = strip_tags($phpWord->getDocInfo()->getTitle() . "\n" . $phpWord->getSections()[0]->getText());

//         // Use $textContent to generate HTML as needed

//         // Convert HTML to PDF using Laravel PDF package
//         $pdf = PDF::loadHTML($textContent);

//         // Optionally, you can set PDF options here, e.g., setPaper, setOrientation, etc.
//         // Example: $pdf->setPaper('letter', 'landscape');

//         // Return the PDF as a stream or download it
//         return $pdf->stream();
//     } else {
//         // File not found, return a 404 error
//         abort(404, 'File not found');
//     }
// }
    // ...

// public function displaySelectedData()
// {
//     $folderData = DB::table('uploaded_files')
//     ->distinct()->pluck('mail_folder_date');
//     $latestMailFolderDate = max($folderData);

//     $daruliftaNames = DB::table('uploaded_files')
//         ->select('darulifta_name')
//         ->where('mail_folder_date', $latestMailFolderDate)
//         ->distinct()
//         ->pluck('darulifta_name');

//     $dataByDaruliftaName = [];

//     foreach ($daruliftaNames as $daruliftaName) {
//         $data = DB::table('uploaded_files')
//             ->select('file_code', 'sender', 'ftype', 'category')
//             ->where('darulifta_name', $daruliftaName)
//             ->where('mail_folder_date', $latestMailFolderDate)
//             ->get();

//         $dataByDaruliftaName[$daruliftaName] = $data;
//     }

//     return view('files.summary', compact('dataByDaruliftaName'));
// }
//     public function applyFilters(Request $request)
// {
//     $query = DB::table('uploaded_files');

//     $daruliftaName = $request->input('darulifta_name');
//     if ($daruliftaName) {
//         $query->where('darulifta_name', $daruliftaName);
//     }

//     $mailFolderDate = $request->input('mail_folder_date');
//     if ($mailFolderDate) {
//         $query->where('mail_folder_date', $mailFolderDate);
//     }
//     $fdata = $query->get();

//     return $fdata;

// }
// public function applyFilters(Request $request)
// {
//     $query = DB::table('uploaded_files');

//     $daruliftaName = $request->input('darulifta_name');
//     if ($daruliftaName) {
//         $query->where('darulifta_name', $daruliftaName);
//     }

//     $mailFolderDate = $request->input('mail_folder_date');
//     if ($mailFolderDate) {
//         $query->where('mail_folder_date', $mailFolderDate);
//     }

//     $fdata = $query->get();

//     return $fdata;
// }
// public function indexSe()
// {
//     $data = $this->fetchDataFromDatabase();
//     $daruliftaNames = $this->getUniqueDaruliftaNames();
//     $fdata = $this->applyFilters(request());

//     return view('files.sending', compact('data', 'daruliftaNames', 'fdata'));
// }

// public function getUniqueDaruliftaNames()
// {
//     // Replace 'uploaded_files' with the actual table name where you store Darulifta names
//     $daruliftaNames = DB::table('uploaded_files')
//         ->select('darulifta_name')
//         ->distinct()
//         ->pluck('darulifta_name');

//     return $daruliftaNames;
// }

// public function getDatesByDaruliftaName(Request $request)
//     {
//         $daruliftaName = $request->input('darulifta_name');
//         $mailFolderDates = DB::table('uploaded_files')
//             ->when($daruliftaName, function ($query) use ($daruliftaName) {
//                 return $query->where('darulifta_name', $daruliftaName);
//             })
//             ->distinct()
//             ->pluck('mail_folder_date')
//             ->filter()
//             ->values()
//             ->toArray();

//         return response()->json($mailFolderDates);
//     }
}
