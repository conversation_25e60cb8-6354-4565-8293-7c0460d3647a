<!-- Detailed Fatawa Section -->
<div class="modern-card" x-data="{ allExpanded: false }">
    <div class="modern-card-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    Remaining Fatawa Details
                </h5>
                <p class="mb-0 opacity-75">Detailed view of all remaining fatawa with download and management options</p>
            </div>
            <button @click="allExpanded = !allExpanded" class="btn-modern btn-outline-modern">
                <i class="fas" :class="allExpanded ? 'fa-compress' : 'fa-expand'" class="me-2"></i>
                <span x-text="allExpanded ? 'Collapse All' : 'Expand All'"></span>
            </button>
        </div>
    </div>
    <div class="modern-card-body">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $daruliftaNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!--[if BLOCK]><![endif]--><?php if(isset($remainingFatawa[$daruliftaName])): ?>
                <div class="toggle-section" x-data="{ expanded: true }" x-init="$watch('allExpanded', value => expanded = value)">
                    <div class="toggle-header-modern" @click="expanded = !expanded">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-building me-2"></i>
                            <span class="fw-bold"><?php echo e($daruliftaName); ?></span>
                        </div>
                        <i class="fas" :class="expanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                    </div>

                    <div x-show="expanded" x-transition class="toggle-content-modern">
                        <?php
                            $serialNumber_fl = 1;
                            $colors = ['#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1'];
                        ?>

                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mailfolderDate; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mailfolderDates): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if(isset($remainingFatawa[$daruliftaName][$mailfolderDates])): ?>
                                <?php
                                    $fatawaCollection = $remainingFatawa[$daruliftaName][$mailfolderDates];
                                    $mailfolderDateCount = is_array($fatawaCollection) ? count($fatawaCollection) : $fatawaCollection->count();
                                    $formattedDate = (new DateTime($mailfolderDates))->format('d-m-Y');
                                ?>

                                <div class="modern-card mb-3" style="background-color: <?php echo e($colors[$loop->index % count($colors)]); ?>;">
                                    <div x-data="{ folderExpanded: <?php echo e($showQue ? 'true' : 'false'); ?> }" x-init="$watch('allExpanded', value => folderExpanded = value)">
                                        <div class="modern-card-header cursor-pointer" @click="folderExpanded = !folderExpanded"
                                             style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-0 text-white">
                                                        <span class="badge bg-white text-primary me-2"><?php echo e($serialNumber_fl++); ?></span>
                                                        <i class="fas fa-calendar-alt me-2"></i>
                                                        <?php echo e($formattedDate); ?>

                                                    </h6>
                                                    <small class="text-white opacity-75">
                                                        <i class="fas fa-file-alt me-1"></i>
                                                        <?php echo e($mailfolderDateCount); ?> Fatawa
                                                    </small>
                                                </div>
                                                <div class="d-flex align-items-center gap-3">
                                                    <!-- Download and Delete Actions -->
                                                    <?php
                                                        // Convert to collection if it's an array and get first item safely
                                                        $fatawaCollectionSafe = is_array($fatawaCollection) ? collect($fatawaCollection) : $fatawaCollection;
                                                        $firstFatawa = $fatawaCollectionSafe->first();
                                                        $downloadedByAdmin = $firstFatawa->downloaded_by_admin ?? null;
                                                        $canDelete = is_null($downloadedByAdmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                                                        $isAdmin = in_array('Admin', Auth::user()->roles->pluck('name')->toArray());

                                                        // Ensure we're working with a collection for the unique pairs
                                                        $remainingFatawaCollection = is_array($remainingFatawa[$daruliftaName][$mailfolderDates])
                                                            ? collect($remainingFatawa[$daruliftaName][$mailfolderDates])
                                                            : $remainingFatawa[$daruliftaName][$mailfolderDates];

                                                        $uniquePairs = $remainingFatawaCollection
                                                            ->filter(function($file) use ($mailfolderDates) {
                                                                return $file->mail_folder_date == $mailfolderDates;
                                                            })
                                                            ->map(function($file) {
                                                                return [
                                                                    'checker' => $file->checker,
                                                                    'transfer_by' => !empty($file->transfer_by) ? $file->transfer_by : 'Mujeeb',
                                                                    'checkedFolder' => $file->checked_folder,
                                                                    'byMufti' => $file->by_mufti,
                                                                    'Id' => $file->id,
                                                                    'mail_folder_date' => $file->mail_folder_date
                                                                ];
                                                            })
                                                            ->unique(function ($item) {
                                                                return $item['mail_folder_date'] . '-' . $item['checker'] . '-' . $item['transfer_by'];
                                                            });
                                                    ?>

                                                    <div class="d-flex gap-2" onclick="event.stopPropagation();">
                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $uniquePairs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pair): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php
                                                                $downloadUrl = route('downloadFolder', [
                                                                    'daruliftaName' => $daruliftaName,
                                                                    'mailfolderDates' => $mailfolderDates,
                                                                    'checker' => $pair['checker'] ?? '',
                                                                    'transferBy' => $pair['transfer_by'] ?? '',
                                                                    'checkedFolder' => $pair['checkedFolder'] ?? '',
                                                                    'byMufti' => $pair['byMufti'] ?? ''
                                                                ]);

                                                                $deleteUrl = route('deleteFolder', [
                                                                    'daruliftaName' => $daruliftaName,
                                                                    'mailfolderDates' => $mailfolderDates,
                                                                    'checker' => $pair['checker'] ?? '',
                                                                    'transferBy' => $pair['transfer_by'] ?? '',
                                                                    'byMufti' => $pair['byMufti'] ?? '',
                                                                    'checkedFolder' => $pair['checkedFolder'] ?? ''
                                                                ]);

                                                                $deleteFolderFormId = 'delete-folder-form-' . $pair['Id'];
                                                            ?>

                                                            <a href="<?php echo e($downloadUrl); ?>" class="btn-modern btn-success-modern btn-sm"
                                                               title="Download Folder <?php echo e($pair['checker']); ?> by <?php echo e($pair['transfer_by']); ?>">
                                                                <i class="fas fa-download action-icon"></i>
                                                            </a>

                                                            <!--[if BLOCK]><![endif]--><?php if($canDelete): ?>
                                                                <button onclick="if (confirm('Are you sure you want to delete this folder?')) { document.getElementById('<?php echo e($deleteFolderFormId); ?>').submit(); }"
                                                                        class="btn-modern btn-danger-modern btn-sm" title="Delete Folder">
                                                                    <i class="fas fa-trash action-icon"></i>
                                                                </button>
                                                                <form id="<?php echo e($deleteFolderFormId); ?>" action="<?php echo e($deleteUrl); ?>" method="POST" class="d-none">
                                                                    <?php echo csrf_field(); ?>
                                                                    <?php echo method_field('DELETE'); ?>
                                                                </form>
                                                            <?php else: ?>
                                                                <button class="btn-modern btn-danger-modern btn-sm opacity-50" disabled
                                                                        title="Cannot delete, downloaded by admin on <?php echo e($downloadedByAdmin); ?>">
                                                                    <i class="fas fa-trash action-icon"></i>
                                                                </button>
                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                    </div>

                                                    <i class="fas text-white" :class="folderExpanded ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Detailed Table View -->
                                        <div x-show="folderExpanded" x-transition class="modern-card-body">
                                            <!--[if BLOCK]><![endif]--><?php if($showDetail): ?>
                                                <div class="table-responsive">
                                                    <table class="table-modern">
                                                        <thead>
                                                            <tr>
                                                                <th class="text-center" style="width: 5%;">
                                                                    <i class="fas fa-hashtag me-1"></i>
                                                                    S.No
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-file-code me-1"></i>
                                                                    Fatwa No
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-user me-1"></i>
                                                                    Mujeeb
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-tag me-1"></i>
                                                                    Type
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-folder me-1"></i>
                                                                    Category
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-user-check me-1"></i>
                                                                    Checker
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-calendar me-1"></i>
                                                                    Send Date
                                                                </th>
                                                                <th style="width: 12%;">
                                                                    <i class="fas fa-clock me-1"></i>
                                                                    Reception Info
                                                                </th>
                                                                <th style="width: 10%;">
                                                                    <i class="fas fa-folder-open me-1"></i>
                                                                    M.Folder
                                                                </th>
                                                                <!--[if BLOCK]><![endif]--><?php if($isAdmin): ?>
                                                                    <th style="width: 9%;">
                                                                        <i class="fas fa-info-circle me-1"></i>
                                                                        Details
                                                                    </th>
                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                <th class="text-center" style="width: 10%;">
                                                                    <i class="fas fa-cogs me-1"></i>
                                                                    Actions
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php $serialNumber_re = 1; ?>
                                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $remainingFatawaCollection; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <tr>
                                                                    <td class="text-center">
                                                                        <span class="badge bg-primary"><?php echo e($serialNumber_re++); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <div class="fw-bold text-primary"><?php echo e($file->file_code); ?></div>
                                                                        <!--[if BLOCK]><![endif]--><?php if(isset($file->source) && $file->source === 'talaq'): ?>
                                                                            <span class="badge bg-warning text-dark mt-1">Talaq</span>
                                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success fw-medium urdu-text"><?php echo e($file->sender); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="badge bg-info text-white"><?php echo e($file->ftype); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success urdu-text"><?php echo e($file->category); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <div class="d-flex flex-column">
                                                                            <span class="fw-bold text-primary"><?php echo e($file->checker); ?></span>
                                                                            <small class="text-muted">
                                                                                by: <?php echo e(!empty($file->transfer_by) ? $file->transfer_by : 'Mujeeb'); ?>

                                                                            </small>
                                                                        </div>
                                                                    </td>
                                                                    <td>
                                                                        <span class="text-success"><?php echo e($file->mail_recived_date); ?></span>
                                                                    </td>
                                                                    <td>
                                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $que_day_r; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <!--[if BLOCK]><![endif]--><?php if(Str::lower($file->file_code) == Str::lower($day->ifta_code)): ?>
                                                                                <?php
                                                                                    $recDate = new \DateTime($day->rec_date);
                                                                                    $currentDate = now();
                                                                                    $daysDifference = $currentDate->diff($recDate)->days;
                                                                                ?>
                                                                                <div class="d-flex flex-column">
                                                                                    <span class="text-primary fw-bold"><?php echo e($day->rec_date); ?></span>
                                                                                    <span class="badge bg-success"><?php echo e($daysDifference); ?> days</span>
                                                                                </div>
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                                    </td>
                                                                    <td>
                                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mahlenazar_null; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mahle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <!--[if BLOCK]><![endif]--><?php if($file->file_code == $mahle->file_code && $mahle->mail_folder_date < $file->mail_folder_date): ?>
                                                                                <span class="text-warning"><?php echo e($mahle->mail_folder_date); ?></span>
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                                    </td>
                                                                    <!--[if BLOCK]><![endif]--><?php if($isAdmin): ?>
                                                                        <td>
                                                                            <a href="<?php echo e(route('fatwa-detail', ['fatwa' => $file->file_code])); ?>"
                                                                               target="_blank" class="btn-modern btn-info-modern btn-sm" title="View Details">
                                                                                <i class="fas fa-info-circle action-icon action-details"></i>
                                                                            </a>
                                                                        </td>
                                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                    <td>
                                                                        <div class="d-flex gap-1 justify-content-center">
                                                                            <!-- View and Download Actions -->
                                                                            <!--[if BLOCK]><![endif]--><?php if(is_null($file->transfer_by) || empty($file->transfer_by)): ?>
                                                                                <a href="<?php echo e(route('viewRemain', [
                                                                                    'date' => $file->mail_folder_date . $file->darulifta_name . $file->checker,
                                                                                    'filename' => $file->file_name
                                                                                ])); ?>" target="_blank" class="btn-modern btn-primary-modern btn-sm" title="View File">
                                                                                    <i class="fas fa-file-alt action-icon action-view"></i>
                                                                                </a>
                                                                                <a href="<?php echo e(route('downloadFile', [
                                                                                    'date' => $file->mail_folder_date . $file->darulifta_name . $file->checker,
                                                                                    'filename' => $file->file_name,
                                                                                    'id' => $file->id
                                                                                ])); ?>" class="btn-modern btn-success-modern btn-sm" title="Download File">
                                                                                    <i class="fas fa-download action-icon action-download"></i>
                                                                                </a>
                                                                            <?php else: ?>
                                                                                <a href="<?php echo e(route('viewRemain', [
                                                                                    'date' => $file->mail_folder_date . $file->darulifta_name . $file->checker . '_by_' . $file->transfer_by,
                                                                                    'filename' => $file->file_name
                                                                                ])); ?>" target="_blank" class="btn-modern btn-primary-modern btn-sm" title="View File">
                                                                                    <i class="fas fa-file-alt action-icon action-view"></i>
                                                                                </a>
                                                                                <a href="<?php echo e(route('downloadFile', [
                                                                                    'date' => $file->mail_folder_date . $file->darulifta_name . $file->checker . '_by_' . $file->transfer_by,
                                                                                    'filename' => $file->file_name,
                                                                                    'id' => $file->id
                                                                                ])); ?>" class="btn-modern btn-success-modern btn-sm" title="Download File">
                                                                                    <i class="fas fa-download action-icon action-download"></i>
                                                                                </a>
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                                            <!-- Delete Action -->
                                                                            <?php
                                                                                $deleteFile = route('deleteFile', [
                                                                                    'mailfolderDates' => $file->mail_folder_date,
                                                                                    'daruliftaName' => $file->darulifta_name,
                                                                                    'checker' => $file->checker ?? '',
                                                                                    'transferby' => $file->transfer_by ?? ''
                                                                                ]);
                                                                                $deleteFileFormId = 'delete-file-form-' . $file->id;
                                                                                $downloadfileByadmin = $file->downloaded_by_admin;
                                                                                $canDeleteFile = is_null($downloadfileByadmin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                                                                            ?>

                                                                            <!--[if BLOCK]><![endif]--><?php if($canDeleteFile): ?>
                                                                                <button onclick="if (confirm('Are you sure you want to delete this file?')) { document.getElementById('<?php echo e($deleteFileFormId); ?>').submit(); }"
                                                                                        class="btn-modern btn-danger-modern btn-sm" title="Delete File">
                                                                                    <i class="fas fa-trash action-icon action-delete"></i>
                                                                                </button>
                                                                                <form id="<?php echo e($deleteFileFormId); ?>" action="<?php echo e($deleteFile); ?>" method="POST" class="d-none">
                                                                                    <?php echo csrf_field(); ?>
                                                                                    <?php echo method_field('DELETE'); ?>
                                                                                </form>
                                                                            <?php else: ?>
                                                                                <button class="btn-modern btn-danger-modern btn-sm opacity-50" disabled
                                                                                        title="Cannot delete, downloaded by admin on <?php echo e($downloadfileByadmin); ?>">
                                                                                    <i class="fas fa-trash action-icon action-delete"></i>
                                                                                </button>
                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                        </div>
                                                                    </td>
                                                                </tr>

                                                                <!-- Question Toggle and Content -->
                                                                <!--[if BLOCK]><![endif]--><?php if($showQue): ?>
                                                                    <tr x-data="{ openQuestion: true }">
                                                                        <td colspan="1" @click="openQuestion = !openQuestion" class="align-middle text-center cursor-pointer" style="background-color: #FFDDCC;">
                                                                            Question <span x-text="openQuestion ? '◀' : '▶'"></span>
                                                                        </td>
                                                                        <td x-show="openQuestion" @click.outside="openQuestion = false" colspan="<?php echo e($isAdmin ? '9' : '8'); ?>" style="background-color: #ffffff;">
                                                                            <!-- Dynamic Content Here -->
                                                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $que_day_r; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                <!--[if BLOCK]><![endif]--><?php if(Str::lower($file->file_code) == Str::lower($day->ifta_code)): ?>
                                                                                    <div class="question-text">سوال: <?php echo e($day->question); ?></div>
                                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                                        </td>
                                                                    </tr>
                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                                                <!--[if BLOCK]><![endif]--><?php if($showChat): ?>
                                                                    <!-- Chat Toggle and Content -->
                                                                    <tr x-data="{ openChat: true }">
                                                                        <td colspan="1" @click="openChat = !openChat" class="align-middle text-center cursor-pointer toggle-chat right-aligned" data-section="chat" style="background-color: #FFDDCC;">
                                                                            Chat <span x-text="openChat ? '◀' : '▶'"></span>
                                                                        </td>
                                                                        <td x-show="openChat" colspan="<?php echo e($isAdmin ? '9' : '8'); ?>" class="align-middle text-center">
                                                                            <div class="d-flex justify-content-center align-items-center chat-container">
                                                                                <div class="col-md-6 col-lg-7 col-xl-8">
                                                                                    <ul class="list-unstyled">
                                                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $messages->sortBy('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                            <!--[if BLOCK]><![endif]--><?php if($message->ifta_code == $file->file_code): ?>
                                                                                                <!--[if BLOCK]><![endif]--><?php if($message->user_id == auth()->user()->id): ?>
                                                                                                    <li class="d-flex justify-content-between mb-4">
                                                                                                        <span class="badge rounded-circle bg-success d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                                                                                            <?php echo e(strtoupper(substr($message->user_name, 0, 1))); ?>

                                                                                                        </span>
                                                                                                        <div class="card w-auto">
                                                                                                            <div class="card-header d-flex justify-content-between p-3">
                                                                                                                <p class="fw-bold mb-0">
                                                                                                                    <?php echo e($message->user_name); ?>

                                                                                                                </p>
                                                                                                                <p class="text-muted small mb-0">
                                                                                                                    <i class="far fa-clock"></i> <?php echo e($message->created_at); ?>

                                                                                                                </p>
                                                                                                            </div>
                                                                                                            <div class="card-body" style="background-color:#ADD8E6;">
                                                                                                                <p class="mb-0">
                                                                                                                    <?php echo e($message->message); ?>

                                                                                                                </p>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </li>
                                                                                                <?php else: ?>
                                                                                                    <!-- Unauthenticated / Other users messages. -->
                                                                                                    <li class="d-flex justify-content-between mb-4">
                                                                                                        <div class="card w-auto">
                                                                                                            <div class="card-header d-flex justify-content-between p-3">
                                                                                                                <p class="fw-bold mb-0">
                                                                                                                    <?php echo e($message->user_name); ?>

                                                                                                                </p>
                                                                                                                <p class="text-muted small mb-0">
                                                                                                                    <i class="far fa-clock"></i> <?php echo e($message->created_at); ?>

                                                                                                                </p>
                                                                                                            </div>
                                                                                                            <div class="card-body">
                                                                                                                <p class="mb-0">
                                                                                                                    <?php echo e($message->message); ?>

                                                                                                                </p>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <span class="badge rounded-circle bg-primary d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                                                                                            <?php echo e(strtoupper(substr($message->user_name, 0, 1))); ?>

                                                                                                        </span>
                                                                                                    </li>
                                                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                                                                        <div class="bg-light">
                                                                                            <div class="input-group">
                                                                                                <input wire:model="message" type="text" placeholder="Type a message" aria-describedby="button-addon2" class="form-control rounded-0 border-0 py-4 bg-light text-end">
                                                                                                <div class="input-group-append">
                                                                                                    <button id="button-addon2" class="btn btn-link" wire:click="sendMessage('<?php echo e($file->file_code); ?>')"> <i class="fa fa-paper-plane"></i></button>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php else: ?>
                                                <div class="text-center py-4">
                                                    <i class="fas fa-info-circle text-muted me-2"></i>
                                                    <span class="text-muted">Enable "Load Fatawa Detail" to view detailed information</span>
                                                </div>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-details.blade.php ENDPATH**/ ?>