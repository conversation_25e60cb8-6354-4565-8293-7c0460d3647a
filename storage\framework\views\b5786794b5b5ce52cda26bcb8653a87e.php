<?php
    $Admin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
?>

<!-- Navigation Buttons (Admin Only) -->
<!--[if BLOCK]><![endif]--><?php if($Admin): ?>
    <div class="modern-card mb-4">
        <div class="modern-card-body">
            <h6 class="mb-3">
                <i class="fas fa-sitemap me-2"></i>
                Quick Navigation
            </h6>
            <div class="d-flex flex-wrap gap-2">
                <?php
                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                    $isAllIftaActive = request()->route('darulifta') == null;
                ?>
                
                <a href="<?php echo e(route('remaining-fatawa', [
                    'selectedmufti' => $this->selectedmufti,
                    'selectedTimeFrame' => $this->selectedTimeFrame,
                    'startDate' => $tempStartDate,
                    'endDate' => $tempEndDate,
                    'showDetail' => $showDetail ? '1' : '0',
                    'showQue' => $showQue ? '1' : '0',
                    'showChat' => $showChat ? '1' : '0',
                    'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                ])); ?>" 
                   class="btn-modern <?php echo e($isAllIftaActive ? 'btn-primary-modern' : 'btn-outline-modern'); ?>">
                    <i class="fas fa-globe me-2"></i>
                    All Ifta
                </a>

                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $daruliftalist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftalistn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $isActive = request()->route('darulifta') == $daruliftalistn;
                    ?>
                    <a href="<?php echo e(route('remaining-fatawa', [
                        'darulifta' => $daruliftalistn,
                        'selectedmufti' => $this->selectedmufti,
                        'selectedTimeFrame' => $this->selectedTimeFrame,
                        'startDate' => $tempStartDate,
                        'endDate' => $tempEndDate,
                        'showDetail' => $showDetail ? '1' : '0',
                        'showQue' => $showQue ? '1' : '0',
                        'showChat' => $showChat ? '1' : '0',
                        'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                    ])); ?>" 
                       class="btn-modern <?php echo e($isActive ? 'btn-primary-modern' : 'btn-outline-modern'); ?>">
                        <i class="fas fa-building me-2"></i>
                        <?php echo e($daruliftalistn); ?>

                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-navigation.blade.php ENDPATH**/ ?>