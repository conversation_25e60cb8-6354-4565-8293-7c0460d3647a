<?php

namespace App\Http\Controllers;

use App\Models\DailyPerformance;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Carbon\Carbon;

class DailyPerformanceController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display the daily performance form.
     */
    public function create()
    {
        $this->authorize('submit-performance');
        
        return view('daily-performance.create');
    }

    /**
     * Store a newly created performance report.
     */
    public function store(Request $request)
    {
        $this->authorize('submit-performance');
        
        $validated = $request->validate([
            'performance_date' => 'required|date',
            'tasks_completed' => 'required|string',
            'challenges_faced' => 'nullable|string',
            'next_day_plan' => 'nullable|string',
            'fatawa_processed' => 'required|integer|min:0',
            'questions_answered' => 'required|integer|min:0',
            'hours_worked' => 'required|numeric|min:0|max:24',
            'overall_rating' => 'required|in:poor,fair,good,excellent',
            'additional_notes' => 'nullable|string',
        ]);

        $validated['user_id'] = auth()->id();
        $validated['is_submitted'] = true;
        $validated['submitted_at'] = Carbon::now();

        DailyPerformance::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'performance_date' => $validated['performance_date'],
            ],
            $validated
        );

        return redirect()->route('dashboard')
            ->with('success', 'Daily performance report submitted successfully!');
    }

    /**
     * Display performance management for supervisors/admins.
     */
    public function index()
    {
        $this->authorize('view-all-performance');
        
        return view('daily-performance.index');
    }

    /**
     * Display the specified performance report.
     */
    public function show(DailyPerformance $dailyPerformance)
    {
        $this->authorize('viewPerformance', $dailyPerformance->user);
        
        return view('daily-performance.show', compact('dailyPerformance'));
    }

    /**
     * Show the form for editing the performance report.
     */
    public function edit(DailyPerformance $dailyPerformance)
    {
        $this->authorize('viewPerformance', $dailyPerformance->user);
        
        // Only allow editing if not submitted or user is admin
        if ($dailyPerformance->is_submitted && !auth()->user()->isNazim()) {
            return redirect()->route('daily-performance.show', $dailyPerformance)
                ->with('error', 'Cannot edit submitted performance report.');
        }
        
        return view('daily-performance.edit', compact('dailyPerformance'));
    }

    /**
     * Update the specified performance report.
     */
    public function update(Request $request, DailyPerformance $dailyPerformance)
    {
        $this->authorize('viewPerformance', $dailyPerformance->user);
        
        $validated = $request->validate([
            'tasks_completed' => 'required|string',
            'challenges_faced' => 'nullable|string',
            'next_day_plan' => 'nullable|string',
            'fatawa_processed' => 'required|integer|min:0',
            'questions_answered' => 'required|integer|min:0',
            'hours_worked' => 'required|numeric|min:0|max:24',
            'overall_rating' => 'required|in:poor,fair,good,excellent',
            'additional_notes' => 'nullable|string',
        ]);

        $dailyPerformance->update($validated);

        return redirect()->route('daily-performance.show', $dailyPerformance)
            ->with('success', 'Performance report updated successfully.');
    }

    /**
     * Get performance statistics.
     */
    public function statistics()
    {
        $this->authorize('view-all-performance');
        
        $user = auth()->user();
        $query = DailyPerformance::query();
        
        // Apply user restrictions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->departments->flatMap->users->pluck('id');
                $userIds = $assistantIds->merge($departmentUserIds)->unique();
                $query->whereIn('user_id', $userIds);
            }
        }

        $stats = [
            'total_reports' => $query->count(),
            'submitted_today' => $query->where('performance_date', Carbon::today())->where('is_submitted', true)->count(),
            'pending_today' => $query->where('performance_date', Carbon::today())->where('is_submitted', false)->count(),
            'avg_hours_worked' => round($query->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
            'avg_fatawa_processed' => round($query->where('is_submitted', true)->avg('fatawa_processed') ?? 0, 1),
            'excellent_ratings' => $query->where('overall_rating', 'excellent')->where('is_submitted', true)->count(),
            'this_week_submitted' => $query->whereBetween('performance_date', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->where('is_submitted', true)->count(),
            'this_month_submitted' => $query->whereMonth('performance_date', Carbon::now()->month)
                ->whereYear('performance_date', Carbon::now()->year)
                ->where('is_submitted', true)->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get user's performance history.
     */
    public function userHistory(User $user)
    {
        $this->authorize('viewPerformance', $user);
        
        $performances = DailyPerformance::where('user_id', $user->id)
            ->orderBy('performance_date', 'desc')
            ->paginate(15);

        return view('daily-performance.user-history', compact('user', 'performances'));
    }

    /**
     * Send reminder to users who haven't submitted today's performance.
     */
    public function sendReminders()
    {
        $this->authorize('view-all-performance');
        
        $today = Carbon::today();
        $submittedUserIds = DailyPerformance::where('performance_date', $today)
            ->where('is_submitted', true)
            ->pluck('user_id');

        $usersToRemind = User::whereNotIn('id', $submittedUserIds)
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
            })
            ->get();

        // Here you would implement the actual reminder logic (email, notification, etc.)
        $reminderCount = $usersToRemind->count();

        return response()->json([
            'message' => "Reminders sent to {$reminderCount} users.",
            'users_reminded' => $reminderCount,
        ]);
    }

    /**
     * Check if user has submitted today's performance.
     */
    public function checkTodaysSubmission()
    {
        $hasSubmitted = DailyPerformance::isSubmittedForToday(auth()->id());
        
        return response()->json([
            'has_submitted' => $hasSubmitted,
            'date' => Carbon::today()->format('Y-m-d'),
        ]);
    }

    /**
     * Get my performance reports.
     */
    public function myReports()
    {
        $performances = DailyPerformance::where('user_id', auth()->id())
            ->orderBy('performance_date', 'desc')
            ->paginate(15);

        return view('daily-performance.my-reports', compact('performances'));
    }
}
