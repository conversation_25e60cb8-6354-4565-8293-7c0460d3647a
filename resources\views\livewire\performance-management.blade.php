<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Reports</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['total_reports'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="ni ni-chart-bar-32 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Submitted Today</p>
                                <h5 class="font-weight-bolder mb-0 text-success">{{ $statistics['submitted_today'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                <i class="ni ni-check-bold text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Pending Today</p>
                                <h5 class="font-weight-bolder mb-0 text-warning">{{ $statistics['pending_today'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="ni ni-time-alarm text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Avg Hours</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['avg_hours_worked'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                <i class="ni ni-watch-time text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Avg Hours</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['avg_hours_worked'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md">
                                <i class="ni ni-time-alarm text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Excellent</p>
                                <h5 class="font-weight-bolder mb-0 text-success">{{ $statistics['excellent_ratings'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                <i class="ni ni-trophy text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Performance Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Performance Management</h5>
                            <p class="text-sm mb-0">Monitor and manage daily performance reports</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <button wire:click="generateMissingReports" class="btn btn-outline-info btn-sm mb-0 me-2">
                                    <i class="fas fa-plus"></i>&nbsp;&nbsp;Generate Missing
                                </button>
                                <button wire:click="exportReport" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-download"></i>&nbsp;&nbsp;Export Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Date</label>
                                <input wire:model.live="selectedDate" type="date" class="form-control form-control-sm">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">User</label>
                                <select wire:model.live="selectedUser" class="form-select form-select-sm">
                                    <option value="all">All Users</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Department</label>
                                <select wire:model.live="selectedDepartment" class="form-select form-select-sm">
                                    <option value="all">All Departments</option>
                                    @foreach($departments as $department)
                                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Status</label>
                                <select wire:model.live="selectedStatus" class="form-select form-select-sm">
                                    <option value="all">All Status</option>
                                    <option value="submitted">Submitted</option>
                                    <option value="pending">Pending</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label class="form-label text-xs">Rating</label>
                                <select wire:model.live="selectedRating" class="form-select form-select-sm">
                                    <option value="all">All Ratings</option>
                                    <option value="excellent">Excellent</option>
                                    <option value="good">Good</option>
                                    <option value="fair">Fair</option>
                                    <option value="poor">Poor</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Table -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hours</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($performances as $performance)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $performance->user->name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $performance->user->email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-xs font-weight-bold mb-0">{{ $performance->performance_date->format('M d, Y') }}</p>
                                        @if($performance->submitted_at)
                                            <p class="text-xs text-secondary mb-0">{{ $performance->submitted_at->format('h:i A') }}</p>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center text-sm">
                                        <span class="badge badge-sm bg-gradient-{{ $this->getStatusColor($performance->is_submitted) }}">
                                            {{ $performance->is_submitted ? 'Submitted' : 'Pending' }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->overall_rating)
                                            <span class="badge badge-sm bg-gradient-{{ $this->getRatingColor($performance->overall_rating) }}">
                                                {{ ucfirst($performance->overall_rating) }}
                                            </span>
                                        @else
                                            <span class="text-secondary">-</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $performance->hours_worked ?? '-' }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button wire:click="openViewModal({{ $performance->id }})" 
                                                    class="btn btn-sm btn-outline-info mb-0" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if(!$performance->is_submitted)
                                                <button wire:click="sendReminder({{ $performance->user_id }})"
                                                        class="btn btn-sm btn-outline-warning mb-0" title="Send Reminder">
                                                    <i class="fas fa-bell"></i>
                                                </button>
                                                @can('view-all-performance')
                                                    <button wire:click="openResetModal({{ $performance->user_id }})"
                                                            class="btn btn-sm btn-outline-danger mb-0" title="Reset Access">
                                                        <i class="fas fa-unlock"></i>
                                                    </button>
                                                @endcan
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <p class="text-secondary mb-0">No performance records found.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="px-4">
                        {{ $performances->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- View Performance Modal -->
    @if($showViewModal && $selectedPerformance)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Performance Report Details</h5>
                    <button type="button" class="btn-close" wire:click="closeModal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-primary">{{ $selectedPerformance->user->name }}</h6>
                            <p class="text-sm text-secondary mb-0">{{ $selectedPerformance->user->email }}</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <span class="badge badge-lg bg-gradient-{{ $this->getStatusColor($selectedPerformance->is_submitted) }}">
                                {{ $selectedPerformance->is_submitted ? 'Submitted' : 'Pending' }}
                            </span>
                            @if($selectedPerformance->overall_rating)
                                <span class="badge badge-lg bg-gradient-{{ $this->getRatingColor($selectedPerformance->overall_rating) }} ms-2">
                                    {{ ucfirst($selectedPerformance->overall_rating) }}
                                </span>
                            @endif
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Date</small>
                                <p class="mb-0">{{ $selectedPerformance->performance_date->format('F d, Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Hours Worked</small>
                                <p class="mb-0">{{ $selectedPerformance->hours_worked ?? 'Not specified' }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Task</small>
                                <p class="mb-0">{{ $selectedPerformance->task->title ?? 'No task assigned' }}</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Department</small>
                                <p class="mb-0">{{ $selectedPerformance->department->name ?? 'No department' }}</p>
                            </div>
                        </div>
                    </div>

                    @if($selectedPerformance->tasks_completed)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Tasks Completed</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->tasks_completed }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->challenges_faced)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Challenges Faced</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->challenges_faced }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->next_day_plan)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Next Day Plan</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->next_day_plan }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->additional_notes)
                    <div class="info-item mb-3">
                        <small class="text-uppercase text-secondary font-weight-bold">Additional Notes</small>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0 text-sm">{{ $selectedPerformance->additional_notes }}</p>
                        </div>
                    </div>
                    @endif

                    @if($selectedPerformance->submitted_at)
                    <div class="info-item">
                        <small class="text-uppercase text-secondary font-weight-bold">Submitted At</small>
                        <p class="mb-0">{{ $selectedPerformance->submitted_at->format('F d, Y h:i A') }}</p>
                    </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModal">Close</button>
                    @if(!$selectedPerformance->is_submitted)
                        <button type="button" class="btn btn-warning" wire:click="sendReminder({{ $selectedPerformance->user_id }})">
                            <i class="fas fa-bell"></i> Send Reminder
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Reset Access Modal -->
    @if($showResetModal && $selectedUserForReset)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-unlock text-danger me-2"></i>
                        Reset User Access
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeResetModal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning" role="alert">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Confirm Access Reset
                        </h6>
                        <p class="mb-3">
                            You are about to reset access for <strong>{{ $selectedUserForReset->name }}</strong>.
                        </p>
                        <hr>
                        <p class="mb-0">
                            This will:
                        </p>
                        <ul class="mb-0">
                            <li>Remove any active performance restrictions</li>
                            <li>Create or mark today's performance as submitted</li>
                            <li>Allow the user to access the system immediately</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <label for="resetReason" class="form-label">Reason for Reset (Optional)</label>
                        <textarea wire:model="resetReason"
                                  id="resetReason"
                                  class="form-control"
                                  rows="3"
                                  placeholder="Enter reason for resetting user access..."></textarea>
                    </div>

                    <div class="user-info mt-3">
                        <div class="card bg-light">
                            <div class="card-body p-3">
                                <h6 class="mb-2">User Information</h6>
                                <p class="mb-1"><strong>Name:</strong> {{ $selectedUserForReset->name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $selectedUserForReset->email }}</p>
                                <p class="mb-0">
                                    <strong>Role:</strong>
                                    @if($selectedUserForReset->isSuperior())
                                        <span class="badge bg-success">Superior</span>
                                    @elseif($selectedUserForReset->isMujeeb())
                                        <span class="badge bg-primary">Mujeeb</span>
                                    @else
                                        <span class="badge bg-secondary">Other</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeResetModal">Cancel</button>
                    <button type="button" class="btn btn-danger" wire:click="resetUserAccess">
                        <i class="fas fa-unlock me-2"></i>
                        Reset Access
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
