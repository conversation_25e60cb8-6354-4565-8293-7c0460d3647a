<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('talaq_fatawa_manage', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('file_name', 255)->collation('utf8mb4_unicode_ci');
            $table->string('file_code', 255)->collation('utf8mb4_unicode_ci');
            $table->string('sender', 255)->collation('utf8mb4_unicode_ci');
            $table->string('file_created_date', 255)->collation('utf8mb4_unicode_ci');
            $table->enum('ftype', ['New', 'Mahl e Nazar'])->collation('utf8mb4_unicode_ci');
            $table->date('mail_folder_date');
            $table->dateTime('mail_recived_date')->nullable();
            $table->string('darulifta_name', 255)->collation('utf8mb4_unicode_ci');
            $table->string('category', 255)->collation('utf8mb4_unicode_ci');
            $table->boolean('selected')->default(0);
            $table->integer('deliver')->nullable()->default(0);
            $table->dateTime('deliver_date')->nullable();
            $table->string('not_deliver_reason', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('checked_folder', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->date('checked_date')->nullable();
            $table->string('checked_file_name', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('checked_grade', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('checked_tasurat', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('checked_Instructions', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->timestamps();
            $table->string('checker', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('transfer_by', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->dateTime('downloaded_by_admin')->nullable();
            $table->string('by_mufti', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->boolean('viral')->nullable()->default(0);
            $table->string('transfer_to', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('viral_upload', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('web_link', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->string('viral_link', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->date('shoba_viral')->nullable();
            $table->string('title', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->date('web_date')->nullable();
            $table->date('viral_date')->nullable();
            $table->integer('talaq_fatawa_id')->nullable();
            $table->integer('talaq_checked_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('talaq_fatawa_manage');
    }
};
