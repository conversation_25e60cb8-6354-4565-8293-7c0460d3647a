<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;

class CheckerController extends Controller
{
    public function index()
    {
    $daruliftas = DB::table('daruliftas')->select('id','darul_name')->get();
    $checker = DB::table('checker')->get();
    return view('pages.checker',compact('daruliftas','checker'));

}
public function store(Request $request)
{
    // Validate the incoming request data
    $validatedData = $request->validate([
        'checker_name' => 'required|string',
        'darul_name' => 'required|array',
        'darul_name.*' => 'required|string', // This validates each item in the array
        'munsab' => 'required|string',
        'folder_id' => 'required|string',
    ]);

    // If your database schema expects a JSON string for 'darul_name', you can json_encode the array.
    // If it expects a single string (e.g., comma-separated values), you can implode the array.
    // Example for JSON string:
    // $darulNames = json_encode($validatedData['darul_name']);

    // Example for comma-separated string:
    $darulNames = implode(', ', $validatedData['darul_name']);

    // Insert the validated data into the 'checker' table
    DB::table('checker')->insert([
        'checker_name' => $validatedData['checker_name'],
        // Use the processed $darulNames here
        'darul_name' => $darulNames,
        'munsab' => $validatedData['munsab'],
        'folder_id' => $validatedData['folder_id'],
    ]);

    // Redirect the user back with a success message
    return redirect()->route('checker')->with('success', 'Checker added successfully.');
}
public function edit($id)
    {
        $upchecker = DB::table('checker')->find($id);
        $daruliftas = DB::table('daruliftas')->select('id', 'darul_name')->get();

        return view('pages.edit_checker', compact('upchecker', 'daruliftas'));
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'checker_name' => 'required',
            'darul_name' => 'required',
            'munsab' => 'required',
        ]);

        DB::table('checker')
            ->where('id', $id)
            ->update([
                'checker_name' => $validatedData['checker_name'],
                'darul_name' => $validatedData['darul_name'],
                'munsab' => $validatedData['munsab'],
            ]);

        return redirect()->route('checker')->with('success', 'Checker updated successfully');
    }

    public function deleteChecker($id): RedirectResponse
    {
        $mujeeb = DB::table('checker')->where('id', $id)->first();

        if (!$mujeeb) {
            return redirect()->route('checker')->with('error', 'Checker not found');
        }

        DB::table('checker')->where('id', $id)->delete();

        return redirect()->route('checker')->with('success', 'Checker deleted successfully');
    }
}