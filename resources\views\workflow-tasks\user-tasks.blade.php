@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">User Tasks</h5>
                            <p class="text-sm mb-0">Tasks assigned to {{ $user->name }}</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <a href="{{ route('workflow-tasks.index') }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to Tasks
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">User Information</h6>
                            <p class="mb-1"><strong>Name:</strong> {{ $user->name }}</p>
                            <p class="mb-1"><strong>Email:</strong> {{ $user->email }}</p>
                            <p class="mb-1"><strong>Role:</strong> 
                                @if($user->isSuperior())
                                    <span class="badge bg-primary">Superior</span>
                                @elseif($user->isMujeeb())
                                    <span class="badge bg-info">Mujeeb</span>
                                @elseif($user->isNazim())
                                    <span class="badge bg-success">Nazim</span>
                                @else
                                    <span class="badge bg-secondary">User</span>
                                @endif
                            </p>
                            @if($user->departments->count() > 0)
                                <p class="mb-1"><strong>Departments:</strong>
                                    @foreach($user->departments as $department)
                                        <span class="badge bg-light text-dark me-1">{{ $department->name }}</span>
                                    @endforeach
                                </p>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">Task Statistics</h6>
                            <p class="mb-1"><strong>Total Tasks:</strong> {{ $tasks->total() }}</p>
                            <p class="mb-1"><strong>Pending:</strong> {{ $tasks->where('status', 'pending')->count() }}</p>
                            <p class="mb-1"><strong>In Progress:</strong> {{ $tasks->where('status', 'in_progress')->count() }}</p>
                            <p class="mb-1"><strong>Completed:</strong> {{ $tasks->where('status', 'completed')->count() }}</p>
                            <p class="mb-1"><strong>Overdue:</strong> {{ $tasks->filter(function($task) { return $task->isOverdue(); })->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Assigned Tasks</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Department</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Type</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($tasks as $task)
                                <tr class="{{ $task->isOverdue() ? 'table-warning' : '' }}">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                                @if($task->isOverdue())
                                                    <span class="badge badge-sm bg-gradient-danger">Overdue</span>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $task->department->name ?? 'No Department' }}</h6>
                                            @if($task->assignedBy)
                                                <p class="text-xs text-secondary mb-0">Assigned by: {{ $task->assignedBy->name }}</p>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary') }}">
                                            {{ ucfirst($task->type) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($task->due_date)
                                            <span class="text-secondary text-xs font-weight-bold">
                                                {{ $task->due_date->format('M d, Y') }}
                                            </span>
                                            @if($task->isOverdue())
                                                <br><small class="text-danger">{{ $task->due_date->diffForHumans() }}</small>
                                            @else
                                                <br><small class="text-muted">{{ $task->due_date->diffForHumans() }}</small>
                                            @endif
                                        @else
                                            <span class="text-secondary text-xs">No due date</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->status === 'completed' ? 'success' : ($task->status === 'in_progress' ? 'info' : ($task->status === 'cancelled' ? 'secondary' : 'warning')) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <a href="{{ route('workflow-tasks.show', $task) }}" 
                                               class="btn btn-sm btn-outline-info mb-0" 
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('update', $task)
                                                <a href="{{ route('workflow-tasks.edit', $task) }}" 
                                                   class="btn btn-sm btn-outline-primary mb-0" 
                                                   title="Edit Task">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-tasks text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted">No Tasks Found</h6>
                                            <p class="text-sm text-muted mb-0">This user doesn't have any tasks assigned yet.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    @if($tasks->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $tasks->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
