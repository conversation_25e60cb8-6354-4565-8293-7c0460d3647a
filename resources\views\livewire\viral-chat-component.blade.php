
    <div class="d-flex justify-content-center align-items-center" wire:poll.2000ms>
        <div class="col-md-6 col-lg-7 col-xl-8">
            <ul class="list-unstyled">
            @foreach ($messages->sortBy('created_at') as $message)
                    @if($message->ifta_code == $file_code)
                        @if ($message->user_id == auth()->user()->id)
                            <li class="d-flex justify-content-between mb-4">
                                <span class="badge rounded-circle bg-success d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                    {{ strtoupper(substr($message->user_name, 0, 1)) }}
                                </span>
                                <div class="card" style="width: 300px; word-wrap: break-word; overflow-wrap: break-word; padding: 0; margin: 0; border: none;">
                                    <div class="card-header d-flex justify-content-between p-2" style="overflow-wrap: break-word; padding: 5px; margin: 0; background-color: #f8f9fa; border: none;">
                                        <p class="fw-bold mb-0" style="margin: 0; font-weight: bold; font-size: 14px;">
                                            {{ $message->user_name }}
                                            <!-- Show blue tick if the message has been seen -->

                                        </p>
                                        <p class="text-muted small mb-0" style="margin: 0; font-size: 12px; color: gray;">
                                            <i class="far fa-clock"></i> {{ $message->created_at }}
                                            @if($message->seen_by_user)
                                                <span style="color: blue;"><i class="fa fa-check-double"></i></span>
                                            @endif
                                            @php
                                                $timeDiff = \Carbon\Carbon::parse($message->created_at)->diffInMinutes(\Carbon\Carbon::now());
                                            @endphp

                                            <!-- Show the delete icon only if the message is within 60 minutes of creation -->
                                            @if($timeDiff <= 60)
                                                <button
                                                    wire:click="deleteMessage({{ $message->id }})"
                                                    class="btn btn-link btn-sm"
                                                    style="color: blue; padding: 0; text-decoration: none; font-size: 12px;">
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            @endif
                                        </p>
                                    </div>
                                    <div class="card-body" style="background-color: #ADD8E6; padding: 5px; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; margin: 0;">
                                        <p class="mb-0" style="margin: 0; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-size: 14px; line-height: 1.2;">
                                            {{ $message->message }}


                                        </p>
                                    </div>
                                </div>



                            </li>
                        @else
                            <li class="d-flex justify-content-between mb-4">
                            <div class="card" style="width: 300px; word-wrap: break-word; overflow-wrap: break-word; padding: 0; margin: 0; border: none;">
                                    <div class="card-header d-flex justify-content-between p-2" style="overflow-wrap: break-word; padding: 5px; margin: 0; background-color: #f8f9fa; border: none;">
                                        <p class="fw-bold mb-0" style="margin: 0; font-weight: bold; font-size: 14px;">
                                            {{ $message->user_name }}
                                            <!-- Show blue tick if the message has been seen -->

                                        </p>
                                        <p class="text-muted small mb-0" style="margin: 0; font-size: 12px; color: gray;">
                                            <i class="far fa-clock"></i> {{ $message->created_at }}

                                        </p>
                                    </div>
                                    <div class="card-body" style="background-color: #E6E6FA; padding: 5px; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; margin: 0;">
                                        <p class="mb-0" style="margin: 0; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-size: 14px; line-height: 1.2;">
                                            {{ $message->message }}


                                        </p>
                                    </div>
                                </div>
                                <span class="badge rounded-circle bg-primary d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                    {{ strtoupper(substr($message->user_name, 0, 1)) }}
                                </span>
                            </li>
                        @endif
                    @endif
                @endforeach

                <div class="bg-light">
                <div class="input-group">
                    <input
                        id="message-input"
                        wire:model.defer="inputmessage"
                        wire:keydown.enter="sendMessage('{{ $file_code }}')"
                        onkeydown="handleEnter(event)"
                        type="text"
                        placeholder="Type a message"
                        aria-describedby="button-addon2"
                        class="form-control rounded-0 border-0 py-4 bg-light text-end"
                        dir="rtl">

                    <div class="input-group-append">
                        <button
                            id="button-addon2"
                            class="btn btn-link"
                            wire:click="sendMessage('{{ $file_code }}')"
                            onclick="clearInput()">
                            <i class="fa fa-paper-plane"></i>
                        </button>
                    </div>
                </div>

            </ul>
        </div>
    </div>
    <script>
   function clearInput() {
       const inputElement = document.getElementById('message-input');
       if (inputElement) {
           inputElement.value = '';
       }
   }

   function handleEnter(event) {
       if (event.key === "Enter") {
           event.preventDefault(); // Prevent default form submission
           document.getElementById('button-addon2').click(); // Trigger the button click
       }
   }

   document.getElementById('button-addon2').addEventListener('click', () => {
       const inputElement = document.getElementById('message-input');
       if (inputElement) {
           inputElement.value = '';
       }
   });

   document.addEventListener('livewire:update', () => {
       clearInput();
   });
</script>
