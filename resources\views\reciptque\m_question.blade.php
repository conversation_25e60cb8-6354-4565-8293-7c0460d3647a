<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="mujeeb"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->


        <x-navbars.navs.auth titlePage="question_list"></x-navbars.navs.auth>
        <style>
        table{
            font-family: <PERSON>eel <PERSON>;

          }
          </style>
        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
            <!-- Link to create Fatawa entry -->
            <div class="container">

                <!-- Link to view Fatawa entries -->
                <meta name="csrf-token" content="{{ csrf_token() }}">
                <a href="{{ route('reciptque.index') }}" class="btn btn-primary">Add Question</a>
                     <!-- You can add more content here as needed -->
                     <div class="container">
                        <h1>Question List</h1>
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>S.No</th>
                                    <th>Daily/Email</th>
                                    <th>Fatwa No</th>
                                    <th>Question Title</th>\
                                    <th>Category</th>
                                    <th>Send To Mujeeb</th>
                                    <th>Action</th>

                                </tr>
                            </thead>
                            <tbody>
                                @php
                                $serialNumber = 1; // Initialize the serial number
                                $questonss = (count(Auth::user()->roles) > 1) ? $questions : $question_b;
                                @endphp
                                @foreach($questonss as $question)
                                <tr class="question-row" data-id="{{ $question->id }}">
                                    <td>{{ $serialNumber++ }}</td> {{-- Increment the serial number --}}
                                    <td>{{ $question->question_type }}</td>
                                    <td>{{ $question->ifta_code }}</td>
                                    <td>{{ $question->question_title }}</td>
                                    <td>{{ $question->issue }}</td>
                                    <td style="width: 15%;">
                                        <select class="mujeeb-select" data-question-id="{{ $question->id }}">
                                            <option value="">Select Mujeeb</option>
                                            @foreach($mujeebs as $mujeeb)
                                                <option value="{{ $mujeeb->mujeeb_name }}"
                                                    @if($question->assign_id == $mujeeb->mujeeb_name)
                                                        selected
                                                    @endif
                                                >
                                                    {{ $mujeeb->mujeeb_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </td>
                                    <td>
                                        <a href="{{ route('reciptque.edit', ['id' => $question->id]) }}" class="btn btn-sm btn-warning">Edit</a>
                                        <form action="{{ route('reciptque.destroy', ['id' => $question->id]) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this entry?')">Delete</button>
                                        </form>
                                    </td>

                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
            </div>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
            <script>
                $(document).ready(function () {
                  $('#myTable').DataTable();

                });
              </script>
              <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

              <script>
                 document.addEventListener("DOMContentLoaded", function() {
    // Your JavaScript code here
    const mujeebSelects = document.querySelectorAll('.mujeeb-select');

    mujeebSelects.forEach(select => {
        select.addEventListener('change', async function() {
            const questionId = this.getAttribute('data-question-id');
            const selectedMujeebId = this.value;

            try {
                // Include the CSRF token in the request headers
                const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                const response = await axios.post(`/update-assign-id/${questionId}`, { selectedMujeebId }, {
                    headers: {
                        'X-CSRF-TOKEN': csrfToken,
                    },
                });

                if (response.data.success) {
                    // You can update the view to reflect the change if needed.
                    // For example, show the selected Mujeeb's name next to the dropdown.
                }
            } catch (error) {
                console.error(error);
            }
        });
    });
});
              </script>
         <script>
    document.addEventListener('DOMContentLoaded', function () {
        const buttons = document.querySelectorAll('.send-to-editor');

        buttons.forEach(button => {
            button.addEventListener('click', function () {
                const questionId = this.getAttribute('data-id');

                fetch(`/questions/${questionId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.error) {
                            alert(data.error);
                        } else {
                            // Redirect to editor page with data
                            const editorUrl = `/talaq-fatawa-manage?rec_date=${encodeURIComponent(data.rec_date)}&ifta_code=${encodeURIComponent(data.ifta_code)}&question=${encodeURIComponent(data.question)}&assign_id=${encodeURIComponent(data.assign_id)}&sayel=${encodeURIComponent(data.sayel)}&issue=${encodeURIComponent(data.issue)}&question_branch=${encodeURIComponent(data.question_branch)}`;
                            window.location.href = editorUrl;
                        }
                    })
                    .catch(error => {
                        console.error('There was a problem with the fetch operation:', error);
                    });
            });
        });
    });
</script>
            <x-footers.auth></x-footers.auth>
        </div>
    </main>
    <x-plugins></x-plugins>

</x-layout>
