<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\ViralChat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use DateTime;



class SelectViral extends Component
{
    use WithPagination;

    public $filter;
    public $search;
    public $daruliftaNames;
    public $darulifta;
    public $daruliftalist;




    protected $queryString = [
        'filter' => ['except' => ''],
        'search' => ['except' => ''],
    ];

    public function mount($filter = null, $darulifta = null)
    {
        $this->filter = $filter;
        $this->darulifta = $darulifta;
        $this->loadDaruliftaNames();


    }
    private function loadDaruliftaNames()
{
    $user = Auth::user();
    $userRoles = $user->roles;
    $roleNames = $userRoles->pluck('name')->toArray();
    $firstRoleName = reset($roleNames);

    if ($this->darulifta === null) {
        // Check if user has more than one role OR if one of the roles is Nazim_Viral
        if (count($userRoles) > 1 || in_array('Nazim_Viral', $roleNames)) {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                ->select('uploaded_files.darulifta_name')
                ->distinct()
                ->orderBy('daruliftas.id')
                ->pluck('uploaded_files.darulifta_name');
        } else {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->distinct()
                ->where('darulifta_name', $firstRoleName)
                ->pluck('darulifta_name');
        }
    } else {
        $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name', $this->darulifta)
            ->distinct()
            ->pluck('darulifta_name');
    }

    $this->daruliftalist = DB::table('uploaded_files')
        ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('uploaded_files.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('uploaded_files.darulifta_name');
}

    public function updatedSearch()
{
    $this->resetPage();
}

    public function updatingFilter()
    {
        $this->resetPage();
    }

    public function downloadFile($fileName)
    {
        // Define the path to the file in the storage directory
        $filePath = 'public/viral/' . $fileName;

        // Check if the file exists
        if (Storage::exists($filePath)) {
            // Download the file from storage
            return Storage::download($filePath, $fileName);
        } else {
            // Handle the error if the file does not exist
            $this->fileErrorMessage = "The file does not exist.";
        }
    }
    public function toggleViral($id)
    {
        $file = DB::table('uploaded_files')->where('id', $id)->first();

        if ($file) {
            $currentUserId = Auth::id();
            $newViralValue = $file->viral == $currentUserId ? 0 : $currentUserId;

            DB::table('uploaded_files')
                ->where('id', $id)
                ->update(['viral' => $newViralValue]);
        }
    }
    public function render()
    {
        // Build the initial query
        $query = DB::table('uploaded_files')
            ->where('checked_folder', "ok")
            ->where('viral', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames);

        // Apply the filter conditions


        // Apply the search conditions
        if ($this->search) {
            $query->where(function ($subQuery) {
                $subQuery->where('sender', 'like', '%' . $this->search . '%')
                         ->orWhere('file_code', 'like', '%' . $this->search . '%')
                         ->orWhere('category', 'like', '%' . $this->search . '%')
                         ->orWhere('darulifta_name', 'like', '%' . $this->search . '%');
            });
        }

        // Paginate results
        $question_b = $query->orderBy('checked_date', 'desc')->paginate(10);
        // Pass data to the view
        return view('livewire.select-viral', [
            'question_b' => $question_b,
            'daruliftalist' => $this->daruliftalist,
        ])->layout('layouts.app');
    }
}
