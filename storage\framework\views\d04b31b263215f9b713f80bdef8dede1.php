<!-- Summary Table -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Darulifta Summary
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="table-responsive">
            <table class="table-modern">
                <thead>
                    <tr>
                        <th>
                            <i class="fas fa-building me-2"></i>
                            Darulifta
                        </th>
                        <!--[if BLOCK]><![endif]--><?php if($selectedmufti == 'all'): ?>
                            <th>
                                <i class="fas fa-user-check me-2"></i>
                                Checker
                            </th>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <th>
                            <i class="fas fa-calendar-alt me-2"></i>
                            Remaining Fatawa Dates
                        </th>
                        <th>
                            <i class="fas fa-chart-bar me-2"></i>
                            Total
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <!--[if BLOCK]><![endif]--><?php if($selectedmufti == 'all'): ?>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $daruliftaNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if(isset($sendingFatawa[$daruliftaName])): ?>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sendingFatawa[$daruliftaName]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $checked => $dates): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $daruliftaTotalCounts = 0;
                                        $folderEntries = [];
                                    ?>

                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $dates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mailfolderDates => $files): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $filesCollection = is_array($files) ? collect($files) : $files;
                                        ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $filesCollection; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $folder = $file->mail_folder_date;
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';

                                                $folderEntries[$folder] = $folderEntries[$folder] ?? ['count' => 0, 'transfer_by' => []];
                                                $folderEntries[$folder]['count']++;
                                                $folderEntries[$folder]['transfer_by'][$transferBy] = ($folderEntries[$folder]['transfer_by'][$transferBy] ?? 0) + 1;

                                                $daruliftaTotalCounts++;
                                            ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                    <tr>
                                        <td>
                                            <!--[if BLOCK]><![endif]--><?php if($loop->first || $loop->parent->first): ?>
                                                <?php
                                                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                                ?>
                                                <a href="<?php echo e(route('sending-fatawa', [
                                                    'darulifta' => $daruliftaName,
                                                    'selectedmujeeb' => $this->selectedmujeeb,
                                                    'selectedmufti' => $this->selectedmufti,
                                                    'selectedTimeFrame' => $this->selectedTimeFrame,
                                                    'startDate' => $this->startDate,
                                                    'endDate' => $this->endDate,
                                                ])); ?>&<?php echo e($selectedMonthsQuery); ?>"
                                                   class="text-decoration-none fw-bold text-primary">
                                                    <i class="fas fa-external-link-alt me-1"></i>
                                                    <?php echo e($daruliftaName); ?>

                                                </a>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo e($checked); ?></span>
                                        </td>
                                        <td>
                                            <div class="folder-entries-modern">
                                                <?php $foldercount = 0; ?>
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $folderEntries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="folder-entry-modern">
                                                        <div class="folder-date-modern">
                                                            <a href="<?php echo e(route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder])); ?>"
                                                               class="text-decoration-none">
                                                                <i class="fas fa-calendar me-1"></i>
                                                                <?php echo e(\Carbon\Carbon::parse($folder)->format('d-m-Y')); ?>

                                                            </a>
                                                            <span class="badge bg-info ms-2"><?php echo e($data['count']); ?></span>
                                                        </div>

                                                        <!--[if BLOCK]><![endif]--><?php if(isset($data['transfer_by'])): ?>
                                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['transfer_by']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transferBy => $transferByCount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="small text-muted">
                                                                    <a href="<?php echo e(route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder, 'transfer_by' => $transferBy])); ?>"
                                                                       class="text-decoration-none">
                                                                        <i class="fas fa-user me-1"></i>
                                                                        <?php echo e($transferBy); ?>

                                                                    </a>
                                                                    <span class="badge bg-secondary ms-1"><?php echo e($transferByCount); ?></span>
                                                                </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </div>
                                                    <?php $foldercount++; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold text-primary">
                                                    <i class="fas fa-file-alt me-1"></i>
                                                    Fatawa: <?php echo e($daruliftaTotalCounts); ?>

                                                </span>
                                                <span class="text-muted small">
                                                    <i class="fas fa-folder me-1"></i>
                                                    Folders: <?php echo e($foldercount); ?>

                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php else: ?>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $daruliftaNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if(isset($remainingFatawa[$daruliftaName])): ?>
                                <?php
                                    $daruliftaTotalCounts = 0;
                                    $folderCounts = [];
                                    $transferByCounts = [];
                                ?>

                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mailfolderDate; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mailfolderDates): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <!--[if BLOCK]><![endif]--><?php if(isset($remainingFatawa[$daruliftaName][$mailfolderDates])): ?>
                                        <?php
                                            $remainingFatawaCollection = is_array($remainingFatawa[$daruliftaName][$mailfolderDates])
                                                ? collect($remainingFatawa[$daruliftaName][$mailfolderDates])
                                                : $remainingFatawa[$daruliftaName][$mailfolderDates];
                                        ?>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $remainingFatawaCollection; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $folder = $file->mail_folder_date;
                                                $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                                                $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                                                $transferByCounts[$folder][$transferBy] = isset($transferByCounts[$folder][$transferBy]) ? $transferByCounts[$folder][$transferBy] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                            ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                                <tr>
                                    <td>
                                        <?php
                                            $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                        ?>
                                        <a href="<?php echo e(route('sending-fatawa', [
                                            'darulifta' => $daruliftaName,
                                            'selectedmujeeb' => $this->selectedmujeeb,
                                            'selectedmufti' => $this->selectedmufti,
                                            'selectedTimeFrame' => $this->selectedTimeFrame,
                                            'startDate' => $tempStartDate,
                                            'endDate' => $tempEndDate,
                                        ])); ?>&<?php echo e($selectedMonthsQuery); ?>"
                                           class="text-decoration-none fw-bold text-primary">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            <?php echo e($daruliftaName); ?>

                                        </a>
                                    </td>
                                    <td>
                                        <div class="folder-entries-modern">
                                            <?php $foldercount = 0; ?>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $folderCounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="folder-entry-modern">
                                                    <div class="folder-date-modern">
                                                        <a href="<?php echo e(route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder])); ?>"
                                                           class="text-decoration-none">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            <?php echo e(\Carbon\Carbon::parse($folder)->format('d-m-Y')); ?>

                                                        </a>
                                                        <span class="badge bg-info ms-2"><?php echo e($count); ?></span>
                                                    </div>

                                                    <!--[if BLOCK]><![endif]--><?php if(isset($transferByCounts[$folder])): ?>
                                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $transferByCounts[$folder]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transferBy => $transferByCount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="small text-muted">
                                                                <a href="<?php echo e(route('store.selected.values', ['selectedDarul' => $daruliftaName, 'selectedFolder' => $folder, 'transfer_by' => $transferBy])); ?>"
                                                                   class="text-decoration-none">
                                                                    <i class="fas fa-user me-1"></i>
                                                                    <?php echo e($transferBy); ?>

                                                                </a>
                                                                <span class="badge bg-secondary ms-1"><?php echo e($transferByCount); ?></span>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                                <?php $foldercount++; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold text-primary">
                                                <i class="fas fa-file-alt me-1"></i>
                                                Fatawa: <?php echo e($daruliftaTotalCounts); ?>

                                            </span>
                                            <span class="text-muted small">
                                                <i class="fas fa-folder me-1"></i>
                                                Folders: <?php echo e($foldercount); ?>

                                            </span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </tbody>
            </table>
        </div>

        <!-- Overall Summary -->
        <div class="mt-4 p-3 bg-light rounded">
            <div class="row text-center">
                <div class="col-md-6">
                    <h5 class="text-primary mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        <span id="finalTotalFatawa"><?php echo e($totalCounts ?? 0); ?></span>
                    </h5>
                    <small class="text-muted">Total Fatawa</small>
                </div>
                <div class="col-md-6">
                    <h5 class="text-success mb-0">
                        <i class="fas fa-folder me-2"></i>
                        <span id="finalTotalFolders"><?php echo e($overallFolderCount ?? 0); ?></span>
                    </h5>
                    <small class="text-muted">Total Folders</small>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-summary.blade.php ENDPATH**/ ?>