@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Performance History</h5>
                            <p class="text-sm mb-0">{{ $user->name }} - Performance Reports</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <a href="{{ route('performance-management') }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to Management
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">User Information</h6>
                            <p class="mb-1"><strong>Name:</strong> {{ $user->name }}</p>
                            <p class="mb-1"><strong>Email:</strong> {{ $user->email }}</p>
                            <p class="mb-1"><strong>Role:</strong> 
                                @if($user->isSuperior())
                                    <span class="badge bg-primary">Superior</span>
                                @elseif($user->isMujeeb())
                                    <span class="badge bg-info">Mujeeb</span>
                                @elseif($user->isNazim())
                                    <span class="badge bg-success">Nazim</span>
                                @else
                                    <span class="badge bg-secondary">User</span>
                                @endif
                            </p>
                            @if($user->departments->count() > 0)
                                <p class="mb-1"><strong>Departments:</strong>
                                    @foreach($user->departments as $department)
                                        <span class="badge bg-light text-dark me-1">{{ $department->name }}</span>
                                    @endforeach
                                </p>
                            @endif
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-3">Performance Statistics</h6>
                            <p class="mb-1"><strong>Total Reports:</strong> {{ $performances->total() }}</p>
                            <p class="mb-1"><strong>Submitted Reports:</strong> {{ $performances->where('is_submitted', true)->count() }}</p>
                            <p class="mb-1"><strong>Draft Reports:</strong> {{ $performances->where('is_submitted', false)->count() }}</p>
                            <p class="mb-1"><strong>Rated Reports:</strong> {{ $performances->whereNotNull('superior_rating')->count() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Reports -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Performance Reports</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hours</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Self Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Superior Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($performances as $performance)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $performance->performance_date->format('M d, Y') }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $performance->performance_date->format('l') }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $performance->task->title ?? 'No Task' }}</h6>
                                            @if($performance->department)
                                                <p class="text-xs text-secondary mb-0">{{ $performance->department->name }}</p>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $performance->hours_worked ?? '-' }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->overall_rating)
                                            <span class="badge badge-sm bg-gradient-{{ $performance->overall_rating === 'excellent' ? 'success' : ($performance->overall_rating === 'good' ? 'info' : ($performance->overall_rating === 'fair' ? 'warning' : 'danger')) }}">
                                                {{ ucfirst($performance->overall_rating) }}
                                            </span>
                                        @else
                                            <span class="text-secondary">-</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->superior_rating)
                                            <span class="badge badge-sm bg-gradient-{{ $performance->superior_rating === 'excellent' ? 'success' : ($performance->superior_rating === 'good' ? 'info' : ($performance->superior_rating === 'fair' ? 'warning' : 'danger')) }}">
                                                {{ ucfirst($performance->superior_rating) }}
                                            </span>
                                            @if($performance->superior_comments)
                                                <br><small class="text-xs text-secondary">Has Comments</small>
                                            @endif
                                        @else
                                            <span class="badge badge-sm bg-gradient-secondary">Not Rated</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->is_submitted)
                                            <span class="badge badge-sm bg-gradient-success">Submitted</span>
                                            @if($performance->submitted_at)
                                                <br><small class="text-xs text-secondary">{{ $performance->submitted_at->format('H:i') }}</small>
                                            @endif
                                        @else
                                            <span class="badge badge-sm bg-gradient-warning">Draft</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <a href="{{ route('daily-performance.show', $performance) }}" 
                                               class="btn btn-sm btn-outline-info mb-0" 
                                               title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @can('update', $performance)
                                                @if(!$performance->is_submitted || auth()->user()->isNazim())
                                                    <a href="{{ route('daily-performance.edit', $performance) }}" 
                                                       class="btn btn-sm btn-outline-primary mb-0" 
                                                       title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                @endif
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-clipboard-list text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted">No Performance Reports Found</h6>
                                            <p class="text-sm text-muted mb-0">This user hasn't submitted any performance reports yet.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    @if($performances->hasPages())
                        <div class="d-flex justify-content-center mt-3">
                            {{ $performances->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
