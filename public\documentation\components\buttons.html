<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
    <link rel="icon" href="../../assets/img/favicon.png" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Creative Tim">
    <title>
        Buttons | Material Dashboard 2 FREE Laravel by Creative Tim & UPDIVISION
    </title>
    <link rel="canonical" href="https://www.creative-tim.com/learning-lab/bootstrap/colors/material-dashboard" />

    <meta name="keywords" content="creative tim, updivision, html dashboard, laravel, material, html css dashboard laravel, material dashboard laravel, laravel material dashboard, material admin, laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, material dashboard bootstrap 5 laravel, frontend, responsive bootstrap 5 dashboard, material dashboard, material laravel bootstrap 5 dashboard" />
    <meta name="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="name" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta itemprop="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta name="twitter:card" content="product" />
    <meta name="twitter:site" content="@creativetim" />
    <meta name="twitter:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta name="twitter:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta name="twitter:creator" content="@creativetim" />
    <meta name="twitter:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="fb:app_id" content="655968634437471" />
    <meta property="og:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta property="og:type" content="article" />
    <meta property="og:url" content="https://www.creative-tim.com/live/material-dashboard-laravel" />
    <meta property="og:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="og:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta property="og:site_name" content="Creative Tim" />


    <link rel="stylesheet" href="../../assets/css/nucleo-icons.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/nextjs-material-dashboard-pro.min.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/material-dashboard.min.css" type="text/css">
    <link rel="stylesheet" href="../../assets/css/demo.css" type="text/css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Roboto+Slab:400,700" rel="stylesheet">

    <link href="../../assets/css/nucleo-icons.css" rel="stylesheet">

    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
    <!-- Anti-flicker snippet (recommended)  -->
    <style>
        .async-hide {
            opacity: 0 !important
        }

    </style>
    <script>
        (function (a, s, y, n, c, h, i, d, e) {
            s.className += ' ' + y;
            h.start = 1 * new Date;
            h.end = i = function () {
                s.className = s.className.replace(RegExp(' ?' + y), '')
            };
            (a[n] = a[n] || []).hide = h;
            setTimeout(function () {
                i();
                h.end = null
            }, c);
            h.timeout = c;
        })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {
            'GTM-K9BGS8K': true
        });

    </script>
    <!-- Analytics-Optimize Snippet -->
    <script>
        (function (i, s, o, g, r, a, m) {
            i['GoogleAnalyticsObject'] = r;
            i[r] = i[r] || function () {
                (i[r].q = i[r].q || []).push(arguments)
            }, i[r].l = 1 * new Date();
            a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
            a.async = 1;
            a.src = g;
            m.parentNode.insertBefore(a, m)
        })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
        ga('create', 'UA-46172202-22', 'auto', {
            allowLinker: true
        });
        ga('set', 'anonymizeIp', true);
        ga('require', 'GTM-K9BGS8K');
        ga('require', 'displayfeatures');
        ga('require', 'linker');
        ga('linker:autoLink', ["2checkout.com", "avangate.com"]);

    </script>
    <!-- end Analytics-Optimize Snippet -->
    <!-- Google Tag Manager -->
    <script>
        (function (w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-NKDMSK6');

    </script>
    <!-- End Google Tag Manager -->
    <!-- This is for docs typography and layout -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="../../assets/css/docs.css" rel="stylesheet" />
</head>

<body class="docs ">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NKDMSK6" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <header class="ct-docs-navbar">
      <a class="ct-docs-navbar-brand" href="javascript:void(0)" aria-label="Bootstrap">
        </a><a href="https://www.creative-tim.com/" class="ct-docs-navbar-text" target="_blank">
          Creative Tim
        </a>
        <div class="ct-docs-navbar-border"></div>
        <a href="../../documentation/getting-started/installation.html" class="ct-docs-navbar-text">
          Docs
        </a>

      <ul class="ct-docs-navbar-nav-left">
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Live Preview</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownPreview">
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-pro-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Support</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownSupport">
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/material-dashboard-laravel/issues" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/ct-material-dashboard-pro-laravel/issues" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
      </ul>
      <ul class="ct-docs-navbar-nav-right">
        <li class="ct-docs-navbar-nav-item">
          <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/material-dashboard-laravel" target="_blank">Download Free</a>
        </li>
      </ul>
      <a href="https://www.creative-tim.com/product/material-dashboard-pro-laravel" target="_blank" class="ct-docs-btn-upgrade">
        <span class="ct-docs-btn-inner--icon">
          <i class="fas fa-download mr-2" aria-hidden="true"></i>
        </span>
        <span class="ct-docs-navbar-nav-link-inner--text">Upgrade to PRO</span>
      </a>
      <button class="ct-docs-navbar-toggler" type="button">
        <span class="ct-docs-navbar-toggler-icon"></span>
      </button>
    </header>
    <div class="ct-docs-main-container">
        <div class="ct-docs-main-content-row">
            <div class="ct-docs-sidebar-col">
                <nav class="ct-docs-sidebar-collapse-links">
                    <div class="ct-docs-sidebar-product">
                      <div class="ct-docs-sidebar-product-image">
                        <img src="../../assets/img/bootstrap.png">
                      </div>
                      <p class="ct-docs-sidebar-product-text">Material Dashboard</p>
                    </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-active-40 text-white"></i>
                          </div>
                        </div>
                        Getting started
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                          <a href="../../documentation/getting-started/overview.html">
                            Overview
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/license.html">
                            License
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/installation.html">
                            Installation
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/build-tools.html">
                            Build Tools
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/getting-started/bootstrap.html">
                            What is Bootstrap
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                        <a class="ct-docs-toc-link" href="javascript:void(0)">
                          <div class="d-inline-block">
                            <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                              <i class="ni ni-folder-17 text-white"></i>
                            </div>
                          </div>
                          Laravel
                        </a>
                        <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                          <li class="">
                            <a href="../../documentation/laravel/login.html">
                              Login
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/sign-up.html">
                              Sign Up
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/forgot-password.html">
                              Forgot Password
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/user-profile.html">
                              User Profile
                            </a>
                          </li>
                          <li class="">
                            <a href="../../documentation/laravel/user-management.html">
                              User Management
                              <span class="ct-docs-sidenav-pro-badge">Pro</span>
                            </a>
                          </li>
                        </ul>
                      </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-folder-17 text-white"></i>
                          </div>
                        </div>
                        Foundation
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class=" ">
                          <a href="../../documentation/foundation/colors.html">
                            Colors
                          </a>
                        </li>
                        <li class=" ">
                          <a href="../../documentation/foundation/grid.html">
                            Grid
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/typography.html">
                            Typography
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/icons.html">
                            Icons
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/foundation/utilities.html">
                            Utilities
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-app text-white"></i>
                          </div>
                        </div>
                        Components
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                          <a href="../../documentation/components/alerts.html">
                            Alerts
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/badge.html">
                            Badge
                          </a>
                        </li>
                        <li class="ct-docs-nav-sidenav-active">
                          <a href="../../documentation/components/buttons.html">
                            Buttons
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/social-buttons.html">
                            Social Buttons
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/cards.html">
                            Cards
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/carousel.html">
                            Carousel
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/collapse.html">
                            Collapse
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/dropdowns.html">
                            Dropdowns
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/forms.html">
                            Forms
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/input-group.html">
                            Input Group
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/list-group.html">
                            List Group
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/modal.html">
                            Modal
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/navs.html">
                            Navs
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/navbar.html">
                            Navbar
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/pagination.html">
                            Pagination
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/popovers.html">
                            Popovers
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/progress.html">
                            Progress
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/spinners.html">
                            Spinners
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/tables.html">
                            Tables
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/components/tooltips.html">
                            Tooltips
                          </a>
                        </li>
                      </ul>
                    </div>
                    <div class="ct-docs-toc-item-active">
                      <a class="ct-docs-toc-link" href="javascript:void(0)">
                        <div class="d-inline-block">
                          <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                            <i class="ni ni-settings text-white"></i>
                          </div>
                        </div>
                        Plugins
                      </a>
                      <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                        <li class="">
                          <a href="../../documentation/plugins/countUpJs.html">
                            CountUp JS
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/charts.html">
                            Charts
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/datepicker.html">
                            Datepicker
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/fullcalendar.html">
                            Fullcalendar
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/sliders.html">
                            Sliders
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/choices.html">
                            Choices
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/dropzone.html">
                            Dropzone
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/datatables.html">
                            Datatables
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/kanban.html">
                            Kanban
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/photo-swipe.html">
                            Photo Swipe
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/quill.html">
                            Quill
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/sweet-alerts.html">
                            Sweet Alerts
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                        <li class="">
                          <a href="../../documentation/plugins/wizard.html">
                            Wizard
                            <span class="ct-docs-sidenav-pro-badge">Pro</span>
                          </a>
                        </li>
                      </ul>
                    </div>
                  </nav>
            </div>
            <div class="ct-docs-toc-col">
                <ul class="section-nav">
                    <li class="toc-entry toc-h2"><a href="#classes">Classes</a></li>
                    <li class="toc-entry toc-h2"><a href="#examples">Examples</a></li>
                    <li class="toc-entry toc-h2"><a href="#outline-buttons">Outline buttons</a></li>
                    <li class="toc-entry toc-h2"><a href="#sizes">Sizes</a></li>
                    <li class="toc-entry toc-h2"><a href="#active-state">Active state</a></li>
                    <li class="toc-entry toc-h2"><a href="#disabled-state">Disabled state</a></li>
                </ul>
            </div>
            <main class="ct-docs-content-col" role="main">
                <div class="ct-docs-page-title">
                    <h1 class="ct-docs-page-h1-title" id="content">
                        Bootstrap Buttons
                    </h1>
                    <div class="avatar-group mt-3">
                    </div>
                </div>
                <p class="ct-docs-page-title-lead">Use Bootstrap buttons and Bootstrap custom styles for actions in
                    forms, dialogues, and more with support for multiple sizes, states, and more.</p>
                <hr class="ct-docs-hr">
                <h2 id="classes">Classes</h2>
                <p>Bootstrap provides different styles of buttons:</p>
                <ul>
                    <li><code class=" highlighter-rouge language-plaintext">.btn</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-default</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-primary</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-success</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-info</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-warning</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-danger</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-link</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-outline-default</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-outline-primary</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-outline-success</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-outline-info</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-outline-warning</code></li>
                    <li><code class=" highlighter-rouge language-plaintext">.btn-outline-danger</code></li>
                </ul>
                <h2 id="examples">Examples</h2>
                <p>Bootstrap includes several predefined button styles, each serving its own semantic purpose, with a
                    few extras thrown in for more control.</p>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button class="btn btn-primary" type="button">Button</button>
                    <button class="btn btn-icon btn-3 btn-primary" type="button">
                        <span class="btn-inner--icon"><i class="material-icons">play_arrow</i></span>
                        <span class="btn-inner--text">With icon</span>
                    </button>
                    <button class="btn btn-icon btn-2 btn-primary" type="button">
                        <span class="btn-inner--icon"><i class="material-icons">lightbulb</i></span>
                    </button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-icon btn-3 btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn-inner--icon<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>i</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>material-icons<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>play_arrow<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>i</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
  <span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn-inner--text<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>With icon<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>

<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-icon btn-2 btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>
	<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>span</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn-inner--icon<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>i</span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>material-icons<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>lightbulb<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>i</span><span class="token punctuation">&gt;</span></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>span</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn btn-primary mt-2 me-2">Primary</button><button type="button"
                        class="btn btn-secondary mt-2 me-2">Secondary</button><button type="button"
                        class="btn btn-info mt-2 me-2">Info</button><button type="button"
                        class="btn btn-success mt-2 me-2">Success</button><button type="button"
                        class="btn btn-danger mt-2 me-2">Danger</button><button type="button"
                        class="btn btn-warning mt-2 me-2">Warning</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-primary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Primary<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-secondary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Secondary<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-info<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Info<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-success<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Success<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-danger<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Danger<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-warning<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Warning<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn bg-gradient-primary mt-2 me-2">Primary</button><button
                        type="button" class="btn bg-gradient-secondary mt-2 me-2">Secondary</button><button
                        type="button" class="btn bg-gradient-info mt-2 me-2">Info</button><button type="button"
                        class="btn bg-gradient-success mt-2 me-2">Success</button><button type="button"
                        class="btn bg-gradient-danger mt-2 me-2">Danger</button><button type="button"
                        class="btn bg-gradient-warning mt-2 me-2">Warning</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn bg-gradient-primary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Primary<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn bg-gradient-secondary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Secondary<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn bg-gradient-info<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Info<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn bg-gradient-success<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Success<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn bg-gradient-danger<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Danger<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn bg-gradient-warning<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Warning<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <h2 id="outline-buttons">Outline buttons</h2>
                <p>In need of a button, but not the hefty background colors they bring? Replace the default modifier
                    classes with the <code class=" highlighter-rouge language-plaintext">.btn-outline-*</code> ones to
                    remove all background images and colors on any button.</p>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn btn-outline-primary mt-2">Primary</button>
                    <button type="button" class="btn btn-outline-secondary mt-2">Secondary</button>
                    <button type="button" class="btn btn-outline-info mt-2">Info</button>
                    <button type="button" class="btn btn-outline-success mt-2">Success</button>
                    <button type="button" class="btn btn-outline-danger mt-2">Danger</button>
                    <button type="button" class="btn btn-outline-warning mt-2">Warning</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-outline-primary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Primary<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-outline-secondary<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Secondary<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-outline-info<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Info<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-outline-success<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Success<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-outline-danger<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Danger<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-outline-warning<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Warning<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <h2 id="sizes">Sizes</h2>
                <p>Fancy larger or smaller buttons? Add <code
                        class=" highlighter-rouge language-plaintext">.btn-lg</code> or <code
                        class=" highlighter-rouge language-plaintext">.btn-sm</code> for additional sizes.</p>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn btn-primary btn-lg">Large button</button>
                    <button type="button" class="btn btn-secondary btn-lg">Large button</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-primary btn-lg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Large button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-secondary btn-lg<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Large button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn btn-primary btn-sm">Small button</button>
                    <button type="button" class="btn btn-secondary btn-sm">Small button</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-primary btn-sm<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Small button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-secondary btn-sm<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Small button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <p>Create block level buttons—those that span the full width of a parent—by adding <code
                        class=" highlighter-rouge language-plaintext">.w-100</code>.</p>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn btn-primary btn-lg w-100">Block level button</button>
                    <button type="button" class="btn btn-secondary btn-lg w-100">Block level button</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-primary btn-lg w-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Block level button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-secondary btn-lg w-100<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Block level button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <h2 id="active-state">Active state</h2>
                <p>Buttons will appear pressed (with a darker background, darker border, and inset shadow) when active.
                    <strong>There’s no need to add a class to <code
                            class=" highlighter-rouge language-plaintext">&lt;button&gt;</code>s as they use a
                        pseudo-class</strong>. However, you can still force the same active appearance with <code
                        class=" highlighter-rouge language-plaintext">.active</code> (and include the
                    <code>aria-pressed="true"</code> attribute) should you need to replicate the state programmatically.
                </p>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <a href="#" class="btn btn-primary btn-lg active" role="button" aria-pressed="true">Primary link</a>
                    <a href="#" class="btn btn-secondary btn-lg active" role="button" aria-pressed="true">Link</a>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-primary btn-lg active<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">aria-pressed</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Primary link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>a</span> <span class="token attr-name">href</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>#<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-secondary btn-lg active<span class="token punctuation">"</span></span> <span class="token attr-name">role</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">aria-pressed</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>true<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span>Link<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>a</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
                <h2 id="disabled-state">Disabled state</h2>
                <p>Make buttons look inactive by adding the <code
                        class=" highlighter-rouge language-plaintext">disabled</code> boolean attribute to any <code
                        class=" highlighter-rouge language-plaintext">&lt;button&gt;</code> element.</p>
                <div class="ct-example"
                    style="position: relative;border: 2px solid #f5f7ff !important;border-bottom: none !important;padding: 1rem 1rem 2rem 1rem;margin-bottom: -1.25rem;">
                    <button type="button" class="btn btn-lg btn-primary" disabled="">Primary button</button>
                    <button type="button" class="btn btn-secondary btn-lg" disabled="">Button</button>
                </div>
                <div class="position-relative">
                    <div class="bd-clipboard"><span class="btn-clipboard" title=""
                            data-bs-original-title="Copy to clipboard">Copy</span></div>
                    <figure class="highlight">
                        <pre
                            class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-lg btn-primary<span class="token punctuation">"</span></span> <span class="token attr-name">disabled</span><span class="token punctuation">&gt;</span></span>Primary button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span>
<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>button</span> <span class="token attr-name">type</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>button<span class="token punctuation">"</span></span> <span class="token attr-name">class</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>btn btn-secondary btn-lg<span class="token punctuation">"</span></span> <span class="token attr-name">disabled</span><span class="token punctuation">&gt;</span></span>Button<span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>button</span><span class="token punctuation">&gt;</span></span></code></pre>
                    </figure>
                </div>
            </main>
        </div>
        <footer class="ct-docs-footer">
          <div class="ct-docs-footer-inner-row">
            <div class="ct-docs-footer-col">
              <div class="ct-docs-footer-copyright">
                © <script>
                  document.write(new Date().getFullYear())
                </script> <a href="https://creative-tim.com" class="ct-docs-footer-copyright-author" target="_blank">Creative Tim</a> & <a href="https://www.updivision.com" class="ct-docs-footer-copyright-author" target="_blank">UPDIVISION</a>
              </div>
            </div>
            <div class="ct-docs-footer-col">
              <ul class="ct-docs-footer-nav-footer">
                <li>
                  <a href="https://creative-tim.com" class="ct-docs-footer-nav-link" target="_blank">Creative Tim</a>
                </li>
                <li class="nav-item">
                  <a href="https://www.updivision.com" class="ct-docs-footer-nav-link" target="_blank">UPDIVISION</a>
              </li>
                <li>
                  <a href="https://www.creative-tim.com/contact-us" class="ct-docs-footer-nav-link" target="_blank">Contact Us</a>
                </li>
                <li>
                  <a href="https://creative-tim.com/blog" class="ct-docs-footer-nav-link" target="_blank">Blog</a>
                </li>
              </ul>
            </div>
          </div>
        </footer>
    </div>
    <script src="../../assets/js/jquery.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/popper.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="../../assets/js/prism.js" type="text/javascript"></script>
    <script src="../../assets/js/docs.min.js" type="text/javascript"></script>
    <script src="../../assets/js/holder.min.js" type="text/javascript"></script>
    <script src="../../assets/js/moment.min.js" type="text/javascript"></script>
    <script src="../../assets/js/material-dashboard.min.js" type="text/javascript"></script>
    <script>
        Holder.addTheme('gray', {
            bg: '#777',
            fg: 'rgba(255,255,255,.75)',
            font: 'Helvetica',
            fontweight: 'normal'
        })

    </script>
    <script>
        // Facebook Pixel Code Don't Delete
        ! function (f, b, e, v, n, t, s) {
            if (f.fbq) return;
            n = f.fbq = function () {
                n.callMethod ?
                    n.callMethod.apply(n, arguments) : n.queue.push(arguments)
            };
            if (!f._fbq) f._fbq = n;
            n.push = n;
            n.loaded = !0;
            n.version = '2.0';
            n.queue = [];
            t = b.createElement(e);
            t.async = !0;
            t.src = v;
            s = b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t, s)
        }(window,
            document, 'script', '//connect.facebook.net/en_US/fbevents.js');

        try {
            fbq('init', '111649226022273');
            fbq('track', "PageView");

        } catch (err) {
            console.log('Facebook Track Error:', err);
        }

    </script>
    <script src="../../assets/js/docs.js"></script>

</body>

</html>
