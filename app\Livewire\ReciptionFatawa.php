<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;
use Illuminate\Http\Request; // Add this line



// #[Layout('layouts.app')]
class ReciptionFatawa extends Component
{
    use WithPagination;

    // Month-wise Pagination Properties
    public $currentMonthIndex = 0;
    public $currentMonth = null;
    public $availableMonths = [];
    public $monthCounts = []; // Store count for each month
    public $totalMonths = 0;

    public $selectedMonths = [];
    public $reciptions;
    public $notsentiftacode;
    public $datefilter;
    public $showDetail = false;
    public $showQue = false;
    public $showChat = false;
    public $codebylower;
    public $message;
    public $allfatawa;
    public $userName;
    Public $obtain;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $reciptionFatawa;
    public $daruliftaNames;
    public $mujeebs;
    public $recdate;
    public $darulifta;
    public $mailfolder;
    public $selectedmujeeb = 'all';
    public $selectedexclude = 'exclude_ok';
    public $selectedTimeFrame = 'this_month';
    public $tempSelectedMonths = [];
    public $tempSelectedTimeFrame = 'this_month';
    public $resultstatus = [];
    public $daruliftalist;
    public $startDate;
    public $endDate;
    public $tempStartDate;
    public $tempEndDate;
    // protected $listeners = ['updateSelectedTimeFrame'];
    protected $queryString = [
        'selectedmujeeb' => ['except' => 'all'],
        'selectedexclude' => ['except' => 'exclude_ok'],
        'tempSelectedTimeFrame' => ['except' => 'this_month'],
        'tempStartDate' => ['except' => ''],
        'tempEndDate' => ['except' => ''],
        'selectedMonths' => ['except' => []],
        'tempSelectedMonths' => ['except' => []],
        'showDetail' => ['except' => false],
        'showChat' => ['except' => false],
        'showQue' => ['except' => false],
        'currentMonthIndex' => ['except' => 0],
    ];
    
    public function mount(Request $request, $darulifta = null, $mailfolder = null)
        {


            $this->selectedexclude = $request->query('selectedexclude', $this->selectedexclude);
            $this->selectedTimeFrame = $request->query('selectedTimeFrame', $this->selectedTimeFrame);
            $this->selectedmujeeb = $request->query('selectedmujeeb', $this->selectedmujeeb);

            // Handle selectedMonths from URL
            $this->selectedMonths = $request->query('selectedMonths', []);
            if (is_string($this->selectedMonths)) {
                // Convert comma-separated string to array
                $this->selectedMonths = explode(',', $this->selectedMonths);
            }

            // Handle temp values from URL (these are the actual filter values)
            $this->tempSelectedTimeFrame = $request->query('tempSelectedTimeFrame', $this->selectedTimeFrame);
            $this->tempSelectedMonths = $request->query('tempSelectedMonths', $this->selectedMonths);
            if (is_string($this->tempSelectedMonths)) {
                $this->tempSelectedMonths = explode(',', $this->tempSelectedMonths);
            }
            $this->startDate = $request->query('startDate');
            $this->endDate = $request->query('endDate');
            $this->tempStartDate = $request->query('tempStartDate', $this->startDate);
            $this->tempEndDate = $request->query('tempEndDate', $this->endDate);

            // Handle display options from URL
            $this->showQue = $request->query('showQue', false) == '1';
            $this->showDetail = $request->query('showDetail', false) == '1';
            $this->showChat = $request->query('showChat', false) == '1';
        
            $this->userName = Auth::user()->name; // Assuming you have the user's name here
            if ($this->userName == 'Sayed Masood Sahib') {
                $this->selectedmufti = 'sayed_masood';
            }
            $this->checker = DB::table('checker')
                ->where('checker_name', $this->userName)
                ->first()
                ->folder_id ?? null;
        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;
        
        // Load data in correct order
        $this->loadDaruliftaNames();
        $this->loadRecDate();
        $this->initializeMonthPagination();
        $this->loadStaticData();
        $this->fetchDuplicatedFiles();
        $this->reciptionFatawadata();

        // Additional initialization code
    }
   

    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->tempStartDate && $this->tempEndDate && empty($this->tempSelectedMonths)) {
            $this->selectedTimeFrame = 'custom';
            $this->tempSelectedTimeFrame = 'custom';
        }

        // Reset month pagination when filters change
        $this->currentMonthIndex = 0;
        $this->clearMonthSpecificCache();
        $this->loadRecDate(); // This will recalculate available months
        $this->updateUrl(); // Update URL with new filter values
    }
    
    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->tempSelectedTimeFrame = $timeFrame;
        if ($timeFrame === 'this_month' || $timeFrame === 'all') {
            // Resetting the values for 'this_month' and 'all' time frames
            $this->tempStartDate = null;
            $this->tempEndDate = null;
            $this->selectedMonths = [];
            $this->tempSelectedMonths = [];

            // Optional: Debugging statements
            // dd($this->tempStartDate, $this->tempEndDate, $this->selectedMonths, $this->tempSelectedMonths);
        }

    }
    public function updateSelectedExclude($exclude)
    {
        $this->selectedexclude = $exclude;
    }
    public function updateSelectedMujeeb($mujeebFrame)
    {
        $this->selectedmujeeb = $mujeebFrame;
    }


    private function loadDaruliftaNames()
    {
        $user = Auth::user();
$userRoles = $user->roles;
$roleNames = $userRoles->pluck('name')->toArray();
$firstRoleName = reset($roleNames);

if ($this->darulifta === null) {
    if (count(Auth::user()->roles) > 1) {
        $this->daruliftaNames = DB::table('questions')
        ->join('daruliftas', 'questions.question_branch', '=', 'daruliftas.darul_name')
            ->select('questions.question_branch')
            ->distinct()
            ->orderBy('daruliftas.id')
            ->pluck('questions.question_branch');

        $this->mujeebs = DB::table('questions')
            ->select('assign_id')
            ->whereIn('question_branch', $this->daruliftaNames)
            ->distinct()
            ->pluck('assign_id');           
    } else {
        $this->daruliftaNames = DB::table('questions')
            ->select('question_branch')
            ->where('question_branch', $firstRoleName)
            ->distinct()
            ->pluck('question_branch');
            
        $this->mujeebs = DB::table('questions')
        ->select('assign_id')
        ->where('question_branch', $this->daruliftaNames)
        ->distinct()
        ->pluck('assign_id'); 
    }
} else {
    $this->daruliftaNames = DB::table('questions')
        ->select('question_branch')
        ->where('question_branch', $this->darulifta)
        ->distinct()
        ->pluck('question_branch');
        $this->mujeebs = DB::table('questions')
        ->select('assign_id')
        ->where('question_branch', $this->darulifta)
        ->distinct()
        ->pluck('assign_id');
}
    }
    private function loadRecDate()
    {
        if ($this->mailfolder === null) {
            // Build base query for months with counts
            $baseQuery = DB::table('questions')
                ->whereIn('question_branch', $this->daruliftaNames); // Filter by user's darulifta

            // Apply time frame filters to month calculation
            if ($this->tempSelectedTimeFrame == 'all') {
                // No date restriction for "All"
            } elseif ($this->tempSelectedTimeFrame == 'this_month') {
                $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                    now()->startOfMonth()->format('Y-m-d'),
                    now()->endOfMonth()->format('Y-m-d')
                ]);
            } elseif ($this->tempSelectedTimeFrame == 'last_month') {
                $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                    now()->subMonth()->startOfMonth()->format('Y-m-d'),
                    now()->subMonth()->endOfMonth()->format('Y-m-d')
                ]);
            } elseif ($this->tempSelectedTimeFrame == 'other' && !empty($this->tempSelectedMonths)) {
                $formattedSelectedMonths = array_map(function ($date) {
                    return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
                }, $this->tempSelectedMonths);
                $baseQuery->whereIn(DB::raw("DATE_FORMAT(rec_date, '%Y-%m')"), $formattedSelectedMonths);
            } elseif ($this->tempSelectedTimeFrame == 'custom' && $this->tempStartDate && $this->tempEndDate) {
                $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                    Carbon::parse($this->tempStartDate)->format('Y-m-d'),
                    Carbon::parse($this->tempEndDate)->format('Y-m-d')
                ]);
            }

            // Apply mujeeb filter if selected
            if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
                $baseQuery->where('assign_id', $this->selectedmujeeb);
            }

            // Apply exclusion filter if selected
            if ($this->selectedexclude == 'exclude_ok') {
                $baseQuery->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok');
                });
            }

            // Get all unique months from rec_date with counts (only months with data)
            $monthsWithCounts = $baseQuery
                ->selectRaw("DATE_FORMAT(rec_date, '%Y-%m') as month_year, COUNT(*) as count")
                ->groupBy(DB::raw("DATE_FORMAT(rec_date, '%Y-%m')"))
                ->having('count', '>', 0) // Only include months with data
                ->orderBy(DB::raw("MAX(rec_date)"), 'desc') // Order by latest date in each month
                ->get();

            $this->availableMonths = $monthsWithCounts->pluck('month_year')->toArray();
            $this->monthCounts = $monthsWithCounts->pluck('count', 'month_year')->toArray();
        } else {
            // If specific folder is selected, get only that month
            $monthYear = date('Y-m', strtotime($this->mailfolder));
            $this->availableMonths = [$monthYear];

            // Build base query for specific month count
            $baseQuery = DB::table('questions')
                ->whereIn('question_branch', $this->daruliftaNames)
                ->whereRaw("DATE_FORMAT(rec_date, '%Y-%m') = ?", [$monthYear]);

            // Apply time frame filters (for consistency, though specific folder overrides)
            if ($this->tempSelectedTimeFrame == 'custom' && $this->tempStartDate && $this->tempEndDate) {
                $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                    Carbon::parse($this->tempStartDate)->format('Y-m-d'),
                    Carbon::parse($this->tempEndDate)->format('Y-m-d')
                ]);
            }

            // Apply mujeeb filter if selected
            if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
                $baseQuery->where('assign_id', $this->selectedmujeeb);
            }

            // Apply exclusion filter if selected
            if ($this->selectedexclude == 'exclude_ok') {
                $baseQuery->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok');
                });
            }

            $count = $baseQuery->count();
            $this->monthCounts = [$monthYear => $count];
        }

        $this->totalMonths = count($this->availableMonths);

        // Set current month based on index
        if (isset($this->availableMonths[$this->currentMonthIndex])) {
            $this->currentMonth = $this->availableMonths[$this->currentMonthIndex];
        } else {
            $this->currentMonthIndex = 0;
            $this->currentMonth = $this->availableMonths[0] ?? null;
        }

        // Load rec dates based on selected time frame
        if ($this->tempSelectedTimeFrame == 'all') {
            // For "All" time frame, load all available dates
            $this->recdate = DB::table('questions')
                ->select('rec_date')
                ->whereIn('question_branch', $this->daruliftaNames)
                ->distinct()
                ->orderBy('rec_date', 'desc')
                ->pluck('rec_date');
        } elseif ($this->currentMonth) {
            // For month pagination, load dates for current month only
            $this->recdate = DB::table('questions')
                ->select('rec_date')
                ->whereRaw("DATE_FORMAT(rec_date, '%Y-%m') = ?", [$this->currentMonth])
                ->whereIn('question_branch', $this->daruliftaNames) // Filter by user's darulifta
                ->distinct()
                ->orderBy('rec_date', 'desc')
                ->pluck('rec_date');
        } else {
            $this->recdate = collect();
        }
    }

    /**
     * Load static data that doesn't change often (cached)
     */
    private function loadStaticData()
    {
        // Check if we have the required data
        if (empty($this->daruliftaNames)) {
            $this->datefilter = collect();
            $this->daruliftalist = collect();
            return;
        }

        // Load static data only once during initialization
        if (!isset($this->datefilter)) {
            $this->datefilter = DB::table('questions')
                ->selectRaw("DISTINCT YEAR(rec_date) as year, MONTH(rec_date) as month")
                ->whereIn('question_branch', $this->daruliftaNames)
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->limit(24) // Limit to last 2 years for performance
                ->get();
        }

        if (!isset($this->daruliftalist)) {
            $this->daruliftalist = DB::table('uploaded_files')
                ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
                ->select('uploaded_files.darulifta_name')
                ->distinct()
                ->orderBy('daruliftas.id')
                ->pluck('uploaded_files.darulifta_name');
        }
    }

    /**
     * Load essential data for current view (optimized)
     */
    private function loadEssentialData()
    {
        // Check if we have the required data
        if (empty($this->daruliftaNames) || !$this->currentMonth) {
            $this->que_day_r = collect();
            $this->mahlenazar_null = collect();
            $this->notsentiftacode = collect();
            return;
        }

        // Load data based on selected time frame
        if (!isset($this->que_day_r)) {
            $query = DB::table('questions')
                ->whereIn('question_branch', $this->daruliftaNames);

            // Apply time frame filter
            if ($this->tempSelectedTimeFrame == 'all') {
                // No date restriction for "All"
            } elseif ($this->currentMonth) {
                $query->whereRaw("DATE_FORMAT(rec_date, '%Y-%m') = ?", [$this->currentMonth]);
            }

            $this->que_day_r = $query->get();
        }

        // Load mahlenazar_null only for current month
        if (!isset($this->mahlenazar_null)) {
            $this->mahlenazar_null = DB::table('uploaded_files as u1')
                ->whereIn('u1.darulifta_name', $this->daruliftaNames)
                ->where('u1.checked_folder', 'Mahl-e-Nazar')
                ->whereRaw("DATE_FORMAT(u1.mail_folder_date, '%Y-%m') = ?", [$this->currentMonth])
                ->leftJoin('uploaded_files as u2', function ($join) {
                    $join->on('u1.file_code', '=', 'u2.file_code')
                        ->where(function ($query) {
                            $query->where('u2.checked_folder', 'ok')
                                ->orWhere('u2.checked_folder', 'Tahqiqi');
                        });
                })
                ->whereNull('u2.file_code')
                ->select('u1.*')
                ->get();
        }

        // Load notsentiftacode for stats (based on selected time frame)
        if (!isset($this->notsentiftacode)) {
            $query = DB::table('questions')
                ->select('ifta_code')
                ->whereIn('question_branch', $this->daruliftaNames);

            // Apply time frame filter for notsentiftacode
            if ($this->tempSelectedTimeFrame == 'all') {
                // No date restriction for "All"
            } elseif ($this->currentMonth) {
                $query->whereRaw("DATE_FORMAT(rec_date, '%Y-%m') = ?", [$this->currentMonth]);
            }

            $this->notsentiftacode = $query
                ->whereNotIn('ifta_code', function ($subquery) {
                    $subquery->select(DB::raw('LOWER(file_code)'))
                        ->from('uploaded_files');
                })
                ->get();
        }

        // Load duplicated files only when needed
        if (!isset($this->resultstatus) || empty($this->resultstatus)) {
            $this->fetchDuplicatedFiles();
        }
    }

public function sendMessage($iftaCode)
{
    
    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.
    

    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}

/**
 * Initialize month-wise pagination
 */
private function initializeMonthPagination()
{
    $this->currentMonthIndex = request()->query('currentMonthIndex', 0);
    // loadRecDate will handle setting the current month
}

/**
 * Go to next month
 */
public function nextMonth()
{
    if ($this->currentMonthIndex < $this->totalMonths - 1) {
        $this->currentMonthIndex++;
        $this->clearMonthSpecificCache();
        $this->loadRecDate();
        $this->updateUrl();
    }
}

/**
 * Go to previous month
 */
public function previousMonth()
{
    if ($this->currentMonthIndex > 0) {
        $this->currentMonthIndex--;
        $this->clearMonthSpecificCache();
        $this->loadRecDate();
        $this->updateUrl();
    }
}

/**
 * Go to specific month by index
 */
public function goToMonth($monthIndex)
{
    $monthIndex = max(0, min($monthIndex, $this->totalMonths - 1));
    $this->currentMonthIndex = $monthIndex;
    $this->clearMonthSpecificCache();
    $this->loadRecDate();
    $this->updateUrl();
}

/**
 * Clear cached data that's specific to current month
 */
private function clearMonthSpecificCache()
{
    $this->que_day_r = null;
    $this->mahlenazar_null = null;
    $this->notsentiftacode = null;
    $this->reciptionFatawa = [];
    $this->reciptions = collect();
}

/**
 * Go to specific month by month-year string
 */
public function goToMonthYear($monthYear)
{
    $index = array_search($monthYear, $this->availableMonths);
    if ($index !== false) {
        $this->goToMonth($index);
    }
}

/**
 * Handle property updates and reset month pagination when needed
 */
public function updated($propertyName)
{
    // Reset month pagination when filters change
    if (in_array($propertyName, ['selectedmujeeb', 'selectedexclude', 'selectedTimeFrame', 'tempSelectedTimeFrame', 'tempSelectedMonths', 'tempStartDate', 'tempEndDate'])) {
        $this->currentMonthIndex = 0;
        $this->clearMonthSpecificCache();
        $this->loadRecDate();
    }

    // Clear cached data when filters change
    if (in_array($propertyName, ['selectedmujeeb', 'selectedexclude', 'tempSelectedTimeFrame', 'tempSelectedMonths', 'tempStartDate', 'tempEndDate'])) {
        $this->clearMonthSpecificCache();
    }

    // Update URL when display options change
    if (in_array($propertyName, ['showDetail', 'showChat', 'showQue'])) {
        $this->updateUrl();
    }

    // Handle filter changes - update URL
    if (in_array($propertyName, ['selectedmujeeb', 'selectedexclude', 'tempSelectedTimeFrame', 'tempSelectedMonths', 'tempStartDate', 'tempEndDate'])) {
        $this->updateUrl();
    }
}

/**
 * Update URL with current month pagination state
 */
private function updateUrl()
{
    $params = [
        'selectedmujeeb' => $this->selectedmujeeb,
        'selectedexclude' => $this->selectedexclude,
        'tempSelectedTimeFrame' => $this->tempSelectedTimeFrame,
        'tempStartDate' => $this->tempStartDate,
        'tempEndDate' => $this->tempEndDate,
        'showDetail' => $this->showDetail ? '1' : '0',
        'showChat' => $this->showChat ? '1' : '0',
        'currentMonthIndex' => $this->currentMonthIndex,
    ];

    if (!empty($this->selectedMonths)) {
        $params['selectedMonths'] = implode(',', $this->selectedMonths);
    }

    if (!empty($this->tempSelectedMonths)) {
        $params['tempSelectedMonths'] = implode(',', $this->tempSelectedMonths);
    }

    $this->js('window.history.replaceState({}, "", "' . request()->url() . '?' . http_build_query($params) . '")');
}

public function render()
    {
        // Load only essential data for current month
        $this->loadEssentialData();

        // Load main fatawa data (month-wise for detailed view)
        $data = $this->reciptionFatawadata();
        $this->reciptionFatawa = $data['reciptionFatawas'];
        $this->reciptions = $data['reciption'];

        // Calculate total statistics for summary cards (filtered by current selection)
        $totalStats = $this->calculateTotalStatistics();

        // Load messages (lightweight)
        $messages = Chat::latest()->limit(100)->get(); // Limit messages for performance
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.reciption-fatawa', [
            'mujeebs' => $this->mujeebs,
            'recdate' => $this->recdate, // Current month's rec dates
            'daruliftaNames' => $this->daruliftaNames,
            'reciptionFatawa' => $this->reciptionFatawa,
            'obtain' => $this->obtain ?? collect(),
            'mahlenazar_null' => $this->mahlenazar_null ?? collect(),
            'que_day_r' => $this->que_day_r ?? collect(),
            'messages' => $messages,
            'selectedexclude' => $this->selectedexclude,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'allfatawa' => collect(), // Remove heavy query - not needed for month view
            'codebylower' => collect(), // Remove heavy query - not needed for month view
            'datefilter' => $this->datefilter ?? collect(),
            'notsentiftacode' => $this->notsentiftacode ?? collect(),
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'showDetail' => $this->showDetail,
            'resultstatus' => $this->resultstatus ?? [],
            'userName' => $this->userName,
            'daruliftalist' => $this->daruliftalist ?? collect(),
            'reciptions' => $this->reciptions, // Pass the reciption data
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
            'tempStartDate' => $this->tempStartDate,
            'tempEndDate' => $this->tempEndDate,
            // Month pagination data
            'currentMonth' => $this->currentMonth,
            'currentMonthIndex' => $this->currentMonthIndex,
            'availableMonths' => $this->availableMonths,
            'monthCounts' => $this->monthCounts,
            'totalMonths' => $this->totalMonths,
            // Total statistics for summary cards
            'totalStats' => $totalStats,
        ])
        ->layout('layouts.app');
        
    }
    public function reciptionFatawadata()
    {
        $reciptionFatawas = [];
        $reciption = collect(); // Initialize as a collection

        // If no darulifta names, return empty data
        if (empty($this->daruliftaNames)) {
            return ['reciptionFatawas' => $reciptionFatawas, 'reciption' => $reciption];
        }

        // Base query
        $baseQuery = DB::table('questions')
            ->whereIn('question_branch', $this->daruliftaNames);

        // Apply time frame filters - use tempSelectedTimeFrame for consistency
        if ($this->tempSelectedTimeFrame == 'all') {
            // No date restriction for "All"
        } elseif ($this->tempSelectedTimeFrame == 'this_month') {
            $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->startOfMonth()->format('Y-m-d'),
                now()->endOfMonth()->format('Y-m-d')
            ]);
        } elseif ($this->tempSelectedTimeFrame == 'last_month') {
            $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->subMonth()->startOfMonth()->format('Y-m-d'),
                now()->subMonth()->endOfMonth()->format('Y-m-d')
            ]);
        } elseif ($this->tempSelectedTimeFrame == 'other' && !empty($this->tempSelectedMonths)) {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, $this->tempSelectedMonths);
            $baseQuery->whereIn(DB::raw("DATE_FORMAT(rec_date, '%Y-%m')"), $formattedSelectedMonths);
        } elseif ($this->tempSelectedTimeFrame == 'custom' && $this->tempStartDate && $this->tempEndDate) {
            $baseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                Carbon::parse($this->tempStartDate)->format('Y-m-d'),
                Carbon::parse($this->tempEndDate)->format('Y-m-d')
            ]);
        } elseif (!$this->tempSelectedTimeFrame || $this->tempSelectedTimeFrame == '') {
            // Default to current month only if no timeframe is explicitly set
            if ($this->currentMonth) {
                $baseQuery->whereRaw("DATE_FORMAT(rec_date, '%Y-%m') = ?", [$this->currentMonth]);
            }
        }

        // Apply mujeeb filter if selected
        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $baseQuery->where('assign_id', $this->selectedmujeeb);
        }

        // Handling different exclusion conditions
        if ($this->selectedexclude == 'exclude_ok') {
            $baseQuery->whereNotIn('ifta_code', function ($query) {
                $query->select('file_code')
                    ->from('uploaded_files')
                    ->where('checked_folder', '=', 'ok');
            });
        }

        // Get all data in one query
        $allQuestions = $baseQuery->orderBy('rec_date', 'desc')->get();

        // Group the results by darulifta and date
        foreach ($allQuestions as $question) {
            $daruliftaName = $question->question_branch;
            $recDate = $question->rec_date;

            if (!isset($reciptionFatawas[$daruliftaName])) {
                $reciptionFatawas[$daruliftaName] = [];
            }
            if (!isset($reciptionFatawas[$daruliftaName][$recDate])) {
                $reciptionFatawas[$daruliftaName][$recDate] = collect();
            }

            $reciptionFatawas[$daruliftaName][$recDate]->push($question);
            $reciption->push($question);
        }

        return ['reciptionFatawas' => $reciptionFatawas, 'reciption' => $reciption];

    }

    /**
     * Calculate total statistics for summary cards (filtered by current month when month pagination is active)
     */
    public function calculateTotalStatistics()
    {
        if (empty($this->daruliftaNames)) {
            return [
                'totalFatawa' => 0,
                'totalFolders' => 0,
                'emailFatawa' => 0,
                'dailyFatawa' => 0,
                'notAssignedToMujeeb' => 0,
                'notSentForChecking' => 0
            ];
        }

        // Base query for total statistics
        $totalBaseQuery = DB::table('questions')
            ->whereIn('question_branch', $this->daruliftaNames);

        // Apply time frame filters to statistics
        if ($this->tempSelectedTimeFrame == 'all') {
            // No date restriction for "All"
        } elseif ($this->tempSelectedTimeFrame == 'this_month') {
            $totalBaseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->startOfMonth()->format('Y-m-d'),
                now()->endOfMonth()->format('Y-m-d')
            ]);
        } elseif ($this->tempSelectedTimeFrame == 'last_month') {
            $totalBaseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->subMonth()->startOfMonth()->format('Y-m-d'),
                now()->subMonth()->endOfMonth()->format('Y-m-d')
            ]);
        } elseif ($this->tempSelectedTimeFrame == 'other' && !empty($this->tempSelectedMonths)) {
            $formattedSelectedMonths = array_map(function ($date) {
                return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
            }, $this->tempSelectedMonths);
            $totalBaseQuery->whereIn(DB::raw("DATE_FORMAT(rec_date, '%Y-%m')"), $formattedSelectedMonths);
        } elseif ($this->tempSelectedTimeFrame == 'custom' && $this->tempStartDate && $this->tempEndDate) {
            $totalBaseQuery->whereBetween(DB::raw('DATE(rec_date)'), [
                Carbon::parse($this->tempStartDate)->format('Y-m-d'),
                Carbon::parse($this->tempEndDate)->format('Y-m-d')
            ]);
        } elseif (!$this->tempSelectedTimeFrame || $this->tempSelectedTimeFrame == '') {
            // When month pagination is active and no explicit timeframe is set, filter by current month
            if ($this->currentMonth) {
                $totalBaseQuery->whereRaw("DATE_FORMAT(rec_date, '%Y-%m') = ?", [$this->currentMonth]);
            }
        }

        // Apply mujeeb filter
        if ($this->selectedmujeeb && $this->selectedmujeeb != 'all') {
            $totalBaseQuery->where('assign_id', $this->selectedmujeeb);
        }

        // Apply exclusion filter
        if ($this->selectedexclude == 'exclude_ok') {
            $totalBaseQuery->whereNotIn('ifta_code', function ($query) {
                $query->select('file_code')
                    ->from('uploaded_files')
                    ->where('checked_folder', '=', 'ok');
            });
        }

        // Total Fatawa count
        $totalFatawa = $totalBaseQuery->count();

        // Total Folders (unique ifta_code from questions table) - use same filters as fatawa
        // Use selectRaw with COUNT(DISTINCT) for proper distinct counting
        $totalFolders = (clone $totalBaseQuery)
            ->whereNotNull('ifta_code')
            ->where('ifta_code', '!=', '')
            ->selectRaw('COUNT(DISTINCT ifta_code) as folder_count')
            ->value('folder_count');

        // Email Fatawa (questions with question_type = 'Email')
        $emailFatawa = (clone $totalBaseQuery)->where('question_type', 'Email')->count();

        // Daily Fatawa (questions with question_type = 'Daily')
        $dailyFatawa = (clone $totalBaseQuery)->where('question_type', 'Daily')->count();

        // Not Assigned to Mujeeb
        $notAssignedToMujeeb = (clone $totalBaseQuery)->where(function($query) {
            $query->whereNull('assign_id')->orWhere('assign_id', '');
        })->count();

        // Not Sent for Checking (questions where send_to_mufti = 0 or null)
        $notSentForChecking = (clone $totalBaseQuery)->where(function($query) {
            $query->where('send_to_mufti', 0)->orWhereNull('send_to_mufti');
        })->count();

        return [
            'totalFatawa' => $totalFatawa,
            'totalFolders' => $totalFolders,
            'emailFatawa' => $emailFatawa,
            'dailyFatawa' => $dailyFatawa,
            'notAssignedToMujeeb' => $notAssignedToMujeeb,
            'notSentForChecking' => $notSentForChecking
        ];
    }

public function fetchDuplicatedFiles()
    {
        // Initialize resultstatus as empty array if null
        if ($this->resultstatus === null) {
            $this->resultstatus = [];
        }

        // Only fetch if not already loaded
        if (!empty($this->resultstatus)) {
            return;
        }

        // Check if daruliftaNames is available
        if (empty($this->daruliftaNames)) {
            $this->resultstatus = [];
            return;
        }

        // Optimized: Single query with join instead of loop
        $this->resultstatus = DB::table('uploaded_files as uf1')
            ->join(DB::raw('(SELECT file_code, MAX(id) as max_id, COUNT(*) as count FROM uploaded_files GROUP BY file_code HAVING COUNT(*) > 1) as uf2'),
                'uf1.file_code', '=', 'uf2.file_code')
            ->where('uf1.id', '=', DB::raw('uf2.max_id'))
            ->whereIn('uf1.darulifta_name', $this->daruliftaNames) // Filter by user's darulifta
            ->select('uf1.*')
            ->get()
            ->toArray();
    }

}
