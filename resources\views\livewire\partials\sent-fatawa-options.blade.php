<!-- Display Options Section -->
<div class="display-options">
    <h5 class="mb-3">
        <i class="fas fa-cog me-2"></i>
        Display Options
    </h5>
    
    <div class="checkbox-group">
        <div class="checkbox-item">
            <input type="checkbox" id="show-detail" onchange="updateUrl(true)" {{ request('showDetail') === '1' ? 'checked' : '' }}>
            <label for="show-detail">
                <i class="fas fa-list-alt me-1"></i>
                Load Fatawa Detail
            </label>
        </div>
        
        <div class="checkbox-item">
            <input type="checkbox" id="show-que" onchange="updateUrl(true)" {{ request('showQue') === '1' ? 'checked' : '' }}>
            <label for="show-que">
                <i class="fas fa-question-circle me-1"></i>
                Show Questions
            </label>
        </div>
        
        <div class="checkbox-item">
            <input type="checkbox" id="show-chat" onchange="updateUrl(true)" {{ request('showChat') === '1' ? 'checked' : '' }}>
            <label for="show-chat">
                <i class="fas fa-comments me-1"></i>
                Show Chat
            </label>
        </div>
    </div>
</div>

<!-- PDF Export Section (Admin Only) -->
@php
    $daruliftaArray = [];
    $mailfolderArray = [];
    $checkedArray = [];

    foreach ($sendingFatawa as $daruliftaName => $checkers) {
        foreach ($checkers as $checked => $mailfolderGroups) {
            foreach ($mailfolderGroups as $mailfolderDates => $fatawaData) {
                $daruliftaArray[] = $daruliftaName;
                $mailfolderArray[] = $mailfolderDates;
                $checkedArray[] = $checked;
            }
        }
    }

    $daruliftaArray = array_unique($daruliftaArray);
    $mailfolderArray = array_unique($mailfolderArray);
    $checkedArray = array_unique($checkedArray);
    $Roles = Auth::user()->roles->pluck('name')->toArray();
    $ifAdmin = in_array('Admin', $Roles);
@endphp

@if ($ifAdmin)
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <h5 class="mb-0">
                <i class="fas fa-file-pdf me-2"></i>
                Export Options
            </h5>
        </div>
        <div class="modern-card-body">
            <div class="d-flex justify-content-center">
                @php
                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                @endphp
                <a href="{{ route('viewPdf', [
                    'darulifta' => $daruliftaArray,
                    'mailfolder' => $mailfolderArray,
                    'selectedmujeeb' => $selectedmujeeb,
                    'selectedmufti' => $selectedmufti,
                    'selectedchecked' => $selectedchecked,
                    'selectedTimeFrame' => $tempSelectedTimeFrame,
                    'checker' => $checkedArray,
                    'startDate' => $startDate,
                    'endDate' => $endDate,
                ]) }}&{{ $selectedMonthsQuery }}" 
                   class="btn-modern btn-danger-modern" target="_blank">
                    <i class="fas fa-file-pdf me-2"></i>
                    Export to PDF
                </a>
            </div>
        </div>
    </div>
@endif

<script>
function updateUrl(reload = false) {
    const showDetail = document.getElementById('show-detail').checked ? '1' : '0';
    const showQue = document.getElementById('show-que').checked ? '1' : '0';
    const showChat = document.getElementById('show-chat').checked ? '1' : '0';
    
    const url = new URL(window.location);
    url.searchParams.set('showDetail', showDetail);
    url.searchParams.set('showQue', showQue);
    url.searchParams.set('showChat', showChat);
    
    if (reload) {
        window.location.href = url.toString();
    } else {
        window.history.replaceState({}, '', url.toString());
    }
}

// Initialize URL on page load
document.addEventListener('DOMContentLoaded', function() {
    updateUrl(false);
});
</script>
