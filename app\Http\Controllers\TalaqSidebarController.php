<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Header;
use App\Models\ParentModel;
use App\Models\Child;
use App\Models\TalaqFatawa;
use Illuminate\Support\Facades\DB;



class TalaqSidebarController extends Controller
{

    public function saveFatawa(Request $request)
    {
    //    dd($request);
        // Prepare parameters for file_name
        $ifta_code = str_replace('-', '_', $request->input('ifta_code')); // Convert Faj-8447 to Faj_8447
        $assign_id = str_replace(' ', '_', $request->input('assign_id')); // Convert <PERSON> to Abdul_Razzaque
        $rec_date = \Carbon\Carbon::createFromFormat('Y-m-d', $request->input('rec_date'))->format('d_M_Y'); // Convert 2023-12-30 to 30_Dec_2023
        $file_name = str_replace(' ', '_', $request->input('file_name')); // Convert Tin talaq to Tin_talaq

        // Construct the final file name
        $constructed_file_name = "{$ifta_code}_{$assign_id}_{$rec_date}_{$file_name}_editor";

        DB::beginTransaction(); // Start transaction

        try {
            // Check for duplicate ifta_code
            $iftaCode = $request->input('ifta_code'); // Single value

            $existingFile = DB::table('talaq_fatawa_manage')
                ->where('file_code', $iftaCode)
                ->where('selected', 0)
                ->first();

            if ($existingFile) {
                DB::rollBack();
                return back()->with('error', 'Ifta Code "'
                    . $existingFile->file_code . '" already sent for checking to checker "'
                    . $existingFile->checker . '" in mail folder date "'
                    . $existingFile->mail_folder_date . '" by "'
                    . $existingFile->sender . '".');
            }

            // Insert data into talaq_fatawa table and get the ID of the inserted row
            $talaqFatawaId = DB::table('talaq_fatawa')->insertGetId([
                'content' => $request->input('content'),
                'ifta_code' => $request->input('ifta_code'),
                'rec_date' => $request->input('rec_date'),
                'assign_id' => $request->input('assign_id'),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // Check if "Save and Send" action
            if ($request->input('action') === 'save_and_send') {
                DB::table('talaq_fatawa_manage')->insert([
                    'talaq_fatawa_id' => $talaqFatawaId, // Add the ID of the talaq_fatawa record
                    'file_name' => $constructed_file_name, // Use constructed file name
                    'file_code' => $request->input('ifta_code'),
                    'sender' => $request->input('assign_id'),
                    'file_created_date' => $request->input('rec_date'),
                    'ftype' => $request->input('ftype'),
                    'darulifta_name' => $request->input('question_branch'),
                    'category' => $request->input('issue'),
                    'checker' => $request->input('checker'),
                    'mail_folder_date' => now(),
                    'mail_recived_date' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            DB::commit(); // Commit transaction

            return redirect()->route('talaq-remaining')->with('success', 'Data saved and sent successfully.');
        } catch (\Exception $e) {
            DB::rollBack(); // Rollback transaction on error
            return back()->with('error', 'An error occurred while saving data: ' . $e->getMessage());
        }
    }

    public function saveMnFatawa(Request $request, $recordId)
    {
        DB::beginTransaction(); // Start transaction

        try {
            // First, fetch the existing record from talaq_fatawa_manage using talaq_checked_id
            $existingRecord = DB::table('talaq_fatawa_manage')
                ->where('talaq_checked_id', $recordId)
                ->first();

            if (!$existingRecord) {
                DB::rollBack();
                return back()->with('error', 'No matching record found in talaq_fatawa_manage.');
            }

            // Check for duplicate ifta_code in talaq_fatawa_manage with selected = 0
            $existingFile = DB::table('talaq_fatawa_manage')
                ->where('file_code', $existingRecord->file_code)
                ->where('selected', 0)
                ->first();

            if ($existingFile) {
                DB::rollBack();
                return back()->with('error', 'Ifta Code "'
                    . $existingFile->file_code . '" already sent for checking to checker "'
                    . $existingFile->checker . '" in mail folder date "'
                    . $existingFile->mail_folder_date . '" by "'
                    . $existingFile->sender . '".');
            }
            $editorContent = $request->input('content');

            // Insert into talaq_fatawa using the content provided in the form (the textarea)
            $talaqFatawaId = DB::table('talaq_fatawa')->insertGetId([
                'content'   => $editorContent, // using textarea input content
                'ifta_code' => $existingRecord->file_code,
                'rec_date'  => now(),
                'assign_id' => $existingRecord->sender,
                'created_at'=> now(),
                'updated_at'=> now(),
            ]);

            // If the "Save and Send" action is triggered, insert a record in talaq_fatawa_manage.
            if ($request->input('action') === 'save_and_send') {
                DB::table('talaq_fatawa_manage')->insert([
                    'talaq_fatawa_id'  => $talaqFatawaId,
                    'file_name'        => $existingRecord->file_name,
                    'file_code'        => $existingRecord->file_code,
                    'sender'           => $existingRecord->sender,
                    'file_created_date'=> now(),
                    'ftype'            => $request->input('ftype'), // from form select
                    'darulifta_name'   => $existingRecord->darulifta_name,
                    'category'         => $existingRecord->category,
                    'checker'          => $request->input('checker'), // from form select
                    'mail_folder_date' => now(),
                    'mail_recived_date'=> now(),
                    'created_at'       => now(),
                    'updated_at'       => now(),
                ]);
            }

            DB::commit(); // Commit transaction

            return redirect()->route('talaq-remaining')->with('success', 'Data saved and sent successfully.');
        } catch (\Exception $e) {
            DB::rollBack(); // Rollback transaction on error
            return back()->with('error', 'An error occurred while saving data: ' . $e->getMessage());
        }
    }


public function showForm()
{
    $checkers = DB::table('checkers')->get();
    return view('talaq-fatawa-manage', compact('checkers'));
}
    public function index(Request $request)
    {
        $headers = Header::all();

        // Fetch data based on filters
        $selectedHeader = $request->get('header_id');
        $selectedParent = $request->get('parent_id');

        $parents = ParentModel::query();
        $children = Child::query();

        if ($selectedHeader) {
            // Filter parents based on the selected header
            $parents->where('header_id', $selectedHeader);

            // Get parent IDs related to the selected header
            $parentIds = ParentModel::where('header_id', $selectedHeader)->pluck('id');

            // Filter children based on parent IDs
            $children->whereIn('parent_id', $parentIds);
        }

        if ($selectedParent) {
            // Filter children based on the selected parent
            $children->where('parent_id', $selectedParent);
        }

        return view('talaq-fatawa-manage', [
            'headers' => $headers,
            'parents' => $parents->get(),
            'children' => $children->get(),
            'selectedHeader' => $selectedHeader,
            'selectedParent' => $selectedParent,
        ]);
    }
    public function fetchQuestionData($id)
    {
        $question = DB::table('questions')->find($id);

        if (!$question) {
            return response()->json(['error' => 'Question not found'], 404);
        }

        return response()->json([
            'rec_date' => $question->rec_date,
            'ifta_code' => $question->ifta_code,
            'question' => $question->question,
            'assign_id' => $question->assign_id,
            'sayel' => $question->sayel,
            'issue' => $question->issue,
            'question_branch' => $question->question_branch,
        ]);
    }

}
