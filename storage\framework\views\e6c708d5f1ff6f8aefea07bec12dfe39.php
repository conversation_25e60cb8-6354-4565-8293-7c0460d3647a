<?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show bg-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show bg-gray-200']); ?>
    <?php if (isset($component)) { $__componentOriginaleeb4de73933bf6c97cffe74e5846276e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.sidebar','data' => ['activePage' => ''.e($activePage ?? request()->route()->getName() ?? 'dashboard').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => ''.e($activePage ?? request()->route()->getName() ?? 'dashboard').'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $attributes = $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $component = $__componentOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <?php if (isset($component)) { $__componentOriginal778d3beb0063990dd56df93abee65235 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal778d3beb0063990dd56df93abee65235 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.navs.auth','data' => ['titlePage' => ''.e($titlePage ?? 'Dashboard').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.navs.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['titlePage' => ''.e($titlePage ?? 'Dashboard').'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $attributes = $__attributesOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__attributesOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $component = $__componentOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__componentOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
        <!-- End Navbar -->
        
        <?php echo $__env->yieldContent('content'); ?>
        
    </main>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/layouts/user_type/auth.blade.php ENDPATH**/ ?>