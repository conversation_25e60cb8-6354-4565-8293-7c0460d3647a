<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;
use Illuminate\Http\Request; // Add this line


// #[Layout('layouts.app')]
class ViralFatwaDetail extends Component
{
    public $selectedMonths = [];
    public $checkerlist;
    public $notsentiftacode;
    public $munsab;
    public $datefilter;
    public $showTrail = false;
    public $showQue = false;
    public $showChat = false;
    public $codebylower;
    public $message;
    public $allfatawa;
    public $userName;
    public $checker;
    Public $obtain;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $remainingFatawa;
    public $daruliftaNames;
    public $mailfolderDate;
    public $darulifta;
    public $mailfolder;
    public $mujeebs;
    public $daruliftalist;
    public $fileCode;
    public $fatwa;
    public $resultstatus = [];
    public $duplicatedFiles;
    // protected $listeners = ['updateSelectedTimeFrame'];
    
    public function mount(Request $request, $fatwa = null)
        {

            
           
            
            $this->showTrail = $request->query('showTrail', false) == '1';
            $this->showQue = $request->query('showQue', false) == '1';
            $this->showChat = $request->query('showChat', false) == '1';
            $this->userName = Auth::user()->name; // Assuming you have the user's name here

        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;
        
        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();


        // Additional initialization code
    }
    // public function updatedShowQue($value)
    // {
    //     $this->dispatch('toggleAllOpen', ['value' => $value]);
    // }
    public function selectFileCode($fileCode)
    {
        $this->selectedfileCode = $fileCode;
    }
    public function applyFilters()
    {
        $this->selectedTimeFrame = $this->tempSelectedTimeFrame;
        $this->selectedMonths = $this->tempSelectedMonths;
        $this->startDate = $this->tempStartDate;
        $this->endDate = $this->tempEndDate;

        // Check if custom date range is set, and adjust the selectedTimeFrame
        if ($this->startDate && $this->endDate) {
            $this->selectedTimeFrame = 'custom';
        }

        $this->remainingFatawadata();
    }
    
    
    public function updateSelectedChecked($checked)
    {
        $this->selectedchecked = $checked;
    }

    private function loadDaruliftaNames()
    {
        
        
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('file_code', $this->fatwa)
                ->distinct()
                ->pluck('darulifta_name');
        
            $this->mujeebs = DB::table('uploaded_files')
                ->select('sender')
                ->where('selected', 1)
                ->where('file_code', $this->fatwa)
                ->distinct()
                ->pluck('sender');
            $this->fileCode = DB::table('uploaded_files')
                ->select('file_code')
                ->where('selected', 1)
                ->where('file_code', $this->fatwa)
                ->distinct()
                ->pluck('file_code');
        
    }
    private function loadMailfolderDate()
    {
        
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('file_code', $this->fatwa)
    ->distinct()
    
    ->pluck('mail_folder_date');
    
}
public function sendMessage($iftaCode)
{
    
    // Save the message to the database
    Chat::create([
        'ifta_code' => $this->fatwa,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.
    

    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}    
public function render()
    {
        
        // $this->obtain = DB::table('uploaded_files')
        // ->where('selected', 0)
        // // Add any additional conditions here
        // ->select('mail_folder_date', DB::raw('SUM(total_score) as total_score_sum'))
        // ->groupBy('mail_folder_date')
        // ->havingRaw('COUNT(mail_folder_date) > 1')
        // ->get();
        $this->duplicatedFiles = DB::table('uploaded_files')
            ->select('file_code', DB::raw('MAX(id) as max_id'))
            ->groupBy('file_code')
            ->havingRaw('COUNT(file_code) >= 1')  // Changed from >= 1 to > 1 to actually check for duplicates
            ->get();
            foreach ($this->duplicatedFiles as $duplicatedFile) {
                $file = DB::table('uploaded_files')
                    ->where('file_code', $duplicatedFile->file_code)
                    ->where('id', $duplicatedFile->max_id)
                    ->first();
    
                if ($file) {
                    $this->resultstatus[] = $file;
                }
            }
        $this->notsentiftacode = DB::table('questions')
        ->select('ifta_code')
        ->whereNotIn('ifta_code', function ($subquery) {
            $subquery->select(DB::raw('LOWER(file_code)'))
                ->from('uploaded_files');
        })
        ->get();
        $this->daruliftalist = DB::table('uploaded_files')
        ->join('daruliftas', 'uploaded_files.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('uploaded_files.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('uploaded_files.darulifta_name');
        
        // $this->mujeebs = DB::table('uploaded_files')
        // ->select('sender')
        // ->where('selected',1)
        // ->where('darulifta_name',$this->darulifta)
        // ->distinct()
        
        // ->pluck('sender');

        $this->checkerlist = DB::table('checker')
            ->get();
        $this->que_day_r = DB::table('questions')
        ->where('ifta_code', $this->fatwa)
        ->get();
        $this->allfatawa = DB::table('uploaded_files')
        ->where('file_code', $this->fatwa)
    ->orderBy('id', 'asc') // Sorting by 'id' in ascending order
    ->get();
    $this->datefilter= DB::table('uploaded_files')
    ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
    ->orderBy('year', 'asc')
    ->orderBy('month', 'asc')
    ->get();

    $this->codebylower = DB::table('uploaded_files as uf1')
    ->select('uf1.*')
    ->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files GROUP BY file_code) as uf2'), function($join) {
        $join->on('uf1.id', '=', 'uf2.id');
    })
    ->orderBy('uf1.id', 'asc')
    ->get();
    
        $this->mahlenazar_null = DB::table('uploaded_files as u1')
        ->whereIn('u1.darulifta_name', $this->daruliftaNames)
        ->where('u1.checked_folder', 'Mahl-e-Nazar')
        ->where('u1.file_code', $this->fatwa)
        
        ->leftJoin('uploaded_files as u2', function ($join) {
            $join->on('u1.file_code', '=', 'u2.file_code')
                ->where(function ($query) {
                    $query->where('u2.checked_folder', 'ok')
                    
                        ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere
                        
                });
        })
        ->whereNull('u2.file_code')
        
        ->select('u1.*')
        ->get();
        $this->remainingFatawa = $this->remainingFatawadata();
// dd($this->daruliftaNames,$this->fatwa,$this->mailfolderDate,$this->remainingFatawa);
// dd($this->notsentiftacode,$this->remainingFatawa,$this->resultstatus,$this->duplicatedFiles,$this->que_day_r );
        $messages = Chat::latest()->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.viral-fatwa-detail', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'remainingFatawa' => $this->remainingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'showTrail' => $this->showTrail,
            'showChat' => $this->showChat,
            'showQue' => $this->showQue,
            'checkerlist' => $this->checkerlist,
            'daruliftalist' => $this->daruliftalist,
            'mujeebs' => $this->mujeebs,
            'fileCode' => $this->fileCode,
            'fatwa' => $this->fatwa,
            'notsentiftacode' => $this->notsentiftacode,
            'resultstatus' => $this->resultstatus,
            'duplicatedFiles' => $this->duplicatedFiles,
            
            
            
            
            
            // ... (other data to be passed to the view)
        ]
        
        )
        ->layout('layouts.app');
        
    }
    public function remainingFatawadata()
{
    $remainingFatawas = [];

    $query = DB::table('questions')
        ->leftJoin('uploaded_files', function ($join) {
            $join->on(DB::raw('LOWER(questions.ifta_code)'), '=', DB::raw('LOWER(uploaded_files.file_code)'))
                 ->where('uploaded_files.viral', 1);
        })
        ->where('questions.ifta_code', $this->fatwa)
        ->select('questions.*', 'uploaded_files.*')
        ->get();

    if ($query->isNotEmpty()) {
        $remainingFatawas = $query;
    }

    return $remainingFatawas;
}
    // public function fetchDuplicatedFiles()
    // {
    //     $duplicatedFiles = DB::table('uploaded_files')
    //         ->select('file_code', DB::raw('MAX(id) as max_id'))
    //         ->groupBy('file_code')
    //         ->havingRaw('COUNT(file_code) >= 1')  // Changed from >= 1 to > 1 to actually check for duplicates
    //         ->get();

    //     foreach ($duplicatedFiles as $duplicatedFile) {
    //         $file = DB::table('uploaded_files')
    //             ->where('file_code', $duplicatedFile->file_code)
    //             ->where('id', $duplicatedFile->max_id)
    //             ->first();

    //         if ($file) {
    //             $this->resultstatus[] = $file;
    //         }
    //     }
    // }


}
