<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    // database/migrations/xxxx_xx_xx_create_check_fatawa_table.php

public function up()
{
    Schema::create('check_fatawa', function (Blueprint $table) {
        $table->id();
        $table->date('enter_date');
        $table->string('ifta_name');
        $table->string('ifta_code');
        $table->string('ifta_number');
        $table->text('details')->nullable();
        $table->timestamps();
    });
}


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('check_fatawa');
    }
};
