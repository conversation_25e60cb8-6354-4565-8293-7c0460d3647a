<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Performance Review & Rating</h5>
                            <p class="text-sm mb-0">Review and rate subordinate performance submissions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">Date</label>
                                <input type="date" wire:model.live="selectedDate" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">User</label>
                                <select wire:model.live="selectedUser" class="form-select">
                                    <option value="all">All Users</option>
                                    @foreach($subordinateUsers as $user)
                                        <option value="{{ $user->id }}">{{ $user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">Rating Status</label>
                                <select wire:model.live="selectedStatus" class="form-select">
                                    <option value="all">All</option>
                                    <option value="unrated">Unrated</option>
                                    <option value="rated">Rated</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">Statistics</label>
                                <div class="d-flex gap-2">
                                    <span class="badge bg-warning">{{ $performances->where('superior_rating', null)->count() }} Unrated</span>
                                    <span class="badge bg-success">{{ $performances->whereNotNull('superior_rating')->count() }} Rated</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance List -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Performance Submissions</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hours</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Self Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Superior Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($performances as $performance)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $performance->user->name }}</h6>
                                                <p class="text-xs text-secondary mb-0">{{ $performance->user->email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm">{{ $performance->task->title ?? 'No Task' }}</h6>
                                            @if($performance->department)
                                                <p class="text-xs text-secondary mb-0">{{ $performance->department->name }}</p>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $performance->performance_date->format('M d, Y') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            {{ $performance->hours_worked ?? '-' }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->overall_rating)
                                            <span class="badge badge-sm bg-gradient-{{ $this->getRatingColor($performance->overall_rating) }}">
                                                {{ ucfirst($performance->overall_rating) }}
                                            </span>
                                        @else
                                            <span class="text-secondary">-</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($performance->superior_rating)
                                            <span class="badge badge-sm bg-gradient-{{ $this->getRatingColor($performance->superior_rating) }}">
                                                {{ ucfirst($performance->superior_rating) }}
                                            </span>
                                            @if($performance->ratedBy)
                                                <br><small class="text-xs text-secondary">by {{ $performance->ratedBy->name }}</small>
                                            @endif
                                        @else
                                            <span class="badge badge-sm bg-gradient-warning">Unrated</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button wire:click="openRatingModal({{ $performance->id }})" 
                                                    class="btn btn-sm btn-outline-primary mb-0" title="Rate Performance">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            @if($performance->superior_rating)
                                                <button wire:click="removeRating({{ $performance->id }})" 
                                                        class="btn btn-sm btn-outline-danger mb-0" title="Remove Rating">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <p class="text-secondary mb-0">No performance records found.</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3">
                        {{ $performances->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rating Modal -->
    @if($showRatingModal && $selectedPerformance)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-star text-warning me-2"></i>
                        Rate Performance
                    </h5>
                    <button type="button" class="btn-close" wire:click="closeRatingModal"></button>
                </div>
                <div class="modal-body">
                    <!-- Performance Details -->
                    <div class="card bg-light mb-4">
                        <div class="card-body">
                            <h6 class="mb-3">Performance Details</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>User:</strong> {{ $selectedPerformance->user->name }}</p>
                                    <p class="mb-1"><strong>Task:</strong> {{ $selectedPerformance->task->title ?? 'No Task' }}</p>
                                    <p class="mb-1"><strong>Department:</strong> {{ $selectedPerformance->department->name ?? 'No Department' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Date:</strong> {{ $selectedPerformance->performance_date->format('M d, Y') }}</p>
                                    <p class="mb-1"><strong>Hours Worked:</strong> {{ $selectedPerformance->hours_worked ?? '-' }}</p>
                                    <p class="mb-1"><strong>Self Rating:</strong> 
                                        @if($selectedPerformance->overall_rating)
                                            <span class="badge bg-{{ $this->getRatingColor($selectedPerformance->overall_rating) }}">
                                                {{ ucfirst($selectedPerformance->overall_rating) }}
                                            </span>
                                        @else
                                            <span class="text-muted">No self rating</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            
                            @if($selectedPerformance->tasks_completed)
                                <div class="mt-3">
                                    <strong>Tasks Completed:</strong>
                                    <p class="mb-0 mt-1">{{ $selectedPerformance->tasks_completed }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Rating Form -->
                    <form wire:submit.prevent="submitRating">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Superior Rating *</label>
                                    <select wire:model="superiorRating" class="form-select @error('superiorRating') is-invalid @enderror">
                                        <option value="">Select Rating</option>
                                        <option value="excellent">Excellent</option>
                                        <option value="good">Good</option>
                                        <option value="fair">Fair</option>
                                        <option value="poor">Poor</option>
                                    </select>
                                    @error('superiorRating') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Rating Preview</label>
                                    <div class="form-control bg-light">
                                        @if($superiorRating)
                                            <span class="badge bg-{{ $this->getRatingColor($superiorRating) }}">
                                                {{ ucfirst($superiorRating) }}
                                            </span>
                                        @else
                                            <span class="text-muted">Select a rating</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Comments & Instructions</label>
                            <textarea wire:model="superiorComments" 
                                      class="form-control @error('superiorComments') is-invalid @enderror" 
                                      rows="4" 
                                      placeholder="Provide feedback, comments, or instructions for the user..."></textarea>
                            @error('superiorComments') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeRatingModal">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="submitRating">
                        <i class="fas fa-star me-2"></i>
                        Submit Rating
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif
</div>
