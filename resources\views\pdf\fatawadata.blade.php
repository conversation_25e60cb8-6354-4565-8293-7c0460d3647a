<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fatawa Data</title>
    <style>
       .folder-entries {
        display: inline-block;
        width: 100%;
    }

    .folder-entry {
        display: inline-block;
        
        vertical-align: top; /* Align items to the top */
        flex-direction: column;
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #f9f9f9;
        text-align: left; /* Align text to the left */
    }

    .folder-date {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .folder-transfer-by {
        font-size: 12px;
        color: #555;
    }
    body {
            font-size: 10px; /* Reduce font size */
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        table, th, td {
            border: 1px solid black;
        }

        th, td {
            padding: 5px; /* Reduce padding */
            text-align: left;
        }

        /* Adjust column widths */
        th {
            width: auto;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <h1 class="heading">Fatawa Data</h1>

    <table class="table table-bordered align-items-center mb-0">
        <thead>
            <tr>
            <th style="width: 10%; white-space: nowrap; text-align: left; padding: 5px;">
                Darulifta</th>
                @if ($selectedMufti == 'all')
                    <th>Checker</th>
                @endif
                <th style="width: 80%; white-space: nowrap; text-align: left; padding: 5px;">Sent Fatawa Date</th>

                <th style="width: 10%; white-space: nowrap; text-align: left; padding: 5px;">Total</th>
                </tr>
        </thead>
        <tbody>
            @php
                $totalOkCount = 0;
                $totalMahlENazarCount = 0;
                $totalDuplicateFileCodes = 0;
                $totalUniqueFileCodes = 0;
                $seenFileCodes = [];
                $duplicateFileCodesList = [];
            @endphp
            @if ($selectedMufti == 'all')

            @foreach($daruliftaNames as $daruliftaName)
            @if(isset($sendingFatawa[$daruliftaName]))
                @foreach($sendingFatawa[$daruliftaName] as $checked => $dates)
        @php
            $daruliftaTotalCounts = 0;
            $folderCounts = [];
            $checkedFolderCounts = [];
            $daruliftaOkCount = 0;
            $daruliftaMahlENazarCount = 0;
            $daruliftaDuplicateCount = 0;
            $daruliftaUniqueCount = 0;
            $transferByCounts = [];
        @endphp

        @foreach($dates as $mailfolderDates => $files)
                        @foreach($files as $file)
                        @php
                            $folder = $file->mail_folder_date;
                            $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                            $checkedFolder = $file->checked_folder;

                            if ($checkedFolder) {
                                $checkedFolderCounts[$folder][$checkedFolder] = isset($checkedFolderCounts[$folder][$checkedFolder]) ? $checkedFolderCounts[$folder][$checkedFolder] + 1 : 1;
                            }

                            $fileCode = $file->file_code;
                            if (isset($seenFileCodes[$fileCode])) {
                                if ($seenFileCodes[$fileCode] == 1) {
                                    $totalDuplicateFileCodes++;
                                    $daruliftaDuplicateCount++;
                                    $seenFileCodes[$fileCode]++;
                                }
                                $duplicateFileCodesList[$fileCode] = isset($duplicateFileCodesList[$fileCode]) ? $duplicateFileCodesList[$fileCode] + 1 : 2;
                            } else {
                                $seenFileCodes[$fileCode] = 1;
                                $totalUniqueFileCodes++;
                                $daruliftaUniqueCount++;
                            }

                            if ($checkedFolder == 'ok') {
                                $totalOkCount++;
                                $daruliftaOkCount++;
                            } elseif ($checkedFolder == 'Mahl-e-Nazar') {
                                $totalMahlENazarCount++;
                                $daruliftaMahlENazarCount++;
                            }

                            $byMufti = $file->by_mufti ?? "";
                            $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                            $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] = isset($transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti]) ? $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] + 1 : 1;
                            $folderEntries[$folder] = $folderEntries[$folder] ?? ['count' => 0, 'transfer_by' => []];
                            $folderEntries[$folder]['count']++;
                            $folderEntries[$folder]['transfer_by'][$transferBy] = ($folderEntries[$folder]['transfer_by'][$transferBy] ?? 0) + 1;
                            $daruliftaTotalCounts++;
                            $totalCounts++;
                        @endphp
                        @endforeach
                        @endforeach

                        <tr>
                        <td>
                            @if ($loop->first || $loop->parent->first)
                               

                                    {{ $daruliftaName }}
                              
                            @endif
                        </td>
                        <td>
                            {{ $checked }}
                        </td>
                        <td class="align-middle text-center" style="max-width: 1000px;">
    <div class="folder-entries">
        @php
            $foldercount = 0;
            $currentRoute = 'sent-fatawa';
        @endphp

        @foreach ($folderCounts as $folder => $count)
            <div class="folder-entry">
                <div class="folder-date">
                    
                        {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }}
                     (T-{{ $count }})
                </div>

                @if(isset($transferByCounts[$folder]))
                    @foreach($transferByCounts[$folder] as $transferBy => $checkedFolders)
                        @foreach($checkedFolders as $checkedFolder => $byMuftis)
                            @foreach($byMuftis as $byMufti => $transferByCount)
                                <div class="folder-transfer-by">
                                    
                                        {{ $transferBy }}-{{$checkedFolder}}
                                       
                                    - {{ $transferByCount }}
                                </div>
                            @endforeach
                        @endforeach
                    @endforeach
                @endif
            </div>
            @php
                $foldercount++;
                $overallFolderCount++;
            @endphp
        @endforeach
    </div>
</td>

            <td>
                | Fatawa: {{ $daruliftaTotalCounts }} | Folder: {{ $foldercount }} 
                <br>
                | ok: {{ $daruliftaOkCount }} | Mahl-e-Nazar: {{ $daruliftaMahlENazarCount }} 
                <br>
                | Repeated: {{ $daruliftaDuplicateCount }} | Total Repeated: {{ $daruliftaTotalCounts - ($daruliftaUniqueCount-$daruliftaDuplicateCount) }}
                <br>
                | Unique: {{ $daruliftaUniqueCount-$daruliftaDuplicateCount }}
            </td>
        </tr>
        @endforeach
    @endif
@endforeach
</tbody>
</table>

<h5>Overall Total Fatawa: {{ $totalCounts }} And Folder: {{ $overallFolderCount }}</h5>
<h5>Total "ok" Fatawa: {{ $totalOkCount }}</h5>
<h5>Total "Mahl-e-Nazar" Fatawa: {{ $totalMahlENazarCount }}</h5>
<h5>Repeated Fatawa : {{ $totalDuplicateFileCodes }}</h5>
<h5>Total Repeated Fatawa : {{ $totalCounts - ($totalUniqueFileCodes - $totalDuplicateFileCodes) }}</h5>
<h5>Total Unique Fatawa: {{ $totalUniqueFileCodes-$totalDuplicateFileCodes }}</h5>
            @else
            @foreach($daruliftaNames as $daruliftaName)
    @if(isset($sendingFatawa[$daruliftaName]))
        @php
            $daruliftaTotalCounts = 0;
            $folderCounts = [];
            $checkedFolderCounts = [];
            $daruliftaOkCount = 0;
            $daruliftaMahlENazarCount = 0;
            $daruliftaDuplicateCount = 0;
            $daruliftaUniqueCount = 0;
            $transferByCounts = [];
        @endphp

        @foreach($sendingFatawa[$daruliftaName] as $mailfolderDate => $files)
            @foreach($files as $file)
                @php
                    // Initialize folder and checked folder counts
                    $folder = $file->mail_folder_date;
                    $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                    $checkedFolder = $file->checked_folder;

                    if ($checkedFolder) {
                        $checkedFolderCounts[$folder][$checkedFolder] = isset($checkedFolderCounts[$folder][$checkedFolder]) ? $checkedFolderCounts[$folder][$checkedFolder] + 1 : 1;
                    }

                    // Track unique/duplicate file codes
                    $fileCode = $file->file_code;
                    if (isset($seenFileCodes[$fileCode])) {
                        if ($seenFileCodes[$fileCode] == 1) {
                            $totalDuplicateFileCodes++;
                            $daruliftaDuplicateCount++;
                            $seenFileCodes[$fileCode]++;
                        }
                    } else {
                        $seenFileCodes[$fileCode] = 1;
                        $totalUniqueFileCodes++;
                        $daruliftaUniqueCount++;
                    }

                    // Check folder type (ok/Mahl-e-Nazar)
                    if ($checkedFolder == 'ok') {
                        $totalOkCount++;
                        $daruliftaOkCount++;
                    } elseif ($checkedFolder == 'Mahl-e-Nazar') {
                        $totalMahlENazarCount++;
                        $daruliftaMahlENazarCount++;
                    }

                    // Handle transferBy and Mufti conditions
                    $byMufti = $file->by_mufti ?? '';
                    $transferBy = isset($file->transfer_by) && $file->transfer_by !== '' ? $file->transfer_by : 'Mujeeb';
                    $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] = isset($transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti]) ? $transferByCounts[$folder][$transferBy][$checkedFolder][$byMufti] + 1 : 1;

                    $daruliftaTotalCounts++;
                @endphp
            @endforeach
        @endforeach

        <tr>
        <td style="width: 10%; white-space: nowrap; text-align: left; padding: 5px;"> 
                {{ $daruliftaName }}</td>
                
            <td style="width: 100%; padding: 5px;">
    <div class="folder-entries">
        @foreach ($folderCounts as $folder => $count)
            <div class="folder-entry">
                <div class="folder-date">
                    {{ \Carbon\Carbon::parse($folder)->format('d-m-Y') }} (T-{{ $count }})
                </div>
                @if(isset($transferByCounts[$folder]))
                    @foreach($transferByCounts[$folder] as $transferBy => $checkedFolders)
                        @foreach($checkedFolders as $checkedFolder => $byMuftis)
                            @foreach($byMuftis as $byMufti => $transferByCount)
                                <div class="folder-transfer-by">
                                    {{ $transferBy }} - {{ $checkedFolder }} - {{ $transferByCount }}
                                </div>
                            @endforeach
                        @endforeach
                    @endforeach
                @endif
            </div>
        @endforeach
    </div>
</td>


            <td style="width: 10%; white-space: nowrap; text-align: left; padding: 5px;"> 
                | Fatawa: {{ $daruliftaTotalCounts }} | ok: {{ $daruliftaOkCount }} | Mahl-e-Nazar: {{ $daruliftaMahlENazarCount }} |
                Repeated: {{ $daruliftaDuplicateCount }} | Unique: {{ $daruliftaUniqueCount - $daruliftaDuplicateCount }}
            </td>
        </tr>
    @endif
@endforeach

</tbody>
</table>

<h5>Overall Total Fatawa: {{ $totalCounts }} And Folder: {{ $overallFolderCount }}</h5>
<h5>Total "ok" Fatawa: {{ $totalOkCount }}</h5>
<h5>Total "Mahl-e-Nazar" Fatawa: {{ $totalMahlENazarCount }}</h5>
<h5>Repeated Fatawa: {{ $totalDuplicateFileCodes }}</h5>
<h5>Total Unique Fatawa: {{ $totalUniqueFileCodes - $totalDuplicateFileCodes }}</h5>
@endif
</body>
</html>
