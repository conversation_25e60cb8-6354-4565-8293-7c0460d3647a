<style>
    :root {
        --primary-color: #667eea;
        --secondary-color: #764ba2;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --light-bg: #f8fafc;
        --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --border-radius: 12px;
        --transition: all 0.3s ease;
    }

    /* Modern Card Styling */
    .modern-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        border: 1px solid #e5e7eb;
        transition: var(--transition);
        overflow: hidden;
    }

    .modern-card:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 1.5rem;
        border-bottom: none;
    }

    .modern-card-body {
        padding: 1.5rem;
    }

    /* Filter Section */
    .filter-section {
        background: var(--light-bg);
        border-radius: var(--border-radius);
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #e5e7eb;
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        align-items: end;
    }

    .form-group-modern {
        display: flex;
        flex-direction: column;
    }

    .form-group-modern label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .form-control-modern {
        padding: 0.75rem;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: var(--transition);
        background: white;
    }

    .form-control-modern:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Button Styling */
    .btn-modern {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.875rem;
        transition: var(--transition);
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-primary-modern {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
    }

    .btn-primary-modern:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-success-modern {
        background: var(--success-color);
        color: white;
    }

    .btn-warning-modern {
        background: var(--warning-color);
        color: white;
    }

    .btn-danger-modern {
        background: var(--danger-color);
        color: white;
    }

    .btn-outline-modern {
        background: transparent;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
    }

    .btn-outline-modern:hover {
        background: var(--primary-color);
        color: white;
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    /* Table Styling */
    .table-modern {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    .table-modern thead {
        background: linear-gradient(135deg, #f8fafc, #e5e7eb);
    }

    .table-modern th {
        padding: 1rem;
        font-weight: 600;
        color: #374151;
        text-align: left;
        border-bottom: 2px solid #e5e7eb;
    }

    .table-modern td {
        padding: 1rem;
        border-bottom: 1px solid #f3f4f6;
        vertical-align: top;
    }

    .table-modern tbody tr:hover {
        background: #f8fafc;
    }

    /* Folder Entry Styling */
    .folder-entries-modern {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .folder-entry-modern {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
        transition: var(--transition);
    }

    .folder-entry-modern:hover {
        border-color: var(--primary-color);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .folder-date-modern {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    /* Toggle Sections */
    .toggle-section {
        border: 2px solid #e5e7eb;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        overflow: hidden;
    }

    .toggle-header-modern {
        background: linear-gradient(135deg, #f8fafc, #e5e7eb);
        padding: 1rem 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        color: #374151;
        transition: var(--transition);
    }

    .toggle-header-modern:hover {
        background: linear-gradient(135deg, #e5e7eb, #d1d5db);
    }

    .toggle-content-modern {
        padding: 1.5rem;
        background: white;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: var(--border-radius);
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        border-left: 4px solid var(--primary-color);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .filter-grid {
            grid-template-columns: 1fr;
        }

        .folder-entries-modern {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Urdu Font Support */
    .urdu-text {
        font-family: 'Jameel Noori Nastaleeq', 'Noto Nastaliq Urdu', serif;
        direction: rtl;
        text-align: right;
    }

    /* Loading States */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Action Icons */
    .action-icon {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .action-download {
        color: var(--info-color);
    }

    .action-delete {
        color: var(--danger-color);
    }

    .action-view {
        color: var(--success-color);
    }

    .action-details {
        color: var(--info-color);
    }

    /* Enhanced Action Button Styles */
    .btn-modern.btn-info-modern {
        background-color: #17a2b8;
        border-color: #17a2b8;
        color: white;
    }

    .btn-modern.btn-info-modern:hover {
        background-color: #138496;
        border-color: #117a8b;
        color: white;
    }

    .btn-modern.btn-primary-modern {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .btn-modern.btn-primary-modern:hover {
        background-color: #0056b3;
        border-color: #004085;
        color: white;
    }

    .btn-modern.btn-success-modern {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .btn-modern.btn-success-modern:hover {
        background-color: #1e7e34;
        border-color: #1c7430;
        color: white;
    }

    .btn-modern.btn-danger-modern {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .btn-modern.btn-danger-modern:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
    }

    /* Action buttons container */
    .d-flex.gap-1 {
        gap: 0.25rem !important;
    }

    /* Ensure buttons are visible and properly sized */
    .btn-modern.btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
        min-width: 32px;
        height: 32px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Disabled button styling */
    .btn-modern.opacity-50 {
        opacity: 0.5 !important;
        cursor: not-allowed;
    }

    /* Question and Chat Styles */
    .question-text {
        padding: 10px;
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        margin: 5px 0;
        border-radius: 4px;
        font-family: 'Noto Nastaliq Urdu', serif;
        direction: rtl;
        text-align: right;
    }

    /* Chat Interface Styles */
    .chat-message {
        padding: 8px 12px;
        margin: 5px 0;
        background-color: #e9ecef;
        border-radius: 8px;
        border-left: 3px solid #28a745;
    }

    .chat-message strong {
        color: #495057;
    }

    .toggle-chat, .toggle-question {
        transition: background-color 0.3s ease;
    }

    .toggle-chat:hover, .toggle-question:hover {
        background-color: #FFB366 !important;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    /* Chat Card Styles */
    .chat-container .card {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .chat-container .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .chat-container .badge {
        font-size: 0.875rem;
    }

    .chat-container .input-group {
        border-radius: 0.5rem;
    }

    .chat-container .form-control {
        border: none;
        box-shadow: none;
    }

    .chat-container .btn-link {
        color: #007bff;
        text-decoration: none;
    }

    .chat-container .btn-link:hover {
        color: #0056b3;
    }

    /* Chat message alignment */
    .chat-container .list-unstyled li {
        margin-bottom: 1rem;
    }

    .chat-container .w-auto {
        max-width: 70%;
    }
</style>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-styles.blade.php ENDPATH**/ ?>