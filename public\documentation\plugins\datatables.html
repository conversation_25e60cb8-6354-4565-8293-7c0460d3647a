<!DOCTYPE html>
<html>

  <head>
    <meta charset="utf-8">
    <link rel="apple-touch-icon" sizes="76x76" href="../../assets/img/apple-icon.png">
    <link rel="icon" href="../../assets/img/favicon.png" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="author" content="Creative Tim">
    <title>
      Datatables | Material Dashboard 2 FREE Laravel by Creative Tim & UPDIVISION
    </title>
    <link rel="canonical" href="https://www.creative-tim.com/learning-lab/bootstrap/colors/material-dashboard" />

    <meta name="keywords" content="creative tim, updivision, html dashboard, laravel, material, html css dashboard laravel, material dashboard laravel, laravel material dashboard, material admin, laravel dashboard, laravel admin, web dashboard, bootstrap 5 dashboard laravel, bootstrap 5, css3 dashboard, bootstrap 5 admin laravel, material dashboard bootstrap 5 laravel, frontend, responsive bootstrap 5 dashboard, material dashboard, material laravel bootstrap 5 dashboard" />
    <meta name="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="name" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta itemprop="description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta itemprop="image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta name="twitter:card" content="product" />
    <meta name="twitter:site" content="@creativetim" />
    <meta name="twitter:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta name="twitter:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta name="twitter:creator" content="@creativetim" />
    <meta name="twitter:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="fb:app_id" content="655968634437471" />
    <meta property="og:title" content="Material Dashboard 2 Laravel by Creative Tim & UPDIVISION" />
    <meta property="og:type" content="article" />
    <meta property="og:url" content="https://www.creative-tim.com/live/material-dashboard-laravel" />
    <meta property="og:image" content="https://s3.amazonaws.com/creativetim_bucket/products/598/original/material-dashboard-laravel.jpg" />
    <meta property="og:description" content="A free Laravel Dashboard featuring dozens of UI components & basic Laravel CRUDs." />
    <meta property="og:site_name" content="Creative Tim" />

    <link rel="stylesheet" href="../../assets/css/nucleo-icons.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/nextjs-material-dashboard-pro.min.css" type="text/css">

    <link rel="stylesheet" href="../../assets/css/material-dashboard.min.css" type="text/css">
    <link rel="stylesheet" href="../../assets/css/demo.css" type="text/css">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">

    <link href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Roboto+Slab:400,700" rel="stylesheet">

    <link href="../../assets/css/nucleo-icons.css" rel="stylesheet">

    <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script>
    <!-- Anti-flicker snippet (recommended)  -->
    <style>
      .async-hide {
        opacity: 0 !important
      }
    </style>
    <script>
      (function(a, s, y, n, c, h, i, d, e) {
        s.className += ' ' + y;
        h.start = 1 * new Date;
        h.end = i = function() {
          s.className = s.className.replace(RegExp(' ?' + y), '')
        };
        (a[n] = a[n] || []).hide = h;
        setTimeout(function() {
          i();
          h.end = null
        }, c);
        h.timeout = c;
      })(window, document.documentElement, 'async-hide', 'dataLayer', 4000, {
        'GTM-K9BGS8K': true
      });
    </script>
    <!-- Analytics-Optimize Snippet -->
    <script>
      (function(i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function() {
          (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
          m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
      })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');
      ga('create', 'UA-46172202-22', 'auto', {
        allowLinker: true
      });
      ga('set', 'anonymizeIp', true);
      ga('require', 'GTM-K9BGS8K');
      ga('require', 'displayfeatures');
      ga('require', 'linker');
      ga('linker:autoLink', ["2checkout.com", "avangate.com"]);
    </script>
    <!-- end Analytics-Optimize Snippet -->
    <!-- Google Tag Manager -->
    <script>
      (function(w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({
          'gtm.start': new Date().getTime(),
          event: 'gtm.js'
        });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : '';
        j.async = true;
        j.src =
          'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, 'script', 'dataLayer', 'GTM-NKDMSK6');
    </script>
    <!-- End Google Tag Manager -->
    <!-- This is for docs typography and layout -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet">
    <link href="../../assets/css/docs.css" rel="stylesheet" />
  </head>
  <body class="docs ">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NKDMSK6" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <header class="ct-docs-navbar">
      <a class="ct-docs-navbar-brand" href="javascript:void(0)" aria-label="Bootstrap">
        </a><a href="https://www.creative-tim.com/" class="ct-docs-navbar-text" target="_blank">
          Creative Tim
        </a>
        <div class="ct-docs-navbar-border"></div>
        <a href="../../documentation/getting-started/installation.html" class="ct-docs-navbar-text">
          Docs
        </a>

      <ul class="ct-docs-navbar-nav-left">
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Live Preview</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownPreview">
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://material-dashboard-pro-laravel.creative-tim.com" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
        <li class="ct-docs-nav-item-dropdown">
          <a href="javascript:;" class="ct-docs-navbar-nav-link" role="button">
            <span class="ct-docs-navbar-nav-link-inner--text">Support</span>
          </a>
          <div class="ct-docs-navbar-dropdown-menu" aria-labelledby="DropdownSupport">
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/material-dashboard-laravel/issues" target="_blank">
              Material Dashboard 2 Laravel
            </a>
            <a class="ct-docs-navbar-dropdown-item" href="https://github.com/creativetimofficial/ct-material-dashboard-pro-laravel/issues" target="_blank">
              Material Dashboard 2 Pro Laravel
            </a>
          </div>
        </li>
      </ul>
      <ul class="ct-docs-navbar-nav-right">
        <li class="ct-docs-navbar-nav-item">
          <a class="ct-docs-navbar-nav-link" href="https://www.creative-tim.com/product/material-dashboard-laravel" target="_blank">Download Free</a>
        </li>
      </ul>
      <a href="https://www.creative-tim.com/product/material-dashboard-pro-laravel" target="_blank" class="ct-docs-btn-upgrade">
        <span class="ct-docs-btn-inner--icon">
          <i class="fas fa-download mr-2" aria-hidden="true"></i>
        </span>
        <span class="ct-docs-navbar-nav-link-inner--text">Upgrade to PRO</span>
      </a>
      <button class="ct-docs-navbar-toggler" type="button">
        <span class="ct-docs-navbar-toggler-icon"></span>
      </button>
    </header>
    <div class="ct-docs-main-container">
      <div class="ct-docs-main-content-row">
        <div class="ct-docs-sidebar-col">
          <nav class="ct-docs-sidebar-collapse-links">
            <div class="ct-docs-sidebar-product">
              <div class="ct-docs-sidebar-product-image">
                <img src="../../assets/img/bootstrap.png">
              </div>
              <p class="ct-docs-sidebar-product-text">Material Dashboard</p>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-active-40 text-white"></i>
                  </div>
                </div>
                Getting started
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/getting-started/overview.html">
                    Overview
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/license.html">
                    License
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/installation.html">
                    Installation
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/build-tools.html">
                    Build Tools
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/getting-started/bootstrap.html">
                    What is Bootstrap
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
                <a class="ct-docs-toc-link" href="javascript:void(0)">
                  <div class="d-inline-block">
                    <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                      <i class="ni ni-folder-17 text-white"></i>
                    </div>
                  </div>
                  Laravel
                </a>
                <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                  <li class="">
                    <a href="../../documentation/laravel/login.html">
                      Login
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/sign-up.html">
                      Sign Up
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/forgot-password.html">
                      Forgot Password
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/user-profile.html">
                      User Profile
                    </a>
                  </li>
                  <li class="">
                    <a href="../../documentation/laravel/user-management.html">
                      User Management
                      <span class="ct-docs-sidenav-pro-badge">Pro</span>
                    </a>
                  </li>
                </ul>
              </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-folder-17 text-white"></i>
                  </div>
                </div>
                Foundation
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class=" ">
                  <a href="../../documentation/foundation/colors.html">
                    Colors
                  </a>
                </li>
                <li class=" ">
                  <a href="../../documentation/foundation/grid.html">
                    Grid
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/typography.html">
                    Typography
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/icons.html">
                    Icons
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/foundation/utilities.html">
                    Utilities
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-app text-white"></i>
                  </div>
                </div>
                Components
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/components/alerts.html">
                    Alerts
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/badge.html">
                    Badge
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/buttons.html">
                    Buttons
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/social-buttons.html">
                    Social Buttons
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/cards.html">
                    Cards
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/carousel.html">
                    Carousel
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/collapse.html">
                    Collapse
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/dropdowns.html">
                    Dropdowns
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/forms.html">
                    Forms
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/input-group.html">
                    Input Group
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/list-group.html">
                    List Group
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/modal.html">
                    Modal
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/navs.html">
                    Navs
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/navbar.html">
                    Navbar
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/pagination.html">
                    Pagination
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/popovers.html">
                    Popovers
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/progress.html">
                    Progress
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/spinners.html">
                    Spinners
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/tables.html">
                    Tables
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/components/tooltips.html">
                    Tooltips
                  </a>
                </li>
              </ul>
            </div>
            <div class="ct-docs-toc-item-active">
              <a class="ct-docs-toc-link" href="javascript:void(0)">
                <div class="d-inline-block">
                  <div class="icon icon-xs border-radius-md bg-gradient-primary text-center mr-2 d-flex align-items-center justify-content-center me-1">
                    <i class="ni ni-settings text-white"></i>
                  </div>
                </div>
                Plugins
              </a>
              <ul class="ct-docs-nav-sidenav ms-4 ps-1">
                <li class="">
                  <a href="../../documentation/plugins/countUpJs.html">
                    CountUp JS
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/charts.html">
                    Charts
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/datepicker.html">
                    Datepicker
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/fullcalendar.html">
                    Fullcalendar
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/sliders.html">
                    Sliders
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/choices.html">
                    Choices
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/dropzone.html">
                    Dropzone
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="ct-docs-nav-sidenav-active">
                  <a href="../../documentation/plugins/datatables.html">
                    Datatables
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/kanban.html">
                    Kanban
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/photo-swipe.html">
                    Photo Swipe
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/quill.html">
                    Quill
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/sweet-alerts.html">
                    Sweet Alerts
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
                <li class="">
                  <a href="../../documentation/plugins/wizard.html">
                    Wizard
                    <span class="ct-docs-sidenav-pro-badge">Pro</span>
                  </a>
                </li>
              </ul>
            </div>
          </nav>
        </div>
        <div class="ct-docs-toc-col">
          <ul class="section-nav">
            <li class="toc-entry toc-h2"><a href="#usage">Usage</a></li>
            <li class="toc-entry toc-h2"><a href="#example">Example</a>
              <ul>
                <li class="toc-entry toc-h3"><a href="#datatable-basic-example">Datatable Basic example</a></li>
              </ul>
            </li>
          </ul>
        </div>
        <main class="ct-docs-content-col" role="main">
          <div class="ct-docs-page-title">
            <h1 class="ct-docs-page-h1-title" id="content">
              Bootstrap Datatables
            </h1>
            <span class="ct-docs-page-title-pro-line"> - </span>
            <div class="ct-docs-page-title-pro-bage">Pro Component</div>
            <div class="avatar-group mt-3">
            </div>
          </div>
          <p class="ct-docs-page-title-lead">Check out our Bootstrap datatables examples and learn how to add advanced interaction controls to your HTML tables the easy way.</p>
          <hr class="ct-docs-hr">
          <h2 id="usage">Usage</h2>
          <p>In order to use this plugin on your page you will need to include the following script in the “Optional JS” area from the page’s footer:</p>
          <div class="position-relative">
            <div class="bd-clipboard"><span class="btn-clipboard" title="" data-bs-original-title="Copy to clipboard">Copy</span></div><figure class="highlight"><pre class=" language-html"><code class=" language-html" data-lang="html"><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;</span>script</span> <span class="token attr-name">src</span><span class="token attr-value"><span class="token punctuation">=</span><span class="token punctuation">"</span>../../assets/js/plugins/datatables.js<span class="token punctuation">"</span></span><span class="token punctuation">&gt;</span></span><span class="token script"></span><span class="token tag"><span class="token tag"><span class="token punctuation">&lt;/</span>script</span><span class="token punctuation">&gt;</span></span></code></pre>
            </figure>
          </div>
          <h2 id="example">Example</h2>
          <h3 id="datatable-basic-example">Datatable Basic example</h3>
          <div class="table-responsive">
            <table class="table table-flush" id="datatable-basic">
              <thead class="thead-light">
                <tr>
                  <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Name</th>
                  <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Position</th>
                  <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Office</th>
                  <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Age</th>
                  <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Start date</th>
                  <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Salary</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td class="text-sm font-weight-normal">Tiger Nixon</td>
                  <td class="text-sm font-weight-normal">System Architect</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">61</td>
                  <td class="text-sm font-weight-normal">2011/04/25</td>
                  <td class="text-sm font-weight-normal">$320,800</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Garrett Winters</td>
                  <td class="text-sm font-weight-normal">Accountant</td>
                  <td class="text-sm font-weight-normal">Tokyo</td>
                  <td class="text-sm font-weight-normal">63</td>
                  <td class="text-sm font-weight-normal">2011/07/25</td>
                  <td class="text-sm font-weight-normal">$170,750</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Ashton Cox</td>
                  <td class="text-sm font-weight-normal">Junior Technical Author</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">66</td>
                  <td class="text-sm font-weight-normal">2009/01/12</td>
                  <td class="text-sm font-weight-normal">$86,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Cedric Kelly</td>
                  <td class="text-sm font-weight-normal">Senior Javascript Developer</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">22</td>
                  <td class="text-sm font-weight-normal">2012/03/29</td>
                  <td class="text-sm font-weight-normal">$433,060</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Airi Satou</td>
                  <td class="text-sm font-weight-normal">Accountant</td>
                  <td class="text-sm font-weight-normal">Tokyo</td>
                  <td class="text-sm font-weight-normal">33</td>
                  <td class="text-sm font-weight-normal">2008/11/28</td>
                  <td class="text-sm font-weight-normal">$162,700</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Brielle Williamson</td>
                  <td class="text-sm font-weight-normal">Integration Specialist</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">61</td>
                  <td class="text-sm font-weight-normal">2012/12/02</td>
                  <td class="text-sm font-weight-normal">$372,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Herrod Chandler</td>
                  <td class="text-sm font-weight-normal">Sales Assistant</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">59</td>
                  <td class="text-sm font-weight-normal">2012/08/06</td>
                  <td class="text-sm font-weight-normal">$137,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Rhona Davidson</td>
                  <td class="text-sm font-weight-normal">Integration Specialist</td>
                  <td class="text-sm font-weight-normal">Tokyo</td>
                  <td class="text-sm font-weight-normal">55</td>
                  <td class="text-sm font-weight-normal">2010/10/14</td>
                  <td class="text-sm font-weight-normal">$327,900</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Colleen Hurst</td>
                  <td class="text-sm font-weight-normal">Javascript Developer</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">39</td>
                  <td class="text-sm font-weight-normal">2009/09/15</td>
                  <td class="text-sm font-weight-normal">$205,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Sonya Frost</td>
                  <td class="text-sm font-weight-normal">Software Engineer</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">23</td>
                  <td class="text-sm font-weight-normal">2008/12/13</td>
                  <td class="text-sm font-weight-normal">$103,600</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Jena Gaines</td>
                  <td class="text-sm font-weight-normal">Office Manager</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">30</td>
                  <td class="text-sm font-weight-normal">2008/12/19</td>
                  <td class="text-sm font-weight-normal">$90,560</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Quinn Flynn</td>
                  <td class="text-sm font-weight-normal">Support Lead</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">22</td>
                  <td class="text-sm font-weight-normal">2013/03/03</td>
                  <td class="text-sm font-weight-normal">$342,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Charde Marshall</td>
                  <td class="text-sm font-weight-normal">Regional Director</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">36</td>
                  <td class="text-sm font-weight-normal">2008/10/16</td>
                  <td class="text-sm font-weight-normal">$470,600</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Haley Kennedy</td>
                  <td class="text-sm font-weight-normal">Senior Marketing Designer</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">43</td>
                  <td class="text-sm font-weight-normal">2012/12/18</td>
                  <td class="text-sm font-weight-normal">$313,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Tatyana Fitzpatrick</td>
                  <td class="text-sm font-weight-normal">Regional Director</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">19</td>
                  <td class="text-sm font-weight-normal">2010/03/17</td>
                  <td class="text-sm font-weight-normal">$385,750</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Michael Silva</td>
                  <td class="text-sm font-weight-normal">Marketing Designer</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">66</td>
                  <td class="text-sm font-weight-normal">2012/11/27</td>
                  <td class="text-sm font-weight-normal">$198,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Paul Byrd</td>
                  <td class="text-sm font-weight-normal">Chief Financial Officer (CFO)</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">64</td>
                  <td class="text-sm font-weight-normal">2010/06/09</td>
                  <td class="text-sm font-weight-normal">$725,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Gloria Little</td>
                  <td class="text-sm font-weight-normal">Systems Administrator</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">59</td>
                  <td class="text-sm font-weight-normal">2009/04/10</td>
                  <td class="text-sm font-weight-normal">$237,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Bradley Greer</td>
                  <td class="text-sm font-weight-normal">Software Engineer</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">41</td>
                  <td class="text-sm font-weight-normal">2012/10/13</td>
                  <td class="text-sm font-weight-normal">$132,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Dai Rios</td>
                  <td class="text-sm font-weight-normal">Personnel Lead</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">35</td>
                  <td class="text-sm font-weight-normal">2012/09/26</td>
                  <td class="text-sm font-weight-normal">$217,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Jenette Caldwell</td>
                  <td class="text-sm font-weight-normal">Development Lead</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">30</td>
                  <td class="text-sm font-weight-normal">2011/09/03</td>
                  <td class="text-sm font-weight-normal">$345,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Yuri Berry</td>
                  <td class="text-sm font-weight-normal">Chief Marketing Officer (CMO)</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">40</td>
                  <td class="text-sm font-weight-normal">2009/06/25</td>
                  <td class="text-sm font-weight-normal">$675,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Caesar Vance</td>
                  <td class="text-sm font-weight-normal">Pre-Sales Support</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">21</td>
                  <td class="text-sm font-weight-normal">2011/12/12</td>
                  <td class="text-sm font-weight-normal">$106,450</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Doris Wilder</td>
                  <td class="text-sm font-weight-normal">Sales Assistant</td>
                  <td class="text-sm font-weight-normal">Sidney</td>
                  <td class="text-sm font-weight-normal">23</td>
                  <td class="text-sm font-weight-normal">2010/09/20</td>
                  <td class="text-sm font-weight-normal">$85,600</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Angelica Ramos</td>
                  <td class="text-sm font-weight-normal">Chief Executive Officer (CEO)</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">47</td>
                  <td class="text-sm font-weight-normal">2009/10/09</td>
                  <td class="text-sm font-weight-normal">$1,200,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Gavin Joyce</td>
                  <td class="text-sm font-weight-normal">Developer</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">42</td>
                  <td class="text-sm font-weight-normal">2010/12/22</td>
                  <td class="text-sm font-weight-normal">$92,575</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Jennifer Chang</td>
                  <td class="text-sm font-weight-normal">Regional Director</td>
                  <td class="text-sm font-weight-normal">Singapore</td>
                  <td class="text-sm font-weight-normal">28</td>
                  <td class="text-sm font-weight-normal">2010/11/14</td>
                  <td class="text-sm font-weight-normal">$357,650</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Brenden Wagner</td>
                  <td class="text-sm font-weight-normal">Software Engineer</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">28</td>
                  <td class="text-sm font-weight-normal">2011/06/07</td>
                  <td class="text-sm font-weight-normal">$206,850</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Fiona Green</td>
                  <td class="text-sm font-weight-normal">Chief Operating Officer (COO)</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">48</td>
                  <td class="text-sm font-weight-normal">2010/03/11</td>
                  <td class="text-sm font-weight-normal">$850,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Shou Itou</td>
                  <td class="text-sm font-weight-normal">Regional Marketing</td>
                  <td class="text-sm font-weight-normal">Tokyo</td>
                  <td class="text-sm font-weight-normal">20</td>
                  <td class="text-sm font-weight-normal">2011/08/14</td>
                  <td class="text-sm font-weight-normal">$163,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Michelle House</td>
                  <td class="text-sm font-weight-normal">Integration Specialist</td>
                  <td class="text-sm font-weight-normal">Sidney</td>
                  <td class="text-sm font-weight-normal">37</td>
                  <td class="text-sm font-weight-normal">2011/06/02</td>
                  <td class="text-sm font-weight-normal">$95,400</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Suki Burks</td>
                  <td class="text-sm font-weight-normal">Developer</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">53</td>
                  <td class="text-sm font-weight-normal">2009/10/22</td>
                  <td class="text-sm font-weight-normal">$114,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Prescott Bartlett</td>
                  <td class="text-sm font-weight-normal">Technical Author</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">27</td>
                  <td class="text-sm font-weight-normal">2011/05/07</td>
                  <td class="text-sm font-weight-normal">$145,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Gavin Cortez</td>
                  <td class="text-sm font-weight-normal">Team Leader</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">22</td>
                  <td class="text-sm font-weight-normal">2008/10/26</td>
                  <td class="text-sm font-weight-normal">$235,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Martena Mccray</td>
                  <td class="text-sm font-weight-normal">Post-Sales support</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">46</td>
                  <td class="text-sm font-weight-normal">2011/03/09</td>
                  <td class="text-sm font-weight-normal">$324,050</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Unity Butler</td>
                  <td class="text-sm font-weight-normal">Marketing Designer</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">47</td>
                  <td class="text-sm font-weight-normal">2009/12/09</td>
                  <td class="text-sm font-weight-normal">$85,675</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Howard Hatfield</td>
                  <td class="text-sm font-weight-normal">Office Manager</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">51</td>
                  <td class="text-sm font-weight-normal">2008/12/16</td>
                  <td class="text-sm font-weight-normal">$164,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Hope Fuentes</td>
                  <td class="text-sm font-weight-normal">Secretary</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">41</td>
                  <td class="text-sm font-weight-normal">2010/02/12</td>
                  <td class="text-sm font-weight-normal">$109,850</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Vivian Harrell</td>
                  <td class="text-sm font-weight-normal">Financial Controller</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">62</td>
                  <td class="text-sm font-weight-normal">2009/02/14</td>
                  <td class="text-sm font-weight-normal">$452,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Timothy Mooney</td>
                  <td class="text-sm font-weight-normal">Office Manager</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">37</td>
                  <td class="text-sm font-weight-normal">2008/12/11</td>
                  <td class="text-sm font-weight-normal">$136,200</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Jackson Bradshaw</td>
                  <td class="text-sm font-weight-normal">Director</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">65</td>
                  <td class="text-sm font-weight-normal">2008/09/26</td>
                  <td class="text-sm font-weight-normal">$645,750</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Olivia Liang</td>
                  <td class="text-sm font-weight-normal">Support Engineer</td>
                  <td class="text-sm font-weight-normal">Singapore</td>
                  <td class="text-sm font-weight-normal">64</td>
                  <td class="text-sm font-weight-normal">2011/02/03</td>
                  <td class="text-sm font-weight-normal">$234,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Bruno Nash</td>
                  <td class="text-sm font-weight-normal">Software Engineer</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">38</td>
                  <td class="text-sm font-weight-normal">2011/05/03</td>
                  <td class="text-sm font-weight-normal">$163,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Sakura Yamamoto</td>
                  <td class="text-sm font-weight-normal">Support Engineer</td>
                  <td class="text-sm font-weight-normal">Tokyo</td>
                  <td class="text-sm font-weight-normal">37</td>
                  <td class="text-sm font-weight-normal">2009/08/19</td>
                  <td class="text-sm font-weight-normal">$139,575</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Thor Walton</td>
                  <td class="text-sm font-weight-normal">Developer</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">61</td>
                  <td class="text-sm font-weight-normal">2013/08/11</td>
                  <td class="text-sm font-weight-normal">$98,540</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Finn Camacho</td>
                  <td class="text-sm font-weight-normal">Support Engineer</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">47</td>
                  <td class="text-sm font-weight-normal">2009/07/07</td>
                  <td class="text-sm font-weight-normal">$87,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Serge Baldwin</td>
                  <td class="text-sm font-weight-normal">Data Coordinator</td>
                  <td class="text-sm font-weight-normal">Singapore</td>
                  <td class="text-sm font-weight-normal">64</td>
                  <td class="text-sm font-weight-normal">2012/04/09</td>
                  <td class="text-sm font-weight-normal">$138,575</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Zenaida Frank</td>
                  <td class="text-sm font-weight-normal">Software Engineer</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">63</td>
                  <td class="text-sm font-weight-normal">2010/01/04</td>
                  <td class="text-sm font-weight-normal">$125,250</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Zorita Serrano</td>
                  <td class="text-sm font-weight-normal">Software Engineer</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">56</td>
                  <td class="text-sm font-weight-normal">2012/06/01</td>
                  <td class="text-sm font-weight-normal">$115,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Jennifer Acosta</td>
                  <td class="text-sm font-weight-normal">Junior Javascript Developer</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">43</td>
                  <td class="text-sm font-weight-normal">2013/02/01</td>
                  <td class="text-sm font-weight-normal">$75,650</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Cara Stevens</td>
                  <td class="text-sm font-weight-normal">Sales Assistant</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">46</td>
                  <td class="text-sm font-weight-normal">2011/12/06</td>
                  <td class="text-sm font-weight-normal">$145,600</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Hermione Butler</td>
                  <td class="text-sm font-weight-normal">Regional Director</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">47</td>
                  <td class="text-sm font-weight-normal">2011/03/21</td>
                  <td class="text-sm font-weight-normal">$356,250</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Lael Greer</td>
                  <td class="text-sm font-weight-normal">Systems Administrator</td>
                  <td class="text-sm font-weight-normal">London</td>
                  <td class="text-sm font-weight-normal">21</td>
                  <td class="text-sm font-weight-normal">2009/02/27</td>
                  <td class="text-sm font-weight-normal">$103,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Jonas Alexander</td>
                  <td class="text-sm font-weight-normal">Developer</td>
                  <td class="text-sm font-weight-normal">San Francisco</td>
                  <td class="text-sm font-weight-normal">30</td>
                  <td class="text-sm font-weight-normal">2010/07/14</td>
                  <td class="text-sm font-weight-normal">$86,500</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Shad Decker</td>
                  <td class="text-sm font-weight-normal">Regional Director</td>
                  <td class="text-sm font-weight-normal">Edinburgh</td>
                  <td class="text-sm font-weight-normal">51</td>
                  <td class="text-sm font-weight-normal">2008/11/13</td>
                  <td class="text-sm font-weight-normal">$183,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Michael Bruce</td>
                  <td class="text-sm font-weight-normal">Javascript Developer</td>
                  <td class="text-sm font-weight-normal">Singapore</td>
                  <td class="text-sm font-weight-normal">29</td>
                  <td class="text-sm font-weight-normal">2011/06/27</td>
                  <td class="text-sm font-weight-normal">$183,000</td>
                </tr>
                <tr>
                  <td class="text-sm font-weight-normal">Donna Snider</td>
                  <td class="text-sm font-weight-normal">Customer Support</td>
                  <td class="text-sm font-weight-normal">New York</td>
                  <td class="text-sm font-weight-normal">27</td>
                  <td class="text-sm font-weight-normal">2011/01/25</td>
                  <td class="text-sm font-weight-normal">$112,000</td>
                </tr>
              </tbody>
            </table>
          </div>
        </main>
      </div>
      <div class="ct-docs-main-footer-row">
        <div class="ct-docs-main-footer-blank-col">
        </div>
        <div class="ct-docs-main-footer-col">
          <footer class="ct-docs-footer">
            <div class="ct-docs-footer-inner-row">
              <div class="ct-docs-footer-col">
                <div class="ct-docs-footer-copyright">
                  © <script>
                    document.write(new Date().getFullYear())
                  </script> <a href="https://creative-tim.com" class="ct-docs-footer-copyright-author" target="_blank">Creative Tim</a> & <a href="https://www.updivision.com" class="ct-docs-footer-copyright-author" target="_blank">UPDIVISION</a>
                </div>
              </div>
              <div class="ct-docs-footer-col">
                <ul class="ct-docs-footer-nav-footer">
                  <li>
                    <a href="https://creative-tim.com" class="ct-docs-footer-nav-link" target="_blank">Creative Tim</a>
                  </li>
                  <li class="nav-item">
                    <a href="https://www.updivision.com" class="ct-docs-footer-nav-link" target="_blank">UPDIVISION</a>
                </li>
                  <li>
                    <a href="https://www.creative-tim.com/contact-us" class="ct-docs-footer-nav-link" target="_blank">Contact Us</a>
                  </li>
                  <li>
                    <a href="https://creative-tim.com/blog" class="ct-docs-footer-nav-link" target="_blank">Blog</a>
                  </li>
                </ul>
              </div>
            </div>
          </footer>
        </div>
      </div>
    </div>
    <script src="../../assets/js/jquery.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/popper.min.js" type="text/javascript"></script>
    <script src="../../assets/js/core/bootstrap.bundle.min.js" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="" type="text/javascript"></script>
    <script src="../../assets/js/prism.js" type="text/javascript"></script>
    <script src="../../assets/js/docs.min.js" type="text/javascript"></script>
    <script src="../../assets/js/holder.min.js" type="text/javascript"></script>
    <script src="../../assets/js/moment.min.js" type="text/javascript"></script>

    <script src="../../assets/js/datatables.js" type="text/javascript"></script>
    <script type="text/javascript">
      const dataTableBasic = new simpleDatatables.DataTable("#datatable-basic", {
        searchable: false,
        fixedHeight: true
      });
    </script>
        <script src="../../assets/js/material-dashboard.min.js" type="text/javascript"></script>
    <script>
      Holder.addTheme('gray', {
        bg: '#777',
        fg: 'rgba(255,255,255,.75)',
        font: 'Helvetica',
        fontweight: 'normal'
      })
    </script>
    <script>
      // Facebook Pixel Code Don't Delete
      ! function(f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function() {
          n.callMethod ?
            n.callMethod.apply(n, arguments) : n.queue.push(arguments)
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s)
      }(window,
        document, 'script', '//connect.facebook.net/en_US/fbevents.js');

      try {
        fbq('init', '111649226022273');
        fbq('track', "PageView");

      } catch (err) {
        console.log('Facebook Track Error:', err);
      }
    </script>
    <script src="../../assets/js/docs.js"></script>
  </body>
  </html>
