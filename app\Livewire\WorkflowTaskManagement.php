<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use App\Models\DepartmentSupervisorAssistant;
use App\Services\DepartmentTeamManagementService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class WorkflowTaskManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $search = '';
    public $filterStatus = 'all';
    public $filterDepartment = 'all';
    public $filterAssignedTo = 'all';
    public $filterPriority = 'all';
    
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showAssignModal = false;
    
    // Task form properties
    public $taskId;
    public $title = '';
    public $description = '';
    public $type = 'daily';
    public $status = 'pending';
    public $assigned_to = '';
    public $department_id = '';
    public $due_date = '';
    public $priority = 1;
    
    public $departments = [];
    public $users = [];
    public $departmentTeams = [];
    
    protected $departmentTeamService;

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'type' => 'required|in:daily,weekly',
        'assigned_to' => 'required|exists:users,id',
        'department_id' => 'nullable|exists:departments,id',
        'due_date' => 'required|date',
        'priority' => 'required|integer|min:1|max:3',
    ];

    public function boot(DepartmentTeamManagementService $departmentTeamService)
    {
        $this->departmentTeamService = $departmentTeamService;
    }

    public function mount()
    {
        $this->authorize('assign-tasks');
        $this->loadData();
    }

    public function render()
    {
        $tasks = Task::query()
            ->with(['assignedTo', 'assignedBy', 'department'])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhereHas('assignedTo', function ($userQuery) {
                          $userQuery->where('name', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->filterStatus !== 'all', function ($query) {
                $query->where('status', $this->filterStatus);
            })
            ->when($this->filterDepartment !== 'all', function ($query) {
                $query->where('department_id', $this->filterDepartment);
            })
            ->when($this->filterAssignedTo !== 'all', function ($query) {
                $query->where('assigned_to', $this->filterAssignedTo);
            })
            ->when($this->filterPriority !== 'all', function ($query) {
                $query->where('priority', $this->filterPriority);
            })
            ->orderBy('due_date', 'asc')
            ->orderBy('priority', 'desc')
            ->paginate(15);

        return view('livewire.workflow-task-management', [
            'tasks' => $tasks,
        ]);
    }

    public function loadData()
    {
        $this->departments = Department::active()->orderBy('name')->get();

        // Get all users who can be assigned tasks (all users except admin)
        $this->users = User::whereHas('roles', function ($q) {
            $q->whereNotIn('name', ['admin']);
        })->orderBy('name')->get();

        // If no users found with role filtering, get all users
        if ($this->users->isEmpty()) {
            $this->users = User::orderBy('name')->get();
        }

        // Load department teams
        $this->departmentTeams = [];
        foreach ($this->departments as $department) {
            $this->departmentTeams[$department->id] = $this->departmentTeamService->getDepartmentTeamStructure($department);
        }
    }

    public function openCreateModal()
    {
        $this->authorize('create', Task::class);
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function createTask()
    {
        $this->authorize('create', Task::class);
        $this->validate();

        Task::create([
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'assigned_to' => $this->assigned_to,
            'assigned_by' => auth()->id(),
            'department_id' => $this->department_id ?: null,
            'due_date' => $this->due_date,
            'priority' => $this->priority,
        ]);

        $this->showCreateModal = false;
        $this->resetForm();
        session()->flash('message', 'Task created successfully.');
    }

    public function openEditModal($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('update', $task);
        
        $this->taskId = $task->id;
        $this->title = $task->title;
        $this->description = $task->description;
        $this->type = $task->type;
        $this->assigned_to = $task->assigned_to;
        $this->department_id = $task->department_id;
        $this->due_date = $task->due_date->format('Y-m-d');
        $this->priority = $task->priority;
        
        $this->showEditModal = true;
    }

    public function updateTask()
    {
        $task = Task::findOrFail($this->taskId);
        $this->authorize('update', $task);
        $this->validate();

        $task->update([
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'assigned_to' => $this->assigned_to,
            'department_id' => $this->department_id ?: null,
            'due_date' => $this->due_date,
            'priority' => $this->priority,
        ]);

        $this->showEditModal = false;
        $this->resetForm();
        session()->flash('message', 'Task updated successfully.');
    }

    public function changeTaskStatus($taskId, $status)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('update', $task);
        
        $task->update(['status' => $status]);
        
        if ($status === 'completed') {
            $task->update(['completed_at' => now()]);
        }
        
        session()->flash('message', 'Task status updated successfully.');
    }

    public function deleteTask($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('delete', $task);
        
        $task->delete();
        session()->flash('message', 'Task deleted successfully.');
    }

    public function resetForm()
    {
        $this->taskId = null;
        $this->title = '';
        $this->description = '';
        $this->type = 'daily';
        $this->assigned_to = '';
        $this->department_id = '';
        $this->due_date = '';
        $this->priority = 1;
        $this->resetValidation();
    }

    public function closeModals()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showAssignModal = false;
        $this->resetForm();
    }

    public function updatedFilterDepartment()
    {
        $this->resetPage();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function getTaskPriorityColor($priority)
    {
        return match($priority) {
            1 => 'success',
            2 => 'warning',
            3 => 'danger',
            default => 'secondary'
        };
    }

    public function getTaskStatusColor($status)
    {
        return match($status) {
            'pending' => 'secondary',
            'in_progress' => 'info',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    public function getDepartmentTeamMembers($departmentId)
    {
        if (!$departmentId || !isset($this->departmentTeams[$departmentId])) {
            return collect();
        }
        
        $members = collect();
        foreach ($this->departmentTeams[$departmentId] as $team) {
            $members->push($team['supervisor']);
            $members = $members->merge($team['assistants']);
        }
        
        return $members;
    }
}
