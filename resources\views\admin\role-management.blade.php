@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card my-4">
                <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                    <div class="bg-gradient-primary shadow-primary border-radius-lg pt-4 pb-3">
                        <h6 class="text-white text-capitalize ps-3">Role Management</h6>
                    </div>
                </div>
                <div class="card-body px-0 pb-2">
                    <div class="container-fluid">
                        <!-- Role Management Component -->
                        <livewire:role-management />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .card {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: none;
        border-radius: 1rem;
    }

    .card-header {
        border-bottom: none;
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
    }

    .shadow-primary {
        box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(94, 114, 228, 0.4);
    }

    .border-radius-lg {
        border-radius: 0.75rem;
    }

    .text-capitalize {
        text-transform: capitalize;
    }

    .z-index-2 {
        z-index: 2;
    }

    .position-relative {
        position: relative;
    }

    .mt-n4 {
        margin-top: -1.5rem;
    }

    .mx-3 {
        margin-left: 1rem;
        margin-right: 1rem;
    }

    .pt-4 {
        padding-top: 1.5rem;
    }

    .pb-3 {
        padding-bottom: 1rem;
    }

    .ps-3 {
        padding-left: 1rem;
    }

    .px-0 {
        padding-left: 0;
        padding-right: 0;
    }

    .pb-2 {
        padding-bottom: 0.5rem;
    }

    .py-4 {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
    }

    .my-4 {
        margin-top: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .text-white {
        color: #fff;
    }

    .container-fluid {
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .card-header {
            margin-left: 0.5rem;
            margin-right: 0.5rem;
        }
        
        .container-fluid {
            padding-right: 10px;
            padding-left: 10px;
        }
    }

    /* Loading state */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    /* Animation for smooth transitions */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 20px -5px rgba(0, 0, 0, 0.04);
    }
</style>
@endsection
