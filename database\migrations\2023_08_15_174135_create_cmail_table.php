<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('cmail', function (Blueprint $table) {
            $table->id();
            $table->date('mail_recived_date');
            $table->string('darulifta');
            $table->date('mail_folder_date');
            $table->string('mujeeb');
            $table->string('fatwa_no');
            $table->enum('ftype', ['New', 'Mahl e Nazar']);
            $table->string('attachment'); // Add a column for attachment file name
            $table->boolean('selected')->default(false);
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cmail');
    }
};
