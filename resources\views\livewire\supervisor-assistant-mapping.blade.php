<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Superiors</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['total_superiors'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="ni ni-badge text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Active Mappings</p>
                                <h5 class="font-weight-bolder mb-0 text-success">{{ $statistics['active_mappings'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-success shadow text-center border-radius-md">
                                <i class="ni ni-vector text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Assistants</p>
                                <h5 class="font-weight-bolder mb-0 text-info">{{ $statistics['total_assistants'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                <i class="ni ni-single-02 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Unassigned</p>
                                <h5 class="font-weight-bolder mb-0 text-warning">{{ $statistics['unassigned_assistants'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="ni ni-user-run text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Avg Assistants/Superior</p>
                                <h5 class="font-weight-bolder mb-0">{{ $statistics['avg_assistants_per_superior'] ?? 0 }}</h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md">
                                <i class="ni ni-chart-bar-32 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Management Interface -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Supervisor-Assistant Mapping</h5>
                            <p class="text-sm mb-0">Manage Superior-Muawin relationships and team assignments</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <button wire:click="refreshData" class="btn btn-outline-info btn-sm mb-0 me-2">
                                    <i class="fas fa-sync"></i>&nbsp;&nbsp;Refresh
                                </button>
                                <button wire:click="openCreateSuperiorModal" class="btn btn-outline-success btn-sm mb-0 me-2">
                                    <i class="fas fa-user-plus"></i>&nbsp;&nbsp;Create Superior
                                </button>
                                <button wire:click="exportMappings" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-download"></i>&nbsp;&nbsp;Export Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search supervisors or assistants...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select wire:model.live="filterDepartment" class="form-select">
                                    <option value="all">All Departments</option>
                                    @foreach($departments as $department)
                                        <option value="{{ $department->id }}">{{ $department->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="text-sm text-secondary">
                                    Total Mappings: {{ count($mappings) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mappings Display -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="row px-4">
                        @forelse($mappings as $mapping)
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header pb-0">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-lg bg-gradient-primary">
                                            <span class="text-white font-weight-bold">
                                                {{ strtoupper(substr($mapping['supervisor']->name, 0, 2)) }}
                                            </span>
                                        </div>
                                        <div class="ms-3">
                                            <h6 class="mb-0">{{ $mapping['supervisor']->name }}</h6>
                                            <p class="text-sm text-secondary mb-0">Superior</p>
                                            @if($mapping['supervisor']->departments->isNotEmpty())
                                                @foreach($mapping['supervisor']->departments as $department)
                                                    <span class="badge badge-sm bg-gradient-{{ $this->getDepartmentBadgeColor($department->name) }}">
                                                        {{ $department->name }}
                                                    </span>
                                                @endforeach
                                            @endif
                                        </div>
                                        <div class="ms-auto">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle mb-0" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" wire:click="openViewModal({{ $mapping['supervisor']->id }})">View Details</a></li>
                                                    <li><a class="dropdown-item" href="#" wire:click="openAssignModal({{ $mapping['supervisor']->id }})">Manage Assistants</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" wire:click="removeSuperior({{ $mapping['supervisor']->id }})" wire:confirm="Are you sure you want to remove Superior role?">Remove Superior</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body pt-2">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-uppercase text-secondary font-weight-bold">Assistants ({{ count($mapping['assistants']) }})</small>
                                        <button wire:click="openAssignModal({{ $mapping['supervisor']->id }})" class="btn btn-sm btn-outline-primary mb-0">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </div>
                                    
                                    @if(count($mapping['assistants']) > 0)
                                        <div class="assistant-list">
                                            @foreach($mapping['assistants'] as $assistant)
                                            <div class="d-flex align-items-center justify-content-between py-1">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm bg-gradient-info me-2">
                                                        <span class="text-white text-xs font-weight-bold">
                                                            {{ strtoupper(substr($assistant->name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm mb-0 font-weight-bold">{{ $assistant->name }}</p>
                                                        <p class="text-xs text-secondary mb-0">{{ $assistant->email }}</p>
                                                    </div>
                                                </div>
                                                <button wire:click="removeAssistant({{ $mapping['supervisor']->id }}, {{ $assistant->id }})" 
                                                        wire:confirm="Remove this assistant?"
                                                        class="btn btn-sm btn-outline-danger mb-0">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                            @endforeach
                                        </div>
                                    @else
                                        <div class="text-center py-3">
                                            <i class="fas fa-users text-secondary"></i>
                                            <p class="text-sm text-secondary mb-0">No assistants assigned</p>
                                        </div>
                                    @endif
                                    
                                    <div class="mt-3 pt-2 border-top">
                                        <small class="text-muted">
                                            Assigned: {{ \Carbon\Carbon::parse($mapping['assigned_at'])->format('M d, Y') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @empty
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-secondary mb-3"></i>
                                <h5 class="text-secondary">No supervisor-assistant mappings found</h5>
                                <p class="text-sm text-secondary">Create a Superior role to start managing team assignments.</p>
                                <button wire:click="openCreateSuperiorModal" class="btn bg-gradient-primary">
                                    <i class="fas fa-user-plus"></i>&nbsp;&nbsp;Create First Superior
                                </button>
                            </div>
                        </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Assign Assistants Modal -->
    @if($showAssignModal && $selectedSupervisor)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Manage Assistants - {{ $selectedSupervisor->name }}</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="assignAssistants">
                        <div class="form-group">
                            <label class="form-label">Select Assistants</label>
                            <div class="row">
                                @foreach($availableAssistants as $assistant)
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               wire:model="selectedAssistants"
                                               value="{{ $assistant->id }}"
                                               id="assistant_{{ $assistant->id }}">
                                        <label class="form-check-label" for="assistant_{{ $assistant->id }}">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm bg-gradient-info me-2">
                                                    <span class="text-white text-xs font-weight-bold">
                                                        {{ strtoupper(substr($assistant->name, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p class="mb-0 font-weight-bold">{{ $assistant->name }}</p>
                                                    <p class="text-xs text-secondary mb-0">{{ $assistant->email }}</p>
                                                    @if($assistant->departments->isNotEmpty())
                                                        <span class="badge badge-sm bg-gradient-secondary">
                                                            {{ $assistant->departments->first()->name }}
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            @if($availableAssistants->isEmpty())
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No available assistants to assign.
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn bg-gradient-primary" wire:click="assignAssistants">
                        <i class="fas fa-save"></i> Save Assignments
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- View Details Modal -->
    @if($showViewModal && $selectedSupervisor)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Supervisor Details - {{ $selectedSupervisor->name }}</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Personal Information</small>
                                <p class="mb-1"><strong>Name:</strong> {{ $selectedSupervisor->name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $selectedSupervisor->email }}</p>
                                <p class="mb-1"><strong>Roles:</strong>
                                    @foreach($selectedSupervisor->roles as $role)
                                        <span class="badge bg-gradient-info">{{ $role->name }}</span>
                                    @endforeach
                                </p>
                                @if($selectedSupervisor->departments->isNotEmpty())
                                    <p class="mb-1"><strong>Departments:</strong>
                                        @foreach($selectedSupervisor->departments as $department)
                                            <span class="badge bg-gradient-{{ $this->getDepartmentBadgeColor($department->name) }}">{{ $department->name }}</span>
                                        @endforeach
                                    </p>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Team Statistics</small>
                                <p class="mb-1"><strong>Total Assistants:</strong> {{ $selectedSupervisor->assistants->count() }}</p>
                                <p class="mb-1"><strong>Active Assignments:</strong> {{ $selectedSupervisor->assistants->where('pivot.is_active', true)->count() }}</p>
                                <p class="mb-1"><strong>Superior Since:</strong>
                                    {{ $selectedSupervisor->assistants->first()?->pivot?->assigned_at?->format('M d, Y') ?? 'N/A' }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="info-item">
                        <small class="text-uppercase text-secondary font-weight-bold">Assigned Assistants</small>
                        @if($selectedSupervisor->assistants->isNotEmpty())
                            <div class="row mt-2">
                                @foreach($selectedSupervisor->assistants as $assistant)
                                <div class="col-md-6 mb-2">
                                    <div class="card">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm bg-gradient-info me-2">
                                                    <span class="text-white text-xs font-weight-bold">
                                                        {{ strtoupper(substr($assistant->name, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p class="mb-0 font-weight-bold">{{ $assistant->name }}</p>
                                                    <p class="text-xs text-secondary mb-0">{{ $assistant->email }}</p>
                                                    @if($assistant->departments->isNotEmpty())
                                                        <span class="badge badge-sm bg-gradient-secondary">
                                                            {{ $assistant->departments->first()->name }}
                                                        </span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-info-circle"></i> No assistants currently assigned.
                            </div>
                        @endif
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                    <button type="button" class="btn bg-gradient-primary" wire:click="openAssignModal({{ $selectedSupervisor->id }})">
                        <i class="fas fa-edit"></i> Manage Assistants
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Create Superior Modal -->
    @if($showCreateSuperiorModal)
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Superior</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="createSuperior">
                        <div class="form-group mb-3">
                            <label class="form-label">Select User to Promote *</label>
                            <select wire:model="newSuperiorId" class="form-select @error('newSuperiorId') is-invalid @enderror">
                                <option value="">Choose a user...</option>
                                @foreach($eligibleSuperiors as $user)
                                    <option value="{{ $user->id }}">{{ $user->name }} ({{ $user->email }})</option>
                                @endforeach
                            </select>
                            @error('newSuperiorId') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <div class="form-group">
                            <label class="form-label">Assign Initial Assistants (Optional)</label>
                            <div class="row">
                                @foreach($availableAssistants as $assistant)
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox"
                                               wire:model="newSuperiorAssistants"
                                               value="{{ $assistant->id }}"
                                               id="new_assistant_{{ $assistant->id }}">
                                        <label class="form-check-label" for="new_assistant_{{ $assistant->id }}">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm bg-gradient-info me-2">
                                                    <span class="text-white text-xs font-weight-bold">
                                                        {{ strtoupper(substr($assistant->name, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <p class="mb-0 font-weight-bold">{{ $assistant->name }}</p>
                                                    <p class="text-xs text-secondary mb-0">{{ $assistant->email }}</p>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            @if($availableAssistants->isEmpty())
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> No available assistants to assign.
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn bg-gradient-success" wire:click="createSuperior">
                        <i class="fas fa-user-plus"></i> Create Superior
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
