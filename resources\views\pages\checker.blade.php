<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="checker"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Darulifta"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <style>
            /* Styles for the form */
            .checker-form {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ccc;
                border-radius: 5px;
                background-color: #f7f7f7;
                box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            }
            
            .checker-form .form-group {
                margin-bottom: 15px;
            }
            
            .checker-form label {
                font-weight: bold;
            }
            
            .checker-form .form-control {
                width: 100%;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
            }
            
            .checker-form button {
                display: block;
                width: 100%;
                padding: 10px;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                transition: background-color 0.3s;
            }
            
            .checker-form button:hover {
                background-color: #0056b3;
            }
            
            /* Styles for the table */
            .checker-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            
            .checker-table th,
            .checker-table td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
        </style>

        <!-- Form for adding Mujeeb -->
        <div class="container">
            <h1>Checker Management</h1>
            <div class="checker-form">
                <form action="{{ route('checker.store') }}" method="POST" class="checker-form">
                    @csrf
                    <div class="form-group">
                        <label for="checker_name">Checker Name</label>
                        <input type="text" class="form-control" id="checker_name" name="checker_name" placeholder="Enter Checker Name" required>
                    </div>
                    <label for="darul_name">Select Darulifta(s)</label>
<div class="input-group input-group-outline mb-3">                                                
    <select class="form-control" id="darul_name" name="darul_name[]" required multiple>
        
        @foreach($daruliftas as $darulifta)
            <option value="{{ $darulifta->darul_name }}">{{ $darulifta->darul_name }}</option>
        @endforeach
    </select>
</div>                  
                    <div class="form-group">
                        <label for="munsab">Select Munsab</label>
                        <select class="form-control" id="munsab" name="munsab" required>
                            <option value="">Select Munsab</option>
                            <option value="Musaddiq">Musaddiq</option>
                            <option value="Mutakhassis">Mutakhassis</option>
                            
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="folder_id">Folder Id</label>
                        <input type="text" class="form-control" id="folder_id" name="folder_id" placeholder="Enter Folder Id" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Checker</button>
                </form>
            </div>
        </div>
        
        
            <h3>Checker</h3>
            <table class="checker-table">
                <thead>
                    <tr>
                        <th>Checker Name</th>
                        <th>Darulifta</th>
                        <th>Munsab</th>
                        <th>Folder Id</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($checker as $mujeeb)
                    <tr>
                        <td>{{ $mujeeb->checker_name }}</td>
                        <td>{{ $mujeeb->darul_name }}</td>
                        <td>{{ $mujeeb->munsab }}</td>
                        <td>{{ $mujeeb->folder_id }}</td>
                        <td>
                            <a href="{{ route('checker.edit', ['id' => $mujeeb->id]) }}" class="btn btn-sm btn-warning">Edit</a>
                            <form action="{{ route('checker.delete', ['id' => $mujeeb->id]) }}" method="POST">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this record?')">Delete</button>
                            </form>
                            
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
