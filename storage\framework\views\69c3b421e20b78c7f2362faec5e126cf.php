<div>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Daily Performance Report</h5>
                            <p class="text-sm mb-0">
                                Submit your daily performance for <?php echo e(Carbon\Carbon::parse($performance_date)->format('F d, Y')); ?>

                                <!--[if BLOCK]><![endif]--><?php if($isSubmitted): ?>
                                    <span class="badge bg-gradient-success ms-2">Submitted</span>
                                <?php else: ?>
                                    <span class="badge bg-gradient-warning ms-2">Pending</span>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </p>

                            <!-- User Department and Role Info -->
                            <div class="mt-2">
                                <!--[if BLOCK]><![endif]--><?php if($userDepartments->count() > 0): ?>
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>
                                        <strong>Department(s):</strong>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $userDepartments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge bg-light text-dark me-1"><?php echo e($department->name); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </small>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                <!--[if BLOCK]><![endif]--><?php if(auth()->user()->isMujeeb() || auth()->user()->isSuperior()): ?>
                                    <small class="text-muted ms-3">
                                        <i class="fas fa-user-tag me-1"></i>
                                        <strong>Role:</strong>
                                        <span class="badge bg-primary">
                                            <?php if(auth()->user()->isSuperior()): ?>
                                                Superior
                                            <?php elseif(auth()->user()->isMujeeb()): ?>
                                                Mujeeb
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </span>
                                    </small>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <!--[if BLOCK]><![endif]--><?php if($todaysTasks->count() > 0): ?>
                                    <button wire:click="autoFillTasksCompleted" class="btn btn-outline-info btn-sm mb-0 me-2">
                                        <i class="fas fa-magic"></i>&nbsp;&nbsp;Auto-fill Tasks
                                    </button>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                <!--[if BLOCK]><![endif]--><?php if(!$isSubmitted): ?>
                                    <button wire:click="saveAsDraft" class="btn btn-outline-secondary btn-sm mb-0 me-2">
                                        <i class="fas fa-save"></i>&nbsp;&nbsp;Save Draft
                                    </button>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!--[if BLOCK]><![endif]--><?php if(!$hasAssignedTasks): ?>
                        <!-- No Tasks Message -->
                        <div class="alert alert-info text-center" role="alert">
                            <div class="icon-container mb-3">
                                <i class="fas fa-tasks text-info" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="alert-heading">No Tasks Assigned</h4>
                            <p class="mb-3">
                                You currently have no active tasks assigned to you. Daily performance submission is only required for users with assigned tasks.
                            </p>
                            <hr>
                            <p class="mb-0">
                                Please contact your Superior or Admin if you believe this is an error, or if you need tasks to be assigned to you.
                            </p>
                        </div>
                    <?php else: ?>
                        <form wire:submit.prevent="submitReport">
                            <div class="row">
                                <!-- Performance Date -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Performance Date *</label>
                                        <input type="date" wire:model.live="performance_date"
                                               class="form-control <?php $__errorArgs = ['performance_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['performance_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>

                                <!-- Task Selection -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Select Task *</label>
                                        <select wire:model.live="selected_task_id"
                                                class="form-select <?php $__errorArgs = ['selected_task_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>>
                                            <option value="">Choose a task...</option>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $assignedTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($task->id); ?>">
                                                    <?php echo e($task->title); ?>

                                                    <!--[if BLOCK]><![endif]--><?php if($task->department): ?>
                                                        (<?php echo e($task->department->name); ?>)
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    - Due: <?php echo e($task->due_date ? $task->due_date->format('M d') : 'No due date'); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        </select>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['selected_task_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>

                                <!-- Department Display -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Department</label>
                                        <div class="form-control bg-light">
                                            <!--[if BLOCK]><![endif]--><?php if($selectedTaskDepartment): ?>
                                                <span class="badge bg-primary"><?php echo e($selectedTaskDepartment->name); ?></span>
                                            <?php else: ?>
                                                <span class="text-muted">Select a task first</span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Overall Rating (Optional for Mujeeb) -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">
                                        Self Rating
                                        <?php if(auth()->user()->isMujeeb()): ?>
                                            <small class="text-muted">(Optional)</small>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </label>
                                    <select wire:model="overall_rating"
                                            class="form-select <?php $__errorArgs = ['overall_rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>>
                                        <option value="">No Rating</option>
                                        <option value="poor">Poor</option>
                                        <option value="fair">Fair</option>
                                        <option value="good">Good</option>
                                        <option value="excellent">Excellent</option>
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['overall_rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>

                            <!-- Hours Worked -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Hours Worked *</label>
                                    <input type="number" step="0.5" min="0" max="24" wire:model="hours_worked"
                                           class="form-control <?php $__errorArgs = ['hours_worked'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                           placeholder="8.0"
                                           <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['hours_worked'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>

                            <!-- Performance Summary -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Performance Summary</label>
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-gradient-<?php echo e($this->getRatingColor($overall_rating)); ?>">
                                            <?php echo e(ucfirst($overall_rating)); ?>

                                        </span>
                                        <span class="badge bg-gradient-info">
                                            <?php echo e($hours_worked ?: '0'); ?> hrs
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Tasks Completed -->
                        <div class="form-group">
                            <label class="form-label">Tasks Completed *</label>
                            <textarea wire:model="tasks_completed"
                                      class="form-control <?php $__errorArgs = ['tasks_completed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      rows="4"
                                      placeholder="Describe the tasks you completed today..."
                                      <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['tasks_completed'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- Challenges Faced -->
                        <div class="form-group">
                            <label class="form-label">Challenges Faced</label>
                            <textarea wire:model="challenges_faced"
                                      class="form-control <?php $__errorArgs = ['challenges_faced'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      rows="3"
                                      placeholder="Describe any challenges or obstacles you faced..."
                                      <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['challenges_faced'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- Next Day Plan -->
                        <div class="form-group">
                            <label class="form-label">Next Day Plan</label>
                            <textarea wire:model="next_day_plan"
                                      class="form-control <?php $__errorArgs = ['next_day_plan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      rows="3"
                                      placeholder="Outline your plan for tomorrow..."
                                      <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['next_day_plan'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea wire:model="additional_notes"
                                      class="form-control <?php $__errorArgs = ['additional_notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      rows="2"
                                      placeholder="Any additional notes or comments..."
                                      <?php echo e(!$this->canEdit() ? 'disabled' : ''); ?>></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['additional_notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <!-- Submit Button -->
                        <!--[if BLOCK]><![endif]--><?php if($this->canEdit() && !$isSubmitted): ?>
                        <div class="form-group text-end">
                            <button type="submit" class="btn bg-gradient-primary">
                                <i class="fas fa-paper-plane"></i>&nbsp;&nbsp;Submit Performance Report
                            </button>
                        </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </form>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Tasks Card -->
    <!--[if BLOCK]><![endif]--><?php if($todaysTasks->count() > 0): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Today's Tasks (<?php echo e($completedTasksCount); ?>/<?php echo e($todaysTasks->count()); ?> completed)</h6>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $todaysTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($task->title); ?></h6>
                                                <!--[if BLOCK]><![endif]--><?php if($task->description): ?>
                                                    <p class="text-xs text-secondary mb-0"><?php echo e(Str::limit($task->description, 50)); ?></p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success')); ?>">
                                            <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskStatusColor($task->status)); ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <!--[if BLOCK]><![endif]--><?php if($task->status !== 'completed' && $this->canEdit()): ?>
                                            <button wire:click="markTaskCompleted(<?php echo e($task->id); ?>)" 
                                                    class="btn btn-sm btn-outline-success mb-0">
                                                <i class="fas fa-check"></i> Complete
                                            </button>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- All Assigned Tasks Card -->
    <!--[if BLOCK]><![endif]--><?php if($allUserTasks->count() > 0): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">All Assigned Tasks (<?php echo e($allUserTasks->where('status', 'completed')->count()); ?>/<?php echo e($allUserTasks->count()); ?> completed)</h6>
                    <p class="text-sm mb-0">Tasks assigned to you across all departments</p>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Department</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $allUserTasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="<?php echo e($task->isOverdue() ? 'table-warning' : ''); ?>">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($task->title); ?></h6>
                                                <!--[if BLOCK]><![endif]--><?php if($task->description): ?>
                                                    <p class="text-xs text-secondary mb-0"><?php echo e(Str::limit($task->description, 50)); ?></p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                <!--[if BLOCK]><![endif]--><?php if($task->assignedBy): ?>
                                                    <p class="text-xs text-info mb-0">Assigned by: <?php echo e($task->assignedBy->name); ?></p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <!--[if BLOCK]><![endif]--><?php if($task->department): ?>
                                            <span class="badge badge-sm bg-gradient-info"><?php echo e($task->department->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-xs <?php echo e($task->isOverdue() ? 'text-danger fw-bold' : 'text-secondary'); ?>">
                                            <?php echo e($task->due_date ? $task->due_date->format('M d, Y') : '-'); ?>

                                            <!--[if BLOCK]><![endif]--><?php if($task->isOverdue()): ?>
                                                <br><small class="text-danger">(Overdue)</small>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success')); ?>">
                                            <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskStatusColor($task->status)); ?>">
                                            <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <!--[if BLOCK]><![endif]--><?php if($task->status !== 'completed' && $this->canEdit()): ?>
                                            <button wire:click="markTaskCompleted(<?php echo e($task->id); ?>)"
                                                    class="btn btn-sm btn-outline-success mb-0">
                                                <i class="fas fa-check"></i> Complete
                                            </button>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('message')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/daily-performance-report.blade.php ENDPATH**/ ?>