<style>
/* Sending Fatawa Modern Styles */

/* Main Content Layout - Ensure proper sidebar spacing */
.main-content {
    margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
    position: relative; /* Position relative for content positioning */
    max-height: 100vh; /* Full viewport height */
    border-radius: 8px; /* Border radius for styling */
}

/* Force hide wire:loading elements by default, except full-page loading */
[wire\:loading]:not(.full-page-loading) {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Override for full-page loading to show immediately when targeted */
.full-page-loading[wire\:loading][wire\:target="applyFilters"] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Full Page Loading Overlay for Livewire */
.full-page-loading {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.6) !important;
    backdrop-filter: blur(3px) !important;
    z-index: 10000 !important;
    display: none !important;
    justify-content: center !important;
    align-items: center !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Show full-page loading only when Livewire is loading specific targets */
.full-page-loading[wire\:loading] {
    display: flex !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.loading-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.spinner-border-lg {
    width: 4rem;
    height: 4rem;
    border-width: 0.4rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modern Card Styles */
.modern-card {
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.modern-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-bottom: none;
}

.modern-card-body {
    padding: 1.5rem;
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.professional-stat-card {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.professional-stat-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.1);
}

/* Modern Form Elements */
.modern-select, .modern-input {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.modern-select:focus, .modern-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.modern-btn {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Month Selector */
.month-selector-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.month-selector-item {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.month-selector-item:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.month-selector-item input:checked + label {
    color: #007bff;
    font-weight: 600;
}

/* Modern Table */
.modern-table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.modern-table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
}

.modern-table tbody td {
    padding: 1rem;
    border-color: #f1f3f4;
    vertical-align: middle;
}

.table-row-hover:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Folder Entries */
.folder-entries-modern {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.folder-entry-modern {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.folder-entry-modern:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.folder-link-modern {
    text-decoration: none;
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
}

.folder-link-modern:hover {
    color: #007bff;
}

/* Statistics Summary */
.stats-summary-professional {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.stat-item-professional {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.stat-label-professional {
    font-size: 0.85rem;
    color: #6c757d;
}

.stat-value-professional {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .modern-card-body {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stats-summary-professional {
        grid-template-columns: 1fr;
    }
    
    .folder-entries-modern {
        flex-direction: column;
    }
}

/* Utility Classes */
.cursor-pointer {
    cursor: pointer;
}

.text-decoration-none {
    text-decoration: none !important;
}

/* Font Family for Urdu Text */
.table, .card {
    font-family: 'Jameel Noori Nastaleeq', serif;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Form Switches */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}
</style>
