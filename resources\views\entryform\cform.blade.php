<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Checking Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
        <!-- Include jQuery (required for Bootstrap) -->
        

        <!-- Include Bootstrap JavaScript files -->
        {{-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/js/bootstrap.bundle.min.js"></script> --}}
        {{-- <script src="{{ asset('path/to/util.js') }}"></script> --}}


        <!-- Include your custom JavaScript files -->
        <script src="path/to/util.js"></script>

        <!-- Create Fatawa Entry Form -->
        <style>
            /* ... existing styles ... */
            .hidden-cell {
                display: none;
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
               
            /* ... remaining styles ... */
        </style>
        
        <div class="container">
            <div class="container">
                <h1 class="mb-4">Received Mail Folder</h1>
                <title>Onclick increase Table Rows</title>
                <script type="text/javascript">
                    function addRows() {
    var table1 = document.getElementById('emptbl');
    var table2 = document.getElementById('emptbl1');

    // Insert a new row into the second table
    var newRow2 = table2.insertRow();

    // Get the first row from the second table (to clone)
    var firstRow2 = table2.rows[1];

    // Clone the cells from the first row of the second table
    for (var i = 0; i < firstRow2.cells.length; i++) {
        var newCell2 = newRow2.insertCell(i);
        newCell2.innerHTML = firstRow2.cells[i].innerHTML;

        // Hide specific cells by setting display to "none"
        if (i === 0 || i === 1 || i === 2 || i === 3) {
            newCell2.style.display = "none";
        }
    }

    // Clear input values in the new row of the second table
    var inputs2 = newRow2.querySelectorAll('.table2-input');
    for (var j = 0; j < inputs2.length; j++) {
        inputs2[j].value = '';
    }

    // Clone the file input element and copy its value
    var input1 = table1.querySelector('.table1-attachment');
    var input2 = newRow2.querySelector('.table2-attachment');
    var clonedInput1 = input1.cloneNode(true);
    input2.parentNode.replaceChild(clonedInput1, input2);

    // Copy values from the corresponding row in the first table to the new row in the second table
    var rows1 = table1.querySelectorAll('tbody tr');
    var newRow1 = rows1[rows1.length - 1]; // Get the last row of the first table
    var inputs1 = newRow1.querySelectorAll('.table1-input');
    var inputs2 = newRow2.querySelectorAll('.table2-input');

    for (var k = 0; k < inputs1.length; k++) {
        inputs2[k].value = inputs1[k].value;
    }
}
                    function deleteRows() {
                        var table = document.getElementById('emptbl1');
                        var rowCount = table.rows.length;
                        if (rowCount > 2) {
                            table.deleteRow(rowCount - 1);
                        } else {
                            alert('There should be at least one row');
                        }
                    }
                    function updateMujeebOptions() {
                        const selectedDarul = document.getElementById('darul_name').value;
                        const mujeebSelect = document.getElementById('mujeeb_select'); // Use the correct id here

                        // Clear existing options
                        mujeebSelect.innerHTML = '<option value="0">Select Mujeeb</option>';

                        if (selectedDarul !== '0') {
                            fetch(`/get-mujeebs/${selectedDarul}`)
                                .then(response => response.json())
                                .then(data => {
                                    data.forEach(mujeeb => {
                                        const option = document.createElement('option');
                                        option.value = mujeeb.mujeeb_name;
                                        option.textContent = mujeeb.mujeeb_name;
                                        mujeebSelect.appendChild(option);
                                    });
                                });
                        }
                    }
                    /* document.addEventListener('DOMContentLoaded', function () {
    // Initial event listener for the first row
    const darulSelect = document.getElementById('darul_name');
    const fatwaNoInput = document.getElementById('fatwa_no');
    
    darulSelect.addEventListener('change', function () {
        const selectedDarulOption = darulSelect.options[darulSelect.selectedIndex];
        const selectedDarulCode = selectedDarulOption.getAttribute('data-darul-code');
        
        if (selectedDarulOption.value !== '0') {
            fatwaNoInput.value = `${selectedDarulCode}-`;
            fatwaNoInput.removeAttribute('readonly');
        } else {
            fatwaNoInput.value = '';
            fatwaNoInput.setAttribute('readonly', 'readonly');
        }
    });

    // Event delegation for dynamically added rows
    document.addEventListener('change', function (event) {
        if (event.target && event.target.id === 'darul_name') {
            const selectedDarulSelect = event.target;
            const fatwaNoInput = selectedDarulSelect.closest('tr').querySelector('.form-control input[name="fatwano[]"]');
            
            const selectedDarulOption = selectedDarulSelect.options[selectedDarulSelect.selectedIndex];
            const selectedDarulCode = selectedDarulOption.getAttribute('data-darul-code');

            if (selectedDarulOption.value !== '0') {
                fatwaNoInput.value = `${selectedDarulCode}-`;
                fatwaNoInput.removeAttribute('readonly');
            } else {
                fatwaNoInput.value = '';
                fatwaNoInput.setAttribute('readonly', 'readonly');
            }
        }
    });
}); */


function copyInputValues() {
        const table1Rows = document.querySelectorAll('#emptbl tbody tr');
        const table2Rows = document.querySelectorAll('#emptbl1 tbody tr');
        
        table1Rows.forEach((row, index) => {
            const input1 = row.querySelector('input');
            const input2 = table2Rows[index].querySelector('input[type="hidden"]');
            
            input2.value = input1.value;
        });
    }

    // Call the copyInputValues function when needed, for example, on form submission.
    document.querySelector('form').addEventListener('submit', function() {
        copyInputValues();
    });
    function copyValuesToSecondTable() {
                var table1 = document.getElementById('emptbl');
                var table2 = document.getElementById('emptbl1');

                var rows1 = table1.getElementsByTagName('tr');
                var rows2 = table2.getElementsByTagName('tr');

                for (var i = 1; i < rows1.length; i++) {
                    var row1 = rows1[i];
                    var row2 = rows2[i];

                    var inputs1 = row1.querySelectorAll('.table1-input');
                    var inputs2 = row2.querySelectorAll('.table2-input');

                    for (var j = 0; j < inputs1.length; j++) {
                        inputs2[j].value = inputs1[j].value;
                    }
                }
            }

            // Add an event listener to call the copying function whenever an input changes
            document.addEventListener('change', function(event) {
                if (event.target && event.target.classList.contains('table1-input')) {
                    copyValuesToSecondTable();
                }
            });
            function copyAttachmentValuesToSecondTable() {
            var table1 = document.getElementById('emptbl');
            var table2 = document.getElementById('emptbl1');

            var rows1 = table1.getElementsByTagName('tr');
            var rows2 = table2.getElementsByTagName('tr');

            for (var i = 1; i < rows1.length; i++) {
                var row1 = rows1[i];
                var row2 = rows2[i];

                var input1 = row1.querySelector('.table1-attachment');
                var input2 = row2.querySelector('.table2-attachment');

                input2.files = input1.files;
            }
        }

        // Add an event listener to call the copying function whenever a file is selected
        document.addEventListener('change', function(event) {
            if (event.target && event.target.classList.contains('table1-attachment')) {
                copyAttachmentValuesToSecondTable();
            }
        });
        function handleFileUpload(inputElement) {
        const fileInput = inputElement.querySelector('.table1-attachment');
        const fileDetailsInput = inputElement.querySelector('.table1-file-details');
        const file = fileInput.files[0];

        if (file) {
            const formData = new FormData();
            formData.append('attachment', file);

            fetch('/storage/app', {
                method: 'POST',
                body: formData,
            })
                .then(response => response.json())
                .then(data => {
                    fileDetailsInput.value = data.fileName;
                })
                .catch(error => {
                    console.error('Error uploading file:', error);
                });
        }
    }

    // Add an event listener to handle file uploads when a user selects a file
    document.addEventListener('change', function (event) {
        if (event.target && event.target.classList.contains('table1-attachment')) {
            handleFileUpload(event.target.closest('tr'));
        }
    });
                </script>
                </head>
                <body>
                    <form action="{{ route('cmail.store') }}" method="POST" enctype="multipart/form-data">
                    
                    <table id="emptbl">
                        <thead>
                            <tr>
                                 
                                <th>Mail Received Date</th>
                                <th>Darulifta</th>
                                <th>Mail Folder Date</th>
                                <th>Attached Mail Folder</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                
                                <td><input type="Date" class="form-control table1-input" name="date1[]" value="" /></td>
                                <td class="form-control">
                                    <select name="darulifta1[]" id="darul_name" onchange="updateMujeebOptions()" class="table1-input">
                                        <option value="0">Select Darulifta</option>
                                        @foreach($daruliftaData as $data)
                                            <option value="{{ $data->darul_name }}" data-darul-code="{{ $data->darul_code }}">{{ $data->darul_name }}</option>
                                        @endforeach
                                    </select>
                                </td>
                                <td><input type="date" class="form-control table1-input" name="fdate1[]" value="" /></td>
                                <td id="col4">
                                <label for="attachment1">Attachment (ZIP File):</label>
                                <input type="file" name="attachment1" id="attachment1" class="form-control-file table1-attachment">
                                <input type="hidden" class="table1-file-details" name="file_details[]">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    </form>
                    <form action="{{ route('cmail.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <table id="emptbl1">
                        <thead>
                            <tr>
                                {{-- <th class="hidden-cell"></th> --}}
                                <th>Mujeeb</th>
                                <th>Fatwa No</th>
                                <th>New/Mahl-e-Nazar</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <input type="hidden" name="_token" value="{{ csrf_token() }}" />
                                <td style="display: none;"><input type="Date" class="form-control table2-input" name="date[]" value="" /></td>
                                <td style="display: none;" class="form-control">
                                    <select name="darulifta[]" id="darul_name" onchange="updateMujeebOptions()" class="table2-input">
                                        <option value="0">Select Darulifta</option>
                                        @foreach($daruliftaData as $data)
                                            <option value="{{ $data->darul_name }}" data-darul-code="{{ $data->darul_code }}">{{ $data->darul_name }}</option>
                                        @endforeach
                                    </select>
                                </td>
                                <td style="display: none;"><input type="date" class="form-control table2-input" name="fdate[]" value="" /></td>
                                <td style="display: none;" id="col4">
                                    <label for="attachment2">Attachment (ZIP File):</label>
                                    <input type="file" name="attachment" id="attachment2" class="form-control-file table2-attachment">
                                </td>
                                <td class="form-control">
                                    <select name="mujeeb[]" id="mujeeb_select" class="table2-input">
                                        <option value="0">Select Mujeeb</option>
                                        <!-- Mujeeb options will be dynamically populated here using JavaScript -->
                                    </select>
                                </td>
                                <td>
                                    <input type="text" class="form-control table2-input" name="fatwa_no[]" id="fatwa_no">
                                </td>
                                <td class="form-control">
                                    <select name="ftype[]" id="ftype" class="table2-input">
                                        <option value="0">New or Mahle e Nazar</option>
                                        <option value="1">New</option>
                                        <option value="2">Mahl e Nazar</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <div style="text-align: center; margin-top: 20px;">
                        <input type="button" class="btn btn-primary" value="Add Row" onclick="addRows()" />
                        <input type="button" class="btn btn-danger" value="Delete Row" onclick="deleteRows()" />
                        <input type="submit" class="btn btn-success" value="Submit" />
                    </div>
                </form>
                </body> 
            </div>
        </div>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>