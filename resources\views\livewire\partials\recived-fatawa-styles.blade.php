{{-- Received Fatawa Styles Partial --}}
<style>
    /* CSS Variables for consistent theming */
    :root {
        --primary-color: #007bff;
        --secondary-color: #6c757d;
        --success-color: #28a745;
        --info-color: #17a2b8;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --border-color: #dee2e6;
        --border-radius: 0.375rem;
        --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --transition: all 0.15s ease-in-out;
    }

    /* Table Responsive Styles */
    .table-responsive {
        width: 100%;
        overflow-x: auto;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .table-responsive table {
        width: 100%;
        margin-bottom: 1rem;
        background-color: transparent;
        border-collapse: collapse;
    }

    .table-responsive th,
    .table-responsive td {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        border-top: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color);
        border-left: none;
        border-right: none;
        padding: 0.75rem;
        vertical-align: middle;
    }

    .table-responsive th:first-child,
    .table-responsive td:first-child {
        border-left: 1px solid var(--border-color);
    }

    .table-responsive th:last-child,
    .table-responsive td:last-child {
        border-right: 1px solid var(--border-color);
    }

    .table-bordered {
        border-collapse: collapse;
    }

    /* Modern Button Styles */
    .btn-modern {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.375rem 0.75rem;
        margin-bottom: 0;
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 1.5;
        text-align: center;
        text-decoration: none;
        vertical-align: middle;
        cursor: pointer;
        border: 1px solid transparent;
        border-radius: var(--border-radius);
        transition: var(--transition);
        user-select: none;
    }

    .btn-modern:hover {
        text-decoration: none;
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
    }

    .btn-modern:focus {
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .btn-modern:active {
        transform: translateY(0);
        box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    }

    .btn-modern.btn-outline-modern {
        background-color: transparent;
        border-color: var(--border-color);
        color: var(--dark-color);
    }

    .btn-modern.btn-outline-modern:hover {
        background-color: var(--light-color);
        border-color: var(--secondary-color);
    }

    /* Form Controls */
    .form-contro {
        display: block;
        width: 100%;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        font-weight: 400;
        line-height: 1.5;
        color: var(--dark-color);
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        transition: var(--transition);
    }

    .form-contro:focus {
        color: var(--dark-color);
        background-color: #fff;
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    /* Form Groups */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
        display: block;
    }

    /* Card Styles */
    .custom-card {
        background-color: #FFFFCC;
        padding: 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        border: 1px solid var(--border-color);
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--dark-color);
        margin-bottom: 1rem;
    }

    /* Loading Spinner */
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 20px auto;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Action Icons */
    .action-icon {
        width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .action-download {
        color: var(--info-color);
    }

    .action-delete {
        color: var(--danger-color);
    }

    .action-view {
        color: var(--success-color);
    }

    .action-details {
        color: var(--info-color);
    }

    /* Enhanced Action Button Styles */
    .btn-modern.btn-info-modern {
        background-color: #17a2b8;
        border-color: #17a2b8;
        color: white;
    }

    .btn-modern.btn-info-modern:hover {
        background-color: #138496;
        border-color: #117a8b;
        color: white;
    }

    .btn-modern.btn-primary-modern {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .btn-modern.btn-primary-modern:hover {
        background-color: #0056b3;
        border-color: #004085;
        color: white;
    }

    .btn-modern.btn-success-modern {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .btn-modern.btn-success-modern:hover {
        background-color: #1e7e34;
        border-color: #1c7430;
        color: white;
    }

    .btn-modern.btn-danger-modern {
        background-color: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .btn-modern.btn-danger-modern:hover {
        background-color: #c82333;
        border-color: #bd2130;
        color: white;
    }

    /* Action buttons container */
    .d-flex.gap-1 {
        gap: 0.25rem !important;
    }

    /* Ensure buttons are visible and properly sized */
    .btn-modern.btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
        min-width: 32px;
        height: 32px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Disabled button styling */
    .btn-modern.opacity-50 {
        opacity: 0.5 !important;
        cursor: not-allowed;
    }

    /* Question and Chat Styles */
    .question-text {
        padding: 10px;
        background-color: #f8f9fa;
        border-left: 4px solid #007bff;
        margin: 5px 0;
        border-radius: 4px;
        font-family: 'Noto Nastaliq Urdu', serif;
        direction: rtl;
        text-align: right;
    }

    /* Chat Interface Styles */
    .chat-message {
        padding: 8px 12px;
        margin: 5px 0;
        background-color: #e9ecef;
        border-radius: 8px;
        border-left: 3px solid #28a745;
    }

    .chat-message strong {
        color: #495057;
    }

    .toggle-chat, .toggle-question {
        transition: background-color 0.3s ease;
    }

    .toggle-chat:hover, .toggle-question:hover {
        background-color: #FFB366 !important;
    }

    .cursor-pointer {
        cursor: pointer;
    }

    /* Chat Card Styles */
    .chat-container .card {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .chat-container .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .chat-container .badge {
        font-size: 0.875rem;
    }

    .chat-container .input-group {
        border-radius: 0.5rem;
    }

    .chat-container .form-control {
        border: none;
        box-shadow: none;
    }

    .chat-container .btn-link {
        color: #007bff;
        text-decoration: none;
    }

    .chat-container .btn-link:hover {
        color: #0056b3;
    }

    /* Chat message alignment */
    .chat-container .list-unstyled li {
        margin-bottom: 1rem;
    }

    .chat-container .w-auto {
        max-width: 70%;
    }

    /* Display Options Styles */
    .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .form-check-input:focus {
        border-color: #86b7fe;
        outline: 0;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .form-check-label {
        font-weight: 500;
        color: var(--dark-color);
    }

    /* Calendar Styles */
    .calendar-style {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;
        padding: 10px;
        border: 1px solid var(--border-color);
        background-color: #f9f9f9;
        border-radius: var(--border-radius);
        margin: 1rem 0;
    }

    .month-year {
        display: flex;
        align-items: center;
        padding: 3px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #fff;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: var(--transition);
    }

    .month-year:hover {
        background-color: #f8f9fa;
        border-color: var(--primary-color);
    }

    .month-year input[type="checkbox"] {
        margin-right: 5px;
    }

    .month-year label {
        margin: 0;
        font-size: 12px;
        cursor: pointer;
    }

    /* Apply Filters Button */
    .apply-filters-button {
        background-color: #4682B4;
        border: none;
        color: white;
        padding: 10px 20px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 16px;
        margin: 4px 2px;
        cursor: pointer;
        border-radius: 12px;
        transition-duration: 0.4s;
    }

    .apply-filters-button:hover {
        background-color: white;
        color: black;
        border: 2px solid #4CAF50;
    }

    .apply-filters-button-active {
        background-color: #4CAF50;
        border: none;
        color: white;
        padding: 10px 20px;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        font-size: 16px;
        margin: 4px 2px;
        cursor: pointer;
        border-radius: 12px;
        transition-duration: 0.4s;
    }

    /* Question Cell Styles */
    .question-cell {
        background-color: #ffffff;
        max-width: 1000px;
        padding: 10px;
        direction: rtl;
        overflow: wrap;
    }

    .question-text {
        white-space: normal;
        word-wrap: break-word;
        text-align: right;
        max-width: 100%;
    }

    /* Modern Folder Entry Styles */
    .folder-entries-modern {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        justify-content: flex-start;
    }

    .folder-entry-modern {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border: 1px solid #cbd5e1;
        border-radius: 8px;
        padding: 0.75rem;
        min-width: 200px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .folder-entry-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-color: var(--primary-color);
    }

    .folder-date-modern {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #1e293b;
    }

    .folder-date-modern a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        transition: color 0.3s ease;
    }

    .folder-date-modern a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }

    .folder-transfer-by-modern {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        margin-top: 0.5rem;
    }

    .folder-transfer-by-modern .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .folder-transfer-by-modern .badge:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .summary-stats-modern {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }

    .summary-stats-modern .badge {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-weight: 600;
    }

    /* Legacy Folder Entry Styles (for backward compatibility) */
    .folder-entries {
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
    }

    .folder-entry {
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
        background-color: #f9f9f9;
        transition: background-color 0.3s;
    }

    .folder-entry:hover {
        background-color: #e9ecef;
    }

    .folder-date, .folder-status {
        white-space: nowrap;
        margin: 2px 0;
    }

    .date-link {
        text-decoration: none;
        color: #007bff;
    }

    .date-link:hover {
        text-decoration: underline;
    }

    /* Arrow Animation */
    .arrow {
        transition: transform 0.3s;
    }

    .rotate-180 {
        transform: rotate(180deg);
    }

    /* Clickable Link */
    .clickable-link {
        cursor: pointer;
        color: blue;
        text-decoration: underline;
    }

    .clickable-link:hover {
        color: darkblue;
    }

    /* Background Colors */
    .custom-bg-light-red {
        background-color: #FFDDDD;
    }

    .custom-bg-light-blue {
        background-color: #DDDDFF;
    }

    .custom-bg-light-green {
        background-color: #DDFFDD;
    }

    .custom-bg-light-yellow {
        background-color: #FFFFCC;
    }

    .custom-text-dark-black {
        color: #000;
    }

    /* Typography */
    .table, .card, .table2 {
        font-family: 'Jameel Noori Nastaleeq', serif;
        font-size: 20px;
    }

    .not-assigned {
        color: red;
    }

    .future-date {
        color: red !important;
        border: 1px solid red;
    }

    .past-date {
        border: 1px solid green;
    }

    .increased-font {
        font-size: 20px;
    }

    table {
        table-layout: auto;
        font-size: 20px;
    }

    th, td {
        font-size: 20px;
    }

    /* Main Content Layout */
    .main-content {
        margin-left: 270px;
        position: relative;
        max-height: 100vh;
        border-radius: 8px;
    }

    /* Responsive Design */
    @media screen and (max-width: 768px) {
        td {
            white-space: normal;
            overflow: visible;
        }

        .question-text {
            white-space: normal;
            word-wrap: break-word;
        }

        .main-content {
            margin-left: 0;
        }

        .calendar-style {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media screen and (max-width: 480px) {
        td {
            font-size: 0.8rem;
        }

        .calendar-style {
            grid-template-columns: 1fr;
        }
    }
</style>
