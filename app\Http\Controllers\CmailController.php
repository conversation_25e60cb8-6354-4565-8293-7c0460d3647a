<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Cmail;
use Illuminate\Support\Facades\DB; // Import DB facade from the correct namespace

class CmailController extends Controller
{
    public function CheckPage(Request $request)
    {
        $cmailData = DB::table('cmail')
            ->orderBy('darulifta')
            ->orderBy('mail_recived_date')
            ->orderBy('mail_folder_date')
            ->orderBy('ftype')
            ->orderBy('fatwa_no')
            ->select('*', 'selected') // Include the 'selected' column
            ->get();
    
        $formattedData = [];
    
        foreach ($cmailData as $entry) {
            $darulifta = $entry->darulifta;
            $mailReceivedDate = $entry->mail_recived_date;
            $mailFolderDate = $entry->mail_folder_date;
            $mailFatwa_no = $entry->fatwa_no;
            $id = $entry->id;
            $mujeeb = $entry->mujeeb;
            $ftype = $entry->ftype;
            $attachment = $entry->attachment;
            $selected = $entry->selected; // Get the 'selected' value
    
            if (!isset($formattedData[$darulifta])) {
                $formattedData[$darulifta] = [];
            }
    
            if (!isset($formattedData[$darulifta][$mailReceivedDate][$mailFolderDate][$attachment])) {
                $formattedData[$darulifta][$mailReceivedDate][$mailFolderDate][$attachment] = [];
            }
    
            $formattedData[$darulifta][$mailReceivedDate][$mailFolderDate][$attachment][] = [
                'fatwa_no' => $mailFatwa_no,
                'mujeeb' => $mujeeb,
                'ftype' => $ftype,
                'id' => $id,
                'selected' => $selected, // Include the 'selected' value
            ];
        }
        
        if ($request->has('complete_checked')) {
            return view('pages.ccheck', ['cdata' => $formattedData]);
        }
        
        return view('pages.check', ['cdata' => $formattedData]);
    }
    // for show all data with out filter selected 1 or 0
    // public function CheckPage()
    // {
    //     $cmailData = DB::table('cmail')
    //         ->orderBy('darulifta')
    //         ->orderBy('mail_recived_date')
    //         ->orderBy('mail_folder_date')
    //         ->select('*', 'selected') // Include the 'selected' column
    //         ->get();
    
    //     $formattedData = [];
    
    //     foreach ($cmailData as $entry) {
    //         $darulifta = $entry->darulifta;
    //         $mailReceivedDate = $entry->mail_recived_date;
    //         $mailFolderDate = $entry->mail_folder_date;
    //         $id = $entry->id;
    //         $selected = $entry->selected; // Get the 'selected' value
    
    //         if (!isset($formattedData[$darulifta])) {
    //             $formattedData[$darulifta] = [];
    //         }
    
    //         if (!isset($formattedData[$darulifta][$mailReceivedDate])) {
    //             $formattedData[$darulifta][$mailReceivedDate] = [];
    //         }
    
    //         $formattedData[$darulifta][$mailReceivedDate][] = [
    //             'folderDate' => $mailFolderDate,
    //             'id' => $id,
    //             'selected' => $selected, // Include the 'selected' value
    //         ];
    //     }
        
    //     return view('pages.check', ['cdata' => $formattedData]);
    // }
    // public function CheckPage()
    // {
    //     $cmailData = DB::table('cmail')
    //         ->orderBy('darulifta')
    //         ->orderBy('mail_recived_date')
    //         ->orderBy('mail_folder_date')
    //         ->select('*', 'selected')
    //         ->get();
    
    //     $formattedData = [];
    
    //     foreach ($cmailData as $entry) {
    //         $darulifta = $entry->darulifta;
    //         $mailReceivedDate = $entry->mail_recived_date;
    //         $mailFolderDate = $entry->mail_folder_date;
    //         $id = $entry->id;
    //         $selected = $entry->selected;
    
    //         if ($selected === 0) { // Check if selected is 0
    //             if (!isset($formattedData[$darulifta])) {
    //                 $formattedData[$darulifta] = [];
    //             }
    
    //             if (!isset($formattedData[$darulifta][$mailReceivedDate])) {
    //                 $formattedData[$darulifta][$mailReceivedDate] = [];
    //             }
    
    //             $formattedData[$darulifta][$mailReceivedDate][] = [
    //                 'folderDate' => $mailFolderDate,
    //                 'id' => $id,
    //                 'selected' => $selected,
    //             ];
    //         }
    //     }
        
    //     return view('pages.check', ['cdata' => $formattedData]);
    // }

    public function store(Request $request)
{
    $dates = $request->input('date');
    $daruliftas = $request->input('darulifta'); // Assuming 'darulifta' is an array
    $fdates = $request->input('fdate');
    $mujeebs = $request->input('mujeeb');
    $fatwa_nos = $request->input('fatwa_no');
    $ftypes = $request->input('ftype');

    // Loop through the arrays and insert each row
    foreach ($dates as $index => $date) {
        // Get the selected Darulifta name using the option value
        $selectedDarulifta = $daruliftas[$index];
        $selectedMujeeb = $mujeebs[$index];

       // Handle file upload for this row
       $file = $request->file('attachment');

       if ($file) {
           // Check if the file is valid
           if ($file->isValid()) {
               $filename = $file->getClientOriginalName(); // Get the original filename
               $path = $file->storeAs('public/attachments', $filename); // Store the file in the 'attachments' directory
           } else {
               $path = null; // The uploaded file is not valid
           }
       } else {
           $path = null; // No file was uploaded for this row
       }

       $path = str_replace('public', '', $path);
        // Insert the data into the database
        DB::table('cmail')->insert([
            'mail_recived_date' => $date,
            'darulifta' => $selectedDarulifta,
            'mail_folder_date' => $fdates[$index],
            'mujeeb' => $selectedMujeeb,
            'fatwa_no' => $fatwa_nos[$index], // Assuming 'fatwa_no' is an array
            'attachment' => $path, // Store the file path in the database
            'ftype' => $ftypes[$index],
            
        ]);
    }

    // Redirect back with a success message
    return redirect()->back()->with('success', 'Data added successfully.');
}
         public function editcheck()
    {
        $ecmail = DB::table('cmail')
                ->where('id',16)
                ->update(['mail_recived_date' => '2023-08-20']);
    }
    public function UcMail(string $id)
    
    {
        $ecmail = DB::table('cmail')
                    ->find($id);
            // ->where('id', $id)
            // ->get(); // Use 'first' to retrieve a single row
    // return $ecmail;
        return view('entryform.ucform', ['ecmail' => $ecmail]);
    }
    public function ScMail(Request $request, $id)
{
    $updatedDate = $request->input('date')[0];
    $selectedDaruliftaIndex = (int) $request->input('darulifta')[0];
    $updatedFolderDate = $request->input('fdate')[0];

    $daruliftaNames = [
        0 => 'Select Darulifta',
        1 => 'Noorulirfan',
        2 => 'Faizan e Ajmair',
        3 => 'Gulzare Taiba',
        4 => 'Iqtisaad',
    ];

    DB::table('cmail')
        ->where('id', $id)
        ->update([
            'mail_recived_date' => $updatedDate,
            'darulifta' => $daruliftaNames[$selectedDaruliftaIndex],
            'mail_folder_date' => $updatedFolderDate,
        ]);

    return redirect()->route('check.page')->with('success', 'Data updated successfully.');
}
    public function deletecheck(string $id)
    {
        $dcheck = DB::table('cmail')
                ->where('id',$id)
                ->delete();
        if($dcheck){
            return redirect()->route('check.page');

        }
    }
    public function updateSelected(Request $request, $id)
{
    $isChecked = $request->input('isChecked') ? 1 : 0;

    DB::table('cmail')
        ->where('id', $id)
        ->update(['selected' => $isChecked]);

    return response()->json(['success' => true]);
}
public function getMujeebs($daruliftaName)
    {
        // Fetch Mujeebs based on the selected Darulifta's name
        $mujeebs = DB::table('mujeebs')->where('darul_name', $daruliftaName)->get();

        return response()->json($mujeebs);
    }    

    public function create()
{
    $daruliftaData = DB::table('daruliftas')->get(); // Fetch Darulifta data
    $mujeebData = DB::table('mujeebs')->get(); // Fetch Mujeeb data

    // Pass both sets of data to the view
    return view('pages.tests', compact('daruliftaData', 'mujeebData'));
}
    // public function index()
    // {
    //     $data = DB::table('cmail')->get(); // Query the database directly

    //     return view('pages.cmail', compact('data'));
    // }
    public function index(Request $request)
    {
        $cmailData = DB::table('cmail')
            ->orderBy('darulifta')
            ->orderBy('mail_recived_date')
            ->orderBy('mail_folder_date')
            ->orderBy('ftype')
            ->orderBy('fatwa_no')
            ->select('*', 'selected') // Include the 'selected' column
            ->get();
    
        $formattedData = [];
    
        foreach ($cmailData as $entry) {
            $darulifta = $entry->darulifta;
            $mailReceivedDate = $entry->mail_recived_date;
            $mailFolderDate = $entry->mail_folder_date;
            $mailFatwa_no = $entry->fatwa_no;
            $id = $entry->id;
            $mujeeb = $entry->mujeeb;
            $ftype = $entry->ftype;
            $attachment = $entry->attachment;
            $selected = $entry->selected; // Get the 'selected' value
    
            if (!isset($formattedData[$darulifta])) {
                $formattedData[$darulifta] = [];
            }
    
            if (!isset($formattedData[$darulifta][$mailReceivedDate][$mailFolderDate][$attachment])) {
                $formattedData[$darulifta][$mailReceivedDate][$mailFolderDate][$attachment] = [];
            }
    
            $formattedData[$darulifta][$mailReceivedDate][$mailFolderDate][$attachment][] = [
                'fatwa_no' => $mailFatwa_no,
                'mujeeb' => $mujeeb,
                'ftype' => $ftype,
                'id' => $id,
                'selected' => $selected, // Include the 'selected' value
            ];
        }
        
        if ($request->has('complete_checked')) {
            return view('pages.ccmail', ['cdata' => $formattedData]);
        }
        
        return view('pages.cmail', ['cdata' => $formattedData]);
    }
}