<div>



<!-- <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>

<script src="https://rawgit.com/eKoopmans/html2pdf/master/dist/html2pdf.bundle.js"></script> -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

<!-- <script src="https://cdn.jsdelivr.net/npm/alpinejs@2.8.2/dist/alpine.min.js" defer></script> -->
<script src="//unpkg.com/alpinejs" defer></script>
<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">

            <!-- Navbar -->
            <style>
    .pencil-badge {
    position: relative;
    padding: 4px 8px;
    clip-path: polygon(0% 0%, 85% 0%, 100% 50%, 85% 100%, 0% 100%);
    font-size: 10px;
    font-weight: bold;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    background-color: #6c757d; /* Tag color (secondary gray) */
    color: #ffffff; /* Text color (white) */
    z-index: 10;
    margin-right: 5px; /* Minor gap from file_code */
}

.pencil-badge:hover {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    background-color: #495057; /* Slightly darker shade on hover */
}
    .month-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.month-year {
    min-width: 120px; /* Minimum width to accommodate longer month names */
    max-width: 150px; /* Set a maximum width to keep it from stretching too wide */
    padding: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    background-color: #f0f8ff;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    border-radius: 5px;
    margin: 5px;
}

.month-year strong {
    display: block;
    font-size: 16px;
}

.month-year span {
    font-size: 14px;
}

/* Responsive Adjustment */
@media (max-width: 768px) {
    .month-year {
        width: 100%; /* Full width on smaller screens */
    }
}
    .highlight {
        background-color: #ffa07a !important; /* Ensure highlight color takes priority */
        border: 2px solid #ff4500 !important;
    }
    .btn-custom-blue {
        background-color: #007bff;
        color: #fff;
        padding: 5px 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    .btn-custom-blue:hover {
        background-color: #0056b3;
    }
  .result-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 20px;
        margin-bottom: 30px;
    }

    /* Styling each box (for each column like Viral Total, Ready to Print, etc.) */
    .result-box {
        flex: 1 1 22%; /* Each box will take 22% of the row, adjust for spacing */
        background-color: #f9f9f9;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        font-family: Arial, sans-serif;
    }

    /* Styling for box titles */
    .result-box h3 {
        font-size: 18px;
        margin-bottom: 10px;
        color: #007bff;
        text-transform: uppercase;
        font-weight: bold;
    }

    /* Styling for list inside each box */
    .result-box ul {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    /* List item style (Darulifta and counts) */
    .result-box ul li {
        font-size: 16px;
        margin: 5px 0;
        color: #333;
    }

    /* Total row at the bottom */
    .result-box .total {
        font-weight: bold;
        margin-top: 15px;
    }

    /* Responsive layout: for smaller screens, adjust the box width */
    @media (max-width: 768px) {
        .result-box {
            flex: 1 1 48%; /* Two boxes per row on smaller screens */
        }
    }

    @media (max-width: 480px) {
        .result-box {
            flex: 1 1 100%; /* One box per row on very small screens */
        }
    }
.table-responsive {
    width: 100%;
    overflow-x: auto;
}

.table-responsive table {
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: collapse;
}

.table-responsive th,
.table-responsive td {
    white-space: nowrap; /* Prevents wrapping by default */
    overflow: hidden;
    text-overflow: ellipsis; /* Adds ellipsis to overflowed content */
    border-top: 1px solid #dee2e6; /* Adds top border */
    border-bottom: 1px solid #dee2e6; /* Adds bottom border */
    border-left: none; /* Removes left border */
    border-right: none; /* Removes right border */
}

.table-responsive th:first-child,
.table-responsive td:first-child {
    border-left: 1px solid #dee2e6; /* Adds left border to the first cell */
}

.table-responsive th:last-child,
.table-responsive td:last-child {
    border-right: 1px solid #dee2e6; /* Adds right border to the last cell */
}

.table-bordered {
    border-collapse: collapse; /* Ensures borders are not doubled */
}
                .question-cell {
        background-color: #ffffff;
        max-width: 1000px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }
                .custom-card {
            background-color: #FFFFCC;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #5a5a5a;
        }

        .form-group label {
            font-weight: bold;
            color: #5a5a5a;
        }

        .form-group {
            margin-right: 1px;
            margin-bottom: 10px;
        }

        .form-contro {
            display: inline-block;
            width: 100%;
        }

        .apply-button {
            margin-top: 32px;
            background-color: #ff4081;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }

        .apply-button:hover {
            background-color: #e73570;
        }

        .date-group {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
.question-cell {
        background-color: #ffffff;
        max-width: 1000px;
        padding: 10px;
        direction: rtl; /* Right-to-left text direction for Urdu */
        overflow: wrap;
    }
    .question-text {
        white-space: normal; /* Allow text to wrap */
        word-wrap: break-word; /* Ensure long words break onto the next line */
        text-align: right; /* Right-align text */
        max-width: 100%; /* Ensure text doesn't overflow beyond the cell */
        color: black;
    }
    @media (max-width: 768px) {
        .question-text {
            white-space: normal;
            word-wrap: break-word;
        }
    }
.folder-entries {
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
    }
    .folder-entry {
        display: flex;
        flex-direction: column;
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 5px;
        border: 1px solid #ccc; /* Border for each entry */
        border-radius: 4px;
        background-color: #f9f9f9; /* Background color for better contrast */
        transition: background-color 0.3s; /* Smooth transition for hover effect */
    }
    .folder-entry:hover {
        background-color: #e9ecef; /* Highlight color on hover */
    }
    .folder-date, .folder-status {
        white-space: nowrap;
        margin: 2px 0;
    }
    .date-link {
        text-decoration: none;
        color: #007bff; /* Link color */
    }
    .date-link:hover {
        text-decoration: underline; /* Underline on hover */
    }
                .arrow {
            transition: transform 0.3s;
        }
        .rotate-180 {
            transform: rotate(180deg);
        }
                .clickable-link {
        cursor: pointer;
        color: blue;
        text-decoration: underline;
    }
    .clickable-link:hover {
        color: darkblue;
    }
                .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
.main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }
                .view a i {
    color: #4CAF50; /* Green color for view icon */
}

.download a i {
    color: #FF5733; /* Orange color for download icon */
}
td {
    white-space: nowrap; /* Prevents wrapping by default */
    overflow: hidden;    /* Hides overflow */
    text-overflow: ellipsis; /* Adds ellipsis to overflowed content */
}

/* Responsive adjustments */
@media screen and (max-width: 768px) { /* Adjust for tablets */
    td {
        white-space: normal; /* Allows wrapping */
        overflow: visible;
    }
}

@media screen and (max-width: 480px) { /* Adjust for mobile phones */
    td {
        font-size: 0.8rem; /* Smaller font size for smaller screens */
    }
}
                .calendar-style {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 items per row, adjust as needed */
    gap: 5px; /* smaller gap */
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.month-year {
    display: flex;
    align-items: center;
    padding: 3px; /* reduced padding */
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.month-year input[type="checkbox"] {
    margin-right: 5px; /* smaller margin */
}

.month-year label {
    margin: 0;
    font-size: 12px; /* smaller font size */
}


                 .right-aligned {
        text-align: right;
    }
    .card-title-row {
        display: flex;          /* Enable flexbox for the container */
        justify-content: space-between; /* Spread children to each end */
        align-items: center;    /* Center items vertically */
        width: 100%;            /* Ensure the container spans the full width */
    }

    h2.card-title {
        margin: 0;             /* Remove margin to avoid unnecessary spacing */
        flex-grow: 1;          /* Allow the title to take up necessary space */
        white-space: nowrap;   /* Keep the title in a single line */
    }

    select {
        margin-left: auto;     /* Push the select box to the end of the container */
    }
    /* .question-section,
.chat-section {
    display: none;
} */

                    .custom-bg-light-red {
                    background-color: #FFDDDD; /* Lightest shade of red */
        }

        .custom-bg-light-blue {
            background-color: #DDDDFF; /* Lightest shade of blue */
        }

        .custom-bg-light-green {
            background-color: #DDFFDD; /* Lightest shade of green */
        }


        .custom-bg-light-yellow {
            background-color: #FFFFCC; /* Lightest shade of yellow */
        }
        .custom-text-dark-black {
            color: #000; /* Dark black color */
        }
                    .table{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px

                  }
                  .card{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px

                  }
                  .table2{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px

                  }
                    .not-assigned {
            color: red;
            /* Add any other styles for not assigned here */
        }
        .future-date {
            color: red !important;
            border: 1px solid red;
        }

        .past-date {
            border: 1px solid green;
        }
        .increased-font {
                font-size: 20px; /* Change the font size as needed */
            }
            table {
            table-layout: auto;
            font-size: 20px; /* Adjust the font size to your preference */

        }
        th, td {
                font-size: 20px; /* Adjust the font size for table headers and table data cells */
            }

</style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
@livewire('navbar', ['titlePage' => 'Viral Fatawa'])


            @php

            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        @endphp
        <div id="loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.7); z-index:9999; text-align:center;">
            <div style="position:relative; top:50%; transform:translateY(-50%);">
                <h2>Loading...</h2>
                <!-- You can replace this with a spinner or any loader image -->
            </div>
        </div>
        @php
    // Check if the user is authenticated before checking roles
    $Admin = Auth::check() && (
        in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
        in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
    );

    // Determine which button should be highlighted based on the current route.
    $isSelectedActive     = request()->routeIs('selected-viral');
    $isUnselectedActive   = request()->routeIs('select-viral');
    $isViralFatawaActive  = request()->routeIs('viral-fatawa');
@endphp

<!-- Buttons for Selected, Unselected, and Viral Fatawa Detail -->
<div style="display: flex; align-items: center; margin-bottom: 20px;">
    <a href="{{ route('selected-viral') }}"
       class="apply-filters-button {{ $isSelectedActive ? 'apply-filters-button-active' : '' }}"
       style="margin-right: 10px;">
        Selected Fatawa
    </a>
    <a href="{{ route('select-viral') }}"
       class="apply-filters-button {{ $isUnselectedActive ? 'apply-filters-button-active' : '' }}"
       style="margin-right: 10px;">
        Unselected Fatawa
    </a>
    <a href="{{ route('viral-fatawa') }}"
       class="apply-filters-button {{ $isViralFatawaActive ? 'apply-filters-button-active' : '' }}">
        Viral Fatawa Detail
    </a>
</div>
        <div class="month-container">
    <!-- Back Button -->
    <button id="back-button" class="btn-custom-blue" onclick="showPreviousMonths()" style="display: none;">Back</button>

    <div id="months-row" class="d-flex flex-wrap">
        <!-- Months will be inserted here by JavaScript -->
    </div>

    <!-- Next Button -->
    <button id="next-button" class="btn-custom-blue" onclick="showNextMonths()">Next</button>
</div>

        <div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">

        <div class="card mb-4" style="background-color: #FFFFCC;">
            <div class="card-body">
            <div class="table-responsive">
                <h2 class="card-title">Darulifta And Mail Folder Summary of Viral Fatawa</h2>
                <form method="GET" action="{{ route('viral-fatawa', [
    'darulifta' => request()->route('darulifta'),
    'mailfolder' => request()->route('mailfolder')
]) }}">
        <div class="row">
                <div class="col-md-2 form-group">
                        <label for="mujeebframe">Select Mujeeb:</label>
                        <select class="form-contro" id="mujeebframe" name="selectedmujeeb">
                        <option value="all" {{ request('selectedmujeeb') === 'all' ? 'selected' : '' }}>All Mujeeb</option>
                        @foreach ($mujeebs as $sender)
                            <option value="{{ $sender }}" {{ request('selectedmujeeb') === $sender ? 'selected' : '' }}>
                                {{ $sender }}
                            </option>
                        @endforeach
                        </select>
                  </div>

                        <div class="col-md-2 form-group">
                            <label for="timeframe">Time Frame:</label>
                            <select class="form-contro" id="timeframe" name="selectedTimeFrame">
                            <option value="all" selected {{ request('selectedTimeFrame') === 'all' ? 'selected' : '' }}>All</option>
                            <option value="this_month" {{ request('selectedTimeFrame') === 'this_month' ? 'selected' : '' }}>This Month</option>
                                    <option value="last_month" {{ request('selectedTimeFrame') === 'last_month' ? 'selected' : '' }}>Last Month</option>
                                    @if (request('selectedTimeFrame') === 'other')
                                    <option value="other" {{ request('selectedTimeFrame') === 'other' ? 'selected' : '' }}>Other</option>
                                    @endif
                                    @if (request('selectedTimeFrame') === 'custom')
                                        <option value="custom" selected>Selected Date</option>
                                    @endif
                                </select>
                        </div>

                        <div class="col-md-2 form-group">
                            <label for="checked">Checked Status:</label>
                            <select class="form-contro" id="checked" name="selectedchecked">
                                <option value="selected_viral" selected  {{ request('selectedchecked') === 'selected_viral' ? 'selected' : '' }}>Not Ready to Print</option>
                                <option value="ready_print" {{ request('selectedchecked') === 'ready_print' ? 'selected' : '' }}>Ready To Print</option>
                                <option value="web_link" {{ request('selectedchecked') === 'web_link' ? 'selected' : '' }}>Web Link</option>
                                <option value="viral_link" {{ request('selectedchecked') === 'viral_link' ? 'selected' : '' }}>Viral Link</option>
                                <option value="all" {{ request('selectedchecked') === 'all' ? 'selected' : '' }}>All</option>
                            </select>
                        </div>

                        </div>
                        <div class="row">
                        <div class="col-md-2 form-group custom-width">
                        <label for="start_date">Start Date:</label>
                        <input type="date" class="form-contro date-input" id="start_date" name="startDate" value="{{ request('startDate') }}">
                        </div>
                        <div class="col-md-2 form-group custom-width">
                        <label for="end_date">End Date:</label>
                        <input type="date" class="form-contro date-input" id="end_date" name="endDate" value="{{ request('endDate') }}">
                        </div>

                    </div>

                </form>
                <div class="result-container">

<!-- First box for "Total Viral" -->
<div class="result-box">
    <h3>New Fatawa For Viral</h3>
    <ul>
        @php
            // Initialize the total viral count
            $totalViral = 0;
        @endphp

        @foreach($remainingFatawa as $daruliftaName => $fatawaData)
            @php
                $viralCount = $fatawaData->where('viral', '!=', 0)->count();
                // Add to the total viral count
                $totalViral += $viralCount;
            @endphp
            <li>{{ $daruliftaName }}: {{ $viralCount }}</li>
        @endforeach
    </ul>
    <div class="total">Total: {{ $totalViral }}</div>
</div>

<!-- Second box for "Total Ready to Print" -->
<div class="result-box">
    <h3>Total Ready to Print</h3>
    <ul>
        @php
            $totalReadyToPrint = 0;
        @endphp

        @foreach($remainingFatawa as $daruliftaName => $fatawaData)
            @php
                $readyToPrintCount = $fatawaData->whereNotNull('viral_upload')->where('viral_upload', '!=', '')->count();
                $totalReadyToPrint += $readyToPrintCount;
            @endphp
            <li>{{ $daruliftaName }}: {{ $readyToPrintCount }}</li>
        @endforeach
    </ul>
    <div class="total">Total: {{ $totalReadyToPrint }}</div>
</div>

<div class="result-box">
    <h3>Not Ready to Print</h3>
    <ul>
        @php
            $totalNotReadyToPrint = 0;
        @endphp

        @foreach($remainingFatawa as $daruliftaName => $fatawaData)
            @php
                $notReadyToPrintCount = $fatawaData->where('viral', '!=', 0)->whereNull('viral_upload')->count();
                $totalNotReadyToPrint += $notReadyToPrintCount;
            @endphp
            <li>{{ $daruliftaName }}: {{ $notReadyToPrintCount }}</li>
        @endforeach
    </ul>
    <div class="total">Total: {{ $totalNotReadyToPrint }}</div>
</div>
<!-- Third box for "Total Web Upload" -->
<div class="result-box">
    <h3>Viral & Web Upload</h3>
    <ul>
        @php
            $totalWebUpload = 0;
            $totalViralLink = 0;
        @endphp

        @foreach($remainingFatawa as $daruliftaName => $fatawaData)
            @php
                $webUploadCount = $fatawaData->whereNotNull('web_link')->where('web_link', '!=', '')->count();
                $viralLinkCount = $fatawaData->whereNotNull('viral_link')->where('viral_link', '!=', '')->count();
                $totalWebUpload += $webUploadCount;
                $totalViralLink += $viralLinkCount;
            @endphp
            <li>
                <strong>{{ $daruliftaName }}:</strong>
                Viral: {{ $viralLinkCount }}, Web: {{ $webUploadCount }}
            </li>
        @endforeach
    </ul>
    <div class="total">
    <strong>Total Viral:</strong> {{ $totalViralLink }}<br>
    <strong>Total Web :</strong> {{ $totalWebUpload }}
    </div>
</div>

<!-- Fifth box for "Not Ready to Print" -->


<!-- Sixth box for "Not Uploaded to Web" -->
<div class="result-box">
    <h3>Not Viral and Not Uploaded to Web</h3>
    <ul>
        @php
            $totalNotViral = 0;
            $totalNotUploadedToWeb = 0;
        @endphp

        @foreach($remainingFatawa as $daruliftaName => $fatawaData)
            @php
                // Counts for "Not Viral"
                $readyToPrintCount = $fatawaData->whereNotNull('viral_upload')->where('viral_upload', '!=', '')->count();
                $viralLinkCount = $fatawaData->whereNotNull('viral_link')->where('viral_link', '!=', '')->count();
                $notViralCount = $readyToPrintCount - $viralLinkCount;
                $totalNotViral += $notViralCount;

                // Counts for "Not Uploaded to Web"
                $webUploadCount = $fatawaData->whereNotNull('web_link')->where('web_link', '!=', '')->count();
                $notUploadedToWebCount = $readyToPrintCount - $webUploadCount;
                $totalNotUploadedToWeb += $notUploadedToWebCount;
            @endphp
            <li>
                <strong>{{ $daruliftaName }}:</strong>
                Not Viral: {{ $notViralCount }}, Not Uploaded to Web: {{ $notUploadedToWebCount }}
            </li>
        @endforeach
    </ul>
    <div class="total">
        <strong>Total Not Viral:</strong> {{ $totalNotViral }}<br>
        <strong>Total Not Uploaded to Web:</strong> {{ $totalNotUploadedToWeb }}
    </div>
</div>

</div>




</div>
</div>


    </div>
</div>
        <br>
        @php
    // Check if the user is authenticated before checking roles
    $Admin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());

@endphp

@if ($Admin)
    <div style="display: flex; align-items: center;">
        <div>
            @php
                $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                $isAllIftaActive = request()->route('darulifta') == null; // Check if 'darulifta' parameter is not set
            @endphp
            <a href="{{ route('viral-fatawa', [
                'selectedmufti' => $this->selectedmufti,
                'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                'selectedchecked' => $selectedchecked,
                'startDate' => $tempStartDate,
                'endDate' => $tempEndDate,
                'showDetail' => $showDetail ? '1' : '0',
                'showQue' => $showQue ? '1' : '0',
                'showChat' => $showChat ? '1' : '0',
                'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
            ]) }}" class="apply-filters-button {{ $isAllIftaActive ? 'apply-filters-button-active' : '' }}">
                All Ifta
            </a>
        </div>

        @foreach($daruliftalist as $daruliftalistn)
            @php
                $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                $isActive = request()->route('darulifta') == $daruliftalistn; // Check if the current route parameter matches
            @endphp

            <div>
                <a href="{{ route('viral-fatawa', [
                    'darulifta' => $daruliftalistn,
                    'selectedmufti' => $this->selectedmufti,
                    'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                    'selectedchecked' => $selectedchecked,
                    'startDate' => $tempStartDate,
                    'endDate' => $tempEndDate,
                    'showDetail' => $showDetail ? '1' : '0',
                'showQue' => $showQue ? '1' : '0',
                'showChat' => $showChat ? '1' : '0',
                'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
            ]) }}"  class="apply-filters-button {{ $isActive ? 'apply-filters-button-active' : '' }}">
                    {{ $daruliftalistn }}
                </a>
            </div>
        @endforeach
    </div>
@endif


<div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">

            <div class="card z-index-2 mb-3" id="big-card-1" style="background-color: #FFFFCC;">

                <div class="card-header pb-0">
                    <h4>Viral Fatawa </h4>

                    <input type="text" wire:model.live.debounce.300ms="searchQuery" placeholder="Search Fatawa..."/>


                </div>




                <div class="card-body px-0 pb-2">
                @php
                                        $serialNumber_re = 1; // Initialize the serial number
                                        @endphp
                    @foreach($daruliftaNames as $daruliftaName)
                        @if(isset($remainingFatawa[$daruliftaName]))
                        <div x-data="{ open: true }" wire:key="darulifta-{{ $daruliftaName }}">
                        <h5 @click="open = !open" class="cursor-pointer">
                            <span x-text="open ? '▲' : '▼'"></span> {{ $daruliftaName }}
                        </h5>
                        <div x-show="open" class="table-responsive1">


                                @php
                                        $serialNumber_fl = 1; // Initialize the serial number

                                        $mailfolderDateCount = 0;
                                        $colors = ['#F0FFF0', '#F0F8FF', '#FFFFE0', '#FFE4E1', '#F0F8FF', '#F5FFFA', '#E6E6FA', '#FAEBD7', '#ADD8E6']; // Add more colors as needed
                                        @endphp

                                @if(isset($remainingFatawa[$daruliftaName]))
                                @php
                                    $mailfolderDateCount = count($remainingFatawa[$daruliftaName]); // Count the occurrences of $mailfolderDate
                                    $fatawaCollection = $remainingFatawa[$daruliftaName];
                                @endphp
                                @foreach($fatawaCollection as $fatawa)
            @php
                $checker = $fatawa->checker; // Access the 'checker' property of each individual fatawa
                $downloadedByAdmin = $fatawa->downloaded_by_admin;

    $daruliftaName = is_array($daruliftaName) ? implode(', ', $daruliftaName) : $daruliftaName;
    $checker = isset($checker) ? (is_array($checker) ? implode(', ', $checker) : $checker) : '';

    // Convert additional parameters to query string if they are arrays
    $selectedmujeeb = is_array($selectedmujeeb) ? implode(', ', $selectedmujeeb) : $selectedmujeeb;
    $selectedmufti = is_array($selectedmufti) ? implode(', ', $selectedmufti) : $selectedmufti;
    $selectedchecked = is_array($selectedchecked) ? implode(', ', $selectedchecked) : $selectedchecked;
    $tempSelectedTimeFrame = is_array($tempSelectedTimeFrame) ? implode(', ', $tempSelectedTimeFrame) : $tempSelectedTimeFrame;

    // Convert $selectedMonths to a query string if it's an array
    if (is_array($selectedMonths)) {
        $selectedMonths = http_build_query(['selectedMonths' => $selectedMonths]);
    }
    // Get checkbox values

            @endphp
            <!-- Your HTML/Blade code here to display each fatawa and its checker -->
        @endforeach

                                <div class="card z-index-2 mb-3" style="background-color: {{ $colors[$loop->index % count($colors)] }};">
                                    <div  class="data-row-r">



                    <div class="table-responsive">

                    <table  class="table1 table-bordered align-items-center mb-0">


                                    <thead>
                                        <tr>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 3%; white-space: normal; color: black;">S.No</th>
                                        <th class="text-uppercase font-weight-bolder opacity-7" style="width: 3%; white-space: normal; color: black;">Fatwa No</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Mujeeb</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Category</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Selected Viral Date</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Ready To Print File</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Web Link</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Viral Link</th>
                                        <th class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Fatwa Detail</th>
                                        <th class="text-center text-uppercase font-weight-bolder  opacity-7" style="width: 5%; color: black;">View</th>



                                        </tr>
                                    </thead>
                                    <tbody>

                                        @foreach($remainingFatawa[$daruliftaName] as $file)

                                        <tr wire:key="question-row-{{ $file->id }}">
                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="font-weight-bold">{{ $serialNumber_re++ }}</span>
                                                        </td>
                                                        <td class="align-middle text-center" style="position: relative;">
    <div class="d-flex px-2 py-1">
        <div class="d-flex flex-column justify-content-center">
            <h6 style="position: relative; display: flex; align-items: center;">
                @if ($file->viral != 0 && $file->user_name)
                    @php
                        $nameParts = explode(' ', $file->user_name);
                        $initials = count($nameParts) > 1
                            ? strtoupper(substr($nameParts[0], 0, 1) . substr($nameParts[1], 0, 1))
                            : strtoupper(substr($nameParts[0], 0, 2));
                    @endphp
                    <!-- Styled pencil-shaped badge -->
                    <span class="badge pencil-badge bg-secondary text-white me-2" title="{{ $file->user_name }}">
                        {{ $initials }}
                    </span>
                @endif
                <span style="color: blue;">{{ $file->file_code }}</span>
            </h6>
        </div>
    </div>
</td>


                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="mujeeb-column0" style="color: green;width: 5%; white-space: normal;">{{ $file->sender }}</span>
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="font-weight-bold" style="color: green; width: 5%; white-space: normal;">{{ $file->category }}</span>
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="font-weight-bold" style="color: green; width: 5%; white-space: normal;">{{ $file->checked_date }}</span>
                                                        </td>

                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            @if (empty($file->viral_upload))
                                                                @php
                                                                    // Calculate the days difference between the checked date and today
                                                                    $checkedDate = \Carbon\Carbon::parse($file->checked_date);
                                                                    $currentDate = \Carbon\Carbon::now('Asia/Karachi'); // Use Karachi timezone
                                                                    $daysDifference = $checkedDate->diffInDays($currentDate);
                                                                @endphp

                                                                    <!-- Other condition if you want to add something after 7 days -->
                                                                    <span class="font-weight-bold" style="color: red; width: 5%; white-space: normal;">
                                                                        Not Ready ({{ $daysDifference }} days)
                                                                    </span>

                                                            @else
                                                                <a href="{{ route('viewRemain', [
                                                                        'date' => 'viral',  // Use only the base folder without subfolders
                                                                        'filename' => $file->checked_file_name
                                                                    ]) }}" target="_blank">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            @endif
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            @if(empty($file->viral_upload))
                                                                <!-- Condition 1: viral_upload is empty or null -->
                                                                <span class="font-weight-bold" style="color: red; width: 5%; white-space: normal;">Waiting for Ready to Print file</span>
                                                            @else
                                                                <!-- Condition 2: viral_upload is not empty -->
                                                                @if(empty($file->web_link))
                                                                    <!-- Condition 2.1: web_link is empty or null -->
                                                                    @php
                                                                        // Calculate the number of days between viral_upload and current date
                                                                        $currentDate = \Carbon\Carbon::now()->timezone('Asia/Karachi');
                                                                        $viralUploadDate = \Carbon\Carbon::parse($file->viral_upload);
                                                                        $daysDifference = $viralUploadDate->diffInDays($currentDate);
                                                                    @endphp
                                                                    <span class="font-weight-bold" style="color: orange; width: 5%; white-space: normal;">
                                                                        Not uploaded on web — {{ $daysDifference }} days
                                                                    </span>
                                                                @else
                                                                    <!-- Condition 2.2: web_link is not empty -->
                                                                    <a href="{{ $file->web_link }}" target="_blank" class="font-weight-bold" style="color: green;">
                                                                        View On Web
                                                                    </a>
                                                                @endif
                                                            @endif
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            @if(empty($file->viral_upload))
                                                                <!-- Condition 1: viral_upload is empty or null -->
                                                                <span class="font-weight-bold" style="color: red; width: 5%; white-space: normal;">Waiting for Ready to Print file</span>
                                                            @else
                                                                <!-- Condition 2: viral_upload is not empty -->
                                                                @if(empty($file->viral_link))
                                                                    <!-- Condition 2.1: web_link is empty or null -->
                                                                    @php
                                                                        // Calculate the number of days between viral_upload and current date
                                                                        $currentDate = \Carbon\Carbon::now()->timezone('Asia/Karachi');
                                                                        $viralUploadDate = \Carbon\Carbon::parse($file->viral_upload);
                                                                        $daysDifference = $viralUploadDate->diffInDays($currentDate);
                                                                    @endphp
                                                                    <span class="font-weight-bold" style="color: orange; width: 5%; white-space: normal;">
                                                                        Not Viral — {{ $daysDifference }} days
                                                                    </span>
                                                                @else
                                                                    <!-- Condition 2.2: web_link is not empty -->
                                                                    <a href="{{ $file->viral_link }}" target="_blank" class="font-weight-bold" style="color: green;">
                                                                        View Viral Fatwa
                                                                    </a>
                                                                @endif
                                                            @endif
                                                        </td>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                        <span class="view">
                                                                <a href="{{ route('viral-fatwa-detail',
                                                                    ['fatwa' => $file->file_code]) }}" target="_blank">
                                                                    <button class="btn btn-outline-primary">
                                                                        <i class="fas fa-eye"></i> View Details
                                                                    </button>




                                                                </a>
                                                            </span>
                                                        </td>

                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                        <span class="view">
                                                            @php
                                                                // Determine the date parameter based on conditions
                                                                if (empty($file->by_mufti)) {
                                                                    if (empty($file->checker)) {
                                                                        $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked';
                                                                    } else {
                                                                        $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked_by_' . $file->checker;
                                                                    }
                                                                } else {
                                                                    $dateParam = $file->mail_folder_date . $file->darulifta_name . 'Checked_by_' . $file->checker . '_' . $file->by_mufti;
                                                                }
                                                            @endphp

                                                            <a href="{{ route('viewCheck', [
                                                                'date' => $dateParam,
                                                                'folder' => $file->checked_folder,
                                                                'filename' => $file->file_name
                                                            ]) }}" target="_blank">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </span>

                                                        <span class="download">
                                                            <a href="{{ route('downloadCheck', [
                                                                'date' => $dateParam,
                                                                'filename' => $file->file_name,
                                                                'folder' => $file->checked_folder
                                                            ]) }}">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        </span>

                                                        </td>
                                                            </tr>


                                                        @if (!$Admin)
                                                        <tr wire:key="viral-uploaded-{{ $file->id }}">
                                                        <livewire:viral-upload
                                                                    wire:key="viral-upload-{{ $file->id }}"
                                                                    :fileCode="$file->file_code"
                                                                    :viralUpload="$file->viral_upload"
                                                                    :webLink="$file->web_link"
                                                                    :viralLink="$file->viral_link"
                                                                    :fileName="$file->checked_file_name"
                                                                    :title="$file->title"
                                                                    :id="$file->id"
                                                                    :viral="$file->viral"
                                                    />
                                                    </tr>

                                                        @endif

    <!-- Question Toggle and Content -->











                                                    @endforeach

                                    </tbody>

                                </table>


                            </div>

                        </div>
                    </div>

                                @endif

                        </div>
                    </div>

                        @endif
                    @endforeach

                </div>
            </div>


        </div>



        <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.6.0/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
   <script>
   function showLoader() {
    document.getElementById('loader').style.display = 'block';
}

function hideLoader() {
    document.getElementById('loader').style.display = 'none';
}

let selectedMonthsArray = [];
let startDateFilled = false;
let endDateFilled = false;
// Initialize selectedMonthsArray from the URL when the page loads
window.addEventListener('load', () => {
    const urlParams = new URLSearchParams(window.location.search);
    const selectedMonths = urlParams.get('selectedMonths');
    if (selectedMonths) {
        selectedMonthsArray = selectedMonths.split(','); // Populate selectedMonthsArray with values from URL
    }

    // Ensure that the checkboxes are correctly checked based on selectedMonthsArray
    document.querySelectorAll('input[name="selectedMonths[]"]').forEach(checkbox => {
        if (selectedMonthsArray.includes(checkbox.value)) {
            checkbox.checked = true; // Check the checkbox if its value is in selectedMonthsArray
        }
    });

    hideLoader(); // Hide the loader when the page has finished loading
});
// Function to update the URL
function updateUrl(reload = false) {
    showLoader(); // Show the loader

    const url = new URL(window.location.href); // Get current URL
    const params = url.searchParams; // Get query parameters

    // Get filter values from the form
    const selectedMujeeb = document.getElementById('mujeebframe').value;
    const selectedchecked = document.getElementById('checked').value;
    let selectedTimeFrame = document.getElementById('timeframe').value;
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    // If both start date and end date are filled, set selectedTimeFrame to 'custom'
    if (startDateFilled && endDateFilled) {
        selectedTimeFrame = 'custom'; // Update timeframe to custom
        document.getElementById('timeframe').value = 'custom'; // Update select input
    }

    // Update URL with the selected filters
    params.set('selectedmujeeb', selectedMujeeb);
    params.set('selectedTimeFrame', selectedTimeFrame);
    params.set('selectedchecked', selectedchecked);

    // Handle start and end dates
    if (selectedTimeFrame === 'custom') {
        if (startDate) {
            params.set('startDate', startDate);
        }
        if (endDate) {
            params.set('endDate', endDate);
        }
    } else {
        // Remove start and end dates if time frame is not 'custom'
        params.delete('startDate');
        params.delete('endDate');
    }

    // Handle months only if the time frame is "other"
    if (selectedTimeFrame === 'other' && selectedMonthsArray.length > 0) {
        params.set('selectedMonths', selectedMonthsArray.join(','));
    } else if (selectedTimeFrame !== 'other') {
        // Only remove selectedMonths if the timeframe is not "other"
        params.delete('selectedMonths');
    }

    // Handle checkbox states for showDetail and showChat


    // Build the new URL with the updated query parameters
    const newUrl = `${url.pathname}?${params.toString()}`;

    // Update the URL in the address bar
    window.history.replaceState({}, '', newUrl);

    // If reload is true, reload the page with the new URL
    if (reload) {
        window.location.href = newUrl;
    } else {
        hideLoader(); // Hide the loader if no reload is triggered
    }
}

// Function to check if both start and end dates are filled and trigger URL update
function checkDatesAndUpdateUrl() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    startDateFilled = !!startDate; // Check if start date is filled
    endDateFilled = !!endDate; // Check if end date is filled

    if (startDateFilled && endDateFilled) {
        updateUrl(true); // Update the URL once both dates are filled
    }
}

// Add event listeners to all form elements to trigger the updateUrl function
document.getElementById('mujeebframe').addEventListener('change', () => updateUrl(true));
document.getElementById('timeframe').addEventListener('change', () => updateUrl(true));
document.getElementById('checked').addEventListener('change', () => updateUrl(true));

// Update URL only after both start date and end date are filled
document.getElementById('start_date').addEventListener('change', checkDatesAndUpdateUrl);
document.getElementById('end_date').addEventListener('change', checkDatesAndUpdateUrl);

// Update selectedMonthsArray based on checkbox state
document.querySelectorAll('input[name="selectedMonths[]"]').forEach(checkbox => {
    checkbox.addEventListener('change', (e) => {
        const value = e.target.value;
        if (e.target.checked) {
            // Add the month to the array
            if (!selectedMonthsArray.includes(value)) {
                selectedMonthsArray.push(value);
            }
        } else {
            // Remove the month from the array
            selectedMonthsArray = selectedMonthsArray.filter(month => month !== value);
        }
        updateUrl(); // Update URL whenever the checkbox is checked or unchecked
    });
});

// Add event listeners to checkboxes for showDetail and showChat

// Add event listener for the "Filter By Date" button
const filterButton = document.querySelector('.apply-filters-button');
if (filterButton) {
    filterButton.addEventListener('click', (e) => {
        e.preventDefault(); // Prevent form submission
        updateUrl(true); // Update the URL and reload the page
    });
}

// Show the loader when the page loads
window.addEventListener('load', showLoader);

// Hide the loader when the page has finished loading
window.addEventListener('load', hideLoader);

 // Sample data for months and years, replace this with $datefilter from your backend
 const dateFilter = @json($monthfilter);
const selectedMonth = "{{ request('selectedMonths', '') }}"; // e.g., "2024-04"
let currentIndex = 0;
const monthsPerPage = 6;

// Function to determine initial position of selected month
function setInitialIndex() {
    const selectedMonthIndex = dateFilter.findIndex(data => {
        const monthYearValue = `${data.year}-${String(data.month).padStart(2, '0')}`;
        return monthYearValue === selectedMonth;
    });

    // Adjust currentIndex only if the selected month is not in the initial view
    if (selectedMonthIndex !== -1 && (selectedMonthIndex < currentIndex || selectedMonthIndex >= currentIndex + monthsPerPage)) {
        currentIndex = Math.floor(selectedMonthIndex / monthsPerPage) * monthsPerPage;
    }
}

function renderMonths() {
    const monthsRow = document.getElementById('months-row');
    monthsRow.innerHTML = '';

    const visibleMonths = dateFilter.slice(currentIndex, currentIndex + monthsPerPage);

    visibleMonths.forEach(data => {
        const monthName = new Date(data.year, data.month - 1).toLocaleString('default', { month: 'long' });
        const monthYearValue = `${data.year}-${String(data.month).padStart(2, '0')}`; // e.g., "2024-04"
        const isSelected = monthYearValue === selectedMonth;

        console.log(`MonthYearValue: ${monthYearValue}, SelectedMonth: ${selectedMonth}, IsSelected: ${isSelected}`);

        // Create the clickable month-year box with count
        const monthBox = document.createElement('div');
        monthBox.classList.add('month-year', 'p-2', 'm-2', 'border', 'rounded', 'text-center');
        monthBox.style.cursor = 'pointer';

        // Apply highlight class if the month is selected
        if (isSelected) {
            monthBox.classList.add('highlight');
            console.log(`Applied highlight class to ${monthYearValue}`);
        }

        monthBox.innerHTML = `<strong>${monthName} ${data.year}</strong><br><span>(${data.count})</span>`;

        // Update URL with selectedMonths and selectedTimeFrame parameters on click
        monthBox.onclick = () => {
            const url = new URL(window.location.href);
            url.searchParams.set('selectedMonths', monthYearValue);
            url.searchParams.set('selectedTimeFrame', 'other'); // Add selectedTimeFrame parameter with value 'other'
            window.location.href = url.toString();
        };

        monthsRow.appendChild(monthBox);
    });

    document.getElementById('back-button').style.display = currentIndex > 0 ? 'inline-block' : 'none';
    document.getElementById('next-button').style.display = currentIndex + monthsPerPage < dateFilter.length ? 'inline-block' : 'none';
}

function showNextMonths() {
    if (currentIndex + monthsPerPage < dateFilter.length) {
        currentIndex += monthsPerPage;
        renderMonths();
    }
}

function showPreviousMonths() {
    if (currentIndex - monthsPerPage >= 0) {
        currentIndex -= monthsPerPage;
        renderMonths();
    }
}

// Set initial index based on selected month
setInitialIndex();

// Initial render
renderMonths();

   </script>
   <div wire:ignore>
           <x-layout bodyClass="g-sidenav-show  bg-gray-200">
           @php

    $ViralNazim = Auth::check() && in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray());

@endphp
@if ($ViralNazim)
        <x-navbars.vsidebar activePage="{{ request()->route('darulifta') ?? 'viral-fatawa' }}"></x-navbars.vsidebar>
@else
<x-navbars.sidebar activePage="{{ request()->route('darulifta') ?? 'viral-fatawa' }}"></x-navbars.sidebar>
@endif
        </x-layout>
</div>
    <x-footers.auth></x-footers.auth>

</main>




</div>

