<?php

namespace App\Livewire;

use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Sidebar extends Component
{
    public $mahl_e_mujeeb;
    public $daruliftaNames;
    public $mahlenazar_mujeeb;
    public $activePage = ''; // Set a default value

    public function mount($activePage,$mahl_e_mujeeb)
    {
        // Your logic to fetch data and assign it to properties
        $this->activePage = $activePage;
        $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name', 'NOT LIKE', '%3btn%')
            ->distinct()
            ->orderBy(DB::raw('
                CASE 
                    WHEN darulifta_name = "Noorulirfan" THEN 1
                    WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
                    WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
                    WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
                    ELSE 5 -- Adjust as needed
                END
            '))
            ->pluck('darulifta_name');

        $this->mahlenazar_mujeeb = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->distinct()
            ->pluck('u1.sender');

        $this->mahl_e_mujeeb = [];

        $uniquemahle = DB::table('uploaded_files as u1')
            ->where('u1.checked_folder', 'Mahl-e-Nazar')
            ->leftJoin('uploaded_files as u2', function ($join) {
                $join->on('u1.file_code', '=', 'u2.file_code')
                    ->where(function ($query) {
                        $query->where('u2.checked_folder', 'ok')
                            ->orWhere('u2.checked_folder', 'Tahqiqi');
                    });
            })
            ->whereNull('u2.file_code')
            ->select('u1.file_code', DB::raw('MAX(u1.id) AS id'))
            ->groupBy('u1.file_code')
            ->get();

        foreach ($this->mahlenazar_mujeeb as $sender) {
            foreach ($this->daruliftaNames as $daruliftaName) {
                $m_d_mujeeeb = DB::table('uploaded_files as u1')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('sender', $sender)
                    ->select('darulifta_name', 'sender')
                    ->distinct()
                    ->orderBy(DB::raw('
                        CASE 
                            WHEN darulifta_name = "Noorulirfan" THEN 1
                            WHEN darulifta_name = "Faizan-e-Ajmair" THEN 2
                            WHEN darulifta_name = "Gulzar-e-Taiba" THEN 3
                            WHEN darulifta_name = "Markaz-ul-Iqtisaad" THEN 4
                            ELSE 5 -- Adjust as needed
                        END
                    '))
                    ->get();

                if ($m_d_mujeeeb->isNotEmpty()) {
                    $this->mahl_e_mujeeb[$daruliftaName][$sender] = $m_d_mujeeeb;
                }
            }
        }
    }

    public function render()
    {
        return view('livewire.sidebar', ['mahl_e_mujeeb' => $this->mahl_e_mujeeb]);
        
    }
    public function updatedActivePage($value)
    {
        // Handle updates to the activePage property, if needed
    }
}