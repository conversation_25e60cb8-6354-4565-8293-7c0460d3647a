<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - Mahl-e-Nazar Limit</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .blur-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.3);
            z-index: 1000;
        }
        
        .access-denied-container {
            position: relative;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .access-denied-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon-container {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            width: 100px;
            height: 100px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
        }
        
        .icon-container i {
            font-size: 3rem;
            color: white;
        }
        
        .title {
            color: #2c3e50;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .stats-container {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            border-left: 5px solid #ff6b6b;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .stat-item:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }
        
        .stat-label {
            font-weight: 600;
            color: #495057;
        }
        
        .stat-value {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .limit-exceeded {
            color: #dc3545;
        }
        
        .limit-warning {
            color: #fd7e14;
        }
        
        .limit-normal {
            color: #28a745;
        }
        
        .action-buttons {
            margin-top: 30px;
        }
        
        .btn-custom {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 0 10px;
            display: inline-block;
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-secondary-custom {
            background: transparent;
            color: #6c757d;
            border: 2px solid #6c757d;
        }
        
        .btn-secondary-custom:hover {
            background: #6c757d;
            color: white;
            transform: translateY(-2px);
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }
        
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        
        @keyframes floating {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <div class="blur-overlay"></div>
    
    <div class="access-denied-container">
        <div class="access-denied-card floating">
            <div class="icon-container pulse">
                <i class="fas fa-ban"></i>
            </div>
            
            <h1 class="title">Access Restricted</h1>
            <p class="subtitle"><?php echo e($message); ?></p>
            
            <div class="stats-container">
                <div class="stat-item">
                    <span class="stat-label">
                        <i class="fas fa-file-alt me-2"></i>
                        Your Current Fatawa Count
                    </span>
                    <span class="stat-value <?php echo e($count >= $limit ? 'limit-exceeded' : ($count >= 10 ? 'limit-warning' : 'limit-normal')); ?>">
                        <?php echo e($count); ?>

                    </span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">
                        <i class="fas fa-limit me-2"></i>
                        Maximum Allowed Limit
                    </span>
                    <span class="stat-value"><?php echo e($limit); ?></span>
                </div>
                
                <?php if($count < $limit): ?>
                <div class="stat-item">
                    <span class="stat-label">
                        <i class="fas fa-clock me-2"></i>
                        Remaining Submissions
                    </span>
                    <span class="stat-value limit-normal"><?php echo e($limit - $count); ?></span>
                </div>
                <?php endif; ?>
                
                <div class="stat-item">
                    <span class="stat-label">
                        <i class="fas fa-user me-2"></i>
                        User
                    </span>
                    <span class="stat-value"><?php echo e($user->name); ?></span>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="<?php echo e(route('dashboard')); ?>" class="btn-custom btn-primary-custom">
                    <i class="fas fa-home me-2"></i>
                    Go to Dashboard
                </a>
                
                <a href="<?php echo e(route('mahlenazar-fatawa')); ?>" class="btn-custom btn-secondary-custom">
                    <i class="fas fa-list me-2"></i>
                    View Mahl-e-Nazar
                </a>
            </div>
            
            <div class="mt-4">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Contact your Nazim or Admin if you believe this is an error.
                </small>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/mahl-e-nazar-access-denied.blade.php ENDPATH**/ ?>