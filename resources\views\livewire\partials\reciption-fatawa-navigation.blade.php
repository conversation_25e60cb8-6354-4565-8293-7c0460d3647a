<!-- Navigation Section (Admin Only) -->
@php
    $Admin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
@endphp

@if ($Admin)
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <h6 class="mb-0">
                <i class="fas fa-sitemap me-2"></i>
                Darulifta Navigation
            </h6>
        </div>
        <div class="modern-card-body">
            <div class="d-flex flex-wrap gap-2">
                @php
                    $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                    $isAllIftaActive = request()->route('darulifta') == null;
                @endphp
                
                <!-- All Ifta Button -->
                <a href="{{ route('reciption-fatawa', [
                    'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                    'selectedexclude' => $this->selectedexclude,
                    'startDate' => $tempStartDate,
                    'endDate' => $tempEndDate,
                    'showDetail' => $showDetail ? '1' : '0',
                    'showChat' => $showChat ? '1' : '0',
                    'selectedMonths' => implode(',', $this->selectedMonths),
                ]) }}" class="btn {{ $isAllIftaActive ? 'btn-success' : 'btn-outline-primary' }} modern-nav-btn">
                    <i class="fas fa-home me-1"></i>
                    All Ifta
                </a>

                <!-- Individual Darulifta Buttons -->
                @foreach($daruliftalist as $daruliftalistn)
                    @php
                        $isActive = request()->route('darulifta') == $daruliftalistn;
                    @endphp
                    
                    <a href="{{ route('reciption-fatawa', [
                        'darulifta' => $daruliftalistn,
                        'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                        'selectedexclude' => $this->selectedexclude,
                        'startDate' => $tempStartDate,
                        'endDate' => $tempEndDate,
                        'showDetail' => $showDetail ? '1' : '0',
                        'showChat' => $showChat ? '1' : '0',
                        'selectedMonths' => implode(',', $this->selectedMonths),
                    ]) }}" class="btn {{ $isActive ? 'btn-success' : 'btn-outline-primary' }} modern-nav-btn">
                        <i class="fas fa-building me-1"></i>
                        {{ $daruliftalistn }}
                    </a>
                @endforeach
            </div>
        </div>
    </div>
@endif
