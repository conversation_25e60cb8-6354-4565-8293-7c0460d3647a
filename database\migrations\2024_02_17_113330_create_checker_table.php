<?php 

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('checker', function (Blueprint $table) {
            $table->id();
            $table->string('checker_name');
            $table->string('darul_name');
            $table->string('munsab');
            $table->string('folder_id')->nullable(); // Change folder_id to string
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('checker');
    }
};
