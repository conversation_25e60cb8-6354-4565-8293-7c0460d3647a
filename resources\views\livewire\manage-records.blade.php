
<div>
<style>
    .card {
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 15px;
        margin: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .columns {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }
    .column {
        flex: 1 1 calc(33.33% - 20px);
        display: flex;
        flex-direction: column;
    }
    .card h3, .card h4 {
        margin: 10px 0;
    }
#childNameEditor {
        width: 100%;
        height: 300px; /* Adjust this value for height constraint */
        resize: none; /* Prevent resizing for consistent appearance */
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 10px;
        font-size: 14px;
        line-height: 1.6; /* Helps achieve word count with proper spacing */
        box-sizing: border-box; /* Include padding and border in size */
    }
</style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
@livewire('navbar', ['titlePage' => 'Viral Fatawa'])
    <h1>Manage Records</h1>

    <!-- Headers Input Section -->
    <div style="display: flex; gap: 20px;">
    <!-- Header Input Section -->
    <div style="flex: 1;">
        <h2>Header</h2>
        <form wire:submit.prevent="saveHeader">
            <input type="text" wire:model="headerName" placeholder="Header Name">
            <input type="number" wire:model="headerSerialNumber" placeholder="Serial Number">
            <button type="submit">{{ $editingHeader ? 'Update' : 'Add' }} Header</button>
        </form>
    </div>

    <!-- Parent Input Section -->
    <div style="flex: 1;">
        <h2>Parent</h2>
        <form wire:submit.prevent="saveParent">
            <input type="text" wire:model="parentName" placeholder="Parent Name">
            <input type="number" wire:model="parentSerialNumber" placeholder="Serial Number">
            <select wire:model="parentHeaderId">
                <option value="">Select Header</option>
                @foreach($headers as $headerOption)
                    <option value="{{ $headerOption->id }}">{{ $headerOption->name }}</option>
                @endforeach
            </select>
            <button type="submit">{{ $editingParent ? 'Update' : 'Add' }} Parent</button>
        </form>
    </div>

    <!-- Child Input Section -->
    <div style="flex: 2;">
    <h2>Child</h2>
    <form wire:submit.prevent="saveChild">
        <!-- Bind CKEditor with Livewire -->
        <textarea id="childName" data-note="@this" placeholder="Child Name"></textarea>
        <input type="number" wire:model="childSerialNumber" placeholder="Serial Number">
        <select wire:model="childParentId">
            <option value="">Select Parent</option>
            @foreach($parents as $parentOption)
                <option value="{{ $parentOption->id }}">{{ $parentOption->name }}</option>
            @endforeach
        </select>
        <button type="submit">{{ $editingChild ? 'Update' : 'Add' }} Child</button>
    </form>
</div>
</div>
<!-- Data Display Section -->
<h2>Records</h2>
<div class="columns" id="headers-container">
    @foreach($headers->chunk(ceil($headers->count() / 3)) as $headerChunk)
        <div class="column">
            @foreach($headerChunk as $header)
                <div
                    class="card draggable"
                    data-id="{{ $header->id }}"
                    draggable="true"
                >
                    <h2>Header: {{ $header->name }}</h2>
                    <p>Serial Number: {{ $header->serial_number }}</p>
                    <button wire:click="editHeader({{ $header->id }})">Edit</button>
                    <button wire:click="deleteHeader({{ $header->id }})">Delete</button>

                    <!-- Display Parents -->
                    <h3>Parents</h3>
                    @foreach($header->parents as $parent)
                        <div class="card">
                            <h4>Parent: {{ $parent->name }}</h4>
                            <p>Serial Number: {{ $parent->serial_number }}</p>
                            <button wire:click="editParent({{ $parent->id }})">Edit</button>
                            <button wire:click="deleteParent({{ $parent->id }})">Delete</button>

                            <!-- Display Children -->
                            <h4>Children</h4>
                            @foreach($parent->children as $child)
                                <div class="card">
                                    <p>Child: {{ $child->name }}</p>
                                    <p>Serial Number: {{ $child->serial_number }}</p>
                                    <button wire:click="editChild({{ $child->id }})">Edit</button>
                                    <button wire:click="deleteChild({{ $child->id }})">Delete</button>
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                </div>
            @endforeach
        </div>
    @endforeach
</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const container = document.getElementById('headers-container');
        let draggedItem = null;

        // Drag start
        container.addEventListener('dragstart', (e) => {
            draggedItem = e.target.closest('.draggable');
            e.target.style.opacity = '0.5';
        });

        // Drag end
        container.addEventListener('dragend', (e) => {
            e.target.style.opacity = '1';
        });

        // Drag over
        container.addEventListener('dragover', (e) => {
    e.preventDefault();
    const draggedElement = e.target.closest('.draggable');

    // Ensure draggedElement is not null and part of the same container
    if (!draggedElement || !container.contains(draggedItem)) {
        return;
    }

    const afterElement = getDragAfterElement(container, e.clientY);
    if (afterElement == null) {
        container.appendChild(draggedItem);
    } else {
        container.insertBefore(draggedItem, afterElement);
    }
});

        // Handle drop
        container.addEventListener('drop', () => {
    const idsInOrder = Array.from(container.querySelectorAll('.draggable'))
        .map((item) => item.dataset.id);

    console.log('Reordered IDs:', idsInOrder); // Debug the ID sequence
    window.Livewire.dispatch('updateHeaderOrder', { ids: idsInOrder });
});

function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.draggable')];

    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}
    });
</script>


<div wire:ignore>
           <x-layout bodyClass="g-sidenav-show  bg-gray-200">

        </x-layout>
</div>
    <x-footers.auth></x-footers.auth>

</main>
</div>
