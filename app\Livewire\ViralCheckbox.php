<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ViralCheckbox extends Component
{
    public $fileId;
    public $isViral;

    public function mount($fileId, $isViral)
    {
        $this->fileId = $fileId;
        $this->isViral = $isViral;
    }

    public function toggleViral()
    {
        $file = DB::table('uploaded_files')->where('id', $this->fileId)->first();

        if ($file) {
            $currentUserId = Auth::id();
            // Toggle the viral value
            $newViralValue = $file->viral == $currentUserId ? 0 : $currentUserId;

            DB::table('uploaded_files')
                ->where('id', $this->fileId)
                ->update([
                    'viral' => $newViralValue,
                    'updated_at' => now(),
                ]);

            // Update the local property
            $this->isViral = $newViralValue != 0;

            // Emit an event for the parent component
            $this->dispatch('viralUpdated');
        }
    }

    public function render()
    {
        // Refresh the isViral property with the latest database value
        $file = DB::table('uploaded_files')->where('id', $this->fileId)->first();
        $this->isViral = $file && $file->viral != 0;

        return view('livewire.viral-checkbox');
    }
}
