<?php $__env->startSection('content'); ?>
<div class="main-content position-relative max-height-vh-100 h-100">
    <div class="container-fluid">
        <div class="page-header min-height-300 border-radius-xl mt-4" 
             style="background-image: url('<?php echo e(asset('assets/img/curved-images/curved0.jpg')); ?>'); background-position-y: 50%;">
            <span class="mask bg-gradient-primary opacity-6"></span>
        </div>
        
        <div class="card card-body blur shadow-blur mx-4 mt-n6 overflow-hidden">
            <div class="row gx-4">
                <div class="col-auto">
                    <div class="avatar avatar-xl position-relative">
                        <div class="avatar avatar-xl position-relative">
                            <div class="w-100 h-100 rounded-circle bg-gradient-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-shield-alt text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-auto my-auto">
                    <div class="h-100">
                        <h5 class="mb-1">Mahl-e-Nazar Limit Management</h5>
                        <p class="mb-0 font-weight-bold text-sm">Monitor and control 12 Fatawa forwarding limits</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mahl-e-Nazar Management Component -->
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('mahl-e-nazar-management', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1761581378-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
</div>

<style>
    .main-content {
        margin-left: 250px;
    }

    @media (max-width: 991.98px) {
        .main-content {
            margin-left: 0;
        }
    }

    /* Modern styling for Mahl-e-Nazar management */
    .card {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: none;
        border-radius: 0.75rem;
    }

    .btn {
        border-radius: 0.5rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .table th {
        border-bottom: 1px solid #e9ecef;
        font-weight: 600;
        color: #6c757d;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table td {
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
    }

    .table-warning {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .badge {
        font-weight: 500;
        border-radius: 0.5rem;
    }

    .form-control, .form-select {
        border-radius: 0.5rem;
        border: 1px solid #d1d5db;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .alert {
        border-radius: 0.75rem;
        border: none;
    }

    /* Progress bar styling */
    .progress {
        height: 8px;
        border-radius: 4px;
        background-color: #e9ecef;
    }

    .progress-bar {
        border-radius: 4px;
        transition: width 0.6s ease;
    }

    .progress-wrapper {
        position: relative;
    }

    .progress-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.25rem;
    }

    .progress-percentage {
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* Statistics cards styling */
    .icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem;
    }

    .icon i {
        font-size: 1.25rem;
    }

    .numbers h5 {
        font-size: 1.75rem;
        font-weight: 700;
        line-height: 1.2;
    }

    .numbers p {
        font-size: 0.875rem;
        margin-bottom: 0;
        color: #6c757d;
    }

    /* Status colors */
    .bg-gradient-success {
        background: linear-gradient(87deg, #2dce89 0, #2dcecc 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(87deg, #fb6340 0, #fbb140 100%);
    }

    .bg-gradient-danger {
        background: linear-gradient(87deg, #f5365c 0, #f56036 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(87deg, #11cdef 0, #1171ef 100%);
    }

    .bg-gradient-primary {
        background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(87deg, #8392ab 0, #96a2b2 100%);
    }

    /* Modal styling */
    .modal-content {
        border-radius: 1rem;
        border: none;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    .modal-header {
        border-bottom: 1px solid #f1f3f4;
        border-radius: 1rem 1rem 0 0;
    }

    .modal-footer {
        border-top: 1px solid #f1f3f4;
        border-radius: 0 0 1rem 1rem;
    }

    .info-item {
        padding: 0.5rem 0;
    }

    .info-item small {
        display: block;
        margin-bottom: 0.25rem;
    }

    /* Limit status indicators */
    .limit-normal {
        color: #2dce89;
    }

    .limit-near {
        color: #11cdef;
    }

    .limit-at {
        color: #fb6340;
    }

    .limit-over {
        color: #f5365c;
    }

    /* Action buttons */
    .btn-outline-info:hover {
        background-color: #11cdef;
        border-color: #11cdef;
    }

    .btn-outline-success:hover {
        background-color: #2dce89;
        border-color: #2dce89;
    }

    .btn-outline-danger:hover {
        background-color: #f5365c;
        border-color: #f5365c;
    }

    /* Health indicator */
    .health-excellent {
        color: #2dce89;
    }

    .health-good {
        color: #11cdef;
    }

    .health-warning {
        color: #fb6340;
    }

    .health-critical {
        color: #f5365c;
    }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user_type.auth', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/mahl-e-nazar-limit/index.blade.php ENDPATH**/ ?>