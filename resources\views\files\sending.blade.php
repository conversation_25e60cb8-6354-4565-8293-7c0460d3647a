<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Recived Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- Create Appointment Entry Form -->
        <style>
                        .year {
                background-color: #7971ec; /* Year section color */
            }

            .month {
                background-color: #96c47d; /* Month section color */
            }

            .date {
                background-color: #c8d053; /* Date section color */
            }

            .file {
                background-color: #c6d2f4; /* Date section color */
            }
            .download-button {
                display: inline-block;
                padding: 10px 20px; /* Adjust the padding as needed */
                background-color: #007bff; /* Button background color */
                color: #fff; /* Text color */
                text-decoration: none;
                border: none;
                border-radius: 5px; /* Rounded corners */
                cursor: pointer;
            }

            .download-button:hover {
                background-color: #0056b3; /* Change color on hover */
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
        </style>
        {{-- <a href="{{ route('summary') }}" class="btn btn-primary" style="background-color: lightgray;">Summary</a> --}}
        <a href="{{ route('recived') }}" class="btn btn-success" style="background-color: lightgray;">Recived Fatawa</a>
        {{-- <a href="{{ route('sending') }}" class="btn btn-success" style="background-color: rgb(115, 150, 110);">Sending Fatwaw</a> --}}


        <h1>Recived Fatwa Form Darulifta</h1>
        {{-- <form action="{{ route('fetch-data') }}" method="GET"> --}}
            <form>
            @csrf
            <select id="darulifta-filter">
                <option value="">Select Darulifta Name</option>
                @foreach($uniqueDaruliftaNames as $name)
                    <option value="{{ $name }}">{{ $name }}</option>
                @endforeach
            </select>
            <select id="mail-folder-date-filter">
                <option value="">Select Mail Folder Date</option>
                @foreach($uniqueMailFolderDates as $date)
                    <option value="{{ $date }}">{{ $date }}</option>
                @endforeach
            </select>
            
            {{-- <button type="submit">Filter</button> --}}
            <a href="{{ route('downloadAll', ['date' => $date]) }}" class="download-button">
                <span class="button-text">Download Mail Folder</span>
            </a>
        </form>
        <form action="{{ route('files.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            <input type="file" class="form-control" id="ok1" name="files[]" multiple data-label="ok">
            <input type="file" class="form-control" id="mahl-e-nazar" name="files[]" multiple data-label="Mahl-e-Nazar">
            <input type="file" class="form-control" id="tahqiqi" name="files[]" multiple data-label="Tahqiqi">
            {{-- <div class="form-group">
                <label for="ok1">ok</label>
                <input type="file" class="form-control" id="ok1" name="files[]" multiple data-label="ok" required>
            </div>
            <div class="form-group">
                <label for="mahl-e-nazar">Mahl-e-Nazar</label>
                <input type="file" class="form-control" id="mahl-e-nazar" name="files[]" multiple data-label="Mahl-e-Nazar" required>
            </div>
            <div class="form-group">
                <label for="tahqiqi">Tahqiqi</label>
                <input type="file" class="form-control" id="tahqiqi" name="files[]" multiple data-label="Tahqiqi" required>
            </div> --}}
        </form>
        
        
        <!-- Display filtered data -->
        <table id="table1" border="1">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>File Name</th>
                    <th>Sender</th>
                    <th>Checked File Name</th>
                    <th>Checked Folder Name</th>
                    <th>Checked Grade</th> <!-- Additional column -->
                    <th>Checked Tasurat</th> <!-- Additional column -->
                    <th>Selected</th>
                </tr>
            </thead>
            <tbody>
                @foreach($data as $row)
                
                <tr>
                        <td>{{ $row->id }}</td>
                        <td>{{ $row->file_name }}</td>
                        <td>{{ $row->sender }}</td>
                        <td>{{ $row->darulifta_name }}</td>
                        <td>{{ $row->mail_folder_date }}</td>
                        <!-- Add other table columns as needed -->
                <td><input type="text" name="checked_file_name" data-id="{{ $row->id }}"></td>
                <td><input type="text" name="checked_folder[]" data-id="{{ $row->id }}"></td>
                <td><input type="text" name="checked_grade[]" data-id="{{ $row->id }}"></td>
                <td><input type="text" name="checked_tasurat[]" data-id="{{ $row->id }}"></td>
                <td><input type="checkbox" name="selected[]" data-id="{{ $row->id }}" onchange="updateCheckboxValue(this)"></td>
                <input type="hidden" name="row_id[]" value="{{ $row->id }}">
                    </tr>
                @endforeach
            </tbody>
        </table>
        
        <!-- Add a new table to display file details -->
        <table id="table2" border="1">
            <thead>
                <tr>
                    
                    <th>Checked File Name</th>
                    <th>Checked Folder Name</th>
                    <th>Selected</th>
                    <th>ID</th>
                </tr>
            </thead>
            <tbody id="file-details">
                <!-- File details will be added here dynamically -->
            </tbody>
        </table>
        
<!-- File input elements -->
{{-- <input type="file" class="form-control" id="fileInput" multiple data-label="File Label" required> --}}

    
        <!-- JavaScript for filtering data without a button click -->
        <script>
  // Function to filter table data based on darulifta_name and mail_folder_date
  function filterTableData() {
        var daruliftaName = document.getElementById('darulifta-filter').value;
        var mailFolderDate = document.getElementById('mail-folder-date-filter').value;

        var rows = document.querySelectorAll('#table1 tbody tr');
        rows.forEach(function (row) {
            var rowDaruliftaName = row.querySelector('td:nth-child(4)').textContent;
            var rowMailFolderDate = row.querySelector('td:nth-child(5)').textContent;

            if (
                (mailFolderDate !== '' && rowMailFolderDate === mailFolderDate) && // Check mail_folder_date filter
                (daruliftaName === '' || rowDaruliftaName === daruliftaName) // Check darulifta_name filter
            ) {
                row.style.display = 'table-row';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // Listen for changes in the dropdowns and trigger the data fetch
    var daruliftaName = document.getElementById('darulifta-filter').value;
    // Get the selected value from the mail-folder-date filter dropdown
var mailFolderDate = document.getElementById('mail-folder-date-filter').value;


document.getElementById('darulifta-filter').addEventListener('change', filterTableData);
document.getElementById('mail-folder-date-filter').addEventListener('change', filterTableData);

    // Hide all rows initially
    var rows = document.querySelectorAll('#table1 tbody tr');
    rows.forEach(function (row) {
        row.style.display = 'none';
    });
            function updateCheckboxValue(checkbox) {
    var hiddenInput = checkbox.parentElement.querySelector('input[type="hidden"]');
    hiddenInput.value = checkbox.checked ? "1" : "0";
}
     document.addEventListener('DOMContentLoaded', function () {
    // Function to add file details to the table
    function addFileDetails(file, label) {
        var tr = document.createElement('tr');

        // Display the file name in the "Checked File Name" column
        var tdCheckedFileName = document.createElement('td');
        tdCheckedFileName.textContent = file.name;
        tr.appendChild(tdCheckedFileName);

        // Display the label in the "Checked Folder Name" column
        var tdCheckedFolderName = document.createElement('td');
        tdCheckedFolderName.textContent = label;
        tr.appendChild(tdCheckedFolderName);

        // Find the corresponding ID from the first table
        var tdID = document.createElement('td');
        var correspondingID = findCorrespondingID(file.name);
        tdID.textContent = correspondingID;
        tr.appendChild(tdID);

        // Append the row to the table
        document.getElementById('file-details').appendChild(tr);

        // Now that files are in table2, trigger the function to populate table1
        populateTable1FromTable2();
    }

    // Function to find the corresponding ID in the first table
    function findCorrespondingID(fileName) {
        // Loop through the rows in the first table
        var firstTableRows = document.querySelectorAll('#table1 tbody tr');
        for (var i = 0; i < firstTableRows.length; i++) {
            var firstTableFileName = firstTableRows[i].querySelector('td:nth-child(2)').textContent;
            var firstTableID = firstTableRows[i].querySelector('td:nth-child(1)').textContent;

            // Check if the file name matches
            if (firstTableFileName === fileName) {
                return firstTableID;
            }
        }
        return 'Not Found'; // If no match is found
    }

    // Get all file input elements
    var fileInputs = document.querySelectorAll('input[type="file"]');

    // Listen for changes in the file inputs
    fileInputs.forEach(function (input) {
        input.addEventListener('change', function (e) {
            var files = e.target.files;
            var label = e.target.getAttribute('data-label');

            // Loop through selected files and add them to table2
            for (var i = 0; i < files.length; i++) {
                addFileDetails(files[i], label);
            }
        });
    });

    // Function to populate table1 from table2
    function populateTable1FromTable2() {
        // Get rows from both tables
        var table1Rows = document.querySelectorAll('#table1 tbody tr');
        var table2Rows = document.querySelectorAll('#table2 tbody tr');

        // Loop through table1 and table2 rows to populate data
        table1Rows.forEach(function (table1Row) {
            var table1FileName = table1Row.querySelector('td:nth-child(2)').textContent;

            table2Rows.forEach(function (table2Row) {
                var table2FileName = table2Row.querySelector('td:nth-child(1)').textContent;

                if (table1FileName === table2FileName) {
                    // Populate the corresponding columns in table1
                    table1Row.querySelector('input[name="checked_file_name"]').value = table2Row.querySelector('td:nth-child(1)').textContent;
                    table1Row.querySelector('input[name="checked_folder[]"]').value = table2Row.querySelector('td:nth-child(2)').textContent; // Added this line
                    // You can populate other fields in a similar manner if needed
                    var checkbox = table1Row.querySelector('input[name="selected[]"]');
                checkbox.checked = true;
                }
            });
        });
    }
    $.ajax({
    url: '{{ route("fetch-data") }}',
    type: 'GET',
    data: { daruliftaName: daruliftaName, mailFolderDate: mailFolderDate },
    success: function(data) {
        // Handle the received data
    },
    error: function(xhr, status, error) {
        // Handle errors
    }
});
});

        </script>
        
        
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
