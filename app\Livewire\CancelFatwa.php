<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CancelFatwa extends Component
{
    use WithPagination;

    public $search = '';
    public $fileCodeSearch = '';
    public $selectedFileCode = null;
    public $selectedFatwaDetails = null;
    public $successMessage = '';
    public $errorMessage = '';
    public $confirmingCancel = false;
    public $confirmingResume = false;
    public $confirmingDelete = false;
    public $selectedFileCodeForAction = null;
    public $count = 0;

    protected $queryString = [
        'search' => ['except' => '']
    ];

    public function mount()
    {
        // Check if user is admin
        if (!Auth::check() || !in_array('Admin', Auth::user()->roles->pluck('name')->toArray())) {
            return redirect()->route('dashboard');
        }
    }

    public function updatedFileCodeSearch()
    {
        $this->selectedFileCode = null;
        $this->selectedFatwaDetails = null;
    }

    public function selectFatwa($fileCode)
    {
        $this->selectedFileCode = $fileCode;
        $this->loadFatwaDetails();
        $this->fileCodeSearch = '';
    }

    public function loadFatwaDetails()
    {
        if ($this->selectedFileCode) {
            // Count how many fatwas will be affected
            $this->count = DB::table('uploaded_files')
                ->where('file_code', $this->selectedFileCode)
                ->where('selected', '!=', 3)
                ->count();
                
            // Get a representative fatwa for display
            $this->selectedFatwaDetails = DB::table('uploaded_files')
                ->where('file_code', $this->selectedFileCode)
                ->where('selected', '!=', 3)
                ->first();
        }
    }

    public function confirmCancel($fileCode)
    {
        $this->selectedFileCodeForAction = $fileCode;
        
        // Count how many fatwas will be affected
        $this->count = DB::table('uploaded_files')
            ->where('file_code', $fileCode)
            ->where('selected', '!=', 3)
            ->count();
            
        if ($this->count > 0) {
            $this->confirmingCancel = true;
        } else {
            $this->errorMessage = 'No active fatwas found with this file code.';
        }
    }

    public function cancelFatwa()
    {
        try {
            DB::beginTransaction();

            // Update all fatwas with the same file_code
            $affected = DB::table('uploaded_files')
                ->where('file_code', $this->selectedFileCodeForAction)
                ->where('selected', '!=', 3)
                ->update([
                    'previous_selected' => DB::raw('selected'), // Store current selected value
                    'selected' => 3,
                    'updated_at' => now()
                ]);

            DB::commit();

            $this->successMessage = "$affected fatwa(s) have been successfully canceled.";
            $this->confirmingCancel = false;
            $this->selectedFileCodeForAction = null;
            $this->selectedFileCode = null;
            $this->selectedFatwaDetails = null;
            $this->count = 0;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorMessage = 'Error canceling fatwa: ' . $e->getMessage();
        }
    }

    public function confirmResume($fileCode)
    {
        $this->selectedFileCodeForAction = $fileCode;
        
        // Count how many fatwas will be affected
        $this->count = DB::table('uploaded_files')
            ->where('file_code', $fileCode)
            ->where('selected', 3)
            ->whereNotNull('previous_selected')
            ->count();
            
        if ($this->count > 0) {
            $this->confirmingResume = true;
        } else {
            $this->errorMessage = 'No canceled fatwas found with this file code.';
        }
    }

    public function resumeFatwa()
    {
        try {
            DB::beginTransaction();

            // Restore all fatwas with the same file_code
            $affected = DB::table('uploaded_files')
                ->where('file_code', $this->selectedFileCodeForAction)
                ->where('selected', 3)
                ->whereNotNull('previous_selected')
                ->update([
                    'selected' => DB::raw('previous_selected'), // Restore from previous_selected
                    'previous_selected' => null,
                    'updated_at' => now()
                ]);

            DB::commit();

            $this->successMessage = "$affected fatwa(s) have been successfully resumed.";
            $this->confirmingResume = false;
            $this->selectedFileCodeForAction = null;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorMessage = 'Error resuming fatwa: ' . $e->getMessage();
        }
    }
    
    public function confirmDelete($fileCode)
    {
        $this->selectedFileCodeForAction = $fileCode;
        
        // Count how many fatwas will be affected
        $this->count = DB::table('uploaded_files')
            ->where('file_code', $fileCode)
            ->count();
            
        if ($this->count > 0) {
            $this->confirmingDelete = true;
        } else {
            $this->errorMessage = 'No fatwas found with this file code.';
        }
    }
    
    public function deleteFatwas()
    {
        try {
            DB::beginTransaction();
            
            // First get all file names to delete from storage
            $fatwas = DB::table('uploaded_files')
                ->where('file_code', $this->selectedFileCodeForAction)
                ->get();
                
            // Delete files from storage
            foreach ($fatwas as $fatwa) {
                if ($fatwa->file_name && Storage::exists('public/fatawa/' . $fatwa->file_name)) {
                    Storage::delete('public/fatawa/' . $fatwa->file_name);
                }
            }

            // Delete all fatwas with the same file_code from the database
            $affected = DB::table('uploaded_files')
                ->where('file_code', $this->selectedFileCodeForAction)
                ->delete();

            DB::commit();

            $this->successMessage = "$affected fatwa(s) have been permanently deleted.";
            $this->confirmingDelete = false;
            $this->selectedFileCodeForAction = null;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorMessage = 'Error deleting fatwas: ' . $e->getMessage();
        }
    }

    public function cancelAction()
    {
        $this->confirmingCancel = false;
        $this->confirmingResume = false;
        $this->confirmingDelete = false;
        $this->selectedFileCodeForAction = null;
    }

    public function getUniqueFileCodesProperty()
    {
        return DB::table('uploaded_files')
            ->select('file_code')
            ->where('selected', '!=', 3) // Only show active fatwas (not canceled)
            ->when($this->fileCodeSearch, function ($query) {
                return $query->where('file_code', 'like', '%' . $this->fileCodeSearch . '%');
            })
            ->orderBy('file_code')
            ->distinct()
            ->pluck('file_code')
            ->take(10)
            ->toArray();
    }

    public function getCanceledFileCodesProperty()
    {
        return DB::table('uploaded_files')
            ->select('file_code')
            ->where('selected', 3)
            ->whereNotNull('previous_selected')
            ->distinct()
            ->pluck('file_code')
            ->toArray();
    }
    
    public function downloadFile($date, $filename, $folder)
    {
        // Define the path to the file in the storage directory
        $filePath = 'public/' . $folder . '/' . $date . '/' . $filename;

        // Check if the file exists
        if (Storage::exists($filePath)) {
            // Download the file from storage
            return Storage::download($filePath, $filename);
        } else {
            // Handle the error if the file does not exist
            $this->errorMessage = "The file does not exist.";
        }
    }

    public function render()
    {
        // Get mahlenazar_null data for file details
        $mahlenazar_null = DB::table('uploaded_files')
            ->select('id', 'file_code', 'mail_folder_date', 'sender', 'file_name', 'darulifta_name', 'checker', 'by_mufti')
            ->where('selected', 3)
            ->get();
            
        // Query for canceled fatwas grouped by file_code
        $canceledFatwaCodes = DB::table('uploaded_files')
            ->select('file_code', DB::raw('MIN(id) as id'), 'sender', 'darulifta_name', 'previous_selected', DB::raw('COUNT(*) as count'))
            ->where('selected', 3)
            ->whereNotNull('previous_selected')
            ->when($this->search, function ($query) {
                return $query->where(function ($query) {
                    $query->where('file_code', 'like', '%' . $this->search . '%')
                        ->orWhere('sender', 'like', '%' . $this->search . '%')
                        ->orWhere('darulifta_name', 'like', '%' . $this->search . '%');
                });
            })
            ->groupBy('file_code', 'sender', 'darulifta_name', 'previous_selected')
            ->orderBy('file_code')
            ->paginate(10);

        return view('livewire.cancel-fatwa', [
            'canceledFatwas' => $canceledFatwaCodes,
            'uniqueFileCodes' => $this->uniqueFileCodes,
            'canceledFileCodes' => $this->canceledFileCodes,
            'mahlenazar_null' => $mahlenazar_null
        ])->layout('layouts.app');
    }
} 