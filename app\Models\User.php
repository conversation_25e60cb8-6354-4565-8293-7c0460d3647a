<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Contracts\Auth\CanResetPassword;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'location',
        'phone',
        'about',
        'password_confirmation'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];
    
    public function setPasswordAttribute($password)
    {
        $this->attributes['password'] = bcrypt($password);
    }
    public function roles(){
        return $this->belongsToMany(Role::class);
    }

    /**
     * Get the departments this user is assigned to.
     */
    public function departments()
    {
        return $this->belongsToMany(Department::class, 'user_departments')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get tasks assigned to this user.
     */
    public function assignedTasks()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    /**
     * Get tasks assigned by this user.
     */
    public function createdTasks()
    {
        return $this->hasMany(Task::class, 'assigned_by');
    }

    /**
     * Get daily performance records for this user.
     */
    public function dailyPerformance()
    {
        return $this->hasMany(DailyPerformance::class);
    }

    /**
     * Get active restrictions for this user.
     */
    public function activeRestrictions()
    {
        return $this->hasMany(UserRestriction::class)->where('is_active', true);
    }

    /**
     * Get all restrictions for this user.
     */
    public function restrictions()
    {
        return $this->hasMany(UserRestriction::class);
    }

    /**
     * Get assistants if this user is a supervisor.
     */
    public function assistants()
    {
        return $this->belongsToMany(User::class, 'supervisor_assistants', 'supervisor_id', 'assistant_id')
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get supervisor if this user is an assistant.
     */
    public function supervisor()
    {
        return $this->belongsToMany(User::class, 'supervisor_assistants', 'assistant_id', 'supervisor_id')
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps()
                    ->first();
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole($roleName)
    {
        return $this->roles->contains('name', $roleName);
    }

    /**
     * Check if user has any of the specified roles.
     */
    public function hasAnyRole($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }
        return $this->roles->whereIn('name', $roles)->isNotEmpty();
    }

    /**
     * Check if user is a Superior (Shoba Zimmedar).
     */
    public function isSuperior()
    {
        return $this->hasRole('Superior');
    }

    /**
     * Check if user is a Nazim (Admin).
     */
    public function isNazim()
    {
        return $this->hasRole('Nazim') || $this->hasRole('Admin');
    }

    /**
     * Check if user is a Mujeeb.
     */
    public function isMujeeb()
    {
        return $this->hasRole('mujeeb') || $this->hasRole('Muawin');
    }

    /**
     * Check if user has submitted today's performance.
     */
    public function hasSubmittedTodaysPerformance()
    {
        return DailyPerformance::isSubmittedForToday($this->id);
    }

    /**
     * Check if user has any active restrictions.
     */
    public function hasActiveRestrictions()
    {
        return UserRestriction::hasActiveRestrictions($this->id);
    }

    /**
     * Get user's Mahl-e-Nazar fatawa count.
     */
    public function getMahlENazarCount()
    {
        // This will be implemented based on the uploaded_files table structure
        return 0; // Placeholder
    }

}
