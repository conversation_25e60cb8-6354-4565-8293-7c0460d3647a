<div class="container">
    <div class="card">
            @php
        // Check if the user is authenticated before checking roles
        $Admin = Auth::check() && (
            in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
            in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
        );
        @endphp
        @if ($Admin)

        <div class="card-header d-flex justify-content-between">
            <button wire:click="previous" class="btn btn-secondary" @if(!$record) disabled @endif>Previous</button>
            <button wire:click="next" class="btn btn-secondary" @if(!$record) disabled @endif>Next</button>
        </div>
         @endif

        @if($record)
            <div class="card-body">
                <h5 class="card-title">Record ID: {{ $record->id }}</h5>
                <p><strong>Ifta Code:</strong> {{ $record->ifta_code }}</p>
                <p><strong>Rec Date:</strong> {{ $record->rec_date }}</p>
                <p><strong>Assign ID:</strong> {{ $record->assign_id }}</p>
                <p><strong>Content:</strong> {!! $record->content !!}</p>
                <p><strong>Created At:</strong> {{ $record->created_at }}</p>
                <p><strong>Updated At:</strong> {{ $record->updated_at }}</p>
            </div>
            @if ($Admin)
            <div class="add-comment">
    <textarea wire:model="muftiComment" class="form-control" placeholder="Add a comment"></textarea>
    <button wire:click="addComment" class="btn btn-primary mt-2">Add Comment</button>
</div>
<div class="button-group mt-3">
<a href="{{ route('talaq-fatwa-edit', $record->id) }}" class="btn btn-info">Edit</a>

    <button wire:click="markAsChecked('ok', {{ $record->id }})" class="btn btn-success">OK</button>
    <button wire:click="markAsChecked('mahl-e-nazar', {{ $record->id }})" class="btn btn-warning">Mahl-e-Nazar</button>
</div>
@endif

        @else
            <div class="card-body">
                <p>No record found.</p>
            </div>
        @endif
    </div>
    <style>
       .mufti-comment {
    font-family: 'Jameel Noori Nastaleeq';
    font-size: 16px;
    background-color: yellow;
    margin: 0;
}
       .sidebar-content {
                background-color: #f0f8ff; /* Light blue background */
                border-left: 4px solid #007BFF; /* Blue border on the left */
                padding: 5px;
                margin: 5px 0;
            }
        .card {
    margin: 20px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 10px;
}
        @font-face {
            font-family: 'Jameel Noori Nastaleeq';
            src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');

        }
        @font-face {
            font-family: 'Naskh Unicode';
            src: url('/storage/fonts/UrduNaskhUnicode.ttf');
        }
        @font-face {
            font-family: 'Al_Mushaf';
            src: url('/storage/fonts/Al_Mushaf.ttf');
        }

    </style>
</div>
