<!-- Chat Component for Reception Fatawa -->
<div class="chat-messages-container">
    <div class="chat-messages">
        @foreach ($messages->sortBy('created_at') as $message)
            @if($message->ifta_code == $file->ifta_code)
                @if ($message->user_id == auth()->user()->id)
                    <!-- Current User Message -->
                    <div class="message-item message-sent">
                        <div class="message-avatar">
                            <span class="avatar-text bg-primary">
                                {{ strtoupper(substr($message->user_name, 0, 1)) }}
                            </span>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-author">{{ $message->user_name }}</span>
                                <span class="message-time">
                                    <i class="far fa-clock me-1"></i>
                                    {{ $message->created_at->format('M d, Y H:i') }}
                                </span>
                            </div>
                            <div class="message-body">
                                {{ $message->message }}
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Other User Message -->
                    <div class="message-item message-received">
                        <div class="message-avatar">
                            <span class="avatar-text bg-secondary">
                                {{ strtoupper(substr($message->user_name, 0, 1)) }}
                            </span>
                        </div>
                        <div class="message-content">
                            <div class="message-header">
                                <span class="message-author">{{ $message->user_name }}</span>
                                <span class="message-time">
                                    <i class="far fa-clock me-1"></i>
                                    {{ $message->created_at->format('M d, Y H:i') }}
                                </span>
                            </div>
                            <div class="message-body">
                                {{ $message->message }}
                            </div>
                        </div>
                    </div>
                @endif
            @endif
        @endforeach
    </div>
    
    <!-- Chat Input Form -->
    <div class="chat-input-container mt-3">
        <form wire:submit.prevent="sendMessage('{{ $file->ifta_code }}')" class="chat-form">
            <div class="input-group">
                <input type="text" 
                       wire:model="newMessage" 
                       class="form-control chat-input" 
                       placeholder="Type your message..."
                       required>
                <button type="submit" class="btn btn-primary chat-send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </form>
    </div>
</div>
