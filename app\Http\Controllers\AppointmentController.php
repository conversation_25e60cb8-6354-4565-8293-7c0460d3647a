<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
// use App\Models\Appointment;

class AppointmentController extends Controller
{
    // public function ApptStore(Request $request)
    // {
    //     // Validate the form data
    //     $validatedData = $request->validate([
    //         'date' => 'required|date',
    //         'day' => 'required|string',
    //         'time' => 'required|date_format:H:i',
    //         'scheduler' => 'required|string',
    //         'contact' => 'required|numeric',
    //         'description' => 'required|string',
    //     ]);

    //     // Create a new appointment record in the database
    //     Appointment::create($validatedData);

    //     return redirect()->back()->with('success', 'Appointment added successfully.');
    // }
//     public function ApptStore(Request $request)
// {
//     $data = $request->validate([
//         'date.*' => 'required|date',
//         'day.*' => 'required',
//         'time.*' => 'required',
//         'scheduler.*' => 'required|string',
//         'contact.*' => 'required|numeric',
//         'description.*' => 'required|string',
//     ]);

//     foreach ($data['date'] as $index => $date) {
//         Appointment::create([
//             'date' => $date,
//             'day' => $data['day'][$index],
//             'time' => $data['time'][$index],
//             'scheduler' => $data['scheduler'][$index],
//             'contact' => $data['contact'][$index],
//             'description' => $data['description'][$index],
//         ]);
//     }

//     return redirect()->back()->with('success', 'Appointments added successfully!');
// }
public function ApptStore(Request $request)
{
    $dates = $request->input('date');
    $days = $request->input('day');
    $times = $request->input('time');
    $schedulers = $request->input('scheduler');
    $contacts = $request->input('contact');
    $descriptions = $request->input('description');

    // Assuming you have a 'appointments' table
    foreach ($dates as $key => $date) {
        DB::table('appt')->insert([
            'appt_date' => $date,
            'appt_day' => $days[$key],
            'appt_time' => $times[$key],
            'appt_name' => $schedulers[$key],
            'appt_cont' => $contacts[$key],
            'appt_dec' => $descriptions[$key],
        ]);
    }

    return redirect()->back()->with('success', 'Appointments added successfully.');
}
public function index()
{
    $appointments = DB::table('appt')->get(); // Fetch all appointments from the 'appt' table

    return view('pages.appointment', compact('appointments'));

}
public function edit($id)
    {
        $appointment = DB::table('appt')->where('id', $id)->first();

        if (!$appointment) {
            return redirect()->route('appointments')->with('error', 'Appointment not found.');
        }

        return view('pages.edit-appointment', compact('appointment'));
    }

    public function update(Request $request, $id)
    {
        // Validation and update logic here

        DB::table('appt')
            ->where('id', $id)
            ->update([
                'appt_date' => $request->input('date'),
                'appt_day' => $request->input('day'),
                'appt_time' => $request->input('time'),
                'appt_name' => $request->input('scheduler'),
                'appt_cont' => $request->input('contact'),
                'appt_dec' => $request->input('description'),
            ]);

        return redirect()->route('appointment')->with('success', 'Appointment updated successfully');
    }
    public function destroy($id)
    {
        DB::table('appt')->where('id', $id)->delete();

        return redirect()->route('appointment')->with('success', 'Appointment deleted successfully');
    }
}
