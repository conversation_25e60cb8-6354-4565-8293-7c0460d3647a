<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTasuratsTable extends Migration
{
    public function up()
    {
        Schema::create('tasurats', function (Blueprint $table) {
            $table->id();
            $table->string('option'); // This column will store the tasurat option
            $table->integer('tasurat_no')->nullable(); // Tasurat number column, default is null
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('tasurats');
    }
};
