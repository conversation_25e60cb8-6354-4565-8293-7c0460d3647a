

<div class="d-flex justify-content-center align-items-center" wire:poll.2000ms>
    <div class="col-md-6 col-lg-7 col-xl-8">
        <ul class="list-unstyled">
            @foreach ($messages->sortBy('created_at') as $message)
                @if($message->ifta_code == $ifta_code)
                    @if ($message->user_id == auth()->user()->id)
                        <li class="d-flex justify-content-between mb-4">
                            <span class="badge rounded-circle bg-success d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                {{ strtoupper(substr($message->user_name, 0, 1)) }}
                            </span>
                                <div class="card w-auto">
                                <div class="card-header d-flex justify-content-between p-3">
                                    <p class="fw-bold mb-0">
                                        {{ $message->user_name }}
                                    </p>
                                    <p class="text-muted small mb-0">
                                        <i class="far fa-clock"></i> {{ $message->created_at }}
                                    </p>
                                </div>
                                <div class="card-body" style="background-color: #ADD8E6; padding: 5px; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; margin: 0;">
                                        <p class="mb-0" style="margin: 0; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-size: 14px; line-height: 1.2;">
                                            {{ $message->message }}


                                        </p>
                                    </div>
                            </div>
                        </li>
                    @else
                        <!-- Unauthenticated / Other users messages. -->
                        <li class="d-flex justify-content-between mb-4">
                            <div class="card w-auto">
                                <div class="card-header d-flex justify-content-between p-3">
                                    <p class="fw-bold mb-0">
                                        {{ $message->user_name }}
                                    </p>
                                    <p class="text-muted small mb-0">
                                        <i class="far fa-clock"></i> {{ $message->created_at }}
                                    </p>
                                </div>
                                <div class="card-body" style="background-color: #E6E6FA; padding: 5px; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; margin: 0;">
                                        <p class="mb-0" style="margin: 0; word-wrap: break-word; overflow-wrap: break-word; white-space: normal; font-size: 14px; line-height: 1.2;">
                                            {{ $message->message }}


                                        </p>
                                    </div>
                            </div>
                            <span class="badge rounded-circle bg-primary d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                {{ strtoupper(substr($message->user_name, 0, 1)) }}
                            </span>
                        </li>
                    @endif
                @endif
            @endforeach
            <input wire:model="user_name" type="hidden" id="user_name" name="user_name" value="{{ $user_name }}">
            <input wire:model="user_id" type="hidden" id="user_id" name="user_id" value="{{ $user_id }}">
            <input wire:model="ifta_code" type="hidden" id="ifta_code" name="ifta_code" value="{{ $ifta_code }}">

            <div class="bg-light">
                <div class="input-group">
                    <input wire:model="message" type="text" placeholder="Type a message" aria-describedby="button-addon2" class="form-control rounded-0 border-0 py-4 bg-light text-end">
                    <div class="input-group-append">
                        <button id="button-addon2" class="btn btn-link" wire:click='sendMessage'> <i class="fa fa-paper-plane"></i></button>
                    </div>
                </div>
            </div>
        </ul>
    </div>
</div>

</div>


