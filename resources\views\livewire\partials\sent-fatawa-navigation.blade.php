<!-- Navigation Section (Admin Only) -->
@php
    $Admin = Auth::check() && in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
@endphp

@if ($Admin)
    <div class="navigation-section">
        <h5 class="mb-3">
            <i class="fas fa-sitemap me-2"></i>
            Quick Navigation
        </h5>
        
        <div class="navigation-buttons">
            @php
                $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                $isAllIftaActive = request()->route('darulifta') == null;
            @endphp
            
            <!-- All Ifta Button -->
            <a href="{{ route('sent-fatawa', [
                'selectedmufti' => $this->selectedmufti,
                'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                'selectedchecked' => $selectedchecked,
                'startDate' => $tempStartDate,
                'endDate' => $tempEndDate,
                'showDetail' => $showDetail ? '1' : '0',
                'showQue' => $showQue ? '1' : '0',
                'showChat' => $showChat ? '1' : '0',
                'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
            ]) }}" class="nav-button {{ $isAllIftaActive ? 'active' : '' }}">
                <i class="fas fa-globe me-2"></i>
                All Ifta
            </a>

            <!-- Individual Darulifta Buttons -->
            @foreach($daruliftalist as $daruliftalistn)
                @php
                    $isActive = request()->route('darulifta') == $daruliftalistn;
                @endphp
                <a href="{{ route('sent-fatawa', [
                    'darulifta' => $daruliftalistn,
                    'selectedmufti' => $this->selectedmufti,
                    'selectedTimeFrame' => $this->tempSelectedTimeFrame,
                    'selectedchecked' => $selectedchecked,
                    'startDate' => $tempStartDate,
                    'endDate' => $tempEndDate,
                    'showDetail' => $showDetail ? '1' : '0',
                    'showQue' => $showQue ? '1' : '0',
                    'showChat' => $showChat ? '1' : '0',
                    'selectedMonths' => $this->selectedTimeFrame == 'other' ? implode(',', $this->selectedMonths) : null,
                ]) }}" class="nav-button {{ $isActive ? 'active' : '' }}">
                    <i class="fas fa-building me-2"></i>
                    {{ $daruliftalistn }}
                </a>
            @endforeach
        </div>
    </div>
@endif

<!-- Duplicate File Codes Section -->
@php
    $duplicateFileCodesList = [];
    $seenFileCodes = [];
    
    // Calculate duplicates
    foreach ($sendingFatawa as $daruliftaName => $checkers) {
        foreach ($checkers as $checked => $mailfolderGroups) {
            foreach ($mailfolderGroups as $mailfolderDates => $fatawaData) {
                foreach ($fatawaData as $file) {
                    $fileCode = $file->file_code;
                    if (isset($seenFileCodes[$fileCode])) {
                        if ($seenFileCodes[$fileCode] == 1) {
                            $seenFileCodes[$fileCode]++;
                        }
                        $duplicateFileCodesList[$fileCode] = isset($duplicateFileCodesList[$fileCode]) ? $duplicateFileCodesList[$fileCode] + 1 : 2;
                    } else {
                        $seenFileCodes[$fileCode] = 1;
                    }
                }
            }
        }
    }
@endphp

@if (!empty($duplicateFileCodesList))
    <div class="modern-card" x-data="{ showDuplicates: false }">
        <div class="modern-card-header cursor-pointer" @click="showDuplicates = !showDuplicates">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-copy me-2"></i>
                        Duplicate File Codes
                    </h5>
                    <p class="mb-0 opacity-75">{{ count($duplicateFileCodesList) }} duplicate file codes found</p>
                </div>
                <i class="fas" :class="showDuplicates ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
            </div>
        </div>
        <div x-show="showDuplicates" x-transition class="modern-card-body">
            <div class="row">
                @foreach($duplicateFileCodesList as $fileCode => $count)
                    <div class="col-md-4 col-sm-6 mb-2">
                        <a href="{{ route('fatwa-detail', ['fatwa' => $fileCode]) }}" 
                           target="_blank" 
                           class="btn-modern btn-outline-modern w-100 text-start">
                            <i class="fas fa-external-link-alt me-2"></i>
                            {{ $fileCode }}
                            <span class="badge bg-danger ms-auto">{{ $count }} times</span>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif
