
<div>
    <x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="{{ request()->route('darulifta') ?? 'ok-fatawa' }}"></x-navbars.sidebar>
        <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
      
<!-- <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
        
<script src="https://rawgit.com/eKoopmans/html2pdf/master/dist/html2pdf.bundle.js"></script> -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

        <script src="//unpkg.com/alpinejs" defer></script>

            <!-- Navbar -->
            <style>
                td {
    white-space: nowrap; /* Prevents wrapping by default */
    overflow: hidden;    /* Hides overflow */
    text-overflow: ellipsis; /* Adds ellipsis to overflowed content */
}

/* Responsive adjustments */
@media screen and (max-width: 768px) { /* Adjust for tablets */
    td {
        white-space: normal; /* Allows wrapping */
        overflow: visible;
    }
}

@media screen and (max-width: 480px) { /* Adjust for mobile phones */
    td {
        font-size: 0.8rem; /* Smaller font size for smaller screens */
    }
}
                .calendar-style {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 items per row, adjust as needed */
    gap: 5px; /* smaller gap */
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
    border-radius: 8px;
}

.month-year {
    display: flex;
    align-items: center;
    padding: 3px; /* reduced padding */
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.month-year input[type="checkbox"] {
    margin-right: 5px; /* smaller margin */
}

.month-year label {
    margin: 0;
    font-size: 12px; /* smaller font size */
}


                 .right-aligned {
        text-align: right;
    }
    .card-title-row {
        display: flex;          /* Enable flexbox for the container */
        justify-content: space-between; /* Spread children to each end */
        align-items: center;    /* Center items vertically */
        width: 100%;            /* Ensure the container spans the full width */
    }

    h2.card-title {
        margin: 0;             /* Remove margin to avoid unnecessary spacing */
        flex-grow: 1;          /* Allow the title to take up necessary space */
        white-space: nowrap;   /* Keep the title in a single line */
    }

    select {
        margin-left: auto;     /* Push the select box to the end of the container */
    }
    /* .question-section,
.chat-section {
    display: none;
} */
        
                    .custom-bg-light-red {
                    background-color: #FFDDDD; /* Lightest shade of red */
        }
        
        .custom-bg-light-blue {
            background-color: #DDDDFF; /* Lightest shade of blue */
        }
        
        .custom-bg-light-green {
            background-color: #DDFFDD; /* Lightest shade of green */
        }
        
        
        .custom-bg-light-yellow {
            background-color: #FFFFCC; /* Lightest shade of yellow */
        }
        .custom-text-dark-black {
            color: #000; /* Dark black color */
        }
                    .table{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px
                    
                  }
                  .card{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px
                    
                  }
                  .table2{
                    font-family: Jameel Noori Nastaleeq;
                    font-size: 20px
                    
                  }
                    .not-assigned {
            color: red;
            /* Add any other styles for not assigned here */
        }
        .future-date {
            color: red !important;
            border: 1px solid red;
        }
        
        .past-date {
            border: 1px solid green;
        }
        .increased-font {
                font-size: 20px; /* Change the font size as needed */
            }
            table {
            table-layout: auto;
            font-size: 20px; /* Adjust the font size to your preference */
            
        }
        th, td {
                font-size: 20px; /* Adjust the font size for table headers and table data cells */
            }

</style>
            <x-navbars.navs.auth titlePage="Sent Fatawa"></x-navbars.navs.auth>
            
            @php
    
            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        @endphp
        <div wire:loading>
        <div style="display: flex; justify-content: center; align-items: center; background-color: black; 
        position: fixed; top: 0px; left: 0px; z-index: 9999; width: 100%; height: 100%;
        opacity: .75;">
                
                <div class="la-ball-spin la-2x">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                </div>   
        </div>
        <div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
            
    <div class="card mb-4" id="big-card-10" style="background-color: #FFFFCC;">
        <div class="card-body">
            <div class="card-title-row">
                <h2 class="card-title">Darulifta And Mujeeb Summary of Ok Fatawa</h2>
                <select wire:model.live="selectedmufti" id="muftiframe">
                    <option value="all" selected>All</option>
                    <option value="mufti_ali_asghar">Mufti Ali Asghar</option>
                    <option value="sayed_masood">Sayed Masood</option>
                </select>
                <select wire:model.live="selectedTimeFrame" id="timeframe">
                <option value="this_month" selected>This Month</option>
                <option value="last_month" >Last Month</option>
                    <option value="other">Other</option>
                </select>
                <select wire:model.live="selectedchecked" id="checked">
                    <option value="all_checked" selected>All Checked</option>
                    <option value="mahle_nazar">Mahl-e-Nazar</option>
                    <option value="ok_fatawa">Ok</option>
                </select>
            </div>
            <br>
            @if ($selectedTimeFrame === 'other')
            <div class="calendar-style">
    @foreach ($datefilter as $data)
        @php
            $monthName = DateTime::createFromFormat('!m', $data->month)->format('F');
        @endphp
        <div class="month-year">
            <input type="checkbox" wire:model.live="selectedMonths" id="month-{{ $data->year }}-{{ $data->month }}" value="{{ $data->year }}-{{ $data->month }}">
            <label for="month-{{ $data->year }}-{{ $data->month }}">
                {{ $monthName }} {{ $data->year }}
            </label>
        </div>
    @endforeach
</div>
            @endif
            

            <table class="table">
                    <thead>
                        <tr>
                            <th>Darulifta</th>
                            <th>Reciption Fatawa Date</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($okFatawa[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0; // Initialize a variable to store the total count for the current $daruliftaName
                                    
                                    
                                @endphp
                
                                @foreach($mujeeb as $mailfolderDate)
                                    @if(isset($okFatawa[$daruliftaName][$mailfolderDate]))
                                        @foreach($okFatawa[$daruliftaName][$mailfolderDate] as $file)
                                            @php
                                                $folder = $file->sender;
                                                $folderCounts[$daruliftaName][$folder] = isset($folderCounts[$daruliftaName][$folder]) ? $folderCounts[$daruliftaName][$folder] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach
                
                                <tr>
                                    <td>
                                    @php
                                        $selectedMonthsQuery = http_build_query(['selectedMonths' => $this->selectedMonths]);
                                    @endphp
                                        <a href="{{ route('ok-fatawa', ['darulifta' => $daruliftaName, 'selectedmufti' => $this->selectedmufti, 'selectedchecked' => $this->selectedchecked, 'selectedTimeFrame' => $this->selectedTimeFrame]) }}&{{ $selectedMonthsQuery }}">
                                        {{ $daruliftaName }}
                                    </a>
                                    </td>
                                    <td>
                                        @php
                                            $foldercount = 0;
                                        @endphp
                                        @foreach ($folderCounts[$daruliftaName] as $folder => $count)
                                        <a href="{{ route('ok-fatawa', ['darulifta' => $daruliftaName, 'mujeebn' => $folder, 'selectedmufti' => $this->selectedmufti, 'selectedchecked' => $this->selectedchecked, 'selectedTimeFrame' => $this->selectedTimeFrame]) }}&{{ $selectedMonthsQuery }}">
                                        {{ $folder }}</a>({{ $count }})
                                        <span style="display: inline-block;">,</span> 
                                        @php
                                        $foldercount++;    
                                        $overallFolderCount++;
                                        @endphp
                                        @endforeach
                                    </td>
                                    <td>
                                        Fatawa: {{ $daruliftaTotalCounts }} | Mujeeb:{{$foldercount}}
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
                
                <h5>Overall Total Fatawa: {{ $totalCounts }} And Mujeeb: {{ $overallFolderCount }}</h5>
        </div>
    </div>
</div>
<div>
    <input type="checkbox" wire:model.live="showColumns" id="show-columns">
    <label for="show-columns">Mufti & Mujeeb Trail</label>
</div>    
<div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
            
            <div class="card z-index-2 mb-3" id="big-card-1" style="background-color: #FFFFCC;">
                <div class="card-header pb-0">
                    <h4>Ok Fatawa Detail</h4>
                    
                </div>
                
                <div class="card-body px-0 pb-2">
                    @foreach($daruliftaNames as $daruliftaName)
                        @if(isset($okFatawa[$daruliftaName]))
                        <div x-data="{ open: true }"> 
                        <h5 @click="open = !open" class="cursor-pointer">
                            <span x-text="open ? '▲' : '▼'"></span> {{ $daruliftaName }}
                        </h5>
                        <div x-show="open" class="table-responsive1">

                            
                                @php
                                        $serialNumber_fl = 1; // Initialize the serial number    

                                        $mailfolderDateCount = 0;
                                        $colors = ['#F0FFF0', '#F0F8FF', '#FFFFE0', '#FFE4E1', '#F0F8FF', '#F5FFFA', '#E6E6FA', '#FAEBD7', '#ADD8E6']; // Add more colors as needed
                                        @endphp
                                @foreach($mujeeb as $mailfolderDates)
                                @if(isset($okFatawa[$daruliftaName][$mailfolderDates]))
                                @php
                                    $mailfolderDateCount = count($okFatawa[$daruliftaName][$mailfolderDates]); // Count the occurrences of $mailfolderDate
                                @endphp

                                <div class="card z-index-2 mb-3" style="background-color: {{ $colors[$loop->index % count($colors)] }};">
                                    <div  class="data-row-r" data-rec-date-r="{{ $mailfolderDates }}">
                                
                                    <div x-data="{ innerOpen: true }">
                                <h6 @click="innerOpen = !innerOpen" class="toggle-header toggle-inner d-flex cursor-pointer">
                        ({{ $serialNumber_fl++ }}) <span x-text="innerOpen ? '▲' : '▼'"></span> {{ $mailfolderDates }}
                        @foreach ($obtain as $obtains)
                                @if ($mailfolderDates == $obtains->sender)
                                Total({{$obtains->total_score_sum}})
                                @endif
                                @endforeach
                        <span class="ms-auto pr-2">Fatawa:({{ $mailfolderDateCount }})</span>
                    </h6>
                    <table x-show="innerOpen" class="table1 align-items-center mb-0">

                          
                                    <thead>
                                        <tr>
                                        <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                S.No</th>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Fatwa No</th>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Mujeeb</th>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Category</th>
                                            <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Checked Folder Date</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Grade No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Early Ok No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Gair Umoni No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Not Tarmim No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Shandar No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Total No</th>
                                                <th class="text-center text-uppercase text-secondary font-weight-bolder opacity-7">Q.R.Date & Days at Reciption</th>
                                            <th class="text-center text-uppercase text-secondary font-weight-bolder opacity-7">M.Folder</th>
                                            @if ($showColumns)
                                            <th class="text-center text-uppercase text-secondary font-weight-bolder opacity-7">Mufti Shaib Trail</th>
                                            <th class="text-center text-uppercase text-secondary font-weight-bolder opacity-7">Mujeeb Trail</th>
                                            @endif
                                            
                                            <th
                                                            class="text-center text-uppercase text-secondary font-weight-bolder" style="width: 5%;">
                                                            View</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                        $serialNumber_re = 1; // Initialize the serial number    
                                        @endphp
                                        
                                        @foreach($okFatawa[$daruliftaName][$mailfolderDates] as $file)
                                
                                                        <tr>
                                                        <td class="align-middle text-center" style="width: 5%; white-space: normal;">
                                                            <span class="font-weight-bold">{{ $serialNumber_re++ }}</span>
                                                        </td>
                                                        <td>
                                                <div class="d-flex px-2 py-1">
                                                    
                                                    <div class="d-flex flex-column justify-content-center">
                                                        <h6 class="mb-0 ">{{$file->file_code}}</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="mujeeb-column5"> {{$file->sender}} </span>
                                                                                                    </div>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->category}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->mail_folder_date}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->calculated_grade}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->mahl_e_nazar_score}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->gair_umomi}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->tarmim_value}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->shandar_tahqiqi}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->total_score}} </span>
                                            </td>
                                                        <td class="align-middle text-center ">
                                                            @foreach ($que_day_r as $day)
                                                            @if (Str::lower($file->file_code) == Str::lower($day->ifta_code))
                                                                @php
                                                                    $recDate = new \DateTime($day->rec_date);
                                                                    $currentDate = now();
                                                                    $daysDifference = $currentDate->diff($recDate)->days;
                                                                @endphp
                                                                {{$day->rec_date}}
                                                                <br>
                                                                {{ $daysDifference }} days
                                                                <br>
                                                            @endif
                                                        @endforeach
                                                            
                                                        </td>
                                                        <td class="align-middle text-center ">
                                                            @foreach ($mahlenazar_null as $mahle)
                                                            {{-- <span class=" font-weight-bold"> --}}
                                                                
                                                                @if ($file->file_code == $mahle->file_code)
                                                                {{ $mahle->mail_folder_date }}
                                                                <br>
                                                                

                                                                                                                            
                                                                {{-- </span> --}}
                                                                @endif
                                                                
                                                        @endforeach
                                                        
                                                        </td>
                                                        <td class="align-middle text-center">
                                                            <span class="font-weight-bold">{{ $file->checked_folder }}</span>
                                                        </td>
                                                        @if ($showColumns)

                                                        <td class="align-middle text-center">
                                                            @php
                                                                $Qr = 1;
                                                                $Qc = 1;
                                                                $totalDays = 0; // Initialize total days accumulator
                                                            @endphp

                                                            @foreach ($allfatawa as $fatawa)
                                                                @if ($file->file_code == $fatawa->file_code)
                                                                    @php
                                                                        $mailFolderDate = \Carbon\Carbon::parse($fatawa->mail_folder_date);
                                                                        $checkedDate = \Carbon\Carbon::parse($fatawa->checked_date);
                                                                        $daysDifference1 = $mailFolderDate->diffInDays($checkedDate);
                                                                        $totalDays += $daysDifference1; // Add to total
                                                                    @endphp
                                                                    {{$Qr++}}R|{{ $mailFolderDate->format('d-m-Y') }}|{{$Qc++}}C|{{ $checkedDate->format('d-m-Y') }}|Days:{{ $daysDifference1 }}
                                                                    <br>
                                                                @endif
                                                            @endforeach

                                                            <!-- Display the total days after the loop -->
                                                            <strong>Total Days: {{ $totalDays }}</strong>
                                                        </td>
                                                        <td class="align-middle text-center">
                                                        @php
                                                        $datesDifference = [];
                                                        @endphp
                                                            @foreach ($codebylower as $code)
                                                            @if (Str::lower($file->file_code) == Str::lower($code->file_code))
                                                                @php
                                                                    $lowerdate = new \DateTime($code->mail_folder_date);
                                                                                                                                       
                                                                @endphp
                                                                
                                                            @endif
                                                        @endforeach
                                                       
                                                       
                                                        @php
                                                            $hasCalculated = false;
                                                        @endphp

                                                        @foreach ($que_day_r as $day)
                                                            @if (Str::lower($file->file_code) == Str::lower($day->ifta_code))
                                                                @if (!$hasCalculated)
                                                                    @php
                                                                        $recDate = new \DateTime($day->rec_date);
                                                                        
                                                                        
                                                                        $hasCalculated = true; // Set the flag as true after calculation
                                                                    @endphp
                                                                    
                                                                
                                                            @endif
                                                            @endif
                                                        
                                                        @endforeach
                                                        @php
                                                        $daysDifference3 = $recDate->diff($lowerdate)->days;
                                                        @endphp
                                                        From: {{ $recDate->format('Y-m-d') }} To: {{ $lowerdate->format('Y-m-d') }} - {{ $daysDifference3 }} days
                                                        <br>
                                                        @php
                                                            $previousMailFolderDate = null;
                                                            
                                                        @endphp

                                                        @foreach ($allfatawa as $index => $fatawa)
                                                        @if ($file->file_code == $fatawa->file_code)
                                                                @if (!is_null($previousMailFolderDate) && !is_null($fatawa->mail_folder_date))
                                                                    @php
                                                                        $checkedDate = \Carbon\Carbon::parse($fatawa->mail_folder_date);
                                                                        $daysDifference2 = $previousMailFolderDate->diffInDays($checkedDate, false);
                                                                        $datesDifference[] = [$daysDifference2, $daysDifference3];
                                                                    @endphp
                                                                    From {{ $previousMailFolderDate->format('d-m-Y') }} to {{ $checkedDate->format('d-m-Y') }}: Days = {{ $daysDifference2 }}
                                                                    <br>
                                                                @endif
                                                                @php
                                                                    $previousMailFolderDate = \Carbon\Carbon::parse($fatawa->checked_date);
                                                                @endphp
                                                            @endif
                                                        @endforeach

                                                        @if (count($datesDifference) > 0)
                                                                @php
                                                                    $totalDays = 0;
                                                                    foreach ($datesDifference as $differences) {
                                                                        $totalDays += array_sum($differences);  // Sum the elements of each nested array
                                                                    }
                                                                @endphp
                                                                <strong>Total Days: {{ $totalDays }}</strong>
                                                            @endif
                                                        </td>
                                                        @endif
                                                        <td>
                                                        <span class="view">
                                                                <a href="{{ route('viewCheck',
                                                                    ['date' => $file->mail_folder_date . $file->darulifta_name .'Checked',
                                                                    'folder' => $file->checked_folder,
                                                                    'filename' => $file->checked_file_name]) }}" target="_blank">
                                                                    <i class="fas fa-eye"></i> 
                                                                </a>
	                                                            </span>
                                                                <span class="download">
                                                                <a href="{{ route('downloadCheck',
                                                                ['date' => $file->mail_folder_date . $file->darulifta_name . 'Checked',
                                                                'folder' => $file->checked_folder,
                                                                'filename' => $file->checked_file_name]) }}">
                                                                    <i class="fas fa-download"></i> 
                                                                </a>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    
    <!-- Question Toggle and Content -->
            
            
    <tr x-data="{ openQuestion: false }">
        <td colspan="1" @click="openQuestion = !openQuestion" class="align-middle text-center cursor-pointer" style="background-color: #FFDDCC;">
            Question <span x-text="openQuestion ? '◀' : '▶'"></span>
        </td>
    
        <td x-show="openQuestion" @click.outside="openQuestion = false" colspan="9" style="background-color: #ffffff;">
            <!-- Dynamic Content Here -->
            @foreach ($que_day_r as $day)
                @if (strtolower($file->file_code) == strtolower($day->ifta_code))
                    سوال: {{ $day->question }}<br>
                @endif
            @endforeach
        </td>
    </tr>
        
        
        <!-- Chat Toggle and Content -->
        <tr x-data="{ openChat: false }">
            <td colspan="1" @click="openChat = !openChat" class="align-middle text-center cursor-pointer toggle-chat right-aligned" data-section="chat" style="background-color: #FFDDCC;">
                Chat <span x-text="openChat ? '◀' : '▶'"></span>
            </td>
        
                                                    
                                                        <td x-show="openChat" colspan="8" class="align-middle text-center"  >
                                                        <div class="d-flex justify-content-center align-items-center">
                                                        <div class="col-md-6 col-lg-7 col-xl-8">
                                                            <ul class="list-unstyled">
                                                                @foreach ($messages->sortBy('created_at') as $message)
                                                                    @if($message->ifta_code == $file->file_code)
                                                                        @if ($message->user_id == auth()->user()->id)
                                                                            <li class="d-flex justify-content-between mb-4">
                                                                                <span class="badge rounded-circle bg-success d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                                                                    {{ strtoupper(substr($message->user_name, 0, 1)) }}
                                                                                </span>
                                                                                    <div class="card w-auto">
                                                                                    <div class="card-header d-flex justify-content-between p-3">
                                                                                        <p class="fw-bold mb-0">
                                                                                            {{ $message->user_name }}
                                                                                        </p>
                                                                                        <p class="text-muted small mb-0">
                                                                                            <i class="far fa-clock"></i> {{ $message->created_at }}
                                                                                        </p>
                                                                                    </div>
                                                                                    <div class="card-body" style="background-color:#ADD8E6;">
                                                                                        <p class="mb-0">
                                                                                            {{ $message->message }}
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                            </li>
                                                                        @else
                                                                            <!-- Unauthenticated / Other users messages. -->
                                                                            <li class="d-flex justify-content-between mb-4">
                                                                                <div class="card w-auto">
                                                                                    <div class="card-header d-flex justify-content-between p-3">
                                                                                        <p class="fw-bold mb-0">
                                                                                            {{ $message->user_name }}
                                                                                        </p>
                                                                                        <p class="text-muted small mb-0">
                                                                                            <i class="far fa-clock"></i> {{ $message->created_at }}
                                                                                        </p>
                                                                                    </div>
                                                                                    <div class="card-body">
                                                                                        <p class="mb-0">
                                                                                            {{ $message->message }}
                                                                                        </p>
                                                                                    </div>
                                                                                </div>
                                                                                <span class="badge rounded-circle bg-primary d-flex align-items-center justify-content-center ms-3 shadow-1-strong" style="width: 40px; height: 40px;">
                                                                                    {{ strtoupper(substr($message->user_name, 0, 1)) }}
                                                                                </span>
                                                                            </li>
                                                                        @endif
                                                                    @endif
                                                                @endforeach
                                                                
                                                                
                                                                
                                                                <div class="bg-light">
                                                                    <div class="input-group">
                                                                        <input wire:model="message" type="text" placeholder="Type a message" aria-describedby="button-addon2" class="form-control rounded-0 border-0 py-4 bg-light text-end">
                                                                        
                                                                        <div class="input-group-append">
                                                                        <button id="button-addon2" class="btn btn-link" wire:click="sendMessage('{{ $file->file_code }}')"> <i class="fa fa-paper-plane"></i></button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </ul>
                                                        </div>
                                                    </div>

                                                    </div>
                                                        
                                                        </td>
                                                        </tr>
    


        
        
        
                                        

                                                    
                                                    @endforeach
                                                    
                                    </tbody>
                                </table>
                                
                                
                            </div>
                        </div>
                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                        @endif
                    @endforeach
                
                </div>
            </div>
        </div>
   

   <script>
    document.addEventListener('DOMContentLoaded', function () {
    const checkboxes = document.querySelectorAll('.calendar-style input[type="checkbox"]');

    // Example: Adding an event listener to each checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if(this.checked) {
                console.log(this.value + ' is checked');
            } else {
                console.log(this.value + ' is unchecked');
            }
        });
    });
});

   </script>
           
   
    <x-footers.auth></x-footers.auth>

</main>
<x-plugins></x-plugins>

</x-layout>

</div>

