<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="{{ request()->route('role') ?? 'mahlenazar' }}"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Dashboard"></x-navbars.navs.auth>

    <div>
        <h1>Your Dashboard</h1>

        {{-- Livewire Component --}}
        @livewire('dash-board')

        {{-- Rest of your content --}}
        <!-- ... -->
    </div>

<table>

    @php
        function renderItems($items) {
            echo '<ul>';
            foreach ($items as $item) {
                if ($item['type'] === 'directory') {
                    echo '<li><strong>' . htmlspecialchars($item['name']) . '</strong>';
                    if (!empty($item['children'])) {
                        renderItems($item['children']);
                    }
                    echo '</li>';
                } else {
                    echo '<li>' . htmlspecialchars($item['name']) . '</li>';
                }
            }
            echo '</ul>';
        }
    @endphp

    @foreach ($storageItems as $item)
        @php renderItems([$item]); @endphp
    @endforeach </ul>
</table>
</x-layout>