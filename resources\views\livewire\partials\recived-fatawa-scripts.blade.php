<!-- JavaScript for URL Updates and Modern Interactions -->
<script>
    // Update URL when checkboxes change
    function updateUrl(reload = false) {
        const showDetail = document.getElementById('show-detail').checked ? '1' : '0';
        const showQue = document.getElementById('show-que').checked ? '1' : '0';
        const showChat = document.getElementById('show-chat').checked ? '1' : '0';

        const url = new URL(window.location);

        // Preserve existing filter parameters
        const currentParams = new URLSearchParams(window.location.search);

        // Set display options
        url.searchParams.set('showDetail', showDetail);
        url.searchParams.set('showQue', showQue);
        url.searchParams.set('showChat', showChat);

        // Update URL and reload if requested
        if (reload) {
            window.location.href = url.toString();
        } else {
            window.history.replaceState({}, '', url.toString());
        }
    }

    // Initialize display options from URL parameters
    function initializeDisplayOptions() {
        const urlParams = new URLSearchParams(window.location.search);

        const showDetail = urlParams.get('showDetail') === '1';
        const showQue = urlParams.get('showQue') === '1';
        const showChat = urlParams.get('showChat') === '1';

        // Set checkbox states
        const showDetailCheckbox = document.getElementById('show-detail');
        const showQueCheckbox = document.getElementById('show-que');
        const showChatCheckbox = document.getElementById('show-chat');

        if (showDetailCheckbox) showDetailCheckbox.checked = showDetail;
        if (showQueCheckbox) showQueCheckbox.checked = showQue;
        if (showChatCheckbox) showChatCheckbox.checked = showChat;
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        initializeDisplayOptions();
        initializeModernInteractions();
        updateStatistics();
    });

    // Update statistics display
    function updateStatistics() {
        // Update statistics in real-time if needed
        const totalFatawaElement = document.getElementById('totalFatawaCount');
        const totalFolderElement = document.getElementById('totalFolderCount');
        const finalTotalFatawa = document.getElementById('finalTotalFatawa');
        const finalTotalFolders = document.getElementById('finalTotalFolders');

        // Sync values between different stat displays
        if (totalFatawaElement && finalTotalFatawa) {
            finalTotalFatawa.textContent = totalFatawaElement.textContent;
        }
        if (totalFolderElement && finalTotalFolders) {
            finalTotalFolders.textContent = totalFolderElement.textContent;
        }
    }

    // Enhanced form interactions
    function initializeModernInteractions() {
        // Auto-submit form when filters change (optional)
        const filterSelects = document.querySelectorAll('#mujeebframe, #muftiframe, #timeframe');
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                // Optional: Auto-submit on filter change
                // this.closest('form').submit();
            });
        });

        // Enhanced date picker interactions
        const startDate = document.getElementById('start_date');
        const endDate = document.getElementById('end_date');
        const timeframe = document.getElementById('timeframe');

        if (startDate && endDate && timeframe) {
            startDate.addEventListener('change', function() {
                if (this.value && endDate.value) {
                    timeframe.value = 'custom';
                    // Add custom option if it doesn't exist
                    if (!timeframe.querySelector('option[value="custom"]')) {
                        const customOption = document.createElement('option');
                        customOption.value = 'custom';
                        customOption.textContent = 'Custom Date Range';
                        customOption.selected = true;
                        timeframe.appendChild(customOption);
                    }
                }
            });

            endDate.addEventListener('change', function() {
                if (this.value && startDate.value) {
                    timeframe.value = 'custom';
                    // Add custom option if it doesn't exist
                    if (!timeframe.querySelector('option[value="custom"]')) {
                        const customOption = document.createElement('option');
                        customOption.value = 'custom';
                        customOption.textContent = 'Custom Date Range';
                        customOption.selected = true;
                        timeframe.appendChild(customOption);
                    }
                }
            });
        }

        // Enhanced button interactions
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('#mujeebframe, #muftiframe');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            });
        }
    });
</script>
