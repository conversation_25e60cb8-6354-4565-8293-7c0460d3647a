<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Checking Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Link to create Fatawa entry -->
       

        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">
        <!-- Include this line at the top of your <script> section -->
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js" integrity="sha384-vFJXuSJphROIrBnz7yo7oB41mKfc8JzQZiCq4NCceLEaO4IHwicKwpJf9c9IpFgh" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/js/bootstrap.min.js" integrity="sha384-alpBpkh1PFOepccYVYDB4do5UnbKysX5WZXm3XxPqe5iKTfUKjNkCk9SaVuEZflJ" crossorigin="anonymous"></script>
    {{-- <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/css/bootstrap.min.css" integrity="sha384-PsH8R72JQ3SOdhVi3uxftmaW6Vc51MKb0q5P2rRUpPvrszuE4W1povHYgTpBfshb" crossorigin="anonymous"> --}}
 	{{-- <link rel="stylesheet" type="text/css" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"> --}}
 	{{-- <script type="text/javascript" src="./js/manage.js"></script>
	<script type="text/javascript" src="./js/main.js"></script> --}}
	 {{-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta2/dist/css/bootstrap.min.css" rel="stylesheet"
     integrity="sha384-BmbxuPwQa2lc/FVzBcNJ7UAyJxM6wuqIj61tLrc4wSX0szH/Ev+nYRRuWlolflfl" crossorigin="anonymous"> --}}
    <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">

<div class="container d-flex justify-content-between align-items-center">
    <div>
        <a href="{{ route('check.page') }}" class="btn btn-primary">Pending</a>
        <a href="{{ route('check.page', ['complete_checked' => 1]) }}" class="btn btn-success">Complete Checked</a>
    </div>
    <h1 class="m-0">Checking Fatawa</h1>
    <a href="{{ route('cform') }}" class="btn btn-primary">Add Mail Folder</a>
</div>
<table class="table" id="myTable2">
    <thead>
        <tr>
            <th style="width: 15%;" class="th-darulifta">
                Darulifta
                <br>
                <input type="text" id="darulifta_filter" class="filter-input">
            </th>
            <th style="width: 15%;" class="th-received-date">
                Mail Received Date
                <br>
                <input type="text" id="mail_received_filter" class="filter-input">
            </th>
            <th style="width: 15%;" class="th-folder-dates">
                Mail Folder Dates
                <br>
                <input type="text" id="mail_folder_filter" class="filter-input">
                <br>
            </th>
            <th style="width: 15%;" class="th-folder-dates">
                 Attachment
                <br>
                <input type="text" id="mail_folder_filter" class="filter-input">
                <br>
            </th>
                
        </tr>
    </thead>
    <tbody>
        @foreach ($cdata as $darulifta => $datesData)
    @foreach ($datesData as $date => $folderDates)
        @foreach ($folderDates as $fdate => $attachments)
        @foreach ($attachments as $attach => $fatwa_nos)
               @foreach ($fatwa_nos as $fatwa_no)
                     @if ($fatwa_no['selected'] === 0)
                    <tr class="first-row">
                                                   
                        <td style="width: 15%;" data-toggle-button class="th-darulifta">{{ $darulifta }}
                            <button class="toggleFatwaColumn btn btn-sm btn-secondary">&#9650;&#9660;</button>
                        </td>
                        <td style="width: 15%;">{{ $date }}</td>
                        <td style="width: 15%;" >{{ $fdate }}
                        </td>
                        <td style="width: 15%;">
                            <a href="{{ url('/storage' . $attach) }}" download class="btn btn-sm btn-secondary">
                                Download Attachment &#9660;
                            </a>
                        </td>
                    </tr>
                    <tr class="second-row">
                        <td colspan="4">
                            <table class="inner-table" id="myTable2">
                                <thead>
                                    <tr>
                                        <th style="width: 15%;" class="th-darulifta">Mujeeb</th>
                                        <th style="width: 15%;" class="th-received-date">Fatwa Type</th>
                                        <th style="width: 15%;" class="th-folder-dates">Fatwa No</th>
                                        <th style="width: 15%;" class="th-folder-dates">Checked</th>
                                        <th style="width: 15%;" class="th-folder-dates">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($fatwa_nos as $fatwa_no)
                                        <tr>
                                            <td style="width: 15%;">{{ $fatwa_no['mujeeb'] }}</td>
                                            <td style="width: 15%;">{{ $fatwa_no['ftype'] }}</td>
                                            <td style="width: 15%;">{{ $fatwa_no['fatwa_no'] }}</td>
                                            <td style="width: 15%;">
                                                <input
                                                    type="checkbox"
                                                    class="folder-checkbox"
                                                    data-folder-date="{{ $fatwa_no['fatwa_no'] }}"
                                                    data-folder-id="{{ $fatwa_no['id'] }}"
                                                    title="Select this folder"
                                                    {{ $fatwa_no['selected'] === 1 ? 'checked' : '' }}
                                                >
                                            </td>
                                            <td style="width: 15%;">
                                                <a href="{{ route('ucmail', $fatwa_no['id']) }}" type="button"
                                                    class="btn btn-success btn-link" data-toggle="modal">
                                                    <i class="material-icons">edit</i>
                                                </a>
                                                <a href="{{ route('delete.check', $fatwa_no['id']) }}"
                                                    class="btn btn-danger btn-link">
                                                    <i class="material-icons">close</i>
                                                </a>
                                                <input type="hidden" class="folder-id" value="{{ $fatwa_no['id'] }}">
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    @break
                @endif
            @endforeach
        @endforeach
        @endforeach
    @endforeach
@endforeach
    </tbody>
</table>

        {{-- <table class="table">
            <thead>
                <tr>
                    <th class="th-darulifta">
                        Darulifta
                        <br>
                        <input type="text" id="darulifta_filter" class="filter-input">
                    </th>
                    <th class="th-received-date">
                        Mail Received Date
                        <br>
                        <input type="text" id="mail_received_filter" class="filter-input">
                    </th>
                    <th class="th-folder-dates">
                        Mail Folder Dates
                        <br>
                        <input type="text" id="mail_folder_filter" class="filter-input">
                        <br>
                    
                </tr>
            </thead>
            <tbody>
                @foreach ($cdata as $darulifta => $datesData)
                    @foreach ($datesData as $date => $folderDates)
                        <tr>
                            <td>{{ $darulifta }}</td>
                            <td>{{ $date }}</td>
                            <td class="td-folder-dates folder-dates">
                                @foreach ($folderDates as $folderData)
                                    <div class="folder-date">
                                        <span>{{ $folderData['folderDate'] }}</span>
                                        <input
                                            type="checkbox"
                                            class="folder-checkbox"
                                            data-folder-date="{{ $folderData['folderDate'] }}"
                                            data-folder-id="{{ $folderData['id'] }}"
                                            {{ $folderData['selected'] === 1 ? 'checked' : '' }}
                                        >
                                        <form action="{{ route('ucmail', $folderData['id']) }}" method="POST" class="d-inline-block">
                                            <a href={{ route('ucmail', $folderData['id']) }} type="button" class="btn btn-success edit-button" data-toggle="modal">Edit</a>
                                        </form>
                                        <a href="{{ route('delete.check', $folderData['id']) }}" class="btn btn-danger">Delete</a>
                                        <input type="hidden" class="folder-id" value="{{ $folderData['id'] }}">
                                    </div>
                                @endforeach
                            </td>
                        </tr>
                    @endforeach
                @endforeach
            </tbody>
        </table> --}}
        <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
        {{-- <script>
            $(document).ready(function () {
              $('#myTable').DataTable();
        
            });
          </script> --}}
          <script>
            $(document).ready(function () {
              $('#myTable2').DataTable();
        
            });
          </script>
        <script>
            $(document).ready(function () {
                // Handle checkbox change event to show/hide the "Fatwa No" column in the second row
                $('.toggleFatwaColumn').on('click', function () {
                    const row = $(this).closest('tr');
                    const secondRow = row.next('tr.second-row');
                    secondRow.toggleClass('d-none');
                });
            });
       </script>
        
        <script>
            
          // JavaScript code to handle filtering
        const filters = document.querySelectorAll('.filter-input');
        const rows = document.querySelectorAll('.table tbody tr');

        filters.forEach(filter => {
            filter.addEventListener('input', function() {
                const columnIndex = Array.from(filter.parentNode.parentNode.children).indexOf(filter.parentNode);
                const filterValue = this.value.trim().toLowerCase();

                rows.forEach(row => {
                    const cell = row.querySelector(`td:nth-child(${columnIndex + 1})`);
                    if (cell) {
                        const cellText = cell.textContent.trim().toLowerCase();
                        if (cellText.includes(filterValue)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            });
        });
        const checkboxes = document.querySelectorAll('.folder-checkbox');

checkboxes.forEach(checkbox => {
    checkbox.addEventListener('change', async function() {
        const folderId = this.getAttribute('data-folder-id');
        const isChecked = this.checked;

        try {
            // Include the CSRF token in the request headers
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            const response = await axios.post(`/update-selected/${folderId}`, { isChecked }, {
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                },
            });

            if (response.data.success) {
                // Update the 'selected' attribute of the checkbox based on the isChecked value
                this.checked = isChecked;
            }
        } catch (error) {
            console.error(error);
        }
    });
});

        </script>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>

<style>
     /* Define the default column widths for larger screens */
     th, td {
        width: auto; /* Adjust the default width as needed */
    }

    /* Apply different column widths for smaller screens using media queries */
    @media (max-width: 768px) {
        th.custom-column, td.custom-column {
            width: 100%; /* Use 100% width for smaller screens */
        }
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th, .table td {
        padding: 10px;
        border: 1px solid #000000;
        text-align: center;
    }

    .th-darulifta, .th-received-date, .th-folder-dates {
        background-color: #e9a21e;
        color: black;
    }
    .first-row {
        background-color: #e9a21e;
        color: black;
    }
    .second-row {
    background-color: #4b8ece; /* You can change this color to your preferred background color */
}
    .th-darulifta input, .th-received-date input, .th-folder-dates input {
        width: 100%;
        padding: 5px;
        border: 1px solid #2803ff;
        border-radius: 5px;
    }

    .folder-dates {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .folder-date {
        margin: 5px;
    }
            /* tr:nth-child(odd) {
                background-color: #f0c537;
            }
            
            tr:nth-child(even) {
                background-color: #60cc8d;
            } */
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                th {
                    font-weight: bold;
                }
                
                td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
</style>