<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;

// #[Layout('layouts.app')]
class OkFatawa extends Component
{
    public $selectedMonths = [];
    public $datefilter;
    public $showColumns;
    public $codebylower;
    public $message;
    public $allfatawa;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $obtain;
    Public $okFatawa;
    public $daruliftaNames;
    public $mujeeb;
    public $darulifta;
    public $mujeebn;
    public $selectedmufti = 'all';
    public $selectedchecked = 'all_checked';
    public $selectedTimeFrame= 'all';

    Public function mount($darulifta = null,$mujeebn = null)
    {

$this->selectedmufti = request()->query('selectedmufti', $this->selectedmufti);
            $this->selectedchecked = request()->query('selectedchecked', $this->selectedchecked);
            $this->selectedTimeFrame = request()->query('selectedTimeFrame', $this->selectedTimeFrame);
            $this->selectedMonths = request()->query('selectedMonths', $this->selectedMonths);
        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;
        // dd($this->darulifta,$this->mujeebn);
        
        $this->loadDaruliftaNames();
        $this->loadMujeeb();


        // Additional initialization code
    }
    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->selectedTimeFrame = $timeFrame;
    }
    public function updateSelectedMufti($muftiFrame)
    {
        $this->selectedmufti = $muftiFrame;
    }
    public function updateSelectedChecked($checked)
    {
        $this->selectedchecked = $checked;
    }

    private function loadDaruliftaNames()
    {
        if ($this->darulifta === null) {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', 'NOT LIKE', '%3btn%')
                ->distinct()
                ->pluck('darulifta_name');
        } else {
            // $this->daruliftaNames = $this->darulifta;
            $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name',$this->darulifta)
                
                ->distinct()
                ->pluck('darulifta_name');
        }
    }
    private function loadMujeeb()
    {
        if ($this->mujeebn === null) {
        $this->mujeeb = DB::table('uploaded_files')
        ->where('checked_folder', 'ok')
    // Add any additional conditions here
    ->select('sender', DB::raw('SUM(total_score) as total_score_sum'))
    ->groupBy('sender')
    ->havingRaw('COUNT(sender) > 1')
    ->orderBy('total_score_sum', 'desc') // Order by total_score_sum in descending order
        
        ->distinct()
        
        ->pluck('sender');
    } else {
    $this->mujeeb = DB::table('uploaded_files')
    ->select('sender')
    ->where('sender',$this->mujeebn)
    ->distinct()
    
    ->pluck('sender');
    }
}
    public function render()
    {

        $this->obtain = DB::table('uploaded_files')
    ->where('checked_folder', 'ok')
    // Add any additional conditions here
    ->select('sender', DB::raw('SUM(total_score) as total_score_sum'))
    ->groupBy('sender')
    ->havingRaw('COUNT(sender) > 1')
    ->orderBy('total_score_sum', 'desc') // Order by total_score_sum in descending order
    ->get();
    $this->que_day_r = DB::table('questions')
    ->whereIn('question_branch', $this->daruliftaNames)
    ->get();
    $this->allfatawa = DB::table('uploaded_files')
->orderBy('id', 'asc') // Sorting by 'id' in ascending order
->get();
$this->datefilter= DB::table('uploaded_files')
->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
->orderBy('year', 'asc')
->orderBy('month', 'asc')
->get();

$this->codebylower = DB::table('uploaded_files as uf1')
->select('uf1.*')
->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files GROUP BY file_code) as uf2'), function($join) {
    $join->on('uf1.id', '=', 'uf2.id');
})
->orderBy('uf1.id', 'asc')
->get();

    $this->mahlenazar_null = DB::table('uploaded_files as u1')
    ->whereIn('u1.darulifta_name', $this->daruliftaNames)
    ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
    
    // ->leftJoin('uploaded_files as u2', function ($join) {
    //     $join->on('u1.file_code', '=', 'u2.file_code')
    //         ->where(function ($query) {
    //             $query->where('u2.checked_folder', 'ok')
                
    //                 ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere
                    
    //         });
    // })
    // ->whereNull('u2.file_code')
    
    ->select('u1.*')
    ->get();
    


        $this->okFatawa = $this->okFatawadata();
        $messages = Chat::latest()->get();

        // dd($this->mahlenazar_null);
        return view('livewire.ok-fatawa', [
            'mujeeb' => $this->mujeeb,
            'daruliftaNames' => $this->daruliftaNames,
            'okFatawa' => $this->okFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmufti' => $this->selectedmufti,
            'selectedchecked' => $this->selectedchecked,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            'showColumns' => $this->showColumns,
            // ... (other data to be passed to the view)
        ]
        
        )
        ->layout('layouts.app');
    }
    public function okFatawadata()
    {
        $okFatawas = [];

        $formattedSelectedMonths = array_map(function ($date) {
            return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
        }, $this->selectedMonths);


        foreach ($this->mujeeb as $mujeebs) {
            foreach ($this->daruliftaNames as $daruliftaName) {
                $query = collect();

            
                $baseQuery = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('sender', $mujeebs)
                    ->where('checked_folder','ok');
                    
                    if ($this->selectedmufti == 'mufti_ali_asghar') {
                        $baseQuery->where('checker', 'mufti_ali_asghar');
                    } elseif ($this->selectedmufti == 'sayed_masood') {
                        $baseQuery->where('checker', 'sayed_masood');
                    }
                    if ($this->selectedTimeFrame == 'this_month') {
                        $query = $baseQuery->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                            now()->startOfMonth(),
                            now()->endOfMonth()
                        ])->get();
                    } elseif ($this->selectedTimeFrame == 'last_month') {
                        $query = $baseQuery->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                            now()->subMonth()->startOfMonth(),
                            now()->subMonth()->endOfMonth()
                        ])->get();
                    } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
                        $query = $baseQuery->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths)->get();
                    } else {
                        // No matching timeframe, $query remains as empty collection
                        $query = collect();
                    }
        
                    // Check if $query is not empty and store results
                    if ($query->isNotEmpty()) {
                    $okFatawas[$daruliftaName][$mujeebs] = $query;
                }
            }
        }

        return $okFatawas;
    }


}
