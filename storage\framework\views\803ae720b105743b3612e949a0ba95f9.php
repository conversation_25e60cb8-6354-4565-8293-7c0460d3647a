<!-- JavaScript for URL Updates and Modern Interactions -->
<script>
    // Update URL when checkboxes change
    function updateUrl(reload = false) {
        const showDetail = document.getElementById('show-detail').checked ? '1' : '0';
        const showQue = document.getElementById('show-que').checked ? '1' : '0';
        const showChat = document.getElementById('show-chat').checked ? '1' : '0';

        const url = new URL(window.location);

        // Preserve existing filter parameters
        const currentParams = new URLSearchParams(window.location.search);

        // Set display options
        url.searchParams.set('showDetail', showDetail);
        url.searchParams.set('showQue', showQue);
        url.searchParams.set('showChat', showChat);

        // Preserve filter parameters
        if (currentParams.get('selectedmujeeb')) {
            url.searchParams.set('selectedmujeeb', currentParams.get('selectedmujeeb'));
        }
        if (currentParams.get('selectedmufti')) {
            url.searchParams.set('selectedmufti', currentParams.get('selectedmufti'));
        }
        if (currentParams.get('selectedTimeFrame')) {
            url.searchParams.set('selectedTimeFrame', currentParams.get('selectedTimeFrame'));
        }
        if (currentParams.get('startDate')) {
            url.searchParams.set('startDate', currentParams.get('startDate'));
        }
        if (currentParams.get('endDate')) {
            url.searchParams.set('endDate', currentParams.get('endDate'));
        }
        if (currentParams.get('selectedMonths')) {
            url.searchParams.set('selectedMonths', currentParams.get('selectedMonths'));
        }

        if (reload) {
            window.location.href = url.toString();
        } else {
            window.history.replaceState({}, '', url.toString());
        }
    }

    // Show loading overlay during form submissions
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('form');
        const loadingOverlay = document.getElementById('loadingOverlay');

        forms.forEach(form => {
            form.addEventListener('submit', function() {
                loadingOverlay.style.display = 'flex';
            });
        });

        // Update statistics dynamically
        updateStatistics();

        // Initialize modern interactions
        initializeModernInteractions();
    });

    function updateStatistics() {
        // Update statistics in real-time if needed
        const totalFatawaElement = document.getElementById('totalFatawaCount');
        const totalFolderElement = document.getElementById('totalFolderCount');
        const finalTotalFatawa = document.getElementById('finalTotalFatawa');
        const finalTotalFolders = document.getElementById('finalTotalFolders');

        // Sync values between different stat displays
        if (totalFatawaElement && finalTotalFatawa) {
            finalTotalFatawa.textContent = totalFatawaElement.textContent;
        }
        if (totalFolderElement && finalTotalFolders) {
            finalTotalFolders.textContent = totalFolderElement.textContent;
        }
    }

    // Enhanced form interactions
    function initializeModernInteractions() {
        // Auto-submit form when filters change (optional)
        const filterSelects = document.querySelectorAll('#mujeebframe, #muftiframe, #timeframe');
        filterSelects.forEach(select => {
            select.addEventListener('change', function() {
                // Optional: Auto-submit on filter change
                // this.closest('form').submit();
            });
        });

        // Enhanced date picker interactions
        const startDate = document.getElementById('start_date');
        const endDate = document.getElementById('end_date');
        const timeframe = document.getElementById('timeframe');

        if (startDate && endDate && timeframe) {
            startDate.addEventListener('change', function() {
                if (this.value && endDate.value) {
                    timeframe.value = 'custom';
                    // Add custom option if it doesn't exist
                    if (!timeframe.querySelector('option[value="custom"]')) {
                        const customOption = document.createElement('option');
                        customOption.value = 'custom';
                        customOption.textContent = 'Custom Date Range';
                        customOption.selected = true;
                        timeframe.appendChild(customOption);
                    }
                }
            });

            endDate.addEventListener('change', function() {
                if (this.value && startDate.value) {
                    timeframe.value = 'custom';
                    // Add custom option if it doesn't exist
                    if (!timeframe.querySelector('option[value="custom"]')) {
                        const customOption = document.createElement('option');
                        customOption.value = 'custom';
                        customOption.textContent = 'Custom Date Range';
                        customOption.selected = true;
                        timeframe.appendChild(customOption);
                    }
                }
            });
        }

        // Enhanced button interactions
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Smooth scrolling for anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Utility function to show notifications
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Enhanced delete confirmations
    function confirmDelete(message = 'Are you sure you want to delete this item?') {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Confirm Delete</h5>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            modal.querySelector('#confirmDeleteBtn').addEventListener('click', () => {
                resolve(true);
                bootstrapModal.hide();
                modal.remove();
            });

            modal.addEventListener('hidden.bs.modal', () => {
                resolve(false);
                modal.remove();
            });
        });
    }

    // Performance optimization: Lazy load images
    function initializeLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    // Initialize lazy loading when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeLazyLoading);
    } else {
        initializeLazyLoading();
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + F for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('#mujeebframe, #muftiframe');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to close modals
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal.show');
            openModals.forEach(modal => {
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                if (bootstrapModal) {
                    bootstrapModal.hide();
                }
            });
        }
    });
</script>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-scripts.blade.php ENDPATH**/ ?>