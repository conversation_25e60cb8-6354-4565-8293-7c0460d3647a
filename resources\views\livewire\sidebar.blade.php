<div>

@props(['activePage'])

<style>
    .custom-active {
        color: #fff;
        background-color: #3490dc;
    }
</style>
<aside
    class="sidenav navbar navbar-vertical navbar-expand-xs border-0 border-radius-xl my-3 fixed-start ms-3   bg-gradient-dark"
    id="sidenav-main">
    <div class="sidenav-header">
        <i class="fas fa-times p-3 cursor-pointer text-white opacity-5 position-absolute end-0 top-0 d-none d-xl-none"
            aria-hidden="true" id="iconSidenav"></i>
        <a class="navbar-brand m-0 d-flex text-wrap align-items-center" href=" {{ route('dashboard') }} ">
            <img src="{{ asset('assets') }}/img/logo-ct.png" class="navbar-brand-img h-100" alt="main_logo">
            <span class="ms-3 font-weight-bold text-white">Fatawa Checking Management System</span>
        </a>
    </div>
     
    {{-- @php
    dd($<PERSON><PERSON><PERSON><PERSON><PERSON>,$Mujeeb)
@endphp --}}
    <hr class="horizontal light mt-0 mb-2">
    <div class="  w-auto  max-height-vh-100" id="sidenav-collapse-main">
        <ul class="navbar-nav">
            @php
                    $userRoles = Auth::user()->roles->pluck('name')->toArray();
                    // dd($userRoles);
                    @endphp
            @if(!in_array('Mahlenazar', $userRoles))
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'dashboard' ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('dashboard') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">dashboard</i>
                    </div>
                    <span class="nav-link-text ms-1">Dashboard</span>
                </a>
            </li>
            @if(request()->is('remaining-fatawa*'))     
                @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white {{ request()->routeIs('remaining-fatawa') || request()->route('darulifta') ? 'custom-active' : '' }}"
               href="{{ route('remaining-fatawa') }}">
                <span class="nav-link-text ms-1">Remaining Fatawa</span>
            </a> 
        </li>
        <ul>
            @foreach(['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'] as $darulifta)
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == $darulifta ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('remaining-fatawa', $darulifta) }}">
                    <span class="nav-link-text ms-1">{{ $darulifta }}</span>
                </a>
            </li>
            @endforeach
        </ul>
    @endif
@endif
@if(request()->is('recived-fatawa*'))     
                @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white {{ request()->routeIs('recived-fatawa') || request()->route('darulifta') ? 'custom-active' : '' }}"
               href="{{ route('recived-fatawa') }}">
                <span class="nav-link-text ms-1">Recived Fatawa</span>
            </a> 
        </li>
        <ul>
            @foreach(['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'] as $darulifta)
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == $darulifta ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('recived-fatawa', $darulifta) }}">
                    <span class="nav-link-text ms-1">{{ $darulifta }}</span>
                </a>
            </li>
            @endforeach
        </ul>
    @endif
@endif
@if(request()->is('mahlenazar*'))     
                @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->

<li class="nav-item">
            <a class="nav-link text-white {{ request()->routeIs('mahlenazar') || request()->route('darulifta') ? 'custom-active' : '' }}"
               href="{{ route('mahlenazar') }}">
                <span class="nav-link-text ms-1">Mahlenazar Fatawa</span>
            </a> 
        </li>
        <ul>
            @foreach(['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'] as $darulifta)
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == $darulifta ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('mahlenazar', $darulifta) }}">
                    <span class="nav-link-text ms-1">{{ $darulifta }}</span>
                </a>
            </li>
            @endforeach
        </ul>
    @endif
@endif
@if(request()->is('sending1*'))     
                @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->

<li class="nav-item">

                                                             
                                                             <a class="nav-link text-white {{ $activePage == 'send' ? ' active bg-gradient-primary' : '' }}"
                                                                 href="{{ route('store.selected.values') }}">
                                                             
                                 
                                                                 <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                                     <i class="material-icons opacity-10">receipt_long</i>
                                                                 </div>
                                                                 <span class="nav-link-text ms-1">Sending Fatawa</span>
                                                             </a>
            </li>
    @endif
@endif
@if(request()->is('reciption-fatawa*'))     
                @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white {{ request()->routeIs('reciption-fatawa') || request()->route('darulifta') ? 'custom-active' : '' }}"
               href="{{ route('reciption-fatawa') }}">
                <span class="nav-link-text ms-1">Recived Fatawa</span>
            </a> 
        </li>
        <ul>
            @foreach(['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'] as $darulifta)
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == $darulifta ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('reciption-fatawa', $darulifta) }}">
                    <span class="nav-link-text ms-1">{{ $darulifta }}</span>
                </a>
            </li>
            @endforeach
        </ul>
    @endif
@endif
@if(request()->is('ok-fatawa*'))     
                @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))
                    <!-- User has the 'Admin' role -->
                    <!-- Additional menu items for Admin -->
                    <!-- Add this to your sidebar HTML -->
                    
<li class="nav-item">
            <a class="nav-link text-white {{ request()->routeIs('ok-fatawa') || request()->route('darulifta') ? 'custom-active' : '' }}"
               href="{{ route('ok-fatawa') }}">
                <span class="nav-link-text ms-1">Ok Fatawa</span>
            </a> 
        </li>
        <ul>
            @foreach(['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'] as $darulifta)
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == $darulifta ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('ok-fatawa', $darulifta) }}">
                    <span class="nav-link-text ms-1">{{ $darulifta }}</span>
                </a>
            </li>
            @endforeach
        </ul>
    @endif
@endif

                @if(count(Auth::user()->roles) < 2)
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == 'reciption' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('question.index') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">group</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Reciption Que</span>
                                </a>
                                <a class="nav-link text-white {{ $activePage == 'mujeeb' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('m_question.index') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">account_circle</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Mujeeb Assign Que</span>
                                </a>
                            @endif  
                @if(count(Auth::user()->roles) > 1)
                            
                @php
    $urlrecived = explode('/', request()->path());
    $lasturlrec = last($urlrecived);

    // Check if $lasturlrec is not in the specified values, set it to null
    $validValues = ['noorulirfan', 'faizaneajmair', 'gulzahretaiba', 'iqtisad'];
    $lasturlrec = (!in_array($lasturlrec, $validValues)) ? null : $lasturlrec;
@endphp
                <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == $lasturlrec ? 'custom-active' : '' }}"
                                    href="{{ route('recivedNor') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Recived Fatawa</span>
                                </a>
                            </li>
                @endif                              
                @if(count(Auth::user()->roles) > 1)
                    @if(request()->is('noorulirfan','faizaneajmair','gulzahretaiba','iqtisad'))
                <ul>
                
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == 'noorulirfan' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('recivedNor') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10"></i>
                                    </div>
                                    <span class="nav-link-text ms-1">Noorulirfan</span>
                                </a>
                            </li>
                                <li class="nav-item">
                                                             
                                    <a class="nav-link text-white {{ $activePage == 'faizaneajmair' ? ' active bg-gradient-primary' : '' }}"
                                        href="{{ route('recivedFaj') }}">
                                    
        
                                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                            <i class="material-icons opacity-10"></i>
                                        </div>
                                        <span class="nav-link-text ms-1">Fazian-e-Ajmair</span>
                                    </a>
                                </li>
                                    <li class="nav-item">
                                                             
                                        <a class="nav-link text-white {{ $activePage == 'gulzahretaiba' ? ' active bg-gradient-primary' : '' }}"
                                            href="{{ route('recivedGul') }}">
                                        
            
                                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                <i class="material-icons opacity-10"></i>
                                            </div>
                                            <span class="nav-link-text ms-1">Gulzahr-e-Taiba</span>
                                        </a>
                                    </li>
                                        <li class="nav-item">
                                                             
                                            <a class="nav-link text-white {{ $activePage == 'iqtisad' ? ' active bg-gradient-primary' : '' }}"
                                                href="{{ route('recivedIec') }}">
                                            
                
                                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                    <i class="material-icons opacity-10"></i>
                                                </div>
                                                <span class="nav-link-text ms-1">Markaz-ul-Iqtsiaat</span>
                                            </a>
                                        </li>
                </ul>
                    @endif
                @else
                
                            

                
                    {{-- @if(in_array('Noorulirfan', $userRoles) --}}
{{--                     
                    @if(count(Auth::user()->roles) > 1)
                    <li class="nav-item dropdown">
                        <a class="nav-link text-white {{ $activePage == 'check' ? ' active bg-gradient-primary' : '' }}" href="#" id="recivedFatawaDropdown" role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                <i class="material-icons opacity-10">sticky_note_2</i>
                            </div>
                            <span class="nav-link-text ms-1">Recived Fatawa</span>
                        </a>
                        <div class="dropdown-menu" aria-labelledby="recivedFatawaDropdown">
                            
                            <a class="dropdown-item" href="{{ route('recivedNor') }}">Noorulirfan</a>
                            <a class="dropdown-item" href="{{ route('recivedFaj') }}">Faizan-e-Ajmair</a>
                            <a class="dropdown-item" href="{{ route('recivedGul') }}">Gulzhar-e-Taiba</a>
                            <a class="dropdown-item" href="{{ route('recivedIec') }}">Markaz-ul-Iqtisad</a>
                            
                        </div>
                    </li>
                    
                    @else --}}
                    
                       
                        <!-- Checking Fatawa Link -->
                        <li class="nav-item">
                            @if(in_array('Noorulirfan', $userRoles)
                            )  
                            <a class="nav-link text-white {{ $activePage == 'nor' ? ' active bg-gradient-primary' : '' }}"
                            href="{{ route('recivedNor','mufti_ali_asghar') }}">
                            @elseif (in_array('Faizan-e-Ajmair', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'faj' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedFaj') }}">
                            @elseif (in_array('Gulzar-e-Taiba', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'gul' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedGul') }}">
                            @elseif (in_array('Markaz-ul-Iqtisaad', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'iec' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedIec') }}">
                                @endif 

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    <i class="material-icons opacity-10">receipt_long</i>
                                </div>
                                <span class="nav-link-text ms-1">Sent Fatawa</span>
                            </a>
                        </li>

                        <ul>
                            @if(in_array('Noorulirfan', $userRoles)
                            )  
                            <a class="nav-link text-white {{ $activePage == 'nor' ? ' active bg-gradient-primary' : '' }}"
                            href="{{ route('recivedNor','mufti_ali_asghar') }}">
                            @elseif (in_array('Faizan-e-Ajmair', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'faj' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedFaj') }}">
                            @elseif (in_array('Gulzar-e-Taiba', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'gul' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedGul') }}">
                            @elseif (in_array('Markaz-ul-Iqtisaad', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'iec' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedIec') }}">
                                @endif 

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    <i class="material-icons opacity-10">receipt_long</i>
                                </div>
                                <span class="nav-link-text ms-1">Mufti Ali Asghar Sahib</span>
                            </a>
                        </ul>
                        <ul>
                            @if(in_array('Noorulirfan', $userRoles)
                            )  
                            <a class="nav-link text-white {{ $activePage == 'nor' ? ' active bg-gradient-primary' : '' }}"
                            href="{{ route('recivedNor','sayed_masood') }}">
                            @elseif (in_array('Faizan-e-Ajmair', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'faj' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedFaj') }}">
                            @elseif (in_array('Gulzar-e-Taiba', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'gul' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedGul') }}">
                            @elseif (in_array('Markaz-ul-Iqtisaad', $userRoles))
                            <a class="nav-link text-white {{ $activePage == 'iec' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('recivedIec') }}">
                                @endif 

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    <i class="material-icons opacity-10">receipt_long</i>
                                </div>
                                <span class="nav-link-text ms-1">Sayed Masood Sahib</span>
                            </a>
                        </ul>
                        @endif
                        
                        @if(in_array('Checker', $userRoles))
                            
                @php
    $urlrecived = explode('/', request()->path());
    $lasturlrec = last($urlrecived);

    // Check if $lasturlrec is not in the specified values, set it to null
    $validValues = ['noorulirfan/mufti_ali_asghar', 'faizaneajmair/mufti_ali_asghar', 'gulzahretaiba/mufti_ali_asghar', 'iqtisad/mufti_ali_asghar'];
    $lasturlrec = (!in_array($lasturlrec, $validValues)) ? null : $lasturlrec;
@endphp
                <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == $lasturlrec ? 'custom-active' : '' }}"
                                    href="{{ route('recivedNor','mufti_ali_asghar') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Sent To Mufti Sahib</span>
                                </a>
                            </li>
                @endif                              
                @if(count(Auth::user()->roles) > 1)
                    @if(request()->is('noorulirfan/mufti_ali_asghar','faizaneajmair/mufti_ali_asghar','gulzahretaiba/mufti_ali_asghar','iqtisad/mufti_ali_asghar'))
                <ul>
                
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == 'noorulirfan' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('recivedNor','mufti_ali_asghar') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10"></i>
                                    </div>
                                    <span class="nav-link-text ms-1">Noorulirfan</span>
                                </a>
                            </li>
                                <li class="nav-item">
                                                             
                                    <a class="nav-link text-white {{ $activePage == 'faizaneajmair' ? ' active bg-gradient-primary' : '' }}"
                                        href="{{ route('recivedFaj','mufti_ali_asghar') }}">
                                    
        
                                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                            <i class="material-icons opacity-10"></i>
                                        </div>
                                        <span class="nav-link-text ms-1">Fazian-e-Ajmair</span>
                                    </a>
                                </li>
                                    <li class="nav-item">
                                                             
                                        <a class="nav-link text-white {{ $activePage == 'gulzahretaiba' ? ' active bg-gradient-primary' : '' }}"
                                            href="{{ route('recivedGul','mufti_ali_asghar') }}">
                                        
            
                                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                <i class="material-icons opacity-10"></i>
                                            </div>
                                            <span class="nav-link-text ms-1">Gulzahr-e-Taiba</span>
                                        </a>
                                    </li>
                                        <li class="nav-item">
                                                             
                                            <a class="nav-link text-white {{ $activePage == 'iqtisad' ? ' active bg-gradient-primary' : '' }}"
                                                href="{{ route('recivedIec','mufti_ali_asghar') }}">
                                            
                
                                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                    <i class="material-icons opacity-10"></i>
                                                </div>
                                                <span class="nav-link-text ms-1">Markaz-ul-Iqtsiaat</span>
                                            </a>
                                        </li>
                </ul>
                    @endif
                        @endif                              
                        @if(count(Auth::user()->roles) > 1)

                        @php
                $urlchecked = explode('/', request()->path());
                $lasturlcheck = last($urlchecked);
                $validValuescheck = ['checkednor','checkedfaj','checkedgul','checkedIec'];
                $lasturlcheck = (!in_array($lasturlcheck, $validValuescheck)) ? null : $lasturlcheck;

                @endphp
                        <li class="nav-item">
                        <a class="nav-link text-white {{ $activePage == $lasturlcheck ? 'custom-active' : '' }}"
                                href="{{ route('checkedNor') }}">
                            

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    <i class="material-icons opacity-10">verified_user</i>
                                </div>
                                <span class="nav-link-text ms-1">Checked Fatawa</span>
                            </a>
                        </li>
                        @if(request()->is('checkednor','checkedfaj','checkedgul','checkedIec'))
                        <ul>
                        <li class="nav-item">
                                                             
                            <a class="nav-link text-white {{ $activePage == 'checkednor' ? ' active bg-gradient-primary' : '' }}"
                                href="{{ route('checkedNor') }}">
                            

                                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                    
                                </div>
                                <span class="nav-link-text ms-1">Noorulirfan</span>
                            </a>
                        </li>
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == 'checkedfaj' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('checkedFaj') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        
                                    </div>
                                    <span class="nav-link-text ms-1">Faizan-e-Ajmair</span>
                                </a>
                            </li>
                                <li class="nav-item">
                                                             
                                    <a class="nav-link text-white {{ $activePage == 'checkedgul' ? ' active bg-gradient-primary' : '' }}"
                                        href="{{ route('checkedGul') }}">
                                    
        
                                        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                            
                                        </div>
                                        <span class="nav-link-text ms-1">Gulzahr-e-Taiba</span>
                                    </a>
                                </li>
                                    <li class="nav-item">
                                                             
                                        <a class="nav-link text-white {{ $activePage == 'checkedIec' ? ' active bg-gradient-primary' : '' }}"
                                            href="{{ route('checkedIec') }}">
                                        
            
                                            <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                                
                                            </div>
                                            <span class="nav-link-text ms-1">Markaz-ul-Iqtisad</span>
                                        </a>
                                    </li>
                            
                        </ul>
                           @endif
                           @else
                            <!-- Checking Fatawa Link -->
                            <li class="nav-item">
                                @if(in_array('Noorulirfan', $userRoles)
                                )  
                                <a class="nav-link text-white {{ $activePage == 'checknor' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('checkedNor') }}">
                                @elseif (in_array('Faizan-e-Ajmair', $userRoles))
                                <a class="nav-link text-white {{ $activePage == 'checkfaj' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('checkedFaj') }}">
                                    @elseif (in_array('Gulzar-e-Taiba', $userRoles))
                                <a class="nav-link text-white {{ $activePage == 'checkgul' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('checkedGul') }}">
                                    @elseif (in_array('Markaz-ul-Iqtisaad', $userRoles))
                                <a class="nav-link text-white {{ $activePage == 'checkiec' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('checkedIec') }}">
                                    @endif
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">verified_user</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Checked Fatawa</span>
                                </a>
                            </li>
                            @endif
                            @if(count(Auth::user()->roles) > 1)
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == 'send' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('store.selected.values') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Sending Fatawa</span>
                                </a>
                            @endif
                            @if(count(Auth::user()->roles) < 2)
                            <li class="nav-item">
                                                             
                                <a class="nav-link text-white {{ $activePage == 'deliver' ? ' active bg-gradient-primary' : '' }}"
                                    href="{{ route('not_deliver') }}">
                                
    
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">receipt_long</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Not Deliver To Sayel</span>
                                </a>
                            @endif    
                            </li> 
                            @endif
                            <li class="nav-item">
                                <a class="nav-link text-white {{ $activePage == 'mahlenazar' ? 'active bg-gradient-primary' : '' }}"
                                    href="{{ route('mahlenazar') }}">
                                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                                        <i class="material-icons opacity-10">dashboard</i>
                                    </div>
                                    <span class="nav-link-text ms-1">Mahl-e-Nazar</span>
                                </a>
                            </li>
                            {{-- @if(request()->is('dashboard','dashboard/Noorulirfan','dashboard/Faizan-e-Ajmair','dashboard/Gulzar-e-Taiba','dashboard/Markaz-ul-Iqtisaad'))      --}}
                                {{-- @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray())) --}}
                                    <!-- User has the 'Admin' role -->
                                    <!-- Additional menu items for Admin -->
                                    <!-- Add this to your sidebar HTML -->
                                    {{-- @php
                    $urlSegments = explode('/', request()->path());
                    $lastName = last($urlSegments);
                @endphp
                                    <li class="nav-item">
                                        <div id="clickedCardName" class="nav-link text-white {{ $activePage == $lastName ? 'custom-active' : '' }}">
                         
                                            
                                            <span class="nav-link-text ms-1">Remaining Status</span>
                                            </div>
                                            
                                        
                                    </li> --}}
                                    
                                   
                                    <ul>
                                        @php
                                            $urlPath = request()->path();
                                            $urlPathWithoutSlash = explode('/', $urlPath, 2)[0];
                                            // echo "$urlPathWithoutSlash";
                                        @endphp


                                        @if($urlPathWithoutSlash == 'mahlenazar')

                                        
                                        @if (is_array($mahl_e_mujeeb))
    @php
        $allowedRoles = ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'];
        $userRoles = Auth::user()->roles->pluck('name')->toArray();
        
    @endphp

    @foreach ($mahl_e_mujeeb as $daruliftaName => $senders)
    
    {{-- @php
        dd($daruliftaName,$userRoles);
    @endphp
     --}}
        {{-- Only display if $daruliftaName matches the user's role --}}
        @if (in_array($daruliftaName, $userRoles))
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == $daruliftaName ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('mahlenazar', ['role' => $daruliftaName]) }}">
                    <span class="nav-link-text ms-1">{{ $daruliftaName }}</span>
                </a>
                @if (isset($senders) && is_array($senders))
                    <ul>
                        @foreach ($senders as $sender => $m_d_mujeeeb)
                            <li class="nav-item">
                                {{-- Additional condition to check if the user has one of the allowed roles --}}
                                @if (in_array($daruliftaName, $allowedRoles))
                                    <a class="nav-link text-white {{ $activePage == $sender ? 'active bg-gradient-primary' : '' }}"
                                        href="{{ route('mahlenazar', ['role' => $daruliftaName, 'fatwa_no' => $sender]) }}">
                                        <span class="nav-link-text ms-1">{{ $sender }}</span>
                                    </a>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                @endif
            </li>
        @endif
        
    @endforeach
@endif   


@endif
                                    </ul>
<li class="nav-item">
    <a class="nav-link text-white {{ $activePage == 'ok-fatawa' ? 'active bg-gradient-primary' : '' }}"
        href="{{ route('ok-fatawa') }}">
        <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
            <i class="material-icons opacity-10">check_circle</i>
        </div>
        <span class="nav-link-text ms-1">Ok Fatawa</span>
    </a>
</li>
        <!-- User has the 'Admin' role -->
        <!-- Additional menu items for Admin -->
        <!-- Add this to your sidebar HTML -->

        

        <ul>
            @if(request()->is('ok-fatawa','ok-fatawa/Noorulirfan','ok-fatawa/Faizan-e-Ajmair','ok-fatawa/Gulzar-e-Taiba','ok-fatawa/Markaz-ul-Iqtisaad'))     
    @if(in_array('Admin', auth()->user()->roles->pluck('name')->toArray()))

            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'Noorulirfan' ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('ok-fatawa', 'Noorulirfan') }}">
                    <span class="nav-link-text ms-1">Noorulirfan</span>
                </a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'Faizan-e-Ajmair' ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('ok-fatawa', 'Faizan-e-Ajmair') }}">
                    <span class="nav-link-text ms-1">Faizan-e-Ajmair</span>
                </a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'Gulzar-e-Taiba' ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('ok-fatawa', 'Gulzar-e-Taiba') }}">
                    <span class="nav-link-text ms-1">Gulzar-e-Taiba</span>
                </a>
            </li>
    
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'Markaz-ul-Iqtisaad' ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('ok-fatawa', 'Markaz-ul-Iqtisaad') }}">
                    <span class="nav-link-text ms-1">Markaz-ul-Iqtisaad</span>
                </a>
            </li>
        </ul>
    @endif
@endif
                                    {{-- <ul>
                                        
                                        <li class="nav-item">
                                        <a class="nav-link text-white {{ $activePage == 'Noorulirfan' ? 'active bg-gradient-primary' : '' }}"
                                            href="{{ route('mahlenazar', ['role' => 'Noorulirfan']) }}">
                                            <span class="nav-link-text ms-1">Noorulirfan</span>
                                        </a>
                                        
                                    </li>
                                        <ul>
                                            <li class="nav-item">
                                                <a class="nav-link text-white {{ $activePage == 'Abid Madani' ? 'active bg-gradient-primary' : '' }}"
                                                    href="{{ route('mahlenazar', ['role' => 'Noorulirfan', 'fatwa_no' => 'Abid Madani']) }}">
                                                    <span class="nav-link-text ms-1">Abid Madani</span>
                                                </a>
                                            </li>
                                        </ul>

                                
                                        <li class="nav-item">
                                            <a class="nav-link text-white {{ $activePage == 'Faizan-e-Ajmair' ? 'active bg-gradient-primary' : '' }}"
                                                href="{{ route('mahlenazar', 'Faizan-e-Ajmair') }}">
                                                <span class="nav-link-text ms-1">Faizan-e-Ajmair</span>
                                            </a>
                                        </li>
                                
                                        <li class="nav-item">
                                            <a class="nav-link text-white {{ $activePage == 'Gulzar-e-Taiba' ? 'active bg-gradient-primary' : '' }}"
                                                href="{{ route('mahlenazar', 'Gulzar-e-Taiba') }}">
                                                <span class="nav-link-text ms-1">Gulzar-e-Taiba</span>
                                            </a>
                                        </li>
                                
                                        <li class="nav-item">
                                            <a class="nav-link text-white {{ $activePage == 'Markaz-ul-Iqtisaad' ? 'active bg-gradient-primary' : '' }}"
                                                href="{{ route('mahlenazar', 'Markaz-ul-Iqtisaad') }}">
                                                <span class="nav-link-text ms-1">Markaz-ul-Iqtisaad</span>
                                            </a>
                                        </li>
                                    </ul> --}}
                                
                            
                            



                 
                    <!-- Other sidebar links go here -->
                
                
            {{-- <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'recfatwa' ? ' active bg-gradient-primary' : '' }} "
                    href="{{ route('recfatwa') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">file_copy</i>
                    </div>
                    <span class="nav-link-text ms-1">Recived Fatawa</span>
                </a>
            </li> --}}
            @if(in_array('Manager ', $userRoles)
                            )  
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'appointment' ? ' active bg-gradient-primary' : '' }} "
                    href="{{ route('appointment') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">receipt_long</i>
                    </div>
                    <span class="nav-link-text ms-1">Appointment</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'schedule' ? ' active bg-gradient-primary' : '' }} "
                    href="{{ route('schedule') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">speaker_notes</i>
                    </div>
                    <span class="nav-link-text ms-1">Schedule</span>
                </a>
            </li>
        </li>
        {{-- <li class="nav-item">
            <a class="nav-link text-white {{ $activePage == 'task' ? ' active bg-gradient-primary' : '' }} "
                href="{{ route('task') }}">
                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="material-icons opacity-10">task</i>
                </div>
                <span class="nav-link-text ms-1">Task</span>
            </a>
        </li> --}}
        <li class="nav-item">
            <a class="nav-link text-white {{ $activePage == 'notice' ? ' active bg-gradient-primary' : '' }} "
                href="{{ route('notice') }}">
                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="material-icons opacity-10">sticky_note_2</i>
                </div>
                <span class="nav-link-text ms-1">Notice Board</span>
            </a>
        </li>
        

        {{-- <li class="nav-item">
            <a class="nav-link text-white {{ $activePage == 'check' ? ' active bg-gradient-primary' : '' }} "
                href="{{ route('check') }}">
                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="material-icons opacity-10">verified_user</i>
                </div>
                <span class="nav-link-text ms-1">Checking Fatawa</span>
            </a>
        </li> --}}
        <li class="nav-item">
            <a class="nav-link text-white {{ $activePage == 'otheri' ? ' active bg-gradient-primary' : '' }} "
                href="{{ route('otheri') }}">
                <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                    <i class="material-icons opacity-10">attach_file</i>
                </div>
                <span class="nav-link-text ms-1">Other Ifta</span>
            </a>
        </li>
        
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'virtual-reality' ? ' active bg-gradient-primary' : '' }}  "
                    href="{{ route('virtual-reality') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">view_in_ar</i>
                    </div>
                    <span class="nav-link-text ms-1">Virtual Reality</span>
                </a>
            </li>
            @endif
            @if(in_array('Admin ', $userRoles)
            )
            <li class="nav-item mt-3">
                <h6 class="ps-4 ms-2 text-uppercase text-xs text-white font-weight-bolder opacity-8">Management</h6>
            </li>
            
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'darulifta' ? 'active bg-gradient-primary' : '' }} "
                    href="{{ route('darulifta') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1.2rem;" class="fas fa-user-circle ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Darulifta</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'mujeeb' ? 'active bg-gradient-primary' : '' }}"
                    href="{{ route('mujeeb.store') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1.2rem;" class="fas fa-user-circle ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Mujeeb</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'user-profile' ? 'active bg-gradient-primary' : '' }} "
                    href="{{ route('user-profile') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1.2rem;" class="fas fa-user-circle ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">User Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'user-management' ? ' active bg-gradient-primary' : '' }} "
                    href="{{ route('user-management') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1rem;" class="fas fa-lg fa-list-ul ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Management</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'performance-holidays' ? ' active bg-gradient-primary' : '' }} "
                    href="{{ route('performance-holidays') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1rem;" class="fas fa-lg fa-calendar-alt ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Performance Holidays</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'locked-accounts' ? ' active bg-gradient-primary' : '' }} "
                    href="{{ route('locked-accounts') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i style="font-size: 1rem;" class="fas fa-lg fa-lock ps-2 pe-2 text-center"></i>
                    </div>
                    <span class="nav-link-text ms-1">Locked Accounts</span>
                </a>
            </li>
            <li class="nav-item mt-3">
                <h6 class="ps-4 ms-2 text-uppercase text-xs text-white font-weight-bolder opacity-8">Account pages</h6>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white {{ $activePage == 'profile' ? ' active bg-gradient-primary' : '' }}  "
                    href="{{ route('profile') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">person</i>
                    </div>
                    <span class="nav-link-text ms-1">Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white " href="{{ route('static-sign-in') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">login</i>
                    </div>
                    <span class="nav-link-text ms-1">Sign In</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link text-white " href="{{ route('static-sign-up') }}">
                    <div class="text-white text-center me-2 d-flex align-items-center justify-content-center">
                        <i class="material-icons opacity-10">assignment</i>
                    </div>
                    <span class="nav-link-text ms-1">Sign Up</span>
                </a>
            </li>
            @endif
        </ul>
    </div>
    <div class="sidenav-footer position-absolute w-0 bottom-0 ">
        
    </div>
</aside>


</div>
