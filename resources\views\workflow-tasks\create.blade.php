@extends('layouts.user_type.auth')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Create New Task</h5>
                            <p class="text-sm mb-0">Assign a new task to a user</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <a href="{{ route('workflow-tasks.index') }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to Tasks
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Form -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Task Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('workflow-tasks.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" name="title" 
                                           class="form-control @error('title') is-invalid @enderror"
                                           value="{{ old('title') }}"
                                           placeholder="Enter task title">
                                    @error('title') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Type *</label>
                                    <select name="type" class="form-select @error('type') is-invalid @enderror">
                                        <option value="">Select Type</option>
                                        <option value="daily" {{ old('type') === 'daily' ? 'selected' : '' }}>Daily</option>
                                        <option value="weekly" {{ old('type') === 'weekly' ? 'selected' : '' }}>Weekly</option>
                                        <option value="monthly" {{ old('type') === 'monthly' ? 'selected' : '' }}>Monthly</option>
                                        <option value="one_time" {{ old('type') === 'one_time' ? 'selected' : '' }}>One Time</option>
                                    </select>
                                    @error('type') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select name="department_id" id="department_select" class="form-select @error('department_id') is-invalid @enderror">
                                        <option value="">Select Department</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}" {{ old('department_id') == $department->id ? 'selected' : '' }}>
                                                {{ $department->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select name="assigned_to" id="assigned_to_select" class="form-select @error('assigned_to') is-invalid @enderror">
                                        <option value="">Select User</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" {{ old('assigned_to') == $user->id ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to') <div class="invalid-feedback">{{ $message }}</div> @enderror

                                    <!-- Department Team Members -->
                                    <div id="department_team_info" class="mt-2" style="display: none;">
                                        <div class="card border">
                                            <div class="card-body p-2">
                                                <h6 class="text-sm mb-2">Department Team Members:</h6>
                                                <div id="team_members_list"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Priority *</label>
                                    <select name="priority" class="form-select @error('priority') is-invalid @enderror">
                                        <option value="">Select Priority</option>
                                        <option value="1" {{ old('priority') == '1' ? 'selected' : '' }}>Low</option>
                                        <option value="2" {{ old('priority') == '2' ? 'selected' : '' }}>Medium</option>
                                        <option value="3" {{ old('priority') == '3' ? 'selected' : '' }}>High</option>
                                    </select>
                                    @error('priority') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Due Date</label>
                                    <input type="date" name="due_date" 
                                           class="form-control @error('due_date') is-invalid @enderror"
                                           value="{{ old('due_date') }}">
                                    @error('due_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea name="description" 
                                      class="form-control @error('description') is-invalid @enderror"
                                      rows="4"
                                      placeholder="Describe the task details, requirements, and expectations...">{{ old('description') }}</textarea>
                            @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group text-end">
                            <a href="{{ route('workflow-tasks.index') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i>&nbsp;&nbsp;Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i>&nbsp;&nbsp;Create Task
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
