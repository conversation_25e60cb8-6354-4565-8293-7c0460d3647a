
<div>
    <x-layout bodyClass="g-sidenav-show  bg-gray-200">
        <x-navbars.sidebar activePage="{{ request()->route('role') ?? 'ok-fatawa' }}"></x-navbars.sidebar>
        <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
            <!-- Navbar -->
            <x-navbars.navs.auth titlePage="Ok Fatawa"></x-navbars.navs.auth>
            
            @php
    
            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        @endphp
        <div wire:loading>
        <div style="display: flex; justify-content: center; align-items: center; background-color: black; 
        position: fixed; top: 0px; left: 0px; z-index: 9999; width: 100%; height: 100%;
        opacity: .75;">
                
                <div class="la-ball-spin la-2x">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
                </div>   
        </div>

            <div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
    <div class="card mb-4" id="big-card-10" style="background-color: #FFFFCC; "> <!-- Added "mb-4" for margin at the bottom -->
        <div class="card-body">
            <h2 class="card-title">Darulifta And Mujeeb Summary of Ok Fatawa</h2>       
            <select wire:model.live="selectedmufti" id="muftiframe">
                    <option value="all" selected>All</option>
                    <option value="mufti_ali_asghar">Mufti Ali Asghar</option>
                    <option value="sayed_masood">Sayed Masood</option>
                </select>
                <select wire:model.live="selectedTimeFrame" id="timeframe">
                <option value="this_month" selected>This Month</option>
                <option value="last_month" >Last Month</option>
                    <option value="other">Other</option>
                </select>
                </div>
            <br>
            @if ($selectedTimeFrame === 'other')
            <div class="calendar-style">
    @foreach ($datefilter as $data)
        @php
            $monthName = DateTime::createFromFormat('!m', $data->month)->format('F');
        @endphp
        <div class="month-year">
            <input type="checkbox" wire:model.live="selectedMonths" id="month-{{ $data->year }}-{{ $data->month }}" value="{{ $data->year }}-{{ $data->month }}">
            <label for="month-{{ $data->year }}-{{ $data->month }}">
                {{ $monthName }} {{ $data->year }}
            </label>
        </div>
    @endforeach
</div>
            @endif
            <table class="table">
                    <thead>
                        <tr>
                            <th>Darulifta</th>
                            <th>Reciption Fatawa Date</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($daruliftaNames as $daruliftaName)
                            @if(isset($okFatawa[$daruliftaName]))
                                @php
                                    $daruliftaTotalCounts = 0; // Initialize a variable to store the total count for the current $daruliftaName
                                    
                                    
                                @endphp
                
                                @foreach($mujeeb as $mailfolderDate)
                                    @if(isset($okFatawa[$daruliftaName][$mailfolderDate]))
                                        @foreach($okFatawa[$daruliftaName][$mailfolderDate] as $file)
                                            @php
                                                $folder = $file->sender;
                                                $folderCounts[$daruliftaName][$folder] = isset($folderCounts[$daruliftaName][$folder]) ? $folderCounts[$daruliftaName][$folder] + 1 : 1;
                                                $daruliftaTotalCounts++;
                                                $totalCounts++;
                                            @endphp
                                        @endforeach
                                    @endif
                                @endforeach
                
                                <tr>
                                    <td>
                                        <a href="{{ route('ok-fatawa', ['darulifta' => $daruliftaName]) }}">{{ $daruliftaName }}</a>
                                    </td>
                                    <td>
                                        @php
                                            $foldercount = 0;
                                        @endphp
                                        @foreach ($folderCounts[$daruliftaName] as $folder => $count)
                                        <a href="{{ route('ok-fatawa', ['darulifta' => $daruliftaName, 'mujeebn' => $folder]) }}">
                                            {{ $folder }}</a>({{ $count }}) , 
                                        @php
                                        $foldercount++;    
                                        $overallFolderCount++;
                                        @endphp
                                        @endforeach
                                    </td>
                                    <td>
                                        Fatawa: {{ $daruliftaTotalCounts }} | Mujeeb:{{$foldercount}}
                                    </td>
                                </tr>
                            @endif
                        @endforeach
                    </tbody>
                </table>
                
                <h5>Overall Total Fatawa: {{ $totalCounts }} And Mujeeb: {{ $overallFolderCount }}</h5>
        </div>
    </div>
</div>

            <div class="col-lg-12 col-md-6 mt-1 mb-md-0 mb-4">
                <div class="card z-index-2 mb-4" id="big-card-6" style="background-color: #90d9df;">
                    
                        
                        <div class="card-header pb-0">
                            
                                    <h4>Ok Fatawa</h4>
                                    
                            </div>
                        
                        <div class="card-body px-0 pb-2">
                            {{-- @php
                        $filesToLoop = (count(Auth::user()->roles) > 1) ? $remainingFatawa : $CurrentWeekokMulti;
                    @endphp --}}


                    {{-- @foreach ($filesToLoop as $file) --}}
                    @foreach($daruliftaNames as $daruliftaName)
                    @if(isset($okFatawa[$daruliftaName]))
                    <h5 class="toggle-header toggle-parent"><span class="toggle-arrow">▼</span>{{ $daruliftaName }}</h5>
                    <div class="toggle-content toggle-parent">
                        <div class="table-responsive4">
                            @php
                                    $serialNumber_fl1 = 1; // Initialize the serial number    

                                    $mailfolderDateCount1 = 0;
                                    $colors = ['#F0FFF0', '#F0F8FF', '#FFFFE0', '#FFE4E1', '#F0F8FF', '#F5FFFA', '#E6E6FA', '#FAEBD7', '#ADD8E6']; // Add more colors as needed
                                    @endphp
                            @foreach($mujeeb as $mailfolderDate)
                            @if(isset($okFatawa[$daruliftaName][$mailfolderDate]))
                            @php
                                $mailfolderDateCount1 = count($okFatawa[$daruliftaName][$mailfolderDate]); // Count the occurrences of $mailfolderDate
                            @endphp

                            <div class="card z-index-2 mb-3" style="background-color: {{ $colors[$loop->index % count($colors)] }};">
                                <div  class="data-row" data-rec-date="{{ $mailfolderDate }}" >
                            
                            <h6 class="toggle-header toggle-child d-flex">
                                ({{ $serialNumber_fl1++ }})<span class="toggle-arrow">▼</span>{{ $mailfolderDate }}
                                @foreach ($obtain as $obtains)
                                @if ($mailfolderDate == $obtains->sender)
                                Total({{$obtains->total_score_sum}})
                                @endif
                                @endforeach
                                <span class="ms-auto pr-2">
                                    
                                    Fatawa:({{ $mailfolderDateCount1 }})</span></h6>
                                
                            <div class="toggle-content toggle-child">
                                <table class="table6 align-items-center mb-0">
                                    <thead>
                                        <tr>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                S.No</th>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Fatwa No</th>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Mujeeb</th>
                                            <th
                                            class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Category</th>
                                            <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Checked Folder Date</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Grade No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Early Ok No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Gair Umoni No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Not Tarmim No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Shandar No</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Total No</th>
                                                
                                                {{-- <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Deliver Status</th>
                                                <th
                                                class="text-center text-uppercase text-secondary font-weight-bolder opacity-7" style="width: 5%; white-space: normal;">
                                                Recived To Ok Days</th> --}}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @php
                                    $serialNumber_ch = 1; // Initialize the serial number    
                                    @endphp
                                   @foreach($okFatawa[$daruliftaName][$mailfolderDate] as $file)



                                        <tr>
                                            <td class="align-middle text-center " style="width: 5%; white-space: normal;">
                                                <span class=" font-weight-bold">{{ $serialNumber_ch++ }}</span>
                                            </td>
                                            <td>
                                                <div class="d-flex px-2 py-1">
                                                    
                                                    <div class="d-flex flex-column justify-content-center">
                                                        <h6 class="mb-0 ">{{$file->file_code}}</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="mujeeb-column5"> {{$file->sender}} </span>
                                                                                                    </div>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->category}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->mail_folder_date}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->calculated_grade}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->mahl_e_nazar_score}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->gair_umomi}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->tarmim_value}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->shandar_tahqiqi}} </span>
                                            </td>
                                            <td class="align-middle text-center ">
                                                <span class=" font-weight-bold">{{$file->total_score}} </span>
                                            </td>
                                            
                                            {{-- <td class="align-middle text-center">
                                                @if($file->deliver == 0)
                                                @if($file->not_deliver_reason == null)
                                                    <span class="font-weight-bold text-danger">Not Delivered</span>
                                                @else
                                                <span class="font-weight-bold">{{$file->not_deliver_reason}}</span>
                                                @endif
                                                @else
                                                    
                                                <span class="font-weight-bold text-success">{{$file->deliver_date}}</span>
                                                <br>
                                                @foreach ($q_rec_t as $qrec)
                                                @if ($qrec->ifta_code == $file->file_code)
                                                @php
                                                                $recDate = new \DateTime($qrec->rec_date);
                                                                $deliverdate = new \DateTime($file->deliver_date);
                                                                $daysDifference4 = $deliverdate->diff($recDate)->days;
                                                            @endphp
                                            <span class=" font-weight-bold">{{ $daysDifference4 }} days
                                            </span>    
                                            @endif
                                                    @endforeach
                                                @endif
                                            </td>
                                            <td class="align-middle text-center ">
                                                @foreach ($q_rec_t as $qrec)
                                                    @if ($qrec->ifta_code == $file->file_code)
                                                    
                                                    @php
                                                                $recDate = new \DateTime($qrec->rec_date);
                                                                // $CheckedDate = new \DateTime($file->checked_date);
                                                                $CheckedDate = new \DateTime($file->checked_date ?? $file->mail_folder_date); // Use checked_date if not null, otherwise use mail_folder_date
                                                                $daysDifference5 = $recDate->diff($CheckedDate)->days;
                                                            
                                                            // dd($recDate,$CheckedDate,$daysDifference5)
                                                            @endphp
                                            <span class=" font-weight-bold">{{ $daysDifference5 }} days
                                            </span>    

                                                    
                                                    
                                                    
                                                   
                                                        @endif
                                                        @endforeach
                                            </td> --}}
                                            
                                        </tr>
                                        
                                    @endforeach
                                </tbody>
                            </table>
                            
                        </div>
                    </div>
                </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                    @endif
                @endforeach
            
            </div>
        
   
   
   
   
    <x-footers.auth></x-footers.auth>

</main>
<x-plugins></x-plugins>
</x-layout>

</div>

