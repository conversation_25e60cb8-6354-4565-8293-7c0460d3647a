<?php

namespace App\Livewire;

use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Livewire\Component;

class Dashboard extends Component
{
    public $mailfolderDates;
    public $daruliftaNames;
    public $sendToMufti;
    public $currentmonthrecived;
    public $currentmonthchecked;
    public $totalmahlenazar;
    public $remainfolderDate;
    public $unselectedFiles;
    public $remainingFatawa;
    public $totalQuestionsCount;
    public $reciptDates;
    public $recquetionse;
    public $fatwa_nums = null;
    public $selectedCard = 'remaining_status'; // Default card
    public $remainingData;

    public $selectedTimeFrame = 'all';
    public $selectedTimeFrame1 = 'exclude_ok';
    public $firstDayOfMonth;
    public $lastDayOfMonth;

    // ... add other properties for the queried data

    // protected $listeners = ['updateSelectedTimeFrame'];

    public function mount()
    {
        $this->loadMailFolderDates();
        $this->loadDaruliftaNames();
        $this->loadReciptDates();
        $this->remainingData = $this->selectedCard == 'remaining_status' ? $this->fetchRemainingData() : null;
    }

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->selectedTimeFrame = $timeFrame;
    }
    public function updateSelectedTimeFrame1($timeFrame1)
    {
        $this->selectedTimeFrame1 = $timeFrame1;
    }

    private function loadMailFolderDates()
    {
        $this->mailfolderDates = DB::table('uploaded_files')
            ->select('mail_folder_date')
            ->where('selected', 0)
            ->distinct()
            ->orderBy('mail_folder_date', 'desc')
            ->pluck('mail_folder_date');
    }

    private function loadDaruliftaNames()
    {
        $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name', 'NOT LIKE', '%3btn%')
            ->distinct()
            ->pluck('darulifta_name');
    }
    private function loadReciptDates()
    {
        $this->reciptDates = DB::table('questions')
        ->select('assign_id')
        
        ->distinct()
        ->orderBy('assign_id', 'desc') // Sort in descending order
        ->pluck('assign_id');
    // dd($this->reciptDates);
    }

    public function render()
    {
        $user = Auth::user();
        $firstDayOfMonth = Carbon::now()->startOfMonth();
        $lastDayOfMonth = Carbon::now()->endOfMonth();
        $firstDayOfLastMonth = Carbon::now()->subMonth()->startOfMonth();
        $lastDayOfLastMonth = Carbon::now()->subMonth()->endOfMonth();
// dd($firstDayOfMonth,$lastDayOfMonth,$firstDayOfLastMonth,$lastDayOfLastMonth);

        $this->sendToMufti = DB::table('questions')
        ->select('ifta_code')
        ->whereIn('ifta_code', function ($subquery) {
            $subquery->select(DB::raw('LOWER(file_code)'))
                ->from('uploaded_files');
        })
        ->count();

        if ($this->selectedTimeFrame1 == 'exclude_ok') {
            $this->totalQuestionsCount = DB::table('questions')
                ->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok')
                        ->distinct();
                })
                ->distinct()
                ->count('ifta_code');

                $this->sendToMufti = DB::table('questions')
                ->select('ifta_code')
                ->whereIn('ifta_code', function ($subquery) {
                    $subquery->select(DB::raw('LOWER(file_code)'))
                        ->from('uploaded_files');
                })
                ->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok')
                        ->distinct();
                })
                ->distinct()
                ->count('ifta_code');

        } elseif ($this->selectedTimeFrame1 == 'exclude_ok_this_month') {
            $this->totalQuestionsCount = DB::table('questions')
                ->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok')
                        ->distinct();
                })
                ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfMonth, $lastDayOfMonth])
                ->distinct()
                ->count('ifta_code');

                $this->sendToMufti = DB::table('questions')
                ->select('ifta_code')
                ->whereIn('ifta_code', function ($subquery) {
                    $subquery->select(DB::raw('LOWER(file_code)'))
                        ->from('uploaded_files');
                })
                ->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok')
                        ->distinct();
                })
                ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfMonth, $lastDayOfMonth])
                ->distinct()
                ->count('ifta_code');
        }
        elseif ($this->selectedTimeFrame1 == 'exclude_ok_last_month') {
            $this->totalQuestionsCount = DB::table('questions')
                ->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok')
                        ->distinct();
                })
                ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfLastMonth, $lastDayOfLastMonth])
                ->distinct()
                ->count('ifta_code');

                $this->sendToMufti = DB::table('questions')
                ->select('ifta_code')
                ->whereIn('ifta_code', function ($subquery) {
                    $subquery->select(DB::raw('LOWER(file_code)'))
                        ->from('uploaded_files');
                })
                ->whereNotIn('ifta_code', function ($query) {
                    $query->select('file_code')
                        ->from('uploaded_files')
                        ->where('checked_folder', '=', 'ok')
                        ->distinct();
                })
                ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfLastMonth, $lastDayOfLastMonth])
                ->distinct()
                ->count('ifta_code');
        } elseif ($this->selectedTimeFrame1 == 'all') {
            $this->totalQuestionsCount = DB::table('questions')->count();
            $this->sendToMufti = DB::table('questions')
        ->select('ifta_code')
        ->whereIn('ifta_code', function ($subquery) {
            $subquery->select(DB::raw('LOWER(file_code)'))
                ->from('uploaded_files');
        })
        ->count();
        }

        elseif ($this->selectedTimeFrame1 == 'all_this_month') {
            $this->totalQuestionsCount = DB::table('questions')
            ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfMonth, $lastDayOfMonth])
            ->count();

            $this->sendToMufti = DB::table('questions')
        ->select('ifta_code')
        ->whereIn('ifta_code', function ($subquery) {
            $subquery->select(DB::raw('LOWER(file_code)'))
                ->from('uploaded_files');
        })
        ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfMonth, $lastDayOfMonth])
        ->count();
        }
        elseif ($this->selectedTimeFrame1 == 'all_last_month') {
            $this->totalQuestionsCount = DB::table('questions')
            ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfLastMonth, $lastDayOfLastMonth])
            ->count();

            $this->sendToMufti = DB::table('questions')
        ->select('ifta_code')
        ->whereIn('ifta_code', function ($subquery) {
            $subquery->select(DB::raw('LOWER(file_code)'))
                ->from('uploaded_files');
        })
        ->whereBetween(DB::raw('DATE(rec_date)'), [$firstDayOfLastMonth, $lastDayOfLastMonth])
        ->count();
        }
        // dd($this->sendToMufti,);
            
        // $this->totalQuestionsCount = DB::table('questions')
        // ->join('uploaded_files', function ($join) {
        //     $join->on('questions.ifta_code', '=', 'uploaded_files.file_code')
        //         ->whereRaw('LOWER(uploaded_files.file_code) = LOWER(questions.ifta_code)');
        // })
        // ->whereNotIn(DB::raw('LOWER(uploaded_files.checked_folder)'), ['ok'])
        // ->get();
        
        $this->currentmonthrecived = DB::table('uploaded_files')
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth])
            ->count();

        $this->remainfolderDate = $this->getRemainingFolderData();
        $this->unselectedFiles = $this->getUnselectedFilesData();
        // $this->remainingFatawa = $this->getRemainingFatawa();
        // $this->recquetionse = $this->getReciptionFatwa();
        $reciptionData = $this->selectedCard == 'reciption_status' ? $this->fetchReciptionData() : null;
        

        return view('livewire.dash-board', [
            'mailfolderDates' => $this->mailfolderDates,
            'daruliftaNames' => $this->daruliftaNames,
            'sendToMufti' => $this->sendToMufti,
            'currentmonthrecived' => $this->currentmonthrecived,
            'remainfolderDate' => $this->remainfolderDate,
            'unselectedFiles' => $this->unselectedFiles,
            'remainingFatawa' => $this->remainingFatawa,
            'totalQuestionsCount' => $this->totalQuestionsCount,
            'reciptDatest' => $this->reciptDates,
            'recquetionse' => $this->recquetionse,
            'fatwa_nums' => $this->fatwa_nums,
            'remainingData' => $this->remainingData,
            'reciptionData' => $reciptionData,
            'selectedCard' => $this->selectedCard,
            // ... (other data to be passed to the view)
        ]);
    }

    public function getRemainingFolderData()
    {
        $query = DB::table('uploaded_files')
            ->distinct()
            ->where('selected', 0)
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->where('darulifta_name', 'NOT LIKE', '%3btn%');

        if ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        }

        return $query->pluck(DB::raw("CONCAT(darulifta_name, ' - ', mail_folder_date) as merged_value"));
    }

    public function getUnselectedFilesData()
    {
        $query = DB::table('uploaded_files')
            ->whereIn('darulifta_name', $this->daruliftaNames)
            ->where('selected', 0);

        if ($this->selectedTimeFrame == 'this_month') {
            $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                now()->startOfMonth(),
                now()->endOfMonth()
            ]);
        }

        return $query->get();
    }
    private function fetchRemainingData()
    {
        $dataByDaruliftaName = [];

        foreach ($this->mailfolderDates as $mailfolderDate) {
            $data = [];

            foreach ($this->daruliftaNames as $daruliftaName) {
                $query = DB::table('uploaded_files')
                    ->where('darulifta_name', $daruliftaName)
                    ->where('mail_folder_date', $mailfolderDate)
                    ->where('selected', 0);
                    if ($this->selectedTimeFrame == 'this_month') {
                        $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                            now()->startOfMonth(),
                            now()->endOfMonth()
                        ]);
                    }
                // Store the data for the current combination in the array
                if ($query->exists()) {
                    $dataByDaruliftaName[$daruliftaName][$mailfolderDate] = $query->get();
                }
            }
        }

        return $dataByDaruliftaName;
    }
    private function fetchReciptionData()
    {
        $recquetions = [];

        foreach ($this->reciptDates as $reciptDate) {
            foreach ($this->daruliftaNames as $daruliftaName) {
                if ($this->selectedTimeFrame1 == 'exclude_ok') {
                    $query = DB::table('questions')
    ->where('question_branch', $daruliftaName)
    ->where('assign_id', $reciptDate)
    ->whereNotIn('ifta_code', function ($query) {
        $query->select('file_code')
            ->from('uploaded_files')
            ->where('checked_folder', '=', 'ok');
    })
    ->get();
    
                } elseif ($this->selectedTimeFrame1 == 'all') {
                    $query = DB::table('questions')
                        ->where('question_branch', $daruliftaName)
                        ->where('assign_id', $reciptDate)
                        ->get();
                }
                elseif ($this->selectedTimeFrame1 == 'exclude_ok_this_month') {
                    $query = DB::table('questions')
                    ->where('question_branch', $daruliftaName)
                    ->where('assign_id', $reciptDate)
                    ->whereBetween(DB::raw('DATE(rec_date)'), [
                        now()->startOfMonth(),
                        now()->endOfMonth()
                    ])
                    ->whereNotIn('ifta_code', function ($query) {
                        $query->select('file_code')
                            ->from('uploaded_files')
                            ->where('checked_folder', '=', 'ok');
                    })
                    ->get();
                }elseif ($this->selectedTimeFrame1 == 'exclude_ok_last_month') {

$query = DB::table('questions')
    ->where('question_branch', $daruliftaName)
    ->where('assign_id', $reciptDate)
    ->whereBetween(DB::raw('DATE(rec_date)'), [
        now()->subMonth()->startOfMonth(),
        now()->subMonth()->endOfMonth()
    ])
    ->whereNotIn('ifta_code', function ($query) {
        $query->select('file_code')
            ->from('uploaded_files')
            ->where('checked_folder', '=', 'ok');
    })
    ->get();
}elseif ($this->selectedTimeFrame1 == 'all_this_month') {

    $query = DB::table('questions')
        ->where('question_branch', $daruliftaName)
        ->where('assign_id', $reciptDate)
        ->whereBetween(DB::raw('DATE(rec_date)'), [
            now()->startOfMonth(),
            now()->endOfMonth()        ])
       
        ->get();
    }elseif ($this->selectedTimeFrame1 == 'all_last_month') {

        $query = DB::table('questions')
            ->where('question_branch', $daruliftaName)
            ->where('assign_id', $reciptDate)
            ->whereBetween(DB::raw('DATE(rec_date)'), [
                now()->subMonth()->startOfMonth(),
                now()->subMonth()->endOfMonth()
            ])
            
            ->get();
        }


        
                    if ($query->isNotEmpty()) {
                        $recquetions[$daruliftaName][$reciptDate] = $query;
                    }
                // }
            }
        }

        return $recquetions;
       
    }
    public function switchCard($card)
    {
        $this->selectedCard = $card;

        // Update the remaining data when switching to the remaining status card
        if ($card == 'remaining_status') {
            $this->remainingData = $this->fetchRemainingData();
        }
    }
}
