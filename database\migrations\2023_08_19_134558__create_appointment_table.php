<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('appt', function (Blueprint $table) {
            $table->id();
            $table->date('appt_date');
            $table->string('appt_day');
            $table->time('appt_time');
            $table->string('appt_name');
            $table->string('appt_cont');
            $table->string('appt_dec');
            $table->boolean('selected')->default(0);
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
