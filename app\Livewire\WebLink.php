<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;


class WebLink extends Component
{
    use WithFileUploads;

    public $fileCode;
    public $webDate;  // Add this at the top
    public $title;  // Initialize title if it exists
    public $viralUpload;
    public $daruliftaNames;
    public $shobaViralDate;
    public $WebDays;
    public $toViralDate;
    public $fileErrorMessage;
    public $uploadedFile;
    public $fileName;
    public $webLink;
    public $viralLink;  // Add viralLink property
    public $isEditingWebLink = false;
    public $isEditingViralLink = false;  // Add isEditingViralLink property
    public $isEditingTitle = false;  // Add isEditingViralLink property

    public function mount($title, $fileName, $fileCode, $viralUpload, $webLink = null, $viralLink = null)  // Add viralLink parameter
    {
        $this->fileCode = $fileCode;
        $this->fileName = $fileName;
        $this->viralUpload = $viralUpload;
        $this->webLink = $webLink;
        $this->viralLink = $viralLink;  // Initialize viralLink if it exists
        $this->title = $title;  // Initialize title if it exists


        $fileRecord = DB::table('uploaded_files')
        ->where('checked_file_name', $this->fileName)
        ->where('checked_folder', 'ok')
        ->where('viral', '!=', 0)
        ->first();

    $this->shobaViralDate = $fileRecord ? $fileRecord->shoba_viral : null;
    $this->daruliftaNames = DB::table('daruliftas')
    ->pluck('darul_name')
    ->toArray();
    $this->webDate = $fileRecord ? $fileRecord->web_date : null;
    $this->toViralDate = $fileRecord ? $fileRecord->shoba_viral : null;
if (is_null($this->webDate) && $this->toViralDate) {
        $this->WebDays = Carbon::parse($this->toViralDate)->diffInDays(Carbon::now('Asia/Karachi'));
    } else {
        $this->WebDays = null;
    }

    }




    public function updatedWebLink()
    {
        $currentDatePakistan = Carbon::now('Asia/Karachi')->toDateString();

        if (empty($this->webLink)) {
            DB::table('uploaded_files')
                ->where('file_code', $this->fileCode)
                ->update([
                    'web_link' => null,
                    'web_date' => null
                ]);
            $this->webDate = null;
        } else {
            DB::table('uploaded_files')
                ->where('file_code', $this->fileCode)
                ->update([
                    'web_link' => $this->webLink,
                    'web_date' => $currentDatePakistan
                ]);
            $this->webDate = $currentDatePakistan;
        }

        $this->dispatch('webLinkUpdated');
    }
        // Handle viralLink updates

    public function enableWebLinkEditing()
    {
        $this->isEditingWebLink = true;
    }

    public function saveWebLink()
    {
        $this->updatedWebLink();
        $this->isEditingWebLink = false;
    }

    public function cancelWebLinkEditing()
    {
        $this->isEditingWebLink = false;
    }

    // Methods for viral link editing





    public function render()
    {
        return view('livewire.web-link');
    }
}
