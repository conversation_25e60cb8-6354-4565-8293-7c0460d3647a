<?php

if (!function_exists('current_controller')) {
    function current_controller($type = null) {
        $class = request()->route()->getAction('controller');
        $class = class_basename($class);

        if ($type == 'l') {
            $class = lcfirst($class);
        }

        return $class;
    }
}

if (!function_exists('current_method')) {
    function current_method() {
        return request()->route()->getActionMethod();
    }
}