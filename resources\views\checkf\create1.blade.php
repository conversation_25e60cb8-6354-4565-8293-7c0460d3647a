<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="check"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Checking Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Create Fatawa Entry Form -->
        <style>
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #col0, #col2 {
                width: 25%;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
        </style>
        
        <div class="container">
            <div class="container">
                <h1 class="mb-4">Recived Mail Folder</h1>
                <title>Onclick increase Table Rows</title>
<script type="text/javascript">
function addRows() {
    var table = document.getElementById('emptbl');
    var rowCount = table.rows.length;
    var cellCount = table.rows[0].cells.length;
    var row = table.insertRow(rowCount);
    
    for (var i = 0; i < cellCount; i++) { // Changed the loop condition to < cellCount
        var cell = 'cell' + i;
        cell = row.insertCell(i);
        var copycel = document.getElementById('col' + i).innerHTML;
        cell.innerHTML = copycel;
    }
}

function deleteRows(){
	var table = document.getElementById('emptbl');
	var rowCount = table.rows.length;
	if(rowCount > '2'){
		var row = table.deleteRow(rowCount-1);
		rowCount--;
	}
	else{
		alert('There should be atleast one row');
	}
}
</script>
</head>
<body>
<form action="#" method="get">    
	<table id="emptbl">
		<tr>
			<th>Mail Recived Date</th>
			<th>Darulifta</th> 
			<th>Mail Folder Date</th>
		</tr> 
		<tr> 
			<td id="col0"><input type="Date" name="date[]" value="" /></td> 
			<td id="col1"> 
			<select name="darulifta[]" id="ifta"> 
			<option value="0">Select Darulifta</option> 
			<option value="1">Noorulirfan</option>
			<option value="2">Faizan e Ajmair</option>
			<option value="3">Gulzare Taiba</option>
            <option value="4">Iqtisaad</option>
			</select> 
		    </td> 
		    <td id="col2"><input type="date" name="fdate[]" value="" /></td> 
		</tr>  
	</table> 
	<div style="text-align: center; margin-top: 20px;">
        <input type="button" class="btn btn-primary" value="Add Row" onclick="addRows()" />
        <input type="button" class="btn btn-danger" value="Delete Row" onclick="deleteRows()" />
        <input type="submit" class="btn btn-success" value="Submit" />
    </div>
 </form> 
</body> 

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
