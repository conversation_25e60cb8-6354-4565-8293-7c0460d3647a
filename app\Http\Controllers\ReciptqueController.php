<?php

namespace App\Http\Controllers;

use App\Models\Issue; // Import the Issue model
use App\Models\SubIssue; // Import the SubIssue model
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ReciptqueController extends Controller
{
    public function index()
    {
        $userRoles = auth()->user()->roles;

        // Extract the names of the roles into an array
        $roleNames = $userRoles->pluck('name')->toArray();

        // Now you can use the $roleNames array in your controller method
        // For example, if you need to access the first role name:
        $firstRoleName = reset($roleNames);

        $question_b = DB::table('questions')
        ->whereNull('assign_id')
        ->where('question_branch',$firstRoleName)
        ->orderBy('id','desc')  // Add this line to order by the 'id' column
        ->get();
        $questions = DB::table('questions')
        ->whereNull('assign_id')
        ->get();
        $mujeebs = DB::table('mujeebs')
        ->where('darul_name',$firstRoleName)
        ->get();

        return view('reciptque.question', compact('question_b','mujeebs','questions'));
    }
    public function mujeebindex()
{
    $userRoles = auth()->user()->roles;

    // Extract the names of the roles into an array
    $roleNames = $userRoles->pluck('name')->toArray();

    $mujeebName = null;
    $darulName = null;
    $question_b = null;
    $mujeebs = null;
    $questions = null;

    // Check if the user has the 'mujeeb' role
    if (in_array('mujeeb', $roleNames)) {
        // Get the user's name
        $userName = auth()->user()->name;

        // Query the mujeebs table to find the matching record
        $mujeeb = DB::table('mujeebs')
                    ->where('mujeeb_name', $userName)
                    ->first();

        // If a record is found, get the darul_name and mujeeb_name
        if ($mujeeb) {
            $darulName = $mujeeb->darul_name;
            $mujeebName = $mujeeb->mujeeb_name;
        }

        // Query based on mujeeb-specific data
        $question_b = DB::table('questions')
        ->where('assign_id',$mujeebName)
        ->where('question_branch', $darulName)
            ->orderBy('id', 'desc') // Order by the 'id' column
            ->get();

        $mujeebs = DB::table('mujeebs')
            ->where('mujeeb_name', $mujeebName)
            ->get();
            $questions = DB::table('questions')
            ->where('assign_id',$mujeebName)
            ->get();
    } else {
        // Fallback logic for non-mujeeb users
        $firstRoleName = reset($roleNames);

        $question_b = DB::table('questions')
            ->whereNotNull('assign_id')
            ->where('question_branch', $firstRoleName)
            ->orderBy('id', 'desc') // Order by the 'id' column
            ->get();

        $mujeebs = DB::table('mujeebs')
            ->where('darul_name', $firstRoleName)
            ->get();
            $questions = DB::table('questions')
            ->whereNotNull('assign_id')
            ->get();
    }

    // Retrieve all questions for both roles


    return view('reciptque.m_question', compact('question_b', 'mujeebs', 'questions'));
}

public function mujeebassign()
{
    $userRoles = auth()->user()->roles;

    // Extract the names of the roles into an array
    $roleNames = $userRoles->pluck('name')->toArray();

    $mujeebName = null;
    $darulName = null;
    $question_b = null;
    $mujeebs = null;
    $questions = null;

    // Check if the user has the 'mujeeb' role
    if (in_array('mujeeb', $roleNames)) {
        // Get the user's name
        $userName = auth()->user()->name;

        // Query the mujeebs table to find the matching record
        $mujeeb = DB::table('mujeebs')
                    ->where('mujeeb_name', $userName)
                    ->first();

        // If a record is found, get the darul_name and mujeeb_name
        if ($mujeeb) {
            $darulName = $mujeeb->darul_name;
            $mujeebName = $mujeeb->mujeeb_name;
        }

        // Query based on mujeeb-specific data
        $question_b = DB::table('questions')
        ->where('assign_id',$mujeebName)
        ->where('question_branch', $darulName)
            ->orderBy('id', 'desc') // Order by the 'id' column
            ->get();

        $mujeebs = DB::table('mujeebs')
            ->where('mujeeb_name', $mujeebName)
            ->get();
            $questions = DB::table('questions')
            ->where('assign_id',$mujeebName)
            ->get();
    } else {
        // Fallback logic for non-mujeeb users
        $firstRoleName = reset($roleNames);

        $question_b = DB::table('questions')
            ->whereNotNull('assign_id')
            ->where('question_branch', $firstRoleName)
            ->orderBy('id', 'desc') // Order by the 'id' column
            ->get();

        $mujeebs = DB::table('mujeebs')
            ->where('darul_name', $firstRoleName)
            ->get();
            $questions = DB::table('questions')
            ->whereNotNull('assign_id')
            ->get();
    }

    // Retrieve all questions for both roles


    return view('reciptque.mujeeb_assign_page', compact('question_b', 'mujeebs', 'questions'));
}
    public function getQuestion($id)
{
    $question = DB::table('questions')
        ->select('rec_date', 'ifta_code', 'question', 'question_branch', 'assign_id','sayel','issue')
        ->where('id', $id)
        ->first();

    if (!$question) {
        return response()->json(['error' => 'Question not found'], 404);
    }

    return response()->json($question);
}
    // public function selectFatwa()
    // {
    //     $userRoles = auth()->user()->roles;

    //     // Extract the names of the roles into an array
    //     $roleNames = $userRoles->pluck('name')->toArray();

    //     // Now you can use the $roleNames array in your controller method
    //     // For example, if you need to access the first role name:
    //     $firstRoleName = reset($roleNames);

    //     $question_b = DB::table('questions')
    //     ->whereNotNull('assign_id')
    //     ->where('question_branch',$firstRoleName)
    //     ->get();
    //     // dd($question_b);
    //     $questions = DB::table('questions')
    //     ->whereNotNull('assign_id')
    //     ->get();
    //     $mujeebs = DB::table('mujeebs')
    //     ->where('darul_name',$firstRoleName)
    //     ->get();

    //     return view('files.create', compact('question_b','mujeebs','questions'));
    // }

    public function create()
    {


        $userRoles = auth()->user()->roles;

        // Extract the names of the roles into an array
        $roleNames = $userRoles->pluck('name')->toArray();

        // Now you can use the $roleNames array in your controller method
        // For example, if you need to access the first role name:
        $firstRoleName = reset($roleNames);

        if (count(Auth::user()->roles) < 2) {
            $lastIftaCode = DB::table('questions')
                ->where('question_branch', $firstRoleName)
                ->max('ifta_code');
            }
            else {
                $lastIftaCode = '';
            }
            $parts = explode('-', $lastIftaCode);

            // Check if $parts is not empty
            if (count($parts) === 2) { // Check if $parts contains two elements
                $numericPart = intval($parts[1]);
                $numericPart++; // Increment by 1
                $newIftaCode = $parts[0] . '-' . $numericPart;
            } else {
                $newIftaCode = '';
            }

        // dd($newIftaCode);
        $issues = Issue::all(); // Fetch issues using the Issue model


        $branches = DB::table('daruliftas')->get();
        $rolebranches = DB::table('daruliftas')
        ->where('darul_name',$firstRoleName)
        ->get();
        $user = DB::table('mujeebs')->get();
        $rolemujeeb = DB::table('mujeebs')
        ->where('darul_name',$firstRoleName)
        ->get();

        return view('reciptque.rform', compact('issues', 'branches','rolebranches', 'user','rolemujeeb','newIftaCode'));


    }
    public function recMujeeb()
    {

        $userRoles = auth()->user()->roles;

        // Extract the names of the roles into an array
        $roleNames = $userRoles->pluck('name')->toArray();

        // Initialize variables
        $mujeebName = null;
        $darulName = null;

        // Check if the user has the 'mujeeb' role
        if (in_array('mujeeb', $roleNames)) {
            // Get the user's name
            $userName = auth()->user()->name;

            // Query the mujeebs table to find the matching record
            $mujeeb = DB::table('mujeebs')
                        ->where('mujeeb_name', $userName)
                        ->first();

            // If a record is found, get the darul_name and mujeeb_name
            if ($mujeeb) {
                $darulName = $mujeeb->darul_name;
                $mujeebName = $mujeeb->mujeeb_name;
            }
        }


        if (count(Auth::user()->roles) < 2) {
            $lastIftaCode = DB::table('questions')
                ->where('question_branch', $darulName)
                ->max('ifta_code');
            }
            else {
                $lastIftaCode = '';
            }
            $parts = explode('-', $lastIftaCode);

            // Check if $parts is not empty
            if (count($parts) === 2) { // Check if $parts contains two elements
                $numericPart = intval($parts[1]);
                $numericPart++; // Increment by 1
                $newIftaCode = $parts[0] . '-' . $numericPart;
            } else {
                $newIftaCode = '';
            }

        // dd($newIftaCode);
        $issues = Issue::all(); // Fetch issues using the Issue model


        $branches = DB::table('daruliftas')->get();
        $rolebranches = DB::table('daruliftas')
        ->where('darul_name',$darulName)
        ->get();
        $user = DB::table('mujeebs')->get();
        $rolemujeeb = DB::table('mujeebs')
        ->where('mujeeb_name',$mujeebName)
        ->get();

        return view('reciptque.rform-mujeeb', compact('issues', 'branches','rolebranches', 'user','rolemujeeb','newIftaCode'));


    }
    public function getSubIssues(Request $request)
    {
        $issueId = $request->issue_id;
        $subIssues = SubIssue::where('parent_id', $issueId)->get();

        return response()->json($subIssues);
    }

    public function store(Request $request)
{

    $request->validate([
        'ifta_code' => 'required|unique:questions,ifta_code',
        'assign_id' => 'nullable|string',
        // Other validation rules...
    ]);

    // Create an array to hold the data to insert
    $selectedIssueId = $request->input('issue');
$issueName = Issue::find($selectedIssueId)->name;

$selectedSubIssueId = $request->input('sub_issue');
$subIssueName = SubIssue::find($selectedSubIssueId)->name;
    $dataToInsert = [
        'rec_date' => $request->rec_date,
        'question_type' => $request->question_type,
        'ifta_code' => $request->ifta_code,
        'question' => $request->question,
        'issue' => $issueName,
        'sub_issue' => $subIssueName,
        'sayel' => $request->sayel,
        'address' => $request->address,
        'phone' => $request->phone,
        'email' => $request->email,
        'expected_date' => $request->expected_date,
        'question_title' => $request->question_title,
        'question' => $request->question,
        'question_branch' => $request->question_branch,
        'assign_id' => $request->assign_id === '' ? null : $request->assign_id,

        'created_at' => now(),
        'updated_at' => now(),
    ];

    // Check if 'assign_id' is not null, and if so, add 'mujeeb_send_date' to the data array
    if (!is_null($request->assign_id)) {
        $dataToInsert['mujeeb_send_date'] = now();
    }

// dd($dataToInsert);
    // Insert the data into the 'questions' table
    DB::table('questions')->insert($dataToInsert);
    // $opening = '11:00'; // Set the opening time here
    // $closing = '05:00'; // Set the closing time here
    // $break_start = '01:00'; // Set the break start time here
    // $break_end = '02:00'; // Set the break end time here
    // $weekend = 'Sunday'; // Set the weekend day here
    // $branch_no = '03100000000';
    // $data = [
    //     'rec_date' => $request->input('rec_date'),
    //     'question_type' => $request->input('question_type'),
    //     'ifta_code' => $request->input('ifta_code'),
    //     'question_branch' => $request->input('question_branch'),
    //     'sayel' => $request->input('sayel'),
    //     'issue' => $issueName,
    //     'phone' => $request->input('phone'),
    //     'expected_date' => $request->input('expected_date'),
    //     'address' => $request->input('address'),
    //     'opening' => date('h:i', strtotime($opening)),
    //     'closing' => date('h:i', strtotime($closing)),
    //     'break_start' => date('h:i', strtotime($break_start)),
    //     'break_end' => date('h:i', strtotime($break_end)),
    //     'weekend' => $weekend,
    //     'branch_no' => $branch_no
    // ];

    return redirect()->route('question.index')->with('success', 'Reciptque record updated successfully!');
}
// public function store(Request $request)
// {
//     // Define a local function to sanitize input data
//     $sanitize = function($value) {
//         // Apply htmlspecialchars to string values, array_map recursively for arrays
//         return is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $value;
//     };

//     // Sanitize input data
//     $sanitizedInput = array_map($sanitize, $request->all());

//     // Prepare your data with sanitized inputs
//     $selectedIssueId = $sanitizedInput['issue'];
//     $issueName = Issue::find($selectedIssueId)->name;

//     $selectedSubIssueId = $sanitizedInput['sub_issue'];
//     $subIssueName = SubIssue::find($selectedSubIssueId)->name;

//     $dataToInsert = [
//         'rec_date' => $sanitizedInput['rec_date'],
//         'question_type' => $sanitizedInput['question_type'],
//         'ifta_code' => $sanitizedInput['ifta_code'],
//         'question' => $sanitizedInput['question'],
//         'issue' => $issueName,
//         'sub_issue' => $subIssueName,
//         'sayel' => $sanitizedInput['sayel'],
//         'address' => $sanitizedInput['address'],
//         'phone' => $sanitizedInput['phone'],
//         'email' => $sanitizedInput['email'],
//         'expected_date' => $sanitizedInput['expected_date'],
//         'question_title' => $sanitizedInput['question_title'],
//         'question_branch' => $sanitizedInput['question_branch'],
//         'assign_id' => $sanitizedInput['assign_id'],
//         'created_at' => now(),
//         'updated_at' => now(),
//     ];

//     // Additional logic for 'mujeeb_send_date'
//     if (!is_null($sanitizedInput['assign_id'])) {
//         $dataToInsert['mujeeb_send_date'] = now();
//     }
//     // dd($dataToInsert);
//     // Insert sanitized data into the database
//     DB::table('questions')->insert($dataToInsert);

//     // Redirect or respond
//     return redirect()->route('question.index')->with('success', 'Record updated successfully!');
// }
public function checkPhoneExist(Request $request)
{
    $phone = $request->phone;

    $result = DB::table('questions')
        ->where('phone', $phone)
        ->first();

    return response()->json($result);
}

public function checkEmailExist(Request $request)
{
    $email = $request->email;

    $result = DB::table('questions')
        ->where('email', $email)
        ->first();

    return response()->json($result);
}
public function edit($id)
{

    $reciptque = DB::table('questions')->where('id', $id)->first();
$issueValue = $reciptque->issue;

$issueData = DB::table('issues')
    ->where('name', $issueValue)
    ->first();

if ($issueData) {
    $issueId = $issueData->id;
} else {
    // Handle the case where the issue data doesn't exist
    $issueId = null; // You can set it to null or some other default value
}


    // $issueValue = $issuebabe->id;

    $branch = $reciptque->question_branch;
    $mujeeb = DB::table('mujeebs')
    ->where('darul_name',$branch)
    ->get();

    $issues = DB::table('issues')->get(); // Assuming you have a table named "issues"
    $subissues = DB::table('sub_issues')
    ->where('parent_id',$issueId)
    ->get();


    return view('reciptque.edit_q', ['reciptque' => $reciptque, 'issues' => $issues, 'subissues' => $subissues, 'mujeeb' => $mujeeb]);
}
    public function update(Request $request, $id)
    {
        // dd($request);
        $selectedIssueId = $request->input('issue');
$issueName = Issue::find($selectedIssueId)->name;

$selectedSubIssueId = $request->input('sub_issue');
$subIssueName = SubIssue::find($selectedSubIssueId)->name;
        // Update the data in the 'reciptque' table
        DB::table('questions')->where('id', $id)->update([
            // Update fields based on the request
            'question_type' => $request->question_type,
            'ifta_code' => $request->ifta_code,
            'question' => $request->question,
            'issue' => $issueName,
            'sub_issue' => $subIssueName,
            'sayel' => $request->sayel,
            'phone' => $request->phone,
            'address' => $request->address,
            'email' => $request->email,
            'rec_date' => $request->rec_date,
            'question_branch' => $request->question_branch,
            'assign_id' => $request->assign_id,

            'expected_date' => $request->expected_date,
            'question_title' => $request->question_title,
            'updated_at' => now(),



        ]);

        return redirect()->route('reciptque.index')->with('success', 'Reciptque record updated successfully!');
    }

    public function destroy($id)
    {
        // Delete the record from the 'reciptque' table
        DB::table('questions')->where('id', $id)->delete();

        return back()->with('success', 'Record deleted successfully!');
    }
    public function listIssues()
{
    $issues = DB::table('issues')->get(); // Assuming you have a table named "issues"



    return view('reciptque.rform', compact('issues'));
}
public function editIssues()
{
    $issues = DB::table('issues')->get(); // Assuming you have a table named "issues"



    return view('reciptque.edit_q', compact('issues'));
}

public function updateAssignId(Request $request, $id)
{
    $selectedMujeebId = $request->input('selectedMujeebId');

    // Get the current date and time
    $currentDate = now();

    // Update the 'assign_id' and 'mujeeb_send_date' columns in the 'questions' table
    DB::table('questions')
        ->where('id', $id)
        ->update([
            'assign_id' => $selectedMujeebId,
            'mujeeb_send_date' => $currentDate,
        ]);

    return response()->json(['success' => true]);
}
public function getQuestionData(Request $request)
{
    $iftaCode = $request->input('ifta_code');

    // Fetch data from the 'questions' table based on the 'ifta_code' using the DB facade
    $question = DB::table('questions')
        ->where('ifta_code', $iftaCode)
        ->first();

    if ($question) {
        // Return the data as a JSON response
        return response()->json([
            'assign_id' => $question->assign_id,
            'mujeeb_send_date' => $question->mujeeb_send_date,
            'issue' => $question->issue,
        ]);
    } else {
        // Handle the case where 'ifta_code' is not found
        return response()->json([
            'assign_id' => null,
            'mujeeb_send_date' => null,
            'issue' => null,
        ]);
    }
}
}
