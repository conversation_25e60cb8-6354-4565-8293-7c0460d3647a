<tr>

    <td colspan="1" class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">Working.....</td>
    <td colspan="2" class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">
    @if(!$viralUpload)
        <!-- Show file input if viral_upload is empty -->
        <input type="file" wire:model="uploadedFile">
        <div wire:loading wire:target="uploadedFile">Uploading...</div>

        <!-- Display error message if fileErrorMessage is set -->
        @if($fileErrorMessage)
            <div class="text-danger mt-2">{{ $fileErrorMessage }}</div>
        @endif
    @else
        <!-- Show view and delete buttons if viral_upload is not empty -->
        <a href="{{ route('viewRemain', [
                                    'date' => 'viral',  // Use only the base folder without subfolders
                                    'filename' => $fileName
                                ]) }}" target="_blank" class="btn btn-primary btn-sm">
            <i class="fas fa-eye"> </i>View
        </a>
        <button wire:click="downloadFile" class="btn btn-success btn-sm">Download</button>

        <!-- Disable the Delete button if shoba_viral is not null -->
        <div
            @if($shobaViralDate)
                title="This Fatawa was sent to Shoba Viral on {{ \Carbon\Carbon::parse($shobaViralDate)->format('d/m/Y H:i') }}"
            @endif>
            <button
                wire:click="deleteFile"
                class="btn btn-danger btn-sm"
                @if($shobaViralDate)
                    disabled
                @endif>
                Delete
            </button>
        </div>
    @endif
</td>



    <td colspan="3" class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;" dir="rtl">
    @if(!$title)
        <!-- Show input for title if it's not available -->
        <input type="text" wire:model.lazy="title" placeholder="عنوان درج کریں">
        <div wire:loading wire:target="title">عنوان محفوظ ہو رہا ہے...</div>
    @else
        <!-- Show view and edit buttons for title -->
        <span>{{ $title }}</span>
        <br>
        <button wire:click="enableTitleEditing" class="btn btn-secondary btn-sm">ترمیم کریں</button>

        @if($isEditingTitle)
            <!-- Show input for editing title -->
            <input type="text" wire:model.lazy="title" placeholder="عنوان میں ترمیم کریں">
            <button wire:click="saveTitle" class="btn btn-success btn-sm">محفوظ کریں</button>
            <button wire:click="cancelTitleEditing" class="btn btn-secondary btn-sm">منسوخ کریں</button>
        @endif
    @endif
</td>
<td colspan="3" class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">
    @php
        $ViralNazim = Auth::check() && in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray());
    @endphp

    <!-- Permanent message showing the date -->
    @if ($shobaViralDate)
        <span class="text-success">Sent to Shoba Viral on {{ \Carbon\Carbon::parse($shobaViralDate)->format('d/m/Y H:i') }}</span>
    @endif

    <!-- Buttons only visible if ViralNazim and viralUpload conditions are true -->
    @if ($ViralNazim && $viralUpload)
        @if ($shobaViralDate)
            <button wire:click="cancelShobaViral" class="btn btn-secondary btn-sm mt-2">Cancel</button>
        @else
            <button wire:click="sendToShobaViral" class="btn btn-primary btn-sm mt-2">Send VFatawa To Shoba Viral</button>
        @endif
    @endif
</td>

        </tr>
