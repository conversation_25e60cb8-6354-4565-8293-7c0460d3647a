<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User;
use App\Models\UserRestriction;
use App\Models\DailyPerformance;
use Carbon\Carbon;

class LockedAccountManagement extends Component
{
    use WithPagination;

    protected $layout = 'layouts.user_type.auth';

    public $filterType = '';
    public $search = '';
    public $showUnlockModal = false;
    public $selectedUser = null;
    public $unlockReason = '';

    protected $rules = [
        'unlockReason' => 'required|string|min:10|max:500',
    ];

    protected $messages = [
        'unlockReason.required' => 'Please provide a reason for unlocking this account.',
        'unlockReason.min' => 'Reason must be at least 10 characters long.',
        'unlockReason.max' => 'Reason cannot exceed 500 characters.',
    ];

    public function render()
    {
        $query = User::whereHas('activeRestrictions', function ($q) {
            if ($this->filterType) {
                $q->where('restriction_type', $this->filterType);
            }
        })->with(['activeRestrictions', 'roles']);

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        $lockedUsers = $query->paginate(10);

        // Get restriction statistics
        $stats = [
            'total_locked' => UserRestriction::where('is_active', true)->distinct('user_id')->count('user_id'),
            'performance_locked' => UserRestriction::where('restriction_type', UserRestriction::TYPE_PERFORMANCE_NOT_SUBMITTED)
                ->where('is_active', true)->count(),
            'fatawa_locked' => UserRestriction::where('restriction_type', UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED)
                ->where('is_active', true)->count(),
            'manual_locked' => UserRestriction::where('restriction_type', UserRestriction::TYPE_MANUAL_LOCK)
                ->where('is_active', true)->count(),
        ];

        return view('livewire.locked-account-management', [
            'lockedUsers' => $lockedUsers,
            'stats' => $stats,
            'restrictionTypes' => [
                UserRestriction::TYPE_PERFORMANCE_NOT_SUBMITTED => 'Performance Not Submitted',
                UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED => 'Fatawa Limit Exceeded',
                UserRestriction::TYPE_MANUAL_LOCK => 'Manual Lock',
                UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED => 'Mahl-e-Nazar Restricted',
            ]
        ]);
    }

    public function showUnlockModal($userId)
    {
        $this->selectedUser = User::with(['activeRestrictions'])->findOrFail($userId);
        $this->unlockReason = '';
        $this->showUnlockModal = true;
        $this->resetValidation();
    }

    public function hideUnlockModal()
    {
        $this->showUnlockModal = false;
        $this->selectedUser = null;
        $this->unlockReason = '';
        $this->resetValidation();
    }

    public function unlockAccount()
    {
        $this->validate();

        if (!$this->selectedUser) {
            session()->flash('error', 'User not found.');
            return;
        }

        try {
            // Unlock all active restrictions for this user
            UserRestriction::where('user_id', $this->selectedUser->id)
                ->where('is_active', true)
                ->update([
                    'is_active' => false,
                    'lifted_by' => auth()->id(),
                    'lifted_at' => now(),
                    'lift_reason' => $this->unlockReason,
                ]);

            session()->flash('success', "Account for {$this->selectedUser->name} has been unlocked successfully!");
            $this->hideUnlockModal();
        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while unlocking the account.');
        }
    }

    public function getMissedPerformanceDays($user)
    {
        $missedDays = [];
        $today = Carbon::now();
        
        // Check last 30 days for missed performance
        for ($i = 1; $i <= 30; $i++) {
            $checkDate = $today->copy()->subDays($i);
            
            // Skip weekends and holidays (you can customize this logic)
            if ($checkDate->isSunday()) {
                continue;
            }
            
            // Check if user submitted performance for this date
            $hasSubmitted = DailyPerformance::where('user_id', $user->id)
                ->whereDate('created_at', $checkDate->format('Y-m-d'))
                ->exists();
            
            if (!$hasSubmitted) {
                $missedDays[] = $checkDate->format('Y-m-d');
            }
        }
        
        return array_slice($missedDays, 0, 10); // Return only last 10 missed days
    }

    public function getRestrictionBadgeClass($type)
    {
        return match($type) {
            UserRestriction::TYPE_PERFORMANCE_NOT_SUBMITTED => 'bg-gradient-warning',
            UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED => 'bg-gradient-danger',
            UserRestriction::TYPE_MANUAL_LOCK => 'bg-gradient-dark',
            UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED => 'bg-gradient-info',
            default => 'bg-gradient-secondary',
        };
    }

    public function getRestrictionIcon($type)
    {
        return match($type) {
            UserRestriction::TYPE_PERFORMANCE_NOT_SUBMITTED => 'fas fa-clipboard-check',
            UserRestriction::TYPE_FATAWA_LIMIT_EXCEEDED => 'fas fa-exclamation-triangle',
            UserRestriction::TYPE_MANUAL_LOCK => 'fas fa-lock',
            UserRestriction::TYPE_MAHL_E_NAZAR_DEFAULT_RESTRICTED => 'fas fa-eye',
            default => 'fas fa-ban',
        };
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilterType()
    {
        $this->resetPage();
    }
}
