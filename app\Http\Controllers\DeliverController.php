<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DeliverController extends Controller
{
    public function index()
    {
        $userRoles = auth()->user()->roles;

        // Extract the names of the roles into an array
        $roleNames = $userRoles->pluck('name')->toArray();
    
        // Now you can use the $roleNames array in your controller method
        // For example, if you need to access the first role name:
        $firstRoleName = reset($roleNames);
        // Get the first day of the current month
        $firstDayOfMonth = Carbon::now()->startOfMonth();

        // Get the last day of the current month
        $lastDayOfMonth = Carbon::now()->endOfMonth();

        if(count(Auth::user()->roles) > 1){
            $daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->distinct()
            ->pluck('darulifta_name');
        }
            else {
            $daruliftaNames = DB::table('uploaded_files')
            
            ->select('darulifta_name')
            ->distinct()
            ->where('darulifta_name',$firstRoleName)
            ->pluck('darulifta_name');
            
        }
        $mailfolderDates = DB::table('uploaded_files')

                ->select('mail_folder_date')
                
                ->distinct()
                ->orderBy('mail_folder_date', 'desc') // Sort in descending order
                ->pluck('mail_folder_date');
        $okFatawa = [];
    
                foreach ($mailfolderDates as $mailfolderDate) {
                foreach ($daruliftaNames as $daruliftaName) {
                    $data = DB::table('uploaded_files')
                        ->where('darulifta_name', $daruliftaName)
                        ->where('mail_folder_date', $mailfolderDate)
                        ->where('checked_folder', 'ok')
                        ->where('deliver', 0)
                        // ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth])
                        
                        ->get();
                        if ($data->isNotEmpty()) {
                $okFatawa[$daruliftaName][$mailfolderDate] = $data;

                }
            }
        }
        // dd($okFatawa,$mailfolderDates,$daruliftaNames);        
        return view('deliver.not_deliver',compact('okFatawa','daruliftaNames','mailfolderDates'));
    }
    public function indexDel()
    {
        $userRoles = auth()->user()->roles;

        // Extract the names of the roles into an array
        $roleNames = $userRoles->pluck('name')->toArray();
    
        // Now you can use the $roleNames array in your controller method
        // For example, if you need to access the first role name:
        $firstRoleName = reset($roleNames);
        // Get the first day of the current month
        $firstDayOfMonth = Carbon::now()->startOfMonth();

        // Get the last day of the current month
        $lastDayOfMonth = Carbon::now()->endOfMonth();

        if(count(Auth::user()->roles) > 1){
            $daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->distinct()
            ->pluck('darulifta_name');
        }
            else {
            $daruliftaNames = DB::table('uploaded_files')
            
            ->select('darulifta_name')
            ->distinct()
            ->where('darulifta_name',$firstRoleName)
            ->pluck('darulifta_name');
            
        }
        $mailfolderDates = DB::table('uploaded_files')

                ->select('mail_folder_date')
                
                ->distinct()
                ->orderBy('mail_folder_date', 'desc') // Sort in descending order
                ->pluck('mail_folder_date');
        $okFatawa = [];
    
                foreach ($mailfolderDates as $mailfolderDate) {
                foreach ($daruliftaNames as $daruliftaName) {
                    $data = DB::table('uploaded_files')
                        ->where('darulifta_name', $daruliftaName)
                        ->where('mail_folder_date', $mailfolderDate)
                        ->where('checked_folder', 'ok')
                        ->where('deliver', 1)
                        // ->whereBetween(DB::raw('DATE(mail_folder_date)'), [$firstDayOfMonth, $lastDayOfMonth])
                        
                        ->get();
                        if ($data->isNotEmpty()) {
                $okFatawa[$daruliftaName][$mailfolderDate] = $data;

                }
            }
        }
        // dd($okFatawa,$mailfolderDates,$daruliftaNames);        
        return view('deliver.deliver',compact('okFatawa','daruliftaNames','mailfolderDates'));
    }
    public function updateDeliver(Request $request, $id)
{
    $isChecked = $request->input('isChecked') ? 1 : 0;
    
    DB::table('uploaded_files')
        ->where('id', $id)
        ->update([
            'deliver' => $isChecked,
            'deliver_date' => $isChecked ? Carbon::now() : null,
        ]);

    return response()->json(['success' => true]);
}
    public function create()
    {
        // Your code for the create action
        return view('deliver.create');
    }

    public function store(Request $request)
    {
        // Your code for storing data
        // This method typically handles form submissions
    }
    public function updateDeliverDate(Request $request, $id)
{
    $newDeliverDate = $request->input('newDeliverDate');
    
    DB::table('uploaded_files')
        ->where('id', $id)
        ->update(['deliver_date' => $newDeliverDate]);

    return response()->json(['success' => true]);
}
public function updateNotDeliverReason(Request $request, $id)
{
    $newNotDeliverReason = $request->input('newNotDeliverReason');

    try {
        DB::table('uploaded_files')
            ->where('id', $id)
            ->update(['not_deliver_reason' => $newNotDeliverReason]);

        return response()->json(['success' => true]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'error' => $e->getMessage()]);
    }
}
}
