<?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show bg-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show bg-gray-200']); ?>
    <?php if (isset($component)) { $__componentOriginaleeb4de73933bf6c97cffe74e5846276e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.sidebar','data' => ['activePage' => 'check']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => 'check']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $attributes = $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $component = $__componentOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <?php if (isset($component)) { $__componentOriginal778d3beb0063990dd56df93abee65235 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal778d3beb0063990dd56df93abee65235 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.navs.auth','data' => ['titlePage' => 'Send Fatawa']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.navs.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['titlePage' => 'Send Fatawa']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $attributes = $__attributesOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__attributesOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $component = $__componentOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__componentOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
        <!-- End Navbar -->


        <!-- Create Appointment Entry Form -->
        <link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
/>
        <style>
                .custom-table {
        background-color: #f8f9fa; /* Light gray background color */
        text-align: center; /* Center align table content */
    }

    .custom-table th {
        background-color: #343a40; /* Dark background for table header */
        color: #fff; /* White text for table header */
    }

    .custom-table tbody tr:nth-child(odd) {
        background-color: #e9ecef; /* Light gray for odd rows */
    }

    .custom-table tbody tr:nth-child(even) {
        background-color: #ffffff; /* White for even rows */
    }
    /* Style the select element */
.custom-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: none;
  padding-right: 20px; /* Adjust the value based on the icon size */
}

/* Style the FontAwesome icon */
.custom-select::after {
  content: "\f078"; /* Unicode for the FontAwesome down-arrow icon */
  font-family: "Font Awesome 5 Free"; /* Use the correct font-family for FontAwesome */
  position: absolute;
  top: 50%;
  right: 10px; /* Adjust the position based on your icon size */
  transform: translateY(-50%);
  color: #333; /* Adjust the color */
}
        </style>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                // Get the input element by its ID
                var dateInput = document.getElementById("name");
                
                // Create a new date object with the current date
                var currentDate = new Date();
                
                // Format the current date as "YYYY-MM-DD"
                var formattedDate = currentDate.toISOString().split('T')[0];
                
                // Set the formatted date as the input's value
                dateInput.value = formattedDate;

                
            });
            
            
        </script>
        
   
        
         
        <div class="container">
            <!-- Only display the file upload role dropdown if the user has an allowed role -->
            
            <?php if(count(Auth::user()->roles) > 1): ?>
    <label for="select-role">Select Ifta:</label>
    <select id="select-role" name="selected_role">
        <?php $__currentLoopData = Auth::user()->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(in_array($role->name, ['Noorulirfan', 'Faizan-e-Ajmair', 'Gulzar-e-Taiba', 'Markaz-ul-Iqtisaad'])): ?>
                <option value="<?php echo e($role->name); ?>"><?php echo e($role->name); ?></option>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
<?php else: ?>
<p id="single-role"><?php echo e(Auth::user()->roles->first()->name); ?></p>
<?php endif; ?>

    
    <?php
    $checker = request()->route('checker') ?? null;
    $transfer_by = request()->route('transfer_by') ?? null;
    ?>
            
            <h1 class="mb-2 text-center">Send Fatawa For Checking To <?php echo e($checker); ?> by <?php echo e($transfer_by); ?></h1>
            
            
                
                <?php if(session('success')): ?>
    <div class="alert alert-success">
        <?php echo e(session('success')); ?>

    </div>
<?php elseif(session('fileErrors')): ?>
    <div class="alert alert-danger">
        <?php $__currentLoopData = session('fileErrors'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php echo e($error); ?>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php elseif(session('error')): ?>
    <div class="alert alert-danger">
        <?php echo e(session('error')); ?>

    </div>
<?php endif; ?>
                
                <form action="<?php echo e(route('files.store')); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <div class="container">
                    <div class="row text-center">
                    <div class="form-group col-md-6">
                        <label for="name" class="btn bg-gradient-light mb-1">Select Folder Date:
                        <input type="date" class="form-control text-center" id="name" name="name" value="" required></label>
                    </div>
                    
                    <div class="form-group col-md-6 ">
                        <label for="files" class="btn bg-gradient-light mb-0 "><i class="material-icons text-sm">add</i>&nbsp;&nbsp;Choose Fatawa For Send:
                        <input type="file" class="form-control text-center" id="files" name="files[]" multiple required></label>
                    </div>
                    </div>
                </div>
                

                    <!-- Uploaded Files in Excel-like Table -->
                    <table class="table table-bordered custom-table">
                        <thead>
                            
                            <tr>
                                <th>File Name</th>
                                <th>Fatwa Code</th>
                                <th>Sender</th>
                                <th>File Created Date</th>
                                <th>Category</th>
                                <th>Fatwa Type</th>
                                <th>Darulifta</th>
                                <th>Checker</th>
                                <th>Transfer By</th>
                                
                                <input type="hidden" name="mail_folder_date_header" value="Mail Folder Date">
                                <input type="hidden" name="mail_sender_date_header" value="Mail Sender Date">
                                
                            </tr>
                        </thead>
                        <tbody id="file-details">
                            
                            <!-- Dynamically generated rows will go here -->
                            
                        </tbody>
                    </table>
                    
                    <button type="submit" class="btn btn-primary">Upload</button>
                </form>
                
            
            
            <script>
                
                document.addEventListener('DOMContentLoaded', function () {
                    document.getElementById('files').addEventListener('change', function (e) {
                    // Clear existing file details
                    
                    document.getElementById('file-details').innerHTML = '';
                
                    

                    let folderName = document.getElementById('name').value;
                    let selectedRoleElement = document.getElementById('select-role');
                    let selectedRole = selectedRoleElement ? selectedRoleElement.value : '';
                    let singleRoleElement = document.getElementById('single-role');
                    let singleRole = singleRoleElement ? singleRoleElement.textContent : '';

                    // Check if the user has multiple roles
                    if (singleRole === '' && selectedRole !== '') {
                        singleRole = selectedRole;
                    }                                    
                
                      // Loop through selected files and create input fields
                       // Create an array to store selected files
            const selectedFiles = Array.from(e.target.files);

            

// Sort selected files based on their order
            selectedFiles.sort((a, b) => a.index - b.index);

            
            console.log(selectedFiles);
            // Clear existing file details
            

            // Loop through sorted selected files and create input fields
            selectedFiles.forEach(function (file, index) {
                // console.log(file);
                        // Replace spaces with underscores in the original file name
                        const originalFileName = file.name;

                        const modifiedFileName = originalFileName.replace(/ /g, '_');
                        // console.log('Original File Name:', originalFileName);
                        // console.log('Modified File Name:', modifiedFileName);

                        let tr = document.createElement('tr');

                    // Create a hidden input field to store the original file name
                        let inputOriginalFileName = document.createElement('input');
                        inputOriginalFileName.type = 'hidden';
                        inputOriginalFileName.value = originalFileName;
                        inputOriginalFileName.name = 'original_file_name[]';
                        
                        tr.appendChild(inputOriginalFileName);
                        console.log('Original File Name Input Value:', inputOriginalFileName.value);




                        // Create a hidden input field to store the modified file name
                        let inputModifiedFileName = document.createElement('input');
                        inputModifiedFileName.type = 'hidden';
                        inputModifiedFileName.name = 'modified_file_name[]';
                        inputModifiedFileName.value = modifiedFileName;
                        tr.appendChild(inputModifiedFileName);
                        console.log('Modified File Name Input Value:', inputModifiedFileName.value);

                         // Create a hidden input field to store the file order
                        

                        // Display the modified file name in a separate cell in the table
                        let tdFileName = document.createElement('td');
                        tdFileName.textContent = modifiedFileName;

                
                        // Extract information from the file name
                        let fileNameParts = modifiedFileName.split('_');
                        if (fileNameParts.length >= 7) {
                            // Fatwa Code
                            let fatwaCode = fileNameParts[0] + '-' + fileNameParts[1];
                            fetch('/getQuestionData?ifta_code=' + fatwaCode)
                    .then(response => response.json())
                    .then(data => {
                            // Fatwa Code (Editable)
                            let tdFatwaCode = document.createElement('td');
                        let inputFatwaCode = document.createElement('input');
                        inputFatwaCode.type = 'text';
                        inputFatwaCode.name = 'fatwa_code[]'; // Use appropriate name for form submission
                        inputFatwaCode.className = 'form-control';
                        inputFatwaCode.value = fatwaCode; // Set the initial value
                        tdFatwaCode.appendChild(inputFatwaCode);

                        // Sender Name
                        let senderName = data.assign_id ? data.assign_id : "First send this fatwa to Mujeeb";
                        let tdSender = document.createElement('td');
                        let inputSender = document.createElement('input');
                        inputSender.type = 'text';
                        inputSender.name = 'sender[]';
                        inputSender.className = 'form-control';
                        inputSender.value = senderName;
                        inputSender.required = true;
                        tdSender.appendChild(inputSender);
                        if (!data.assign_id) {
        // If assign_id is null, set the row color to red
        tdSender.style.backgroundColor = 'red';

        // Disable the input field
        inputSender.disabled = true;
        inputSender.value = "No assign_id"; // You can set any desired text
    } else {
        // If assign_id is not null, allow form submission
        inputSender.required = true;
    }
                
                            // File Created Date
                            let createdDate = data.mujeeb_send_date ? data.mujeeb_send_date : "First send this fatwa to Mujeeb";
                            // File Created Date (Editable)
                            let tdDate = document.createElement('td');
                            let inputDate = document.createElement('input');
                            inputDate.type = 'text';
                            inputDate.name = 'file_created_date[]'; // Use appropriate name for form submission
                            inputDate.className = 'form-control';
                            inputDate.value = createdDate; // Set the initial value
                            tdDate.appendChild(inputDate);

                
                            // Category
                            let category = data.issue ? data.issue : "First send this fatwa to Mujeeb";
                            // Category (Editable)
                            let tdCategory = document.createElement('td');
                            let inputCategory = document.createElement('input');
                            inputCategory.type = 'text';
                            inputCategory.name = 'category[]'; // Use appropriate name for form submission
                            inputCategory.className = 'form-control';
                            inputCategory.value = category; // Set the initial value
                            tdCategory.appendChild(inputCategory);

                
                            // Fatwa Type (Dropdown)
                            let tdFatwaType = document.createElement('td');
                            let selectFatwaType = document.createElement('select');
                            selectFatwaType.name = 'fatwa_type[]';
                            selectFatwaType.className = 'custom-select'; // Apply custom CSS class
                            // selectFatwaType.className = 'form-control';
                            let optionNew = document.createElement('option');
                            optionNew.value = 'New';
                            optionNew.textContent = 'New';
                            let optionMahlENazar = document.createElement('option');
                            optionMahlENazar.value = 'Mahl e Nazar';
                            optionMahlENazar.textContent = 'Mahl e Nazar';
                            selectFatwaType.appendChild(optionNew);
                            selectFatwaType.appendChild(optionMahlENazar);
                            tdFatwaType.appendChild(selectFatwaType);
                            // Darulifta (Auto)
                            
                            let tdDarulifta = document.createElement('td');
                            let inputDarulifta = document.createElement('input');
                            inputDarulifta.type = 'text';
                            inputDarulifta.name = 'darulifta[]';
                            inputDarulifta.className = 'form-control';

                            // Set the value for 'darulifta_name' based on singleRole
                            inputDarulifta.value = singleRole;
                            // Function to update the 'darulifta' column value with '3btn' if selected
                            function updateDaruliftaValue() {
                            const select3btn = document.getElementById('3btn');
                            const selected3btnValue = select3btn ? select3btn.value : '';

                            // Concatenate 'darulifta_name' with '3btn' if '3btn' is selected
                            if (selected3btnValue === '3btn') {
                                inputDarulifta.value = singleRole + '3btn';
                            } else {
                                // Set the 'darulifta' value to 'darulifta_name' if '3btn' is not selected
                                inputDarulifta.value = singleRole;
                            }
                        }

                        // Add an event listener to the '3btn' dropdown to update the 'darulifta' value
                        // document.getElementById('3btn').addEventListener('change', updateDaruliftaValue);

                        // Append the <input> element to the <td> element
                        tdDarulifta.appendChild(inputDarulifta);

                        // Initially update the 'darulifta' value based on '3btn' selection
                        updateDaruliftaValue();
                            // Mail Folder Date (Using the stored folderName variable)
                            let tdFolderDate = document.createElement('td');
                            let inputFolderDate = document.createElement('input');
                            inputFolderDate.type = 'date';
                            inputFolderDate.name = 'mail_folder_date[]';
                            inputFolderDate.className = 'form-control';
                            inputFolderDate.value = folderName;
                            inputFolderDate.hidden = true; // Hide this field
                            tdFolderDate.appendChild(inputFolderDate);
                
                            // Mail Sender Date (Auto)
                            let tdSenderDate = document.createElement('td');
                            let inputSenderDate = document.createElement('input');
                            inputSenderDate.type = 'text';
                            inputSenderDate.name = 'mail_sender_date[]';
                            inputSenderDate.className = 'form-control';
                            inputSenderDate.value = new Date().toISOString().split('T')[0];
                            inputSenderDate.disabled = true;
                            inputSenderDate.hidden = true; // Hide this field
                            tdSenderDate.appendChild(inputSenderDate);
                
                            let tdChecker = document.createElement('td');
                            let inputChecker = document.createElement('input');
                            inputChecker.type = 'text'; // Since you're likely not displaying this value
                            inputChecker.name = 'checker[]';
                            inputChecker.className = 'form-control';
                            // Assuming you've already assigned `$checker` to a JavaScript variable
                            // You need to output its value here safely
                            inputChecker.value = "<?php echo e($checker); ?>"; // Use Blade syntax to insert the PHP variable
                            tdChecker.appendChild(inputChecker);

                            let tdTransferBy = document.createElement('td');
                            let inputTransferBy = document.createElement('input');
                            inputTransferBy.type = 'text'; // Since you're likely not displaying this value
                            inputTransferBy.name = 'transfer_by[]';
                            inputTransferBy.className = 'form-control';
                            // Assuming you've already assigned `$checker` to a JavaScript variable
                            // You need to output its value here safely
                            inputTransferBy.value = "<?php echo e($transfer_by); ?>"; // Use Blade syntax to insert the PHP variable
                            tdTransferBy.appendChild(inputTransferBy);
                
                            // Append elements to the row
                            tr.appendChild(tdFileName);
                            
                            tr.appendChild(tdFatwaCode);
                            tr.appendChild(tdSender);
                            tr.appendChild(tdDate);
                            tr.appendChild(tdCategory);
                            tr.appendChild(tdFatwaType);
                            tr.appendChild(tdDarulifta);
                            tr.appendChild(tdChecker);
                            tr.appendChild(tdTransferBy);
                            tr.appendChild(tdFolderDate);
                            tr.appendChild(tdSenderDate);
                            
                
                            document.getElementById('file-details').appendChild(tr);
                    })
                    .catch(error => {
                        // Handle errors, for example, if the Fatwa Code is not found
                        console.error(error);
                    });
            } else {
                // If the file name doesn't match the expected pattern, provide a message
                let tdErrorMessage = document.createElement('td');
                tdErrorMessage.textContent = 'Invalid file name format';
                tr.appendChild(tdFileName);
                tr.appendChild(tdErrorMessage);
            }
       
    });
});

                document.addEventListener('DOMContentLoaded', function () {
    // Your code here

    // Find the form element
    const form = document.querySelector('form');

    // Add a submit event listener to the form
    form.addEventListener('submit', function (e) {
        e.preventDefault(); // Prevent the default form submission behavior

        // Collect the form data
        const formData = new FormData(form);

        // You can now send the formData to the server using a fetch request or other methods
        // For example:
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                // If you need to set headers, you can do it here
            },
        })
        .then(response => response.json()) // If expecting a JSON response
        .then(data => {
            // Handle the response from the server
            console.log(data);
            // You can add code to handle success or error responses here
        })
        .catch(error => {
            // Handle any errors that occurred during the fetch
            console.error('Error:', error);
        });
    });
});
});

                
                </script>
                <script>
            document.addEventListener('DOMContentLoaded', function () {
                const folderDateInput = document.querySelector('[name="name"]');
                const filesInput = document.querySelector('[name="files[]"]');
                const selectRole = document.getElementById('select-role');
        
                filesInput.addEventListener('change', function (e) {
                    // Check if any files are selected
                    if (filesInput.files.length > 0) {
                        folderDateInput.disabled = true; // Disable the "Folder Date" input
                        selectRole.disabled = true;
                    } else {
                        folderDateInput.disabled = false; // Enable the "Folder Date" input
                        selectRole.disabled = false;
                    }
                });
            });
            document.getElementById('name').min = new Date().toISOString().split('T')[0];
        </script>
        </div>
        <?php if (isset($component)) { $__componentOriginalf30276552b63aa6c9559a1667ce359f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf30276552b63aa6c9559a1667ce359f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footers.auth','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('footers.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $attributes = $__attributesOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $component = $__componentOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__componentOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
    </main>
    <?php if (isset($component)) { $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.plugins','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('plugins'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $attributes = $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $component = $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/files/create.blade.php ENDPATH**/ ?>