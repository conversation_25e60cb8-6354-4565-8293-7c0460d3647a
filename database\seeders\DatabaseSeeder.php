<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;


class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        //  User::factory()->create([
        //     'name' => 'Admin',
        //     'email' => '<EMAIL>',
        //     'password' => ('secret')
        // ]);
        
        \App\Models\User::factory(10)->create();
        \App\Models\Role::create(['name'=>'Admin']);
        \App\Models\Role::create(['name'=>'Noorulirfan']);
        \App\Models\Role::create(['name'=>'Faizan-e-Ajmair']);
        \App\Models\Role::create(['name'=>'Gulzar-e-Taiba']);
        \App\Models\Role::create(['name'=>'Markaz-ul-Iqtisaad']);
        \App\Models\Role::create(['name'=>'Manager']);
        \App\Models\Role::create(['name'=>'Edit']);
        \App\Models\Role::create(['name'=>'Delete']);




    }
}
