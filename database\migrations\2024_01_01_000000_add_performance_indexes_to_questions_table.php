<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check existing indexes to avoid duplicates
        $existingIndexes = collect(DB::select("SHOW INDEX FROM questions"))->pluck('Key_name')->toArray();

        Schema::table('questions', function (Blueprint $table) use ($existingIndexes) {
            // Add indexes for better performance on Reception Fatawa queries (only if they don't exist)
            if (!in_array('idx_questions_branch_date', $existingIndexes)) {
                $table->index(['question_branch', 'rec_date'], 'idx_questions_branch_date');
            }
            if (!in_array('idx_questions_rec_date', $existingIndexes)) {
                $table->index(['rec_date'], 'idx_questions_rec_date');
            }
            if (!in_array('idx_questions_branch', $existingIndexes)) {
                $table->index(['question_branch'], 'idx_questions_branch');
            }
            if (!in_array('idx_questions_assign_id', $existingIndexes)) {
                $table->index(['assign_id'], 'idx_questions_assign_id');
            }
            if (!in_array('idx_questions_ifta_code', $existingIndexes)) {
                $table->index(['ifta_code'], 'idx_questions_ifta_code');
            }
        });

        // Add functional index using raw SQL for MariaDB compatibility (only if it doesn't exist)
        if (!in_array('idx_questions_month_branch', $existingIndexes)) {
            try {
                DB::statement('CREATE INDEX idx_questions_month_branch ON questions ((YEAR(rec_date)), (MONTH(rec_date)), question_branch)');
            } catch (\Exception $e) {
                // If functional index fails, create a regular composite index
                DB::statement('CREATE INDEX idx_questions_month_branch ON questions (rec_date, question_branch)');
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('questions', function (Blueprint $table) {
            $table->dropIndex('idx_questions_branch_date');
            $table->dropIndex('idx_questions_rec_date');
            $table->dropIndex('idx_questions_branch');
            $table->dropIndex('idx_questions_assign_id');
            $table->dropIndex('idx_questions_ifta_code');
        });

        // Drop functional index using raw SQL
        DB::statement('DROP INDEX IF EXISTS idx_questions_month_branch ON questions');
    }
};
