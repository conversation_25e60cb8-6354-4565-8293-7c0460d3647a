<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;

class MujeebController extends Controller
{
    public function index()
    {
        $daruliftas = DB::table('daruliftas')->select('id', 'darul_name')->get();
        $mujeebs = DB::table('mujeebs')->get();
        return view('pages.mujeeb', compact('daruliftas', 'mujeebs'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'mujeeb_name' => 'required',
            'darul_name' => 'required',
            'munsab' => 'required',
        ]);

        DB::table('mujeebs')->insert([
            'mujeeb_name' => $validatedData['mujeeb_name'],
            'darul_name' => $validatedData['darul_name'],
            'munsab' => $validatedData['munsab'],
        ]);

        return redirect()->route('mujeeb')->with('success', '<PERSON>jee<PERSON> added successfully.');
    }

    public function edit($id)
    {
        $upmujeeb = DB::table('mujeebs')->find($id);
        $daruliftas = DB::table('daruliftas')->select('id', 'darul_name')->get();

        return view('pages.edit_mujeeb', compact('upmujeeb', 'daruliftas'));
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'mujeeb_name' => 'required',
            'darul_name' => 'required',
            'munsab' => 'required',
        ]);

        DB::table('mujeebs')
            ->where('id', $id)
            ->update([
                'mujeeb_name' => $validatedData['mujeeb_name'],
                'darul_name' => $validatedData['darul_name'],
                'munsab' => $validatedData['munsab'],
            ]);

        return redirect()->route('mujeeb')->with('success', 'Mujeeb updated successfully');
    }

    public function deleteMujeeb($id): RedirectResponse
    {
        $mujeeb = DB::table('mujeebs')->where('id', $id)->first();

        if (!$mujeeb) {
            return redirect()->route('mujeeb')->with('error', 'Mujeeb not found');
        }

        DB::table('mujeebs')->where('id', $id)->delete();

        return redirect()->route('mujeeb')->with('success', 'Mujeeb deleted successfully');
    }
}