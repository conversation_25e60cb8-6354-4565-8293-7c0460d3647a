<div class="access-denied-overlay">
    <!-- Blurred Background -->
    <div class="blurred-background">
        <div class="container-fluid py-4">
            <div class="row">
                <div class="col-12">
                    <div class="card blur-content">
                        <div class="card-header">
                            <h5 class="mb-0">Dashboard</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>Sample Content</h6>
                                            <p>This content is blurred</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>Sample Content</h6>
                                            <p>This content is blurred</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>Sample Content</h6>
                                            <p>This content is blurred</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <h6>Sample Content</h6>
                                            <p>This content is blurred</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Access Denied Modal -->
    <div class="access-denied-modal">
        <div class="modal-content">
            <div class="text-center">
                <div class="icon-container mb-4">
                    <i class="fas fa-lock text-danger" style="font-size: 4rem;"></i>
                </div>
                
                <h3 class="text-danger mb-3">Access Denied</h3>
                
                <div class="alert alert-warning" role="alert">
                    <h5 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Daily Performance Required
                    </h5>
                    <p class="mb-3">
                        You must submit your daily performance report for <strong>{{ $todayDate }}</strong> 
                        before accessing the system.
                    </p>
                    <hr>
                    <p class="mb-0">
                        This is required for all Mujeeb and Superior users to track daily productivity and performance.
                    </p>
                </div>

                @if(auth()->user()->isMujeeb())
                    <div class="user-info mb-3">
                        <span class="badge bg-primary fs-6">
                            <i class="fas fa-user me-1"></i>
                            Mujeeb User
                        </span>
                    </div>
                @elseif(auth()->user()->isSuperior())
                    <div class="user-info mb-3">
                        <span class="badge bg-success fs-6">
                            <i class="fas fa-user-tie me-1"></i>
                            Superior User
                        </span>
                    </div>
                @endif

                <div class="action-buttons">
                    <button wire:click="redirectToPerformance" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-edit me-2"></i>
                        Submit Performance Report
                    </button>
                    
                    <button wire:click="checkPerformanceStatus" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-refresh me-2"></i>
                        Check Status
                    </button>
                </div>

                @if($hasSubmittedToday)
                    <div class="alert alert-success mt-3" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        Performance submitted! Refreshing page...
                    </div>
                    <script>
                        setTimeout(function() {
                            window.location.reload();
                        }, 2000);
                    </script>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
.access-denied-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.8);
}

.blurred-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    filter: blur(5px);
    opacity: 0.3;
    pointer-events: none;
}

.blur-content {
    filter: blur(3px);
    opacity: 0.5;
}

.access-denied-modal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.icon-container {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.action-buttons {
    margin-top: 1.5rem;
}

.user-info {
    margin: 1rem 0;
}

@media (max-width: 768px) {
    .access-denied-modal {
        padding: 1rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .action-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
