<?php

namespace App\Http\Controllers;
use App\Models\Darulifta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\RedirectResponse;

class DaruliftaController extends Controller
{
    public function ShowDarulifta()
    {
        $daruliftas = DB::table('daruliftas')->get();
        
        return view('pages.darulifta', ['daruliftas' => $daruliftas]);
    }
    public function AddDarul(Request $request)
    {
        // Validate the form data
        $validatedData = $request->validate([
            'darul_code' => 'required|unique:daruliftas',
            'darul_name' => 'required',
        ]);

        // Prepare data for insertion
        $data = [
            'darul_code' => $validatedData['darul_code'],
            'darul_name' => $validatedData['darul_name'],
        ];

        // Insert the data into the database
        DB::table('daruliftas')->insert($data);

        return redirect()->route('darulifta.add')->with('success', 'Darulifta added successfully');
    }
    public function UpDarul($id)
    {
        $updarul = DB::table('daruliftas')->find($id);

        return view('pages.udarulifta', ['updarul' => $updarul]);
    }

    public function UpdateDarul(Request $request, $id)
    {
        // Validate the form data
        $validatedData = $request->validate([
            'darul_code' => 'required|unique:daruliftas,darul_code,' . $id,
            'darul_name' => 'required',
        ]);

        // Update the data in the database
        DB::table('daruliftas')->where('id', $id)->update([
            'darul_code' => $validatedData['darul_code'],
            'darul_name' => $validatedData['darul_name'],
        ]);

        return redirect()->route('darulifta')->with('success', 'Darulifta updated successfully');
    }

    public function deleteDarul($id): RedirectResponse
    {
        // Find the Darulifta by ID
        $darulifta = DB::table('daruliftas')->where('id', $id)->first();

        // Check if the Darulifta exists
        if (!$darulifta) {
            return redirect()->route('darulifta')->with('error', 'Darulifta not found');
        }

        // Delete the Darulifta
        DB::table('daruliftas')->where('id', $id)->delete();

        return redirect()->route('darulifta')->with('success', 'Darulifta deleted successfully');
    }
}
