<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
{
    Schema::table('viralchat', function (Blueprint $table) {
        $table->boolean('seen_by_user')->default(false);  // Add column to track seen status
    });
}

public function down()
{
    Schema::table('viralchat', function (Blueprint $table) {
        $table->dropColumn('seen_by_user');  // Drop column if rollback is needed
    });
}

};
