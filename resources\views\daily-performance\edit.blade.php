@extends('layouts.app')

@section('content')
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Edit Performance Report</h5>
                            <p class="text-sm mb-0">
                                {{ $dailyPerformance->user->name }} - {{ $dailyPerformance->performance_date->format('F d, Y') }}
                                @if($dailyPerformance->is_submitted)
                                    <span class="badge bg-gradient-success ms-2">Submitted</span>
                                @else
                                    <span class="badge bg-gradient-warning ms-2">Draft</span>
                                @endif
                            </p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <a href="{{ route('daily-performance.show', $dailyPerformance) }}" class="btn btn-outline-secondary btn-sm mb-0">
                                    <i class="fas fa-arrow-left"></i>&nbsp;&nbsp;Back to View
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Form -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Performance Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('daily-performance.update', $dailyPerformance) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Basic Information (Read-only) -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">User</label>
                                    <input type="text" class="form-control" value="{{ $dailyPerformance->user->name }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Performance Date</label>
                                    <input type="date" class="form-control" value="{{ $dailyPerformance->performance_date->format('Y-m-d') }}" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task</label>
                                    <input type="text" class="form-control" value="{{ $dailyPerformance->task->title ?? 'No Task' }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <input type="text" class="form-control" value="{{ $dailyPerformance->department->name ?? 'No Department' }}" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Hours Worked *</label>
                                    <input type="number" step="0.5" min="0" max="24" name="hours_worked" 
                                           class="form-control @error('hours_worked') is-invalid @enderror"
                                           value="{{ old('hours_worked', $dailyPerformance->hours_worked) }}"
                                           placeholder="8.0">
                                    @error('hours_worked') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Overall Rating</label>
                                    <select name="overall_rating" class="form-select @error('overall_rating') is-invalid @enderror">
                                        <option value="">No Rating</option>
                                        <option value="poor" {{ old('overall_rating', $dailyPerformance->overall_rating) === 'poor' ? 'selected' : '' }}>Poor</option>
                                        <option value="fair" {{ old('overall_rating', $dailyPerformance->overall_rating) === 'fair' ? 'selected' : '' }}>Fair</option>
                                        <option value="good" {{ old('overall_rating', $dailyPerformance->overall_rating) === 'good' ? 'selected' : '' }}>Good</option>
                                        <option value="excellent" {{ old('overall_rating', $dailyPerformance->overall_rating) === 'excellent' ? 'selected' : '' }}>Excellent</option>
                                    </select>
                                    @error('overall_rating') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Tasks Completed -->
                        <div class="form-group">
                            <label class="form-label">Tasks Completed *</label>
                            <textarea name="tasks_completed" 
                                      class="form-control @error('tasks_completed') is-invalid @enderror"
                                      rows="4"
                                      placeholder="Describe the tasks you completed...">{{ old('tasks_completed', $dailyPerformance->tasks_completed) }}</textarea>
                            @error('tasks_completed') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Challenges Faced -->
                        <div class="form-group">
                            <label class="form-label">Challenges Faced</label>
                            <textarea name="challenges_faced" 
                                      class="form-control @error('challenges_faced') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Describe any challenges or obstacles you faced...">{{ old('challenges_faced', $dailyPerformance->challenges_faced) }}</textarea>
                            @error('challenges_faced') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Next Day Plan -->
                        <div class="form-group">
                            <label class="form-label">Next Day Plan</label>
                            <textarea name="next_day_plan" 
                                      class="form-control @error('next_day_plan') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Outline your plan for tomorrow...">{{ old('next_day_plan', $dailyPerformance->next_day_plan) }}</textarea>
                            @error('next_day_plan') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea name="additional_notes" 
                                      class="form-control @error('additional_notes') is-invalid @enderror"
                                      rows="2"
                                      placeholder="Any additional notes or comments...">{{ old('additional_notes', $dailyPerformance->additional_notes) }}</textarea>
                            @error('additional_notes') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        @if(auth()->user()->isSuperior() || auth()->user()->isNazim())
                            <!-- Superior Rating Section -->
                            <hr class="my-4">
                            <h6 class="mb-3">Superior Rating & Comments</h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Superior Rating</label>
                                        <select name="superior_rating" class="form-select @error('superior_rating') is-invalid @enderror">
                                            <option value="">No Rating</option>
                                            <option value="poor" {{ old('superior_rating', $dailyPerformance->superior_rating) === 'poor' ? 'selected' : '' }}>Poor</option>
                                            <option value="fair" {{ old('superior_rating', $dailyPerformance->superior_rating) === 'fair' ? 'selected' : '' }}>Fair</option>
                                            <option value="good" {{ old('superior_rating', $dailyPerformance->superior_rating) === 'good' ? 'selected' : '' }}>Good</option>
                                            <option value="excellent" {{ old('superior_rating', $dailyPerformance->superior_rating) === 'excellent' ? 'selected' : '' }}>Excellent</option>
                                        </select>
                                        @error('superior_rating') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Superior Comments & Instructions</label>
                                <textarea name="superior_comments" 
                                          class="form-control @error('superior_comments') is-invalid @enderror"
                                          rows="4"
                                          placeholder="Provide feedback, comments, or instructions for the user...">{{ old('superior_comments', $dailyPerformance->superior_comments) }}</textarea>
                                @error('superior_comments') <div class="invalid-feedback">{{ $message }}</div> @enderror
                            </div>
                        @endif

                        <!-- Submit Buttons -->
                        <div class="form-group text-end">
                            <a href="{{ route('daily-performance.show', $dailyPerformance) }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i>&nbsp;&nbsp;Cancel
                            </a>
                            <button type="submit" name="action" value="save" class="btn btn-outline-primary me-2">
                                <i class="fas fa-save"></i>&nbsp;&nbsp;Save Changes
                            </button>
                            @if(!$dailyPerformance->is_submitted && $dailyPerformance->user_id === auth()->id())
                                <button type="submit" name="action" value="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane"></i>&nbsp;&nbsp;Save & Submit
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
