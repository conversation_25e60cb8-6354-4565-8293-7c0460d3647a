<?php

namespace App\Http\Controllers;

use App\Models\Department;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class DepartmentController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of departments.
     */
    public function index()
    {
        $this->authorize('manage-departments');
        
        return view('departments.index');
    }

    /**
     * Show the form for creating a new department.
     */
    public function create()
    {
        $this->authorize('create', Department::class);
        
        return view('departments.create');
    }

    /**
     * Store a newly created department.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Department::class);
        
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:departments',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        Department::create($validated);

        return redirect()->route('departments.index')
            ->with('success', 'Department created successfully.');
    }

    /**
     * Display the specified department.
     */
    public function show(Department $department)
    {
        $this->authorize('view', $department);
        
        $users = $department->users()->with('roles')->paginate(10);
        
        return view('departments.show', compact('department', 'users'));
    }

    /**
     * Show the form for editing the department.
     */
    public function edit(Department $department)
    {
        $this->authorize('update', $department);
        
        return view('departments.edit', compact('department'));
    }

    /**
     * Update the specified department.
     */
    public function update(Request $request, Department $department)
    {
        $this->authorize('update', $department);
        
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:departments,name,' . $department->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        $department->update($validated);

        return redirect()->route('departments.index')
            ->with('success', 'Department updated successfully.');
    }

    /**
     * Remove the specified department.
     */
    public function destroy(Department $department)
    {
        $this->authorize('delete', $department);
        
        if ($department->users()->count() > 0) {
            return redirect()->route('departments.index')
                ->with('error', 'Cannot delete department with assigned users.');
        }

        $department->delete();

        return redirect()->route('departments.index')
            ->with('success', 'Department deleted successfully.');
    }

    /**
     * Show user assignment form.
     */
    public function assignUsers(Department $department)
    {
        $this->authorize('assignUsers', $department);
        
        $assignedUsers = $department->users;
        $availableUsers = User::whereNotIn('id', $assignedUsers->pluck('id'))->get();
        
        return view('departments.assign-users', compact('department', 'assignedUsers', 'availableUsers'));
    }

    /**
     * Assign users to department.
     */
    public function storeUserAssignments(Request $request, Department $department)
    {
        $this->authorize('assignUsers', $department);
        
        $validated = $request->validate([
            'user_ids' => 'array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $validated['user_ids'] ?? [];
        
        // Sync users with additional pivot data
        $syncData = [];
        foreach ($userIds as $userId) {
            $syncData[$userId] = [
                'assigned_at' => now(),
                'assigned_by' => auth()->id(),
            ];
        }
        
        $department->users()->sync($syncData);

        return redirect()->route('departments.show', $department)
            ->with('success', 'Users assigned to department successfully.');
    }

    /**
     * Remove user from department.
     */
    public function removeUser(Department $department, User $user)
    {
        $this->authorize('assignUsers', $department);
        
        $department->users()->detach($user);

        return redirect()->route('departments.show', $department)
            ->with('success', 'User removed from department successfully.');
    }

    /**
     * Get department statistics.
     */
    public function statistics()
    {
        $this->authorize('manage-departments');
        
        $stats = [
            'total_departments' => Department::count(),
            'active_departments' => Department::where('is_active', true)->count(),
            'total_assigned_users' => \DB::table('user_departments')->distinct('user_id')->count(),
            'departments_with_users' => Department::has('users')->count(),
        ];

        return response()->json($stats);
    }
}
