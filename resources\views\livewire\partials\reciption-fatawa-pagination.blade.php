<!-- Month-wise Pagination Component -->
<style>
.month-count {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
    margin-top: 2px;
}

.month-grid-item.active .month-count {
    color: #fff;
    font-weight: 600;
}

.month-grid-item:hover .month-count {
    color: #0d6efd;
}

.month-grid-item.active:hover .month-count {
    color: #fff;
}
</style>
@if($totalMonths > 1 && $tempSelectedTimeFrame !== 'all')
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <h6 class="mb-0">
                <i class="fas fa-calendar-alt me-2"></i>
                Month Navigation
            </h6>
        </div>
        <div class="modern-card-body">
            <!-- Current Month Info -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="current-month-info">
                    <span class="text-muted">
                        <i class="fas fa-calendar-day me-1"></i>
                        Currently viewing:
                    </span>
                    <strong class="text-primary">
                        @if($currentMonth)
                            {{ \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->format('F Y') }}
                            @if(isset($monthCounts[$currentMonth]))
                                <span class="text-muted">({{ $monthCounts[$currentMonth] }} fatawa)</span>
                            @endif
                        @endif
                    </strong>
                </div>
                <div class="month-counter">
                    <span class="badge bg-primary">
                        Month {{ $currentMonthIndex + 1 }} of {{ $totalMonths }}
                    </span>
                </div>
            </div>

            <!-- Month Navigation Controls -->
            <div class="month-navigation-controls mb-4">
                <div class="d-flex justify-content-center align-items-center gap-3">
                    <!-- Previous Month -->
                    @if($currentMonthIndex > 0)
                        <button wire:click="previousMonth" class="btn btn-outline-primary month-nav-btn" title="Previous Month" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="previousMonth">
                                <i class="fas fa-chevron-left me-1"></i>
                                Previous Month
                            </span>
                            <span wire:loading wire:target="previousMonth">
                                <i class="fas fa-spinner fa-spin me-1"></i>
                                Loading...
                            </span>
                        </button>
                    @endif

                    <!-- Current Month Display -->
                    <div class="current-month-display">
                        <span class="badge bg-success fs-6 px-3 py-2">
                            @if($currentMonth)
                                {{ \Carbon\Carbon::createFromFormat('Y-m', $currentMonth)->format('F Y') }}
                                @if(isset($monthCounts[$currentMonth]))
                                    <br><small>({{ $monthCounts[$currentMonth] }} fatawa)</small>
                                @endif
                            @endif
                        </span>
                    </div>

                    <!-- Next Month -->
                    @if($currentMonthIndex < $totalMonths - 1)
                        <button wire:click="nextMonth" class="btn btn-outline-primary month-nav-btn" title="Next Month" wire:loading.attr="disabled">
                            <span wire:loading.remove wire:target="nextMonth">
                                Next Month
                                <i class="fas fa-chevron-right ms-1"></i>
                            </span>
                            <span wire:loading wire:target="nextMonth">
                                <i class="fas fa-spinner fa-spin me-1"></i>
                                Loading...
                            </span>
                        </button>
                    @endif
                </div>
            </div>

            <!-- Month Selector Grid -->
            <div class="month-selector-section">
                <h6 class="mb-3 text-center">
                    <i class="fas fa-calendar-week me-2"></i>
                    Quick Month Selection
                </h6>
                <div class="month-grid">
                    @foreach($availableMonths as $index => $monthYear)
                        @php
                            $monthDate = \Carbon\Carbon::createFromFormat('Y-m', $monthYear);
                            $isActive = $index === $currentMonthIndex;
                            $count = $monthCounts[$monthYear] ?? 0;
                        @endphp
                        <button wire:click="goToMonth({{ $index }})"
                                class="month-grid-item {{ $isActive ? 'active' : '' }}"
                                title="View {{ $monthDate->format('F Y') }} ({{ $count }} fatawa)">
                            <div class="month-name">{{ $monthDate->format('M') }}</div>
                            <div class="month-year">{{ $monthDate->format('Y') }}</div>
                            <div class="month-count">({{ $count }})</div>
                        </button>
                    @endforeach
                </div>
            </div>

        </div>
    </div>
@endif

<!-- Loading State for Month Navigation -->
<div wire:loading.delay wire:target="nextMonth,previousMonth,goToMonth" class="text-center my-3">
    <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <span class="ms-2 text-muted">Loading month data...</span>
</div>
