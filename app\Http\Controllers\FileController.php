<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class FileController extends Controller
{
    public function create()
{
    return view('files.create');
}
public function store(Request $request)
{
    DB::beginTransaction(); // Start a database transaction
    $fileStorageErrors = []; // Initialize an array to track file storage errors

    try {
        // Validate the request
        $request->validate([
            'files.*' => 'required|file|max:10240', // 10MB max file size (adjust as needed)
            'fatwa_code.*' => 'required',
            'sender.*' => 'required',
            'file_created_date.*' => 'required',
            'category.*' => 'required',
            'fatwa_type.*' => 'required',
            'mail_folder_date.*' => 'required',
            'darulifta.*' => 'required',
            'checker.*' => 'required',
        ]);

        // Get the fatwa codes from the request
        $fatwaCodes = $request->input('fatwa_code');

        // Find any duplicate fatwa codes
        $duplicateFatwaCodes = array_diff_assoc($fatwaCodes, array_unique($fatwaCodes));

        if (!empty($duplicateFatwaCodes)) {
            DB::rollBack();
            // Get the first duplicate code to show in the error message
            $firstDuplicateCode = reset($duplicateFatwaCodes);

            return back()->with('error', 'Duplicate fatwa code "' . $firstDuplicateCode . '" is not allowed.');
        }
        // Check if any of the fatwa codes already exist in the uploaded_files table
        foreach ($fatwaCodes as $index => $fatwaCode) {
            $existingFile = DB::table('uploaded_files')
                ->where('file_code', $fatwaCode)
                ->where('selected', 0)
                ->first();

            if ($existingFile) {
                // If a record exists, return a message and rollback the transaction
                DB::rollBack();

                return back()->with('error', 'Fatwa No.'
                 . $existingFile->file_code .'already sent for checking to checker '
                    . $existingFile->checker . ' in mail folder date '
                    . $existingFile->mail_folder_date . ' by '
                    . $existingFile->sender);
            }
        }

        // Proceed with the rest of your file handling and data insertion logic

        $mailFolderDates = $request->input('mail_folder_date');
        $daruliftas = $request->input('darulifta');
        $checker = $request->input('checker');
        $transfer_by = $request->input('transfer_by');
        $firstMailFolderDate = $mailFolderDates[0];
        $firstDarulifta = $daruliftas[0];

        // Check for downloaded records (already in your code)
        $existingRecord = DB::table('uploaded_files')
            ->where('mail_folder_date', $firstMailFolderDate)
            ->where('darulifta_name', $firstDarulifta)
            ->where('checker', $checker)
            ->where('selected', 0)
            ->whereNotNull('downloaded_by_admin')
            ->exists();

        if ($existingRecord) {
            DB::rollBack();
            return redirect()->route('create', ['checker' => $checker[0], 'transfer_by' => $transfer_by[0]])
                ->with('error', 'This mail folder has been downloaded by admin, therefore select another mail folder date.');
        }

        // Continue with your file uploading logic and database insertions

        $name = $request->input('mail_folder_date')[0];
        $folderName = Str::slug($name);
        $files = $request->file('files');
        $originalFileNames = $request->input('original_file_name'); // Retrieve the original file names
        $modifiedFileNames = $request->input('modified_file_name'); // Retrieve the modified file names
        $fatwaCodes = $request->input('fatwa_code');
        $senders = $request->input('sender');
        $fileCreatedDates = $request->input('file_created_date');
        $categories = $request->input('category');
        $fatwaTypes = $request->input('fatwa_type');
        $mailFolderDates = $request->input('mail_folder_date');
        $daruliftas = $request->input('darulifta');
        $checker = $request->input('checker');
        $transfer_by = $request->input('transfer_by');
        $transfer = $transfer_by[0];
        $folderchecker = $checker[0];
        $firstDarulifta = $daruliftas[0];

        // Create the folder if it doesn't exist
        $iftaFolderName = $folderName . $firstDarulifta . $folderchecker . $transfer;
        Storage::disk('public')->makeDirectory($iftaFolderName);
        Storage::disk('public')->makeDirectory("$iftaFolderName/Checked");
        Storage::disk('public')->makeDirectory("$iftaFolderName/Mahl-e-Nazar");

        foreach ($files as $index => $file) {
            $originalFilename = $file->getClientOriginalName();
            $modifiedFilename = str_replace(' ', '_', $originalFilename);

            try {
                $path = $file->storeAs($iftaFolderName, $modifiedFilename, 'public');
            } catch (\Exception $e) {
                $fileStorageErrors[] = $e->getMessage();
            }
        }

        if (empty($fileStorageErrors)) {
            foreach ($files as $index => $file) {
                $originalFilename = $originalFileNames[$index];
                $modifiedFilename = $modifiedFileNames[$index];
                $fatwaCode = $fatwaCodes[$index];
                $sender = $senders[$index];
                $fileCreatedDate = $fileCreatedDates[$index];
                $fatwaType = $fatwaTypes[$index];
                $mailFolderDate = $mailFolderDates[$index];
                $daruliftaName = $daruliftas[$index];
                $category = $categories[$index];
                $checkers = $checker[$index];
                $transfers = $transfer_by[$index];

                DB::table('uploaded_files')->insert([
                    'file_name' => $modifiedFilename,
                    'file_code' => $fatwaCode,
                    'sender' => $sender,
                    'file_created_date' => $fileCreatedDate,
                    'ftype' => $fatwaType,
                    'mail_folder_date' => $mailFolderDate,
                    'mail_recived_date' => now(),
                    'darulifta_name' => $daruliftaName,
                    'category' => $category,
                    'checker' => $checkers,
                    'transfer_by' => $transfers,
                ]);
            }

            foreach ($fatwaCodes as $code) {
                DB::table('questions')
                    ->where('ifta_code', $code)
                    ->update([
                        'send_to_mufti' => 1,
                    ]);
            }

            DB::commit(); // Commit the transaction if files were stored successfully
            return back()->with('success', 'Files uploaded and data inserted successfully.');
        } else {
            DB::rollBack();
            return back()->with('fileErrors', $fileStorageErrors);
        }
    } catch (\Exception $e) {
        DB::rollBack();
        return back()->with('error', 'An error occurred. Please try again.');
    }
}

public function upload(Request $request)
{
    // Handle file upload logic
    $selectedDarulifta = $request->input('darulifta_select');
    $selectedFolder = $request->input('folder_select');
    $checker_id = $request->input('checker_id');
    $byMufti = $request->input('by_mufti');

    // Initialize the main folder
    $mainFolder = $selectedFolder . $selectedDarulifta . "Checked_by_" . $checker_id;
    Storage::disk('public')->makeDirectory($mainFolder);

    // Get inputs for file names and folders
    $checkedFileNames = $request->input('checked_file_name'); // File names
    $checkedFolders = $request->input('checked_folder'); // Labels for subfolders
    $rowIds = $request->input('row_id'); // Row IDs for database update
   // Viral checkboxes
    $checkers = $request->input('checker');
    $virals = $request->input('viral', []); // Get the 'viral' input


// Convert all values to integers for consistency
$virals = array_map(function ($rowId) use ($virals) {
    return isset($virals[$rowId]) ? intval($virals[$rowId]) : 0;
}, $rowIds);

Log::info('Row IDs:', $rowIds); // Should show [2155, 2156, 2157]
Log::info('Virals:', $virals); // Should align with the row IDs
    // Debugging: Log the received data
    Log::info('Checked Folders:', $checkedFolders);
    Log::info('Checked File Names:', $checkedFileNames);
    Log::info('Viral:', $virals);
    // dd($virals);

    foreach ($rowIds as $index => $rowId) {

        if (empty($checkedFileNames[$index]) || empty($checkedFolders[$index])) {
            Log::info("Skipping row ID {$rowId} due to missing file name or folder.");
            continue;
        }

        $folderLabel = $checkedFolders[$index];
        $fileName = $checkedFileNames[$index];
        $muftiValue = $byMufti[$index];
        $isViral = $virals[$index];
        // Use the checker value directly from the form
        $checker = $checkers[$index]; // Already set in the form, no need to check for null

        // Construct the folder path based on the label and Mufti value
        $finalFolderPath = $selectedFolder . $selectedDarulifta . "Checked_by_" . $checker_id . "_" . $muftiValue . DIRECTORY_SEPARATOR . $folderLabel;
        Storage::disk('public')->makeDirectory($finalFolderPath);

        // Retrieve the file from the request using the file name
        $file = $request->file('checked_file')[$index] ?? null;

        if ($file) {
            // Store the file in the constructed subfolder path
            $originalFilename = $file->getClientOriginalName();
            $file->storeAs($finalFolderPath, $originalFilename, 'public');
        }

        // Move the file if byMufti value is 'to_checker'
        if ($muftiValue === 'to_checker') {
            $previousPath = $selectedFolder . $selectedDarulifta . "Checked_by_" . $checker_id . "_" . "to_mujeeb" . DIRECTORY_SEPARATOR . $folderLabel . DIRECTORY_SEPARATOR . $fileName;
            $newPath = $finalFolderPath . DIRECTORY_SEPARATOR . $fileName;

            if (Storage::disk('public')->exists($previousPath)) {
                Storage::disk('public')->move($previousPath, $newPath);
            }
        }
        Log::info("Updating row ID: {$rowId}", [
            'folderLabel' => $folderLabel,
            'fileName' => $fileName,
            'muftiValue' => $muftiValue,
            'isViral' => $isViral,
        ]);
        // Update the database for each row
        DB::table('uploaded_files')
            ->where('id', $rowId)
            ->update([
                'selected' => 1, // Set 'selected' to 1 if this block runs
                'checked_file_name' => $fileName ?? null,
                'checked_folder' => $folderLabel ?? null,
                'checked_grade' => $request->input('checked_grade')[$index] ?? null,
                'checked_tasurat' => $request->input('checked_tasurat')[$index] ?? null,
                'checked_instructions' => $request->input('checked_Instructions')[$index] ?? null,
                'by_mufti' => $muftiValue ?? null,
                'viral' => $isViral, // Insert viral checkbox value
                'checked_date' => now(),
                'checker' => $checker, // Use checker value from form

            ]);
    }

    // After the foreach loop for uploading files

// Determine the next available folder date after the current one
$username = Auth::user()->name;
$checker = DB::table('checker')
    ->where('checker_name', $username)
    ->first()
    ->folder_id;

$nextFolderData = DB::table('uploaded_files')
    ->distinct()
    ->where('selected', 0)
    ->where('darulifta_name', $selectedDarulifta)
    ->where(function ($query) use ($checker, $selectedFolder) {
        if ($checker == 'mufti_ali_asghar') {
            $query->whereNull('checker')
                  ->orWhere('checker', $checker);
        } else {
            $query->where('checker', $checker);
        }
        $query->whereRaw("STR_TO_DATE(mail_folder_date, '%Y-%m-%d') >= STR_TO_DATE(?, '%Y-%m-%d')", [$selectedFolder]);

    })
    ->orderBy('mail_folder_date', 'asc')
    ->first();

// Debugging to check what data is returned
Log::info('Next Folder Data:', [$nextFolderData]);

// If there's a next folder, redirect to it
if ($nextFolderData && $nextFolderData->mail_folder_date) {
    $nextFolderDate = $nextFolderData->mail_folder_date;
    $nextUrl = url("/sending1/{$selectedDarulifta}/{$nextFolderDate}");
    return redirect($nextUrl)->with('success', 'Files uploaded and records updated successfully');
}

// If no next folder or $nextFolderDate is null, redirect to sending-fatawa
return redirect()->route('sending-fatawa')->with('success', 'Files uploaded and records updated successfully');

}
public function deleteFile(Request $request)
{
    $path = $request->input('path');

    $id = $request->input('id');
    $filename = $request->input('file_name');
    $filcode = $request->input('file_code');

    // Extract the file path to construct the file's location
    Storage::delete("public/$path/$filename");

    // Delete the file entry from the database based on the provided ID
    DB::table('uploaded_files')->where('id', $id)->delete();
    DB::table('questions')->where('ifta_code', $filcode)->update([
        'send_to_mufti' => 0,
    ]);


    // Redirect back to the previous page with a success message
    return redirect()->back()->with('success', 'File deleted successfully.');
}
public function deleteFolder(Request $request)
{
    $fatwaCodes = $request->input('file_code');
    $path = $request->input('path');

    $daruliftaname = $request->input('darulifta_name');
    $folderpath = $path.$daruliftaname;

    // Extract the file path to construct the file's location
    Storage::deleteDirectory("public/$folderpath");

    // Delete the file entry from the database based on the provided ID
    DB::table('uploaded_files')
        ->where('mail_folder_date', $path)
        ->where('darulifta_name', $daruliftaname)
        ->delete();
        foreach ($fatwaCodes as $code){
            DB::table('questions')
                ->where('ifta_code', $code)
                ->update([
                    'send_to_mufti' => 0,

                ]);
            }
    // Redirect back to the previous page with a success message
    return redirect()->back()->with('success', 'File deleted successfully.');
}
public function deleteCheckFile(Request $request)
{
    $id = $request->input('id');
    $path = $request->input('path');
    $foldername = $request->input('folder_name');
    $filename = $request->input('file_name');

    // Extract the file path to construct the file's location
    Storage::delete("public/$path/$foldername/$filename");

    // Delete the file entry from the database based on the provided ID
    DB::table('uploaded_files')->where('id', $id)
    ->update([
        'selected' => 0,  // An integer 0
        'checked_file_name' => null,  // Null for other columns
        'checked_folder' => null,
        'checked_grade' => null,
        'checked_tasurat' => null,
        'checked_instructions' => null,
    ]);

    // Redirect back to the previous page with a success message
    return redirect()->back()->with('success', 'File deleted successfully.');
}

public function deleteCheckFolder(Request $request)
{
    $path = $request->input('path');

    $daruliftaname = $request->input('darulifta_name');
    $folderpath = $path.$daruliftaname.'Checked';

    // Extract the file path to construct the file's location
    Storage::deleteDirectory("public/$folderpath");

    // Delete the file entry from the database based on the provided ID
    DB::table('uploaded_files')
        ->where('mail_folder_date', $path)
        ->where('darulifta_name', $daruliftaname)
        ->update([
            'selected' => 0,  // An integer 0
            'checked_file_name' => null,  // Null for other columns
            'checked_folder' => null,
            'checked_grade' => null,
            'checked_tasurat' => null,
            'checked_instructions' => null,
        ]);

    // Redirect back to the previous page with a success message
    return redirect()->back()->with('success', 'File deleted successfully.');
}
public function storFile()
    {
       // Specify the directory you want to list
       $directory = storage_path('app/public');

       // Get the directory structure
       $storageItems = $this->getDirectoryStructure($directory);

       // Pass the array to the Blade view
       return view('pages.storagelist', [
           'storageItems' => $storageItems,
       ]);


    }
    private function getDirectoryStructure($directory)
    {
        $result = [];

        // Get all files and directories within the specified directory
        $items = array_diff(scandir($directory), array('.', '..'));

        foreach ($items as $item) {
            $path = $directory . DIRECTORY_SEPARATOR . $item;

            if (is_dir($path)) {
                // Recursively get the structure of the subdirectory
                $result[] = [
                    'name' => $item,
                    'type' => 'directory',
                    'children' => $this->getDirectoryStructure($path)
                ];
            } else {
                $result[] = [
                    'name' => $item,
                    'type' => 'file'
                ];
            }
        }

        return $result;
    }
    public function renameFolders()
{
    // Define the base path for your storage
    $basePath = storage_path('app/public');

    // Get all top-level directories
    $directories = File::directories($basePath);

    // Initialize an array to hold all found directories
    $allDirectories = [];

    // Function to recursively get directories
    $this->getAllDirectories($basePath, $allDirectories);

    // Loop through each directory
    foreach ($allDirectories as $directory) {
        // Check if the folder name is 'mahl-e-nazar'
        if (basename($directory) === 'mahl-e-nazar') {
            // Construct the new folder name
            $newDirectory = dirname($directory) . DIRECTORY_SEPARATOR . 'Mahl-e-Nazar';

            // Rename the folder
            File::move($directory, $newDirectory);

            // Log the renaming action (optional)
            \Log::info("Renamed: {$directory} to {$newDirectory}");
        }
    }

    return response()->json(['status' => 'success', 'message' => 'Folders renamed successfully']);
}

// Recursive function to fetch all directories
private function getAllDirectories($basePath, &$allDirectories)
{
    // Get all directories in the current base path
    $directories = File::directories($basePath);

    // Add the current directories to the allDirectories array
    foreach ($directories as $directory) {
        $allDirectories[] = $directory;

        // Recursively fetch directories in the current directory
        $this->getAllDirectories($directory, $allDirectories);
    }
}

public function uploadUpdate(Request $request)
{
    $request->validate([
        'file' => 'required|file|mimes:pdf,doc,docx',
        'id' => 'required|integer',
        'mail_folder_date' => 'required|string',
        'darulifta_name' => 'required|string',
        'checker' => 'required|string',
        'transferBy' => 'nullable|string',
    ]);

    $file = $request->file('file');
    $transferBy = $request->input('transferBy');

    if (!empty($transferBy)) {
        $folderPath = $request->mail_folder_date . $request->darulifta_name . $request->checker . '_by_' . $transferBy;
    } else {
        $folderPath = $request->mail_folder_date . $request->darulifta_name . $request->checker;
    }

    // Save the file in the public storage
    $filePath = $file->storeAs($folderPath, $file->getClientOriginalName(), 'public');

    return response()->json(['success' => true, 'filePath' => $filePath]);
}
    public function updateFatwa(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:pdf,doc,docx',
            'id' => 'required|integer',
            'mail_folder_date' => 'required|string',
            'darulifta_name' => 'required|string',
            'checker' => 'required|string',
            'by_mufti' => 'required|string',
            'checked_folder' => 'required|string'
        ]);

        $file = $request->file('file');
        $folderPath = $request->mail_folder_date . $request->darulifta_name .'Checked_by_'. $request->checker . '_' . $request->by_mufti .'/'. $request->checked_folder;

        // Save the file in the public storage
        $filePath = $file->storeAs($folderPath, $file->getClientOriginalName(), 'public');

        // Save the file path to the database if needed
        // Example: File::create(['path' => $filePath, 'other_columns' => $request->other_columns]);

        return response()->json(['success' => true, 'filePath' => $filePath]);
    }
// public function upload(Request $request)
// {
//     $selectedFolder = $request->input('folder_select');

//     // Include "Checked" in the main folder name
//     $mainFolder = $selectedFolder . "Checked";

//     // Check if the main folder exists, if not, create it
//     Storage::disk('public')->makeDirectory($mainFolder);

//     $dataLabels = array_keys($request->file());

//     foreach ($dataLabels as $dataLabel) {
//         // Create a subfolder within the main folder for each data-label
//         $subfolderPath = $mainFolder . DIRECTORY_SEPARATOR . $dataLabel;

//         // Check if the subfolder exists, if not, create it
//         Storage::disk('public')->makeDirectory($subfolderPath);

//         // Upload files to the subfolder with their original names
//         foreach ($request->file($dataLabel) as $file) {
//             $originalFilename = $file->getClientOriginalName();
//             $file->storeAs($subfolderPath, $originalFilename, 'public');
//         }
//     }

//     return redirect()->back()->with('success', 'Files uploaded successfully');
// }
// public function update(Request $request)
// {

//     // Validate the request data as needed
//     $selected = $request->input('selected');
//     $checkedFileNames = $request->input('checked_file_name');
//     $checkedFolders = $request->input('checked_folder');
//     $checkedGrades = $request->input('checked_grade');
//     $checkedTasurats = $request->input('checked_tasurat');
//     $checkedInstructions = $request->input('checked_Instructions');
//     $rowIds = $request->input('row_id');

//     // Loop through the submitted data and update the database
//     foreach ($rowIds as $index => $rowId) {

//         // Update the record in the database using the DB facade
//         DB::table('uploaded_files')
//             ->where('id', $rowId)
//             ->update([
//                 'selected' => $selected[$index],
//                 'checked_file_name' => $checkedFileNames[$index],
//                 'checked_folder' => $checkedFolders[$index],
//                 'checked_grade' => $checkedGrades[$index],
//                 'checked_tasurat' => $checkedTasurats[$index],
//                 'checked_instructions' => $checkedInstructions[$index],
//             ]);
//     }


//     // Redirect back to the view with a success message
//     return redirect()->back()->with('success', 'Records updated successfully.');
// }
// public function upload(Request $request)
// {
//     // Get an array of unique data-label values from the form
//     $dataLabels = array_keys($request->file());

//     foreach ($dataLabels as $dataLabel) {
//         // Include "Checked" in the selected folder name
//         $selectedFolderWithChecked = $dataLabel . "Checked";

//         // Check if the selected folder exists, if not, create it
//         Storage::disk('public')->makeDirectory($selectedFolderWithChecked);

//         // Upload files to the subfolder
//         foreach ($request->file($dataLabel) as $file) {
//             $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();
//             $file->storeAs($selectedFolderWithChecked, $filename, 'public');
//         }
//     }

//     return redirect()->back()->with('success', 'Files uploaded successfully');
// }
}

// public function store(Request $request)
// {
//     $request->validate([
//         'name' => 'required',
//         'files.*' => 'required|file|max:10240', // 10MB max file size (adjust as needed)
//     ]);

//     $name = $request->input('name');
//     $folderName = Str::slug($name); // Use slug as folder name to ensure it's URL-friendly
//     $files = $request->file('files');

//     // Create the folder if it doesn't exist
//     Storage::disk('public')->makeDirectory($folderName);

//     foreach ($files as $file) {
//         // Get the original filename
//         $originalFilename = $file->getClientOriginalName();

//         // Store the file with the original filename
//         $path = $file->storeAs($folderName, $originalFilename, 'public');
//     }

//     return redirect()->route('files.create')->with('success', 'Files uploaded successfully.');
// }

