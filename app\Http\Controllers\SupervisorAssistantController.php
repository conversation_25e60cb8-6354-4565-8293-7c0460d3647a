<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SupervisorAssistant;
use App\Services\RoleManagementService;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class SupervisorAssistantController extends Controller
{
    use AuthorizesRequests;

    protected $roleManagementService;

    public function __construct(RoleManagementService $roleManagementService)
    {
        $this->roleManagementService = $roleManagementService;
    }

    /**
     * Display the supervisor-assistant mapping interface.
     */
    public function index()
    {
        $this->authorize('assign-supervisors');
        
        return view('supervisor-assistant.index');
    }

    /**
     * Get supervisor-assistant mappings data.
     */
    public function getMappings()
    {
        $this->authorize('assign-supervisors');
        
        $mappings = SupervisorAssistant::with(['supervisor.departments', 'assistant.departments'])
            ->where('is_active', true)
            ->get()
            ->groupBy('supervisor_id')
            ->map(function ($group) {
                return [
                    'supervisor' => $group->first()->supervisor,
                    'assistants' => $group->pluck('assistant'),
                    'assigned_at' => $group->first()->assigned_at,
                    'assigned_by' => $group->first()->assignedBy,
                ];
            })
            ->values();

        return response()->json($mappings);
    }

    /**
     * Assign assistants to a supervisor.
     */
    public function assignAssistants(Request $request, User $supervisor)
    {
        $this->authorize('assignAssistants', $supervisor);
        
        $validated = $request->validate([
            'assistant_ids' => 'array',
            'assistant_ids.*' => 'exists:users,id',
        ]);

        $this->roleManagementService->assignAssistants(
            $supervisor,
            $validated['assistant_ids'] ?? [],
            auth()->user()
        );

        return response()->json([
            'message' => 'Assistants assigned successfully.',
        ]);
    }

    /**
     * Create a new superior and optionally assign assistants.
     */
    public function createSuperior(Request $request)
    {
        $this->authorize('assign-supervisors');
        
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'assistant_ids' => 'array',
            'assistant_ids.*' => 'exists:users,id',
        ]);

        $user = User::findOrFail($validated['user_id']);
        
        $this->roleManagementService->assignSuperiorRole(
            $user,
            $validated['assistant_ids'] ?? [],
            auth()->user()
        );

        return response()->json([
            'message' => "Superior role assigned to {$user->name} successfully.",
        ]);
    }

    /**
     * Remove superior role from a user.
     */
    public function removeSuperior(User $supervisor)
    {
        $this->authorize('assignRoles', $supervisor);
        
        $this->roleManagementService->removeSuperiorRole($supervisor);

        return response()->json([
            'message' => "Superior role removed from {$supervisor->name}.",
        ]);
    }

    /**
     * Remove an assistant from a supervisor.
     */
    public function removeAssistant(User $supervisor, User $assistant)
    {
        $this->authorize('assignAssistants', $supervisor);
        
        SupervisorAssistant::where('supervisor_id', $supervisor->id)
            ->where('assistant_id', $assistant->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        return response()->json([
            'message' => "Removed {$assistant->name} from {$supervisor->name}'s team.",
        ]);
    }

    /**
     * Get available assistants for assignment.
     */
    public function getAvailableAssistants()
    {
        $this->authorize('assign-supervisors');
        
        $assistants = $this->roleManagementService->getEligibleAssistants();
        
        return response()->json($assistants);
    }

    /**
     * Get eligible users for superior role.
     */
    public function getEligibleSuperiors()
    {
        $this->authorize('assign-supervisors');
        
        $superiors = $this->roleManagementService->getEligibleSuperiors();
        
        return response()->json($superiors);
    }

    /**
     * Get mapping statistics.
     */
    public function getStatistics()
    {
        $this->authorize('assign-supervisors');
        
        $totalSuperiors = User::whereHas('roles', function ($query) {
            $query->where('name', 'Superior');
        })->count();

        $activeMappings = SupervisorAssistant::where('is_active', true)->count();
        $totalAssistants = SupervisorAssistant::where('is_active', true)->distinct('assistant_id')->count();
        $unassignedAssistants = $this->roleManagementService->getEligibleAssistants()->count();

        $statistics = [
            'total_superiors' => $totalSuperiors,
            'active_mappings' => $activeMappings,
            'total_assistants' => $totalAssistants,
            'unassigned_assistants' => $unassignedAssistants,
            'avg_assistants_per_superior' => $totalSuperiors > 0 ? round($totalAssistants / $totalSuperiors, 1) : 0,
        ];

        return response()->json($statistics);
    }

    /**
     * Export supervisor-assistant mappings.
     */
    public function exportMappings(Request $request)
    {
        $this->authorize('generate-reports');
        
        $format = $request->get('format', 'json');
        $mappings = $this->getMappings()->getData();
        $statistics = $this->getStatistics()->getData();
        
        $data = [
            'generated_at' => now()->toISOString(),
            'statistics' => $statistics,
            'mappings' => $mappings,
        ];
        
        switch ($format) {
            case 'pdf':
                // Here you would implement PDF generation
                return response()->json(['message' => 'PDF export not yet implemented']);
                
            case 'excel':
                // Here you would implement Excel export
                return response()->json(['message' => 'Excel export not yet implemented']);
                
            default:
                return response()->json($data);
        }
    }

    /**
     * Get supervisor's team information.
     */
    public function getSupervisorTeam(User $supervisor)
    {
        $this->authorize('viewPerformance', $supervisor);
        
        $team = SupervisorAssistant::with(['assistant.departments'])
            ->where('supervisor_id', $supervisor->id)
            ->where('is_active', true)
            ->get()
            ->pluck('assistant');

        return response()->json([
            'supervisor' => $supervisor->load('departments'),
            'assistants' => $team,
            'team_size' => $team->count(),
        ]);
    }

    /**
     * Get assistant's supervisor information.
     */
    public function getAssistantSupervisor(User $assistant)
    {
        $this->authorize('viewPerformance', $assistant);
        
        $mapping = SupervisorAssistant::with(['supervisor.departments'])
            ->where('assistant_id', $assistant->id)
            ->where('is_active', true)
            ->first();

        if (!$mapping) {
            return response()->json([
                'assistant' => $assistant->load('departments'),
                'supervisor' => null,
                'assigned_at' => null,
            ]);
        }

        return response()->json([
            'assistant' => $assistant->load('departments'),
            'supervisor' => $mapping->supervisor,
            'assigned_at' => $mapping->assigned_at,
        ]);
    }
}
