<!-- Sending Fatawa Statistics Component -->
@php
    $totalCounts = 0;
    $overallFolderCount = 0;
    
    // Calculate statistics
    if ($selectedmufti == 'all') {
        foreach($daruliftaNames as $daruliftaName) {
            if(isset($sendingFatawa[$daruliftaName])) {
                foreach($sendingFatawa[$daruliftaName] as $checked => $dates) {
                    $folderEntries = [];
                    foreach($dates as $mailfolderDates => $files) {
                        foreach($files as $file) {
                            $folder = $file->mail_folder_date;
                            $folderEntries[$folder] = $folderEntries[$folder] ?? ['count' => 0];
                            $folderEntries[$folder]['count']++;
                            $totalCounts++;
                        }
                    }
                    $overallFolderCount += count($folderEntries);
                }
            }
        }
    } else {
        foreach($daruliftaNames as $daruliftaName) {
            if(isset($remainingFatawa[$daruliftaName])) {
                $folderCounts = [];
                foreach($mailfolderDate as $mailfolderDates) {
                    if(isset($remainingFatawa[$daruliftaName][$mailfolderDates])) {
                        foreach($remainingFatawa[$daruliftaName][$mailfolderDates] as $file) {
                            $folder = $file->mail_folder_date;
                            $folderCounts[$folder] = isset($folderCounts[$folder]) ? $folderCounts[$folder] + 1 : 1;
                            $totalCounts++;
                        }
                    }
                }
                $overallFolderCount += count($folderCounts);
            }
        }
    }
@endphp

<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2"></i>
            Sending Fatawa Statistics
        </h5>
        <p class="mb-0 opacity-75">Overview of sending fatawa counts and folders</p>
    </div>
    <div class="modern-card-body">
        <div class="row g-4">
            <!-- Total Fatawa -->
            <div class="col-md-3">
                <div class="professional-stat-card text-center">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-file-alt fa-2x text-primary"></i>
                    </div>
                    <div class="stat-number text-primary">{{ number_format($totalCounts) }}</div>
                    <div class="stat-label">Total Fatawa</div>
                </div>
            </div>

            <!-- Total Folders -->
            <div class="col-md-3">
                <div class="professional-stat-card text-center">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-folder fa-2x text-success"></i>
                    </div>
                    <div class="stat-number text-success">{{ number_format($overallFolderCount) }}</div>
                    <div class="stat-label">Total Folders</div>
                </div>
            </div>

            <!-- Daruliftaas -->
            <div class="col-md-3">
                <div class="professional-stat-card text-center">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-building fa-2x text-info"></i>
                    </div>
                    <div class="stat-number text-info">{{ count($daruliftaNames) }}</div>
                    <div class="stat-label">Daruliftaas</div>
                </div>
            </div>

            <!-- Average per Folder -->
            <div class="col-md-3">
                <div class="professional-stat-card text-center">
                    <div class="stat-icon mb-2">
                        <i class="fas fa-calculator fa-2x text-warning"></i>
                    </div>
                    <div class="stat-number text-warning">
                        {{ $overallFolderCount > 0 ? number_format($totalCounts / $overallFolderCount, 1) : '0' }}
                    </div>
                    <div class="stat-label">Avg per Folder</div>
                </div>
            </div>
        </div>

        <!-- Summary Row -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Summary:</strong>
                    <span class="ms-2">
                        Total {{ number_format($totalCounts) }} fatawa distributed across {{ number_format($overallFolderCount) }} folders 
                        from {{ count($daruliftaNames) }} Daruliftaa(s)
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
