<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the users assigned to this department.
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'user_departments')
                    ->withPivot('assigned_at', 'assigned_by')
                    ->withTimestamps();
    }

    /**
     * Get the tasks assigned to this department.
     */
    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    /**
     * Get department supervisor/assistant assignments.
     */
    public function departmentAssignments()
    {
        return $this->hasMany(DepartmentSupervisorAssistant::class);
    }

    /**
     * Get active department supervisor/assistant assignments.
     */
    public function activeDepartmentAssignments()
    {
        return $this->hasMany(DepartmentSupervisorAssistant::class)->active();
    }

    /**
     * Get supervisors assigned to this department.
     */
    public function supervisors()
    {
        return $this->belongsToMany(User::class, 'department_supervisor_assistants')
                    ->wherePivot('role_type', DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type')
                    ->withTimestamps();
    }

    /**
     * Get assistants assigned to this department.
     */
    public function assistants()
    {
        return $this->belongsToMany(User::class, 'department_supervisor_assistants')
                    ->wherePivot('role_type', DepartmentSupervisorAssistant::ROLE_ASSISTANT)
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type', 'supervisor_id')
                    ->withTimestamps();
    }

    /**
     * Get all team members (supervisors + assistants) for this department.
     */
    public function teamMembers()
    {
        return $this->belongsToMany(User::class, 'department_supervisor_assistants')
                    ->wherePivot('is_active', true)
                    ->withPivot('assigned_at', 'assigned_by', 'role_type', 'supervisor_id')
                    ->withTimestamps();
    }

    /**
     * Scope to get only active departments.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
