<x-layout bodyClass="">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <div>
        <div class="container position-sticky z-index-sticky top-0">
            <div class="row">
                <div class="col-12">
                    <!-- Navbar -->
                    <x-navbars.navs.guest signin='static-sign-in' signup='static-sign-up'></x-navbars.navs.guest>
                    <!-- End Navbar -->
                </div>
            </div>
        </div>
        <main class="main-content  mt-0">
            <section>
                <div class="page-header min-vh-100">
                    <div class="container">
                        <div class="row">
                            <div
                                class="col-6 d-lg-flex d-none h-100 my-auto pe-0 position-absolute top-0 start-0 text-center justify-content-center flex-column">
                                <div class="position-relative bg-gradient-primary h-100 m-3 px-7 border-radius-lg d-flex flex-column justify-content-center"
                                    style="background-image: url('{{ asset('assets') }}/img/illustrations/illustration-signup.jpg'); background-size: cover;">
                                </div>
                            </div>
                            <div
                                class="col-xl-4 col-lg-5 col-md-7 d-flex flex-column ms-auto me-auto ms-lg-auto me-lg-5">
                                <div class="card card-plain">
                                    <div class="card-header">
                                        <h4 class="font-weight-bolder">Edit User</h4>
                                        {{-- <p class="mb-0"></p> --}}
                                    </div>
                                    <div class="card-body">
                                        <form action="{{ route('users.update' , $user->id) }}" method="POST">
                                            @csrf
                                            @method('PUT')
                                            <label class="form-label">Name</label>
                                            <div class="input-group input-group-outline mb-3">

                                                <input type="text" class="form-control" name="name" value="{{$user->name}}" required>
                                                @error('name')
                                                <span class="text-danger">{{$message}}</span>

                                                @enderror
                                            </div>
                                            <label class="form-label">Email</label>
                                            <div class="input-group input-group-outline mb-3">

                                                <input type="email" class="form-control" name="email" value="{{$user->email}}" required>
                                                {{-- @error('email')
                                                <span class="text-danger">{{$message}}</span>

                                                @enderror --}}

                                            </div>
                                            <label class="form-label">Password (leave blank to keep current)</label>
                                            <div class="input-group input-group-outline mb-3">
                                                <input
                                                    type="password"
                                                    class="form-control"
                                                    name="password"
                                                    id="password"
                                                    placeholder="Enter new password..."
                                                    autocomplete="new-password"
                                                >
                                                <div class="input-group-append">
                                                    <span class="input-group-text" style="cursor: pointer;">
                                                        <i class="fa fa-eye" id="togglePassword"></i>
                                                    </span>
                                                </div>
                                            </div>
                                            @error('password')
                                            <span class="text-danger">{{$message}}</span>
                                            @enderror

                                            </div>
                                            <label class="form-label">Roles</label>
                                            <div class="input-group input-group-outline mb-3">

                                                <select  class="form-control" multiple name="roles[]" required>
                                                    @foreach($roles as $role)
                                                        <option value="{{$role->id}}"
                                                            @if(in_array($role->id, $user->roles->pluck('id')->toArray()))
                                                            selected @endif>{{$role->name}}</option>

                                                    @endforeach
                                                </select>
                                                @error('roles')
                                                <span class="text-danger">{{$message}}</span>

                                                @enderror

                                            </div>
                                            {{-- <div class="form-check form-check-info text-start ps-0">
                                                <input class="form-check-input" type="checkbox" value=""
                                                    id="flexCheckDefault" checked>
                                                <label class="form-check-label" for="flexCheckDefault">
                                                    I agree the <a href="javascript:;"
                                                        class="text-dark font-weight-bolder">Terms and Conditions</a>
                                                </label>
                                            </div> --}}
                                            <div class="text-center">
                                                <button type="submit"
                                                    class="btn btn-lg bg-gradient-primary btn-lg w-100 mt-4 mb-0">Update User</button>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="card-footer text-center pt-0 px-lg-2 px-1">
                                        <p class="mb-2 text-sm mx-auto">
                                            Already have an account?
                                            <a href="{{ route('static-sign-in') }}"
                                                class="text-primary text-gradient font-weight-bold">Sign in</a>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
</x-layout>
<script>
    document.getElementById('togglePassword').addEventListener('click', function(e) {
        const password = document.getElementById('password');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);

        // Toggle between eye and eye-slash icons
        this.classList.toggle('fa-eye');
        this.classList.toggle('fa-eye-slash');
    });
</script>
