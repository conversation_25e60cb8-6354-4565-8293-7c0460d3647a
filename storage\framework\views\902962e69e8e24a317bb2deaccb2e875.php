<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Users</p>
                                <h5 class="font-weight-bolder mb-0"><?php echo e($statistics['total_users'] ?? 0); ?></h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                <i class="ni ni-single-02 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">At Limit</p>
                                <h5 class="font-weight-bolder mb-0 text-warning"><?php echo e($statistics['users_at_limit'] ?? 0); ?></h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                <i class="ni ni-notification-70 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Over Limit</p>
                                <h5 class="font-weight-bolder mb-0 text-danger"><?php echo e($statistics['users_over_limit'] ?? 0); ?></h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                <i class="ni ni-fat-remove text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Near Limit</p>
                                <h5 class="font-weight-bolder mb-0 text-info"><?php echo e($statistics['users_near_limit'] ?? 0); ?></h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-info shadow text-center border-radius-md">
                                <i class="ni ni-time-alarm text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Avg Per User</p>
                                <h5 class="font-weight-bolder mb-0"><?php echo e($statistics['average_per_user'] ?? 0); ?></h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-secondary shadow text-center border-radius-md">
                                <i class="ni ni-chart-bar-32 text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-sm-6 mb-xl-0 mb-4">
            <div class="card">
                <div class="card-body p-3">
                    <div class="row">
                        <div class="col-8">
                            <div class="numbers">
                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Restricted</p>
                                <h5 class="font-weight-bolder mb-0 text-danger"><?php echo e($statistics['users_with_restrictions'] ?? 0); ?></h5>
                            </div>
                        </div>
                        <div class="col-4 text-end">
                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                <i class="ni ni-lock-circle-open text-lg opacity-10"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Management Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Mahl-e-Nazar Limit Management</h5>
                            <p class="text-sm mb-0">Monitor and manage 12 Fatawa forwarding limits</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <button wire:click="refreshData" class="btn btn-outline-info btn-sm mb-0 me-2">
                                    <i class="fas fa-sync"></i>&nbsp;&nbsp;Refresh
                                </button>
                                <button wire:click="updateAllRestrictions" class="btn btn-outline-warning btn-sm mb-0 me-2">
                                    <i class="fas fa-shield-alt"></i>&nbsp;&nbsp;Update Restrictions
                                </button>
                                <button wire:click="initializeDefaultRestrictions" class="btn btn-outline-danger btn-sm mb-0 me-2">
                                    <i class="fas fa-user-lock"></i>&nbsp;&nbsp;Default Restrict All
                                </button>
                                <button wire:click="sendLimitWarnings" class="btn btn-outline-secondary btn-sm mb-0 me-2">
                                    <i class="fas fa-bell"></i>&nbsp;&nbsp;Send Warnings
                                </button>
                                <button wire:click="openStatsModal" class="btn btn-outline-primary btn-sm mb-0 me-2">
                                    <i class="fas fa-chart-pie"></i>&nbsp;&nbsp;Statistics
                                </button>
                                <button wire:click="openBreakdownModal" class="btn btn-outline-success btn-sm mb-0 me-2">
                                    <i class="fas fa-list-alt"></i>&nbsp;&nbsp;Breakdown
                                </button>
                                <button wire:click="exportReport" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-download"></i>&nbsp;&nbsp;Export Report
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body px-0 pb-0">
                    <div class="row px-4">
                        <div class="col-md-6">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search users...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <select wire:model.live="filterStatus" class="form-select">
                                    <option value="all">All Status</option>
                                    <option value="normal">Normal</option>
                                    <option value="near_limit">Near Limit</option>
                                    <option value="at_limit">At Limit</option>
                                    <option value="restricted">Restricted</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <div class="text-sm text-secondary">
                                    Total Users: <?php echo e($totalUsers); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Count</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Progress</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Remaining</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $userReport; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr class="<?php echo e($item['is_at_limit'] ? 'table-warning' : ''); ?>">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($item['user']->name); ?></h6>
                                                <p class="text-xs text-secondary mb-0"><?php echo e($item['user']->email); ?></p>
                                                <!--[if BLOCK]><![endif]--><?php if($item['user']->departments->isNotEmpty()): ?>
                                                    <span class="badge badge-sm bg-gradient-info"><?php echo e($item['user']->departments->first()->name); ?></span>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-sm font-weight-bold">
                                            <?php echo e($item['mahl_e_nazar_count']); ?>/12
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="progress-wrapper w-75 mx-auto">
                                            <div class="progress-info">
                                                <div class="progress-percentage">
                                                    <span class="text-xs font-weight-bold"><?php echo e(round($this->getProgressPercentage($item['mahl_e_nazar_count']))); ?>%</span>
                                                </div>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar bg-gradient-<?php echo e($this->getProgressColor($item['mahl_e_nazar_count'])); ?>" 
                                                     role="progressbar" 
                                                     style="width: <?php echo e($this->getProgressPercentage($item['mahl_e_nazar_count'])); ?>%">
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            <?php echo e($item['remaining_capacity']); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($this->getStatusColor($item['status'])); ?>">
                                            <?php echo e($this->getStatusText($item['status'])); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button wire:click="openUserModal(<?php echo e($item['user']->id); ?>)"
                                                    class="btn btn-sm btn-outline-info mb-0" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            <?php
                                                $isManuallyAllowed = app(\App\Services\MahlENazarService::class)->isManuallyAllowed($item['user']);
                                            ?>

                                            <!--[if BLOCK]><![endif]--><?php if($isManuallyAllowed): ?>
                                                <button wire:click="restrictUser(<?php echo e($item['user']->id); ?>)"
                                                        class="btn btn-sm btn-outline-danger mb-0" title="Restrict User">
                                                    <i class="fas fa-ban"></i>
                                                </button>
                                            <?php else: ?>
                                                <button wire:click="allowUser(<?php echo e($item['user']->id); ?>)"
                                                        class="btn btn-sm btn-outline-success mb-0" title="Allow User">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                            <!--[if BLOCK]><![endif]--><?php if($item['has_restriction']): ?>
                                                <button wire:click="toggleUserRestriction(<?php echo e($item['user']->id); ?>)"
                                                        class="btn btn-sm btn-outline-warning mb-0" title="Remove Limit Restriction">
                                                    <i class="fas fa-unlock"></i>
                                                </button>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <p class="text-secondary mb-0">No users found.</p>
                                    </td>
                                </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('message')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Statistics Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showStatsModal): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mahl-e-Nazar Statistics</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-gradient-primary">
                                <div class="card-body text-white">
                                    <h3 class="text-white"><?php echo e($detailedBreakdown['totalCounts'] ?? $statistics['total_mahl_e_nazar'] ?? 0); ?></h3>
                                    <p class="mb-0">Total Mahl-e-Nazar Fatawa</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-gradient-info">
                                <div class="card-body text-white">
                                    <h3 class="text-white"><?php echo e($statistics['average_per_user'] ?? 0); ?></h3>
                                    <p class="mb-0">Average Per User</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="card bg-gradient-success">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white"><?php echo e($statistics['total_users'] - $statistics['users_at_limit'] - $statistics['users_over_limit'] - $statistics['users_near_limit']); ?></h4>
                                    <p class="mb-0 text-sm">Normal</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-gradient-info">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white"><?php echo e($statistics['users_near_limit'] ?? 0); ?></h4>
                                    <p class="mb-0 text-sm">Near Limit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-gradient-warning">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white"><?php echo e($statistics['users_at_limit'] ?? 0); ?></h4>
                                    <p class="mb-0 text-sm">At Limit</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-gradient-danger">
                                <div class="card-body text-white text-center">
                                    <h4 class="text-white"><?php echo e($statistics['users_over_limit'] ?? 0); ?></h4>
                                    <p class="mb-0 text-sm">Over Limit</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <h6>System Health</h6>
                        <div class="progress">
                            <?php
                                $healthPercentage = $statistics['total_users'] > 0
                                    ? (($statistics['total_users'] - $statistics['users_over_limit'] - $statistics['users_at_limit']) / $statistics['total_users']) * 100
                                    : 100;
                            ?>
                            <div class="progress-bar bg-gradient-<?php echo e($healthPercentage >= 80 ? 'success' : ($healthPercentage >= 60 ? 'warning' : 'danger')); ?>"
                                 role="progressbar" style="width: <?php echo e($healthPercentage); ?>%">
                                <?php echo e(round($healthPercentage)); ?>% Healthy
                            </div>
                        </div>
                        <small class="text-muted">Users not at or over limit</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- User Details Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showUserModal && $selectedUser): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">User Details - <?php echo e($selectedUser->name); ?></h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">User Information</small>
                                <p class="mb-1"><strong>Name:</strong> <?php echo e($selectedUser->name); ?></p>
                                <p class="mb-1"><strong>Email:</strong> <?php echo e($selectedUser->email); ?></p>
                                <p class="mb-1"><strong>Roles:</strong>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $selectedUser->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge bg-gradient-info"><?php echo e($role->name); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </p>
                                <!--[if BLOCK]><![endif]--><?php if($selectedUser->departments->isNotEmpty()): ?>
                                    <p class="mb-1"><strong>Departments:</strong>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $selectedUser->departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="badge bg-gradient-secondary"><?php echo e($department->name); ?></span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </p>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Mahl-e-Nazar Count</small>
                                <h4 class="text-primary"><?php echo e($this->getUserMahlENazarCount($selectedUser->name)); ?>/12</h4>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item mb-3">
                                <small class="text-uppercase text-secondary font-weight-bold">Remaining Capacity</small>
                                <h4 class="text-success"><?php echo e(max(0, 12 - $this->getUserMahlENazarCount($selectedUser->name))); ?></h4>
                            </div>
                        </div>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if($selectedUser->activeRestrictions->isNotEmpty()): ?>
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Active Restrictions</h6>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $selectedUser->activeRestrictions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $restriction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <p class="mb-1"><strong>Type:</strong> <?php echo e(ucfirst(str_replace('_', ' ', $restriction->restriction_type))); ?></p>
                            <p class="mb-1"><strong>Reason:</strong> <?php echo e($restriction->reason); ?></p>
                            <p class="mb-0"><strong>Since:</strong> <?php echo e($restriction->restricted_at->format('M d, Y h:i A')); ?></p>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <?php
                        $count = $this->getUserMahlENazarCount($selectedUser->name);
                        $percentage = min(100, ($count / 12) * 100);
                    ?>

                    <div class="progress-wrapper">
                        <div class="progress-info">
                            <div class="progress-percentage">
                                <span class="text-sm font-weight-bold"><?php echo e(round($percentage)); ?>% of limit used</span>
                            </div>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-gradient-<?php echo e($percentage >= 100 ? 'danger' : ($percentage >= 83 ? 'warning' : 'success')); ?>"
                                 role="progressbar" style="width: <?php echo e($percentage); ?>%">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                    <!--[if BLOCK]><![endif]--><?php if($selectedUser->activeRestrictions->isNotEmpty()): ?>
                        <button type="button" class="btn btn-success" wire:click="toggleUserRestriction(<?php echo e($selectedUser->id); ?>)">
                            <i class="fas fa-unlock"></i> Remove Restriction
                        </button>
                    <?php else: ?>
                        <button type="button" class="btn btn-warning" wire:click="toggleUserRestriction(<?php echo e($selectedUser->id); ?>)">
                            <i class="fas fa-lock"></i> Apply Restriction
                        </button>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Detailed Breakdown Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showBreakdownModal): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Mahl-e-Nazar Detailed Breakdown</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <!--[if BLOCK]><![endif]--><?php if(isset($detailedBreakdown['summaryData']) && count($detailedBreakdown['summaryData']) > 0): ?>
                        <div class="alert alert-info">
                            <h6 class="alert-heading">Summary</h6>
                            <p class="mb-0">Total Mahl-e-Nazar Fatawa: <strong><?php echo e($detailedBreakdown['totalCounts'] ?? 0); ?></strong></p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-striped">
                                <thead class="table-dark text-center">
                                    <tr>
                                        <th style="width: 20%;">Darulifta</th>
                                        <th style="width: 60%;">Mujeeb Mahl-e-Nazar Details</th>
                                        <th style="width: 20%;">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $detailedBreakdown['summaryData']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="fw-bold text-center align-middle">
                                                <?php echo e($daruliftaName); ?>

                                            </td>
                                            <td class="text-wrap">
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $data['senders']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sender => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <span class="badge bg-light text-primary me-1 mb-1">
                                                        <?php echo e($sender); ?>: <?php echo e($count); ?>

                                                    </span>
                                                    <!--[if BLOCK]><![endif]--><?php if(!$loop->last): ?>
                                                        <span class="text-muted">|</span>
                                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                            </td>
                                            <td class="text-center align-middle">
                                                <span class="badge bg-success fs-6"><?php echo e($data['total']); ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-3 text-end">
                            <h5 class="text-success fw-semibold">
                                Overall Total: <?php echo e($detailedBreakdown['totalCounts'] ?? 0); ?>

                            </h5>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning text-center">
                            <h6>No Mahl-e-Nazar Fatawa Found</h6>
                            <p class="mb-0">There are currently no fatawa in Mahl-e-Nazar status.</p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Close</button>
                    <button type="button" class="btn btn-primary" onclick="window.open('/mahlenazar-fatawa', '_blank')">
                        <i class="fas fa-external-link-alt"></i> View Full Page
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/mahl-e-nazar-management.blade.php ENDPATH**/ ?>