<?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show bg-gray-200']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show bg-gray-200']); ?>

    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <div class="container-fluid py-4">

            <!-- Loader (No changes, logic is preserved) -->
            <div id="loader-overlay" class="loader-overlay" style="display: none;">
                <div id="loader" class="loader"></div>
            </div>

            <!-- Custom Styles - Kept minimal and essential styles for modern UI -->
            <style>
                .folder-entry.highlight {
                    background-color: var(--bs-primary-bg-subtle, #e0f0ff);
                    border-color: var(--bs-primary, #0d6efd);
                    font-weight: bold;
                }
                .folder-transfer-by.highlight {
                    background-color: var(--bs-secondary-bg-subtle, #f0f0f0);
                    border-color: var(--bs-secondary, #6c757d);
                    font-weight: 500;
                }
                .folder-entry, .folder-transfer-by {
                    transition: all 0.3s ease;
                }
                .question-cell {
                    background-color: #f8f9fa;
                    direction: rtl;
                    overflow: wrap;
                }
                .question-text {
                    white-space: normal;
                    word-wrap: break-word;
                    text-align: right;
                    color: black;
                }
                /* Visual grouping of table rows */
                .fatwa-group-start > td {
                    border-top: 2px solid #0d6efd !important;
                }
                .hidden {
                    display: none;
                }
            </style>

            <!-- Page Header Navigation -->
            <div class="card mb-4">
                <div class="card-body p-3">
                    <div class="d-flex flex-wrap gap-2">
                        <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-secondary">Dashboard</a>
                        <a href="<?php echo e(route('recived-fatawa')); ?>" class="btn btn-outline-secondary">Received Fatawa</a>
                        <a href="<?php echo e(route('store.selected.values')); ?>" class="btn btn-primary">Sending Fatawa</a>
                        <a href="<?php echo e(route('sent-fatawa')); ?>" class="btn btn-outline-secondary">Checked Fatawa</a>
                    </div>
                </div>
            </div>

            <!-- FIX: Pre-calculate all folder and count data before displaying -->
            <?php
                $totalCounts = 0;
                $overallFolderCount = 0;
                $folderCounts = [];
                $transferByCounts = [];
                $daruliftaNameForNav = null; // To store the relevant darulifta name for nav links
                $closestBackFolder = null;
                $closestNextFolder = null;

                foreach($daruliftaData as $daruliftaName) {
                    if(isset($remainingFatawas[$daruliftaName])) {
                        $daruliftaNameForNav = $daruliftaName; // Set the name for later use
                        $currentDate = \Carbon\Carbon::parse($selectedFolder);
                        
                        // Calculate next/back folders (original logic)
                        $closestBackFolder = $backFolderData->sortBy(fn($folder) => $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date)))->first();
                        $closestNextFolder = $nextFolderData->sortBy(fn($folder) => $currentDate->diffInDays(\Carbon\Carbon::parse($folder->mail_folder_date)))->first();

                        // Calculate counts (original logic)
                        foreach($folderData as $mailfolderDates) {
                            if(isset($remainingFatawas[$daruliftaName][$mailfolderDates])) {
                                foreach($remainingFatawas[$daruliftaName][$mailfolderDates] as $file) {
                                    $folder = $file->mail_folder_date;
                                    $folderCounts[$folder] = ($folderCounts[$folder] ?? 0) + 1;
                                    $transferBy = $file->transfer_by ?: 'Mujeeb';
                                    $transferByCounts[$folder][$transferBy] = ($transferByCounts[$folder][$transferBy] ?? 0) + 1;
                                    $totalCounts++;
                                }
                            }
                        }
                    }
                }
                $overallFolderCount = count($folderCounts);
            ?>


            <!-- Folder Navigation -->
            <div class="card mb-4">
                <div class="card-header p-3">
                    <h6 class="mb-0">Folder Navigation (Overall Total Fatawa: <?php echo e($totalCounts); ?> | Folders: <?php echo e($overallFolderCount); ?>)</h6>
                </div>
                <div class="card-body p-3">
                     <?php if($daruliftaNameForNav): ?>
                        <div class="d-flex align-items-center justify-content-between">
                            <!-- Back Button -->
                            <div>
                                <?php if($closestBackFolder): ?>
                                    <?php $transferByName = empty($closestBackFolder->transfer_by) ? 'Mujeeb' : $closestBackFolder->transfer_by; ?>
                                    <a href="<?php echo e(url("/sending1/{$selectedDarul}/{$closestBackFolder->mail_folder_date}/{$transferByName}")); ?>" class="btn btn-icon btn-outline-primary mb-0"><i class="fas fa-arrow-left"></i></a>
                                <?php endif; ?>
                            </div>

                            <!-- Folder Entries -->
                            <div class="d-flex flex-wrap justify-content-center gap-3">
                                <?php $__currentLoopData = $folderCounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $folder => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php $isActiveFolder = ($selectedFolder === $folder); ?>
                                    <div class="folder-entry border rounded p-2 text-center <?php echo e($isActiveFolder ? 'highlight' : ''); ?>">
                                        <a href="<?php echo e(url('sending1/' . $daruliftaNameForNav . '/' . urlencode($folder))); ?>" class="text-dark fw-bold text-decoration-none">
                                            <?php echo e(\Carbon\Carbon::parse($folder)->format('d-m-Y')); ?> <span class="badge bg-primary rounded-pill ms-1"><?php echo e($count); ?></span>
                                        </a>
                                        <?php if(isset($transferByCounts[$folder])): ?>
                                            <div class="d-flex flex-wrap justify-content-center gap-1 mt-2">
                                                <?php $__currentLoopData = $transferByCounts[$folder]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transferBy => $transferByCount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php $isActiveTransferBy = $isActiveFolder && ($transfer_by === $transferBy); ?>
                                                    <div class="folder-transfer-by border rounded px-2 py-1 <?php echo e($isActiveTransferBy ? 'highlight' : ''); ?>">
                                                        <a href="<?php echo e(url('sending1/' . $daruliftaNameForNav . '/' . urlencode($folder) . '/' . urlencode($transferBy))); ?>" class="text-secondary text-decoration-none small">
                                                            <?php echo e($transferBy); ?> <span class="badge bg-secondary rounded-pill ms-1"><?php echo e($transferByCount); ?></span>
                                                        </a>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <!-- Next Button -->
                            <div>
                                <?php if($closestNextFolder): ?>
                                    <?php $transferByName = empty($closestNextFolder->transfer_by) ? 'Mujeeb' : $closestNextFolder->transfer_by; ?>
                                    <a href="<?php echo e(url("/sending1/{$selectedDarul}/{$closestNextFolder->mail_folder_date}/{$transferByName}")); ?>" class="btn btn-icon btn-outline-primary mb-0"><i class="fas fa-arrow-right"></i></a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Darulifta Filters -->
            <div class="d-flex flex-wrap gap-2 mb-4">
                <a href="<?php echo e(route('sending-fatawa')); ?>" class="btn <?php echo e(!$selectedDarul ? 'btn-primary' : 'btn-outline-primary'); ?>">All Ifta</a>
                <?php $__currentLoopData = $daruliftalist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftalistn): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('sending-fatawa', ['darulifta' => $daruliftalistn])); ?>" class="btn <?php echo e(request()->route('selectedDarul') == $daruliftalistn ? 'btn-primary' : 'btn-outline-primary'); ?>"><?php echo e($daruliftalistn); ?></a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Main Content Form -->
            <form action="<?php echo e(route('upload')); ?>" method="POST" enctype="multipart/form-data" id="submission-form">
                <?php echo csrf_field(); ?>
                <?php if(!empty($fatwaData) && !empty($selectedFolder) && !empty($selectedDarul)): ?>
                
                <!-- Livewire File Upload Component -->
                <div class="card mb-4">
                    <div class="card-header pb-0">
                         <h4 class="text-center mb-0">
                            <?php echo e($selectedDarul); ?> - <?php echo e(\Carbon\Carbon::parse($selectedFolder)->format('d/m/Y')); ?>

                            <?php if($transfer_by): ?>
                                <span class="text-secondary">by <?php echo e(ucwords(str_replace('_', ' ', $transfer_by))); ?></span>
                            <?php endif; ?>
                        </h4>
                    </div>
                    <div class="card-body">
                         <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('file-upload', ['selectedDarul' => $selectedDarul, 'selectedFolder' => $selectedFolder, 'checker' => $checker]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1625004957-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>
                </div>

                <!-- Hidden inputs (Logic preserved) -->
                <input type="hidden" name="folder_select" id="folder_select" value="<?php echo e($selectedFolder); ?>">
                <input type="hidden" name="darulifta_select" id="darulifta_select" value="<?php echo e($selectedDarul); ?>">
                <input type="hidden" name="checker_id" id="checker_id" value="<?php echo e($checker); ?>">

                <!-- Main Fatawa Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Fatawa for Sending</h6>
                        <?php if(session('success')): ?>
                            <div class="alert alert-success alert-dismissible fade show text-white mb-0" role="alert"><?php echo e(session('success')); ?><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>
                        <?php elseif(session('fileErrors')): ?>
                            <div class="alert alert-danger alert-dismissible fade show text-white mb-0" role="alert"><?php $__currentLoopData = session('fileErrors'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><div><?php echo e($error); ?></div><?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>
                        <?php elseif(session('error')): ?>
                            <div class="alert alert-danger alert-dismissible fade show text-white mb-0" role="alert"><?php echo e(session('error')); ?><button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>
                        <?php endif; ?>
                    </div>
                    <div class="table-responsive">
                        <table id="table1" class="table table-flush align-items-center mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th>Fatwa No</th><th>Sender</th><th>Checked File Name</th><th>Checked Folder Name</th><th>Grade</th><th>Tasurat</th><th>Send To</th><th>Viral</th><th>Instructions</th><th>Select</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $fatwaData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr class="fatwa-group-start" data-id="<?php echo e($row->id); ?>">
                                        <td class="hidden"><?php echo e($row->id); ?></td>
                                        <td class="align-middle text-center text-sm"><?php echo e($row->file_code); ?></td>
                                        <td class="hidden"><?php echo e($row->file_name); ?></td>
                                        <td class="align-middle text-center text-sm"><?php echo e($row->sender); ?></td>
                                        <td class="hidden"><?php echo e($row->darulifta_name); ?></td>
                                        <td class="hidden"><?php echo e($row->mail_folder_date); ?></td>
                                        <td class="align-middle"><input type="text" name="checked_file_name[]" data-id="<?php echo e($row->id); ?>" class="form-control form-control-sm"></td>
                                        <td class="align-middle skip-clear"><input type="text" name="checked_folder[]" data-id="<?php echo e($row->id); ?>" class="form-control form-control-sm"></td>
                                        <td class="align-middle">
                                            <select name="checked_grade[]" data-id="<?php echo e($row->id); ?>" class="form-select form-select-sm">
                                                <option value="">Select Grade</option><option value="Munasib">Munasib</option><option value="Bhetar">Bhetar</option><option value="Mumtaz">Mumtaz</option>
                                            </select>
                                        </td>
                                        <td class="align-middle">
                                            <div class="input-group">
                                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('tasurat-dropdown', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1625004957-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                                <button class="btn btn-outline-secondary mb-0" type="button" data-bs-toggle="modal" data-bs-target="#myModal"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                        <td class="align-middle">
                                            <?php $isMujeeb = request()->segment(4) === 'Mujeeb'; ?>
                                            <select name="by_mufti[]" class="by_mufti form-select form-select-sm" data-id="<?php echo e($row->id); ?>">
                                                <option value="to_mujeeb">Mujeeb</option>
                                                <?php if(!$isMujeeb): ?><option value="to_checker">Checker</option><?php endif; ?>
                                            </select>
                                        </td>
                                        <td class="align-middle text-center">
                                            <div class="form-check"><input type="hidden" name="viral[<?php echo e($row->id); ?>]" value="0"><input class="form-check-input" type="checkbox" name="viral[<?php echo e($row->id); ?>]" value="<?php echo e(Auth::id()); ?>" data-id="<?php echo e($row->id); ?>" <?php echo e($row->viral ? 'checked' : ''); ?>></div>
                                        </td>
                                        <td class="align-middle">
                                            <textarea class="checked_instructions_textarea form-control form-control-sm mb-1" name="checked_Instructions[]" data-id="<?php echo e($row->id); ?>" rows="1"></textarea>
                                            <div class="input-group">
                                                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('instruction-dropdown', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1625004957-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                                <button class="btn btn-outline-secondary mb-0" type="button" data-bs-toggle="modal" data-bs-target="#instructionModal"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                        <td class="align-middle text-center">
                                            <div class="form-check"><input type="checkbox" name="selected[]" data-id="<?php echo e($row->id); ?>" onchange="updateCheckboxValue(this)" class="form-check-input"><input type="hidden" name="row_id[]" value="<?php echo e($row->id); ?>"></div>
                                        </td>
                                    </tr>
                                    <tr><td colspan="10" class="question-cell p-2"><div class="custom-textarea" readonly><?php $__currentLoopData = $quest; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $que): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php if($que->ifta_code == $row->file_code): ?><div class="question-text">سوال: <?php echo e($que->question); ?></div><?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?></div></td></tr>
                                    <tr>
                                        <td colspan="10" class="p-2">
                                            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                                                <span class="badge bg-info">موضوع: <?php echo e($row->category); ?></span>
                                                <span class="badge bg-success">Received: <?php echo e(\Carbon\Carbon::parse($row->mail_recived_date)->format('d-M-y h:i A')); ?></span>
                                                <span><?php $__currentLoopData = $que_day_r; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php if(Str::lower($row->file_code) == Str::lower($day->ifta_code)): ?><span class="badge bg-warning text-dark">Total Days: <?php echo e(now()->diffInDays(new \DateTime($day->rec_date))); ?></span><?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?></span>
                                                <div><strong class="text-xs text-secondary">Mahl-e-Nazar:</strong><?php $__currentLoopData = $mahlenazar_null; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mahle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php if($row->file_code == $mahle->file_code && $mahle->mail_folder_date < $row->mail_folder_date): ?><span class="badge bg-light text-dark me-1"><?php echo e($mahle->mail_folder_date); ?></span><?php endif; ?> <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?></div>
                                                <div class="d-flex gap-2">
                                                    <?php
                                                        $fullPath = ($row->mail_folder_date . $row->darulifta_name . $row->checker) . (empty($row->transfer_by) ? '' : '_by_' . $row->transfer_by);
                                                        $deleteFileUrl = route('deleteFile', ['mailfolderDates' => $row->mail_folder_date, 'daruliftaName' => $row->darulifta_name, 'checker' => $row->checker ?? '', 'transferby' => $row->transfer_by ?? '']);
                                                        $canDeletefile = is_null($row->downloaded_by_admin) || in_array('Admin', Auth::user()->roles->pluck('name')->toArray());
                                                    ?>
                                                    <a href="<?php echo e(route('fatwa-detail', ['fatwa' => $row->file_code])); ?>" target="_blank" class="btn btn-sm btn-outline-primary mb-0"><i class="fas fa-eye me-1"></i>Details</a>
                                                    <a href="<?php echo e(route('viewRemain', ['date' => $fullPath, 'filename' => $row->file_name])); ?>" target="_blank" class="btn btn-sm btn-info mb-0"><i class="fas fa-file-alt me-1"></i>View</a>
                                                    <a href="<?php echo e(route('downloadFile', ['date' => $fullPath, 'filename' => $row->file_name, 'id' => $row->id])); ?>" class="btn btn-sm btn-success mb-0"><i class="fas fa-download me-1"></i>Download</a>
                                                    <?php if($canDeletefile): ?>
                                                        <a href="#" onclick="deleteFile('<?php echo e($row->id); ?>', '<?php echo e($row->file_name); ?>', '<?php echo e($row->file_code); ?>', '<?php echo e($deleteFileUrl); ?>')" class="btn btn-sm btn-danger mb-0"><i class="fas fa-trash me-1"></i>Delete</a>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-danger mb-0" disabled data-bs-toggle="tooltip" title="Downloaded by admin on <?php echo e($row->downloaded_by_admin); ?>"><i class="fas fa-trash me-1"></i>Delete</button>
                                                    <?php endif; ?>
                                                    <button type="button" onclick="clearRow(<?php echo e($row->id); ?>)" class="btn btn-sm btn-warning mb-0"><i class="fas fa-undo me-1"></i>Clear</button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php if(is_null($row->checker) || $row->checker === ''): ?><input type="hidden" name="checker[]" value="mufti_ali_asghar" data-id="<?php echo e($row->id); ?>"><?php else: ?><input type="hidden" name="checker[]" value="<?php echo e($row->checker); ?>" data-id="<?php echo e($row->id); ?>"><?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr><td colspan="10" class="text-center py-5"><p class="text-muted"><?php echo e($message ?? 'No Fatawa found for the selected criteria.'); ?></p></td></tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer text-end">
                        <button type="submit" class="btn btn-lg btn-primary"><i class="fas fa-upload me-2"></i>Upload Selected</button>
                    </div>
                </div>
                <?php endif; ?>
            </form>

            <?php if(empty($fatwaData) || empty($selectedFolder) || empty($selectedDarul)): ?>
                <div class="card"><div class="card-body text-center py-5"><p class="text-muted">No data available to display. Please select a Darulifta and folder.</p></div></div>
            <?php endif; ?>

            <table id="table2" class="hidden"><thead><tr><th>Checked File Name</th><th>Checked Folder Name</th><th>Selected</th><th>ID</th></tr></thead><tbody id="file-details"></tbody></table>

            <!-- Modals (Updated for Bootstrap 5) -->
            <div class="modal fade" id="myModal" tabindex="-1" aria-labelledby="myModalLabel" aria-hidden="true"><div class="modal-dialog modal-dialog-centered modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="myModalLabel">Tasurat Editor</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('tasurat-editor', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1625004957-3', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button></div></div></div></div>
            <div class="modal fade" id="instructionModal" tabindex="-1" aria-labelledby="instructionModalLabel" aria-hidden="true"><div class="modal-dialog modal-dialog-centered modal-lg"><div class="modal-content"><div class="modal-header"><h5 class="modal-title" id="instructionModalLabel">Instruction Editor</h5><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"><?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('instruction-editor', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-1625004957-4', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?></div><div class="modal-footer"><button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button></div></div></div></div>
        </div>

        <!-- All JavaScript logic is preserved without any changes -->
        <script>
            // All original JS is preserved to maintain functionality
            document.addEventListener('DOMContentLoaded', function () {
                function addFileDetails(file, label) {
                    var tr = document.createElement('tr');
                    var tdCheckedFileName = document.createElement('td');
                    tdCheckedFileName.textContent = file.name;
                    tr.appendChild(tdCheckedFileName);
                    var tdCheckedFolderName = document.createElement('td');
                    tdCheckedFolderName.textContent = label;
                    tr.appendChild(tdCheckedFolderName);
                    var tdID = document.createElement('td');
                    var correspondingID = findCorrespondingID(file.name);
                    tdID.textContent = correspondingID;
                    tr.appendChild(tdID);
                    document.getElementById('file-details').appendChild(tr);
                    populateTable1FromTable2();
                }

                function findCorrespondingID(fileName) {
                    var firstTableRows = document.querySelectorAll('#table1 tbody tr[data-id]');
                    for (var i = 0; i < firstTableRows.length; i++) {
                        var firstTableFileName = firstTableRows[i].querySelector('td:nth-child(3)');
                        var firstTableID = firstTableRows[i].querySelector('td:nth-child(1)');
                        if (firstTableFileName && firstTableID && firstTableFileName.textContent.trim() === fileName.trim()) {
                            return firstTableID.textContent.trim();
                        }
                    }
                    return 'Not Found';
                }
                
                var fileInputs = document.querySelectorAll('input[type="file"]');
                fileInputs.forEach(function (input) {
                    input.addEventListener('change', function (e) {
                        var files = e.target.files;
                        var label = e.target.getAttribute('data-label');
                        for (var i = 0; i < files.length; i++) { addFileDetails(files[i], label); }
                    });
                });

                function populateTable1FromTable2() {
                    let table1Rows = document.querySelectorAll('#table1 tbody tr[data-id]');
                    let table2Rows = document.querySelectorAll('#table2 tbody tr');
                    table1Rows.forEach(function (table1Row) {
                        let table1FileNameElement = table1Row.querySelector('td:nth-child(3)');
                        if (!table1FileNameElement) return;
                        let table1FileName = table1FileNameElement.textContent.trim();
                        table2Rows.forEach(function (table2Row) {
                            let table2FileNameElement = table2Row.querySelector('td:nth-child(1)');
                            if (!table2FileNameElement) return;
                            let table2FileName = table2FileNameElement.textContent.trim();
                            if (table1FileName === table2FileName) {
                                let checkedFileNameInput = table1Row.querySelector('input[name="checked_file_name[]"]');
                                let checkedFolderInput = table1Row.querySelector('input[name="checked_folder[]"]');
                                let checkbox = table1Row.querySelector('input[name="selected[]"]');
                                if (checkedFileNameInput && checkedFolderInput && checkbox) {
                                    checkedFileNameInput.value = table2Row.querySelector('td:nth-child(1)').textContent.trim();
                                    checkedFolderInput.value = table2Row.querySelector('td:nth-child(2)').textContent.trim();
                                    checkbox.checked = true;
                                }
                            }
                        });
                    });
                }
            });

            function updateCheckboxValue(checkbox) { }

            function clearRow(rowId) {
                document.querySelectorAll(`tr[data-id="${rowId}"] select, tr[data-id="${rowId}"] textarea`).forEach(input => {
                    if (input.tagName === 'SELECT') input.selectedIndex = 0; else input.value = '';
                });
                document.querySelectorAll(`tr[data-id="${rowId}"] input[type="checkbox"]`).forEach(checkbox => { checkbox.checked = false; });
                Livewire.dispatch('resetTasuratDropdown');
                Livewire.dispatch('resetInstructionDropdown');
            }
        </script>
    </main>
    <?php if (isset($component)) { $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.plugins','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('plugins'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $attributes = $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $component = $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/files/sending1.blade.php ENDPATH**/ ?>