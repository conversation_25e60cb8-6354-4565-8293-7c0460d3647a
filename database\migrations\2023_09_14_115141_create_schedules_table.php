<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('schedules', function (Blueprint $table) {
            $table->id();
            $table->date('schedule_date'); // Sehdule Date (Date)
            $table->string('schedule_day'); // Schedule Day (Monday, Tuesday, etc.)
            $table->time('schedule_time'); // Schedule Time (Time)
            $table->string('event_name'); // Event Name (String)
            $table->string('location'); // Location (String)
            $table->text('description')->nullable(); // Description (Text, Nullable)

            // You can add more columns as needed

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('schedules');
    }
};
