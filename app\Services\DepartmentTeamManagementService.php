<?php

namespace App\Services;

use App\Models\User;
use App\Models\Department;
use App\Models\DepartmentSupervisorAssistant;
use Illuminate\Support\Facades\DB;

class DepartmentTeamManagementService
{
    /**
     * Assign a user as supervisor to a department.
     */
    public function assignSupervisor(Department $department, User $user, User $assignedBy): void
    {
        // Check if user already has a role in this department
        $existing = DepartmentSupervisorAssistant::where('department_id', $department->id)
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->first();

        if ($existing) {
            if ($existing->role_type === DepartmentSupervisorAssistant::ROLE_SUPERVISOR) {
                throw new \Exception('User is already a supervisor in this department.');
            } else {
                // User is currently an assistant, remove that role first
                $existing->update(['is_active' => false]);
            }
        }

        DepartmentSupervisorAssistant::create([
            'department_id' => $department->id,
            'user_id' => $user->id,
            'role_type' => DepartmentSupervisorAssistant::ROLE_SUPERVISOR,
            'supervisor_id' => null,
            'assigned_by' => $assignedBy->id,
            'assigned_at' => now(),
        ]);
    }

    /**
     * Assign a user as assistant to a supervisor in a department.
     */
    public function assignAssistant(Department $department, User $assistant, User $supervisor, User $assignedBy): void
    {
        // Verify supervisor is actually a supervisor in this department
        $supervisorAssignment = DepartmentSupervisorAssistant::where('department_id', $department->id)
            ->where('user_id', $supervisor->id)
            ->where('role_type', DepartmentSupervisorAssistant::ROLE_SUPERVISOR)
            ->where('is_active', true)
            ->first();

        if (!$supervisorAssignment) {
            throw new \Exception('The specified user is not a supervisor in this department.');
        }

        // Check if assistant already has a role in this department
        $existing = DepartmentSupervisorAssistant::where('department_id', $department->id)
            ->where('user_id', $assistant->id)
            ->where('is_active', true)
            ->first();

        if ($existing) {
            if ($existing->role_type === DepartmentSupervisorAssistant::ROLE_ASSISTANT) {
                throw new \Exception('User is already an assistant in this department.');
            } else {
                // User is currently a supervisor, remove that role first
                $existing->update(['is_active' => false]);
            }
        }

        DepartmentSupervisorAssistant::create([
            'department_id' => $department->id,
            'user_id' => $assistant->id,
            'role_type' => DepartmentSupervisorAssistant::ROLE_ASSISTANT,
            'supervisor_id' => $supervisor->id,
            'assigned_by' => $assignedBy->id,
            'assigned_at' => now(),
        ]);
    }

    /**
     * Remove a user's role from a department.
     */
    public function removeUserFromDepartment(Department $department, User $user): void
    {
        $assignment = DepartmentSupervisorAssistant::where('department_id', $department->id)
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->first();

        if ($assignment) {
            // If removing a supervisor, also remove their assistants
            if ($assignment->role_type === DepartmentSupervisorAssistant::ROLE_SUPERVISOR) {
                DepartmentSupervisorAssistant::where('department_id', $department->id)
                    ->where('supervisor_id', $user->id)
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
            }

            $assignment->update(['is_active' => false]);
        }
    }

    /**
     * Get department team structure.
     */
    public function getDepartmentTeamStructure(Department $department): array
    {
        $supervisors = DepartmentSupervisorAssistant::active()
            ->supervisors()
            ->forDepartment($department->id)
            ->with('user')
            ->get();

        $structure = [];

        foreach ($supervisors as $supervisorAssignment) {
            $assistants = DepartmentSupervisorAssistant::active()
                ->assistants()
                ->forDepartment($department->id)
                ->where('supervisor_id', $supervisorAssignment->user_id)
                ->with('user')
                ->get();

            $structure[] = [
                'supervisor' => $supervisorAssignment->user,
                'assistants' => $assistants->pluck('user'),
                'supervisor_assignment' => $supervisorAssignment,
                'assistant_assignments' => $assistants,
            ];
        }

        return $structure;
    }

    /**
     * Get user's department roles summary.
     */
    public function getUserDepartmentRoles(User $user): array
    {
        $assignments = DepartmentSupervisorAssistant::active()
            ->forUser($user->id)
            ->with(['department', 'supervisor'])
            ->get();

        $roles = [
            'supervisor_in' => [],
            'assistant_in' => [],
        ];

        foreach ($assignments as $assignment) {
            if ($assignment->role_type === DepartmentSupervisorAssistant::ROLE_SUPERVISOR) {
                $roles['supervisor_in'][] = [
                    'department' => $assignment->department,
                    'assignment' => $assignment,
                ];
            } else {
                $roles['assistant_in'][] = [
                    'department' => $assignment->department,
                    'supervisor' => $assignment->supervisor,
                    'assignment' => $assignment,
                ];
            }
        }

        return $roles;
    }

    /**
     * Get available users for assignment to a department.
     */
    public function getAvailableUsersForDepartment(Department $department, string $roleType = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['mujeeb', 'Superior', 'Muawin']);
        });

        // If looking for supervisors, only include users with Superior role
        if ($roleType === DepartmentSupervisorAssistant::ROLE_SUPERVISOR) {
            $query->whereHas('roles', function ($q) {
                $q->where('name', 'Superior');
            });
        }

        return $query->orderBy('name')->get();
    }

    /**
     * Get department statistics.
     */
    public function getDepartmentStatistics(): array
    {
        return [
            'total_departments' => Department::active()->count(),
            'departments_with_supervisors' => Department::active()
                ->whereHas('activeDepartmentAssignments', function ($q) {
                    $q->where('role_type', DepartmentSupervisorAssistant::ROLE_SUPERVISOR);
                })->count(),
            'total_supervisors' => DepartmentSupervisorAssistant::active()
                ->supervisors()
                ->distinct('user_id')
                ->count(),
            'total_assistants' => DepartmentSupervisorAssistant::active()
                ->assistants()
                ->distinct('user_id')
                ->count(),
        ];
    }

    /**
     * Bulk assign users to departments.
     */
    public function bulkAssignToDepartment(Department $department, array $userRoleAssignments, User $assignedBy): void
    {
        DB::transaction(function () use ($department, $userRoleAssignments, $assignedBy) {
            foreach ($userRoleAssignments as $assignment) {
                $user = User::findOrFail($assignment['user_id']);
                $roleType = $assignment['role_type'];
                $supervisorId = $assignment['supervisor_id'] ?? null;

                if ($roleType === DepartmentSupervisorAssistant::ROLE_SUPERVISOR) {
                    $this->assignSupervisor($department, $user, $assignedBy);
                } else {
                    $supervisor = User::findOrFail($supervisorId);
                    $this->assignAssistant($department, $user, $supervisor, $assignedBy);
                }
            }
        });
    }
}
