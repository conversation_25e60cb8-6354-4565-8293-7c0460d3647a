<div>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">


<link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <style>
         .apply-filters-button {
    background-color: #4682B4;
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}

.apply-filters-button:hover {
    background-color: white;
    color: black;
    border: 2px solid #4CAF50;
}

.apply-filters-button-active {
    background-color: #4CAF50; /* Change to your preferred active color */
    border: none;
    color: white;
    padding: 10px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    transition-duration: 0.4s;
}
       .main-content {
        margin-left: 270px; /* Set the left margin to 270px to accommodate the sidebar */
        position: relative; /* Position relative for content positioning */
        max-height: 100vh; /* Full viewport height */
        border-radius: 8px; /* Border radius for styling */
    }

        table {
            font-family: Jameel Noori Nastaleeq;
            width: 100%;
            margin: 10px 0;
            border-collapse: collapse;
            background-color: #fff;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 12px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        h1 {
            font-size: 24px;
            margin-bottom: 20px;
            color: #333;
        }
        .search-bar {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .search-bar input {
            padding: 8px;
            width: 100%;
            max-width: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pagination-links {
            margin-top: 20px;
        }

    </style>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    @livewire('navbar', ['titlePage' => 'Shoba Viral'])


            @php

            $totalCounts = 0; // Initialize a variable to store the overall total count
            $overallFolderCount = 0;
        @endphp
        <div id="loader" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(255,255,255,0.7); z-index:9999; text-align:center;">
            <div style="position:relative; top:50%; transform:translateY(-50%);">
                <h2>Loading...</h2>
                <!-- You can replace this with a spinner or any loader image -->
            </div>
        </div>

        <div class="month-container">

    <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">

    <div class="container">
    @php
    // Check if the user is authenticated before checking roles
    $Admin = Auth::check() && (
        in_array('Admin', Auth::user()->roles->pluck('name')->toArray()) ||
        in_array('Nazim_Viral', Auth::user()->roles->pluck('name')->toArray())
    );

    $ShobaRoles = Auth::check() && (
        in_array('Shoba_Viral', Auth::user()->roles->pluck('name')->toArray()) ||
        in_array('Shoba_Viral_Iec', Auth::user()->roles->pluck('name')->toArray())
    );

    // Determine which button should be highlighted based on the current route.
    $isSelectedActive     = request()->routeIs('selected-viral');
    $isUnselectedActive   = request()->routeIs('select-viral');
    $isViralFatawaActive  = request()->routeIs('shoba-viral');
@endphp

<!-- Buttons for Selected, Unselected, and Viral Fatawa Detail -->
@if(!$ShobaRoles)
    <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <a href="{{ route('selected-viral') }}"
           class="apply-filters-button {{ $isSelectedActive ? 'apply-filters-button-active' : '' }}"
           style="margin-right: 10px;">
            Selected Fatawa
        </a>
        <a href="{{ route('select-viral') }}"
           class="apply-filters-button {{ $isUnselectedActive ? 'apply-filters-button-active' : '' }}"
           style="margin-right: 10px;">
            Unselected Fatawa
        </a>

        @if($Admin)
            <a href="{{ route('shoba-viral', 'all') }}"
               class="apply-filters-button {{ $isViralFatawaActive ? 'apply-filters-button-active' : '' }}">
                Shoba Viral
            </a>
        @endif
    </div>
@endif
        <h1>Shoba Viral</h1>


        <div class="row mb-4">
    <!-- Total Fatawa -->
    <div class="col-md-2">
        <div class="card shadow-sm" style="background-color: #6c757d; color: #ffffff; height: 150px;">
            <div class="card-body text-center d-flex flex-column justify-content-center">
                <h5>Total Fatawa (Shoba Viral)</h5>
                <p class="display-6">{{ $counts['totalShobaViral'] }}</p>
            </div>
        </div>
    </div>

    <!-- Total Viral Link -->
    <div class="col-md-2">
        <div class="card shadow-sm" style="background-color: #6c757d; color: #ffffff; height: 150px;">
            <div class="card-body text-center d-flex flex-column justify-content-center">
                <h5>Total Viral Link</h5>
                <p class="display-6">{{ $counts['totalViralLink'] }}</p>
            </div>
        </div>
    </div>

    <!-- Total Web Link -->
    <div class="col-md-2">
        <div class="card shadow-sm" style="background-color: #6c757d; color: #ffffff; height: 150px;">
            <div class="card-body text-center d-flex flex-column justify-content-center">
                <h5>Total Web Link</h5>
                <p class="display-6">{{ $counts['totalWebLink'] }}</p>
            </div>
        </div>
    </div>

    <!-- Remaining Fatawa for Viral -->
    <div class="col-md-2">
        <div class="card shadow-sm" style="background-color: #6c757d; color: #ffffff; height: 150px;">
            <div class="card-body text-center d-flex flex-column justify-content-center">
                <h5>Remaining Fatawa for Viral</h5>
                <p class="display-6">{{ $counts['remainingForViral'] }}</p>
            </div>
        </div>
    </div>

    <!-- Remaining Fatawa for Web -->
    <div class="col-md-2">
        <div class="card shadow-sm" style="background-color: #6c757d; color: #ffffff; height: 150px;">
            <div class="card-body text-center d-flex flex-column justify-content-center">
                <h5>Remaining Fatawa for Web</h5>
                <p class="display-6">{{ $counts['remainingForWeb'] }}</p>
            </div>
        </div>
    </div>
</div>

<div style="display: flex; align-items: center; margin-bottom: 20px;">
    <!-- All Button -->
    <div>
        @php $isRemainingActive = request()->fullUrlIs(route('shoba-viral')); @endphp
        <a href="{{ route('shoba-viral') }}"
           class="apply-filters-button {{ $isRemainingActive ? 'apply-filters-button-active' : '' }}">
           Remaining
        </a>
    </div>
    <div>
        @php $isAllActive = request()->fullUrlIs(route('shoba-viral', ['filter' => 'all'])); @endphp
        <a href="{{ route('shoba-viral', ['filter' => 'all']) }}"
           class="apply-filters-button {{ $isAllActive ? 'apply-filters-button-active' : '' }}">
           All
        </a>
    </div>

    <!-- Remaining Button -->

</div>
        <!-- Search Bar -->
        <div class="search-bar">
    <input
        type="text"
        wire:model.live.debounce.300ms="search"
        placeholder="Search by title, fatwa no, or category...">
</div>



<table class="table">
    <thead>
        <tr>
            <th>S.No</th>
            <th>Fatwa No</th>
            <th>Title</th>
            <th>Category</th>
            <th>Rec V.Fatwa Date</th>
            <th>Manage Viral</th>
            <th>Manage Web Link</th>
            <th>Download</th> <!-- New column for download button -->
        </tr>
    </thead>
    <tbody>
        @php $serialNumber = ($question_b->currentPage() - 1) * $question_b->perPage() + 1; @endphp
        @foreach($question_b as $question)
        <tr wire:key="question-row-{{ $question->id }}">
            <td>{{ $serialNumber++ }}</td>
            <td>{{ $question->file_code }}</td>
            <td>{{ $question->title }}</td>
            <td>{{ $question->category }}</td>
            <td>{{ $question->shoba_viral }}</td>

            <td class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">
                    <livewire:viral-link
                        wire:key="viral-link-{{ $question->id }}"
                        :fileCode="$question->file_code"
                        :viralUpload="$question->viral_upload"
                        :webLink="$question->web_link"
                        :viralLink="$question->viral_link"
                        :fileName="$question->checked_file_name"
                        :title="$question->title"
                    />
            </td>
            <td class="text-center text-uppercase font-weight-bolder opacity-7" style="width: 5%; white-space: normal; color: black;">
                    <livewire:web-link
                        wire:key="web-link-{{ $question->id }}"
                        :fileCode="$question->file_code"
                        :viralUpload="$question->viral_upload"
                        :webLink="$question->web_link"
                        :viralLink="$question->viral_link"
                        :fileName="$question->checked_file_name"
                        :title="$question->title"
                        />
            </td>
            <td>
                <!-- Download button -->
                <button wire:click="downloadFile('{{ $question->checked_file_name }}')" class="btn btn-primary btn-sm">Download</button>
            </td>
        </tr>

        <!-- Chat Toggle and Content -->
        <tr wire:key="chat-row-{{ $question->id }}">

            <td colspan="1"  class="align-middle text-center cursor-pointer toggle-chat right-aligned" data-section="chat" style="background-color: #FFDDCC;">
                Chat <span ></span>
            </td>


                                                        <td colspan="8" class="align-middle text-center"  >
                                                        <livewire:viral-chat-component
                                                        wire:key="chat-{{ $question->id }}"
                                                        :file_code="$question->file_code"
                                                        />


                                                        </td>
                                                        </tr>
        @endforeach
    </tbody>
</table>

<!-- Display error message if the file does not exist -->
@if($fileErrorMessage)
    <div class="alert alert-danger mt-2">{{ $fileErrorMessage }}</div>
@endif

<!-- Pagination Links -->


<div class="pagination-links d-flex justify-content-center">
    {{ $question_b->links() }}
</div>

    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

<div wire:ignore>
           <x-layout bodyClass="g-sidenav-show  bg-gray-200">
           @php

           $ViralNazim = Auth::check() && Auth::user()->roles->pluck('name')->intersect(['Shoba_Viral', 'Shoba_Viral_Iec', 'Nazim_Viral'])->isNotEmpty();

@endphp
@if ($ViralNazim)
    <x-navbars.vsidebar activePage="{{ request()->route('darulifta') ?? 'shoba-fatawa' }}"></x-navbars.vsidebar>
@else
<x-navbars.sidebar activePage="{{ request()->route('darulifta') ?? 'shoba-fatawa' }}"></x-navbars.sidebar>
@endif
</x-layout>
</div>
    <x-footers.auth></x-footers.auth>

</main>



</div>
