<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Header;
use App\Models\ParentModel;
use App\Models\Child;
use App\Models\TalaqFatawa;
use Illuminate\Support\Facades\DB;



class TalaqFatawaController extends Controller
{
    public function index()
    {
        // Eager load parents and children, both ordered by serial_number
        $headers = Header::with([
            'parents' => function ($query) {
                $query->orderBy('serial_number');
            },
            'parents.children' => function ($query) {
                $query->orderBy('serial_number');
            }
        ])->orderBy('serial_number')->get();

        // Fetch parents separately for dropdown, ordered by serial_number
        $parents = ParentModel::orderBy('serial_number')->get();

        return view('talaq-fatawa', compact('headers', 'parents'));
    }


    public function saveHeader(Request $request)
    {
        $request->validate([
            'headerName' => 'required',
            'headerSerialNumber' => 'required|integer|unique:headers,serial_number,' . $request->headerId,
        ]);

        Header::updateOrCreate(['id' => $request->headerId], [
            'name' => $request->headerName,
            'serial_number' => $request->headerSerialNumber,
        ]);

        return redirect()->back()->with('success', 'Header saved successfully.');
    }
    public function updateSerialNumber(Request $request)
{
    $request->validate([
        'id' => 'required|exists:headers,id',
        'new_serial' => 'required|integer|min:1',

    ]);

    $header = Header::findOrFail($request->id);
    $newSerial = $request->new_serial;

    // Check if a header already has the new serial number
    $existingHeader = Header::where('serial_number', $newSerial)->first();

    if ($existingHeader) {
        // Swap serial numbers
        $existingHeader->update(['serial_number' => $header->serial_number]);
    }

    // Update the current header to the new serial number
    $header->update(['serial_number' => $newSerial]);

    return response()->json(['success' => true]);
}

public function updateParentSerial(Request $request)
{
    $request->validate([
        'id' => 'required|exists:parents,id',
        'new_serial' => 'required|integer|min:1',
        'header_id' => 'required|exists:headers,id',
    ]);

    $parent = ParentModel::findOrFail($request->id);
    $newSerial = $request->new_serial;

    // Find conflict parent with the same serial under the same header
    $existingParent = ParentModel::where('serial_number', $newSerial)
                                ->where('header_id', $request->header_id)
                                ->first();

    if ($existingParent) {
        $existingParent->update(['serial_number' => $parent->serial_number]);
    }

    $parent->update(['serial_number' => $newSerial]);

    return response()->json(['success' => true]);
}

public function updateChildSerial(Request $request)
{
    $request->validate([
        'id' => 'required|exists:children,id',
        'new_serial' => 'required|integer|min:1',
        'parent_id' => 'required|exists:parents,id',
    ]);

    $child = Child::findOrFail($request->id);
    $newSerial = $request->new_serial;

    // Find conflict child with the same serial under the same parent
    $existingChild = Child::where('serial_number', $newSerial)
                          ->where('parent_id', $request->parent_id)
                          ->first();

    if ($existingChild) {
        $existingChild->update(['serial_number' => $child->serial_number]);
    }

    $child->update(['serial_number' => $newSerial]);

    return response()->json(['success' => true]);
}

    public function saveParent(Request $request)
    {
        $request->validate([
            'parentName' => 'required',
            'parentSerialNumber' => 'required|integer',
            'parentHeaderId' => 'required|exists:headers,id',
        ]);

        ParentModel::updateOrCreate(['id' => $request->parentId], [
            'name' => $request->parentName,
            'serial_number' => $request->parentSerialNumber,
            'header_id' => $request->parentHeaderId,
        ]);

        return redirect()->back()->with('success', 'Parent saved successfully.');
    }

    public function saveChild(Request $request)
    {

        $request->validate([
            'childName' => 'required',
            'childSerialNumber' => 'required|integer',
            'childParentId' => 'required|exists:parents,id',
        ]);

        Child::updateOrCreate(['id' => $request->childId], [
            'name' => $request->childName,
            'serial_number' => $request->childSerialNumber,
            'parent_id' => $request->childParentId,
        ]);

        return redirect()->back()->with('success', 'Child saved successfully.');
    }

    public function deleteHeader($id)
    {
        Header::findOrFail($id)->delete();
        return redirect()->back()->with('success', 'Header deleted successfully.');
    }

    public function deleteParent($id)
    {
        ParentModel::findOrFail($id)->delete();
        return redirect()->back()->with('success', 'Parent deleted successfully.');
    }

    public function deleteChild($id)
    {
        Child::findOrFail($id)->delete();
        return redirect()->back()->with('success', 'Child deleted successfully.');
    }
    public function view($id)
    {
        // Find the current record
        $record = TalaqFatawa::find($id);

        if (!$record) {
            // Redirect to the first available record if the provided ID doesn't exist.
            $firstId = TalaqFatawa::first()?->id;
            return $firstId
                ? redirect()->route('talaq-fatwa-edit', ['id' => $firstId])
                : redirect()->back()->with('error', 'No records available.');
        }

        // Update the downloaded_by_admin column in talaq_fatawa_manage
        DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', $id)
            ->update(['downloaded_by_admin' => now()]);

        // Get previous and next record IDs
        $previousId = DB::table('talaq_fatawa_manage')
        ->where('selected', 0)
        ->where('talaq_fatawa_id', '<', $id)
        ->max('talaq_fatawa_id');

        $nextId = DB::table('talaq_fatawa_manage')
            ->where('selected', 0)
            ->where('talaq_fatawa_id', '>', $id)
            ->min('talaq_fatawa_id');

            $fileCodes = DB::table('talaq_fatawa_manage')
            ->where('selected', 0) // Filter records where selected is 0
            ->orderBy('talaq_fatawa_id', 'desc') // Order by talaq_fatawa_id in ascending order
            ->pluck('file_code', 'talaq_fatawa_id'); // Retrieve file_code with talaq_fatawa_id as the key

        // Pass data to the view
        return view('talaq-fatwa-edit', compact('record', 'previousId', 'nextId', 'fileCodes', 'id'))
        ->with('currentId', $id);
    }
    public function resendMn($id)
    {
        // Find the current record
        $record = TalaqFatawa::find($id);

        if (!$record) {
            // Redirect to the first available record if the provided ID doesn't exist.
            $firstId = TalaqFatawa::first()?->id;
            return $firstId
                ? redirect()->route('talaq-resend-mn', ['id' => $firstId])
                : redirect()->back()->with('error', 'No records available.');
        }

        // Update the downloaded_by_admin column in talaq_fatawa_manage
        DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', $id)
            ->update(['downloaded_by_admin' => now()]);

        // Get previous and next record IDs
        $previousId = DB::table('talaq_fatawa_manage')
        ->where('selected', 0)
        ->where('talaq_fatawa_id', '<', $id)
        ->max('talaq_fatawa_id');

        $nextId = DB::table('talaq_fatawa_manage')
            ->where('selected', 0)
            ->where('talaq_fatawa_id', '>', $id)
            ->min('talaq_fatawa_id');

            $fileCodes = DB::table('talaq_fatawa_manage')
            ->where('selected', 0) // Filter records where selected is 0
            ->orderBy('talaq_fatawa_id', 'desc') // Order by talaq_fatawa_id in ascending order
            ->pluck('file_code', 'talaq_fatawa_id'); // Retrieve file_code with talaq_fatawa_id as the key

        // Pass data to the view
        return view('talaq-resend-mn', compact('record', 'previousId', 'nextId', 'fileCodes', 'id'))
        ->with('currentId', $id);
    }

    public function next($currentId)
    {
        // Fetch the next record ID
        $nextRecordId = DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', '>', $currentId)
            ->where('selected', 0)
            ->orderBy('talaq_fatawa_id', 'asc')
            ->value('talaq_fatawa_id');

        if ($nextRecordId) {
            return redirect()->route('talaq-fatwa-edit', ['id' => $nextRecordId]);
        }

        return redirect()->back()->with('error', 'No next record available.');
    }

    public function previous($currentId)
    {
        // Fetch the previous record ID
        $previousRecordId = DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', '<', $currentId)
            ->where('selected', 0)
            ->orderBy('talaq_fatawa_id', 'desc')
            ->value('talaq_fatawa_id');

        if ($previousRecordId) {
            return redirect()->route('talaq-fatwa-edit', ['id' => $previousRecordId]);
        }

        return redirect()->back()->with('error', 'No previous record available.');
    }


    public function markAsChecked(Request $request, $recordId)
    {
       // Fetch the corresponding record from talaq_fatawa_manage table for the specific record ID
        $manageRecord = DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', $recordId)
            ->first();

        if ($manageRecord) {
            // Get content from the request
            $editorContent = $request->input('content');
             // Sanitize and process the content
        $processedContent = $this->processContent($editorContent);
// dd($editorContent,$processedContent);
// dd($editorContent);
            // Insert a new record into talaq_fatawa and get the ID
            $talaqFatawaId = DB::table('talaq_fatawa')->insertGetId([
                'content' => $editorContent, // Use processed content
                'ifta_code' => $manageRecord->file_code,
                'rec_date' => $manageRecord->file_created_date,
                'assign_id' => $manageRecord->sender,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $checkedGrade = $request->input("checked_grade_{$recordId}");
        $checkedInstructions = $request->input("checked_instructions_{$recordId}");
        $checkedTasurat = $request->input('checked_tasurat')[0] ?? null; // Get first tasurat
// dd($checkedGrade,$checkedInstructions, $checkedTasurat);
            // Update the corresponding record in talaq_fatawa_manage table
            DB::table('talaq_fatawa_manage')
                ->where('talaq_fatawa_id', $recordId) // Ensure this is the unique ID for the row
                ->update([
                    'selected' => 1, // Set 'selected' to 1
                    'checked_file_name' => $manageRecord->file_name,
                    'checked_folder' => $request->input('folder_label'), // Use folder label from the request
                    'checked_date' => now(),
                    'talaq_checked_id' => $talaqFatawaId, // Add the new talaq_fatawa ID
                    'checked_grade' => $checkedGrade, // Use single grade
                'checked_instructions' => $checkedInstructions, // Use single instruction
                'checked_tasurat' => $checkedTasurat, // Use single tasurat
                ]);

             // Fetch the next record ID
        $nextRecordId = DB::table('talaq_fatawa_manage')
        ->where('talaq_fatawa_id', '>', $recordId)
        ->where('selected', 0)
        ->orderBy('talaq_fatawa_id', 'asc')
        ->value('talaq_fatawa_id');

    if ($nextRecordId) {
        // Redirect to the next record for editing
        return redirect()->route('talaq-fatwa-edit', ['id' => $nextRecordId])
            ->with('success', 'Record marked as ' . $request->input('folder_label'));
    }

    // If no next record, redirect to talaq-checked route
    return redirect()->route('talaq-checked')
        ->with('success', 'All records have been marked. Redirecting to checked list.');
}

// Redirect back with an error message if no record is found
return redirect()->back()->with('error', 'Record not found.');
}
private function processContent($content)
{
    $doc = new \DOMDocument();
    libxml_use_internal_errors(true); // Suppress parsing errors
    $doc->loadHTML('<?xml encoding="utf-8" ?>' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    libxml_clear_errors();

    $body = $doc->getElementsByTagName('body')->item(0);

    if ($body) {
        foreach ($body->childNodes as $node) {
            if ($node->nodeType === XML_TEXT_NODE && trim($node->nodeValue) !== '') {
                // Wrap untagged text in <p> with styles
                $p = $doc->createElement('p', htmlspecialchars($node->nodeValue, ENT_QUOTES | ENT_HTML5));
                $p->setAttribute('dir', 'RTL');
                $p->setAttribute('style', "font-family:'Jameel Noori Nastaleeq'; font-size:15px;");
                $body->replaceChild($p, $node);
            } elseif ($node->nodeType === XML_ELEMENT_NODE && $node->nodeName !== 'p') {
                // Ensure dir="RTL" and font-family style for non-<p> elements
                $existingStyle = $node->getAttribute('style');
                if (strpos($existingStyle, 'font-family') === false) {
                    $existingStyle .= " font-family:'Jameel Noori Nastaleeq';";
                }
                if (strpos($existingStyle, 'font-size') === false) {
                    $existingStyle .= " font-size:15px;";
                }
                $node->setAttribute('dir', 'RTL');
                $node->setAttribute('style', $existingStyle);
            }
        }

        // Skip processing for existing <p> elements
    }

    return $doc->saveHTML();
}

}
