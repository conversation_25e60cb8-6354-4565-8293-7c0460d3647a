<?php

namespace App\Livewire;

use DateTime;
use Livewire\Component;
use Livewire\Attributes\Layout;
use Carbon\Carbon;
use Livewire\WithPagination; // Correct import for WithPagination trait
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Chat;



// #[Layout('layouts.app')]
class RemainingFatawa extends Component
{
    use WithPagination;
    // public $perPage = 20;
    // public $loadMoreCount = 20;
    public $selectedMonths = [];
    public $datefilter;
    public $codebylower;
    public $message;
    public $allfatawa;
    Public $obtain;
    Public $que_day_r;
    Public $mahlenazar_null;
    Public $remainingFatawa;
    public $daruliftaNames;
    public $mailfolderDate;
    public $darulifta;
    public $mailfolder;
    public $selectedmufti = 'all';
    public $selectedTimeFrame;
    protected $listeners = ['selectedmuftiUpdated', 'selectedTimeFrameUpdated','fetchFatawaData'];
    // protected $listeners = ['updateSelectedTimeFrame'];
    
    // protected $listeners = ['fetchFatawaData'];
    
    // public function loadMore()
    // {
    //     $this->perPage += $this->loadMoreCount;
    // }
    public function mount($darulifta = null, $mailfolder = null)
        {

        
        // $this->darulifta = $darulifta;
        // $this->darulifta = $mujeebn;
        
        
        $this->loadDaruliftaNames();
        $this->loadMailfolderDate();


        // Additional initialization code
    }
    public function fetchFatawaData($daruliftaName)
{
    $fatawa = DB::table('uploaded_files')
    ->where('darulifta_name', $daruliftaName)->get();
    $this->dispatch('fatawaFetched', $fatawa);
}

    public function updateSelectedTimeFrame($timeFrame)
    {
        $this->selectedTimeFrame = $timeFrame;
    }
    public function updateSelectedMufti($muftiFrame)
    {
        $this->selectedmufti = $muftiFrame;
    }

    private function loadDaruliftaNames()
    {
        if ($this->darulifta === null) {
            $this->daruliftaNames = DB::table('uploaded_files')
                ->select('darulifta_name')
                ->where('darulifta_name', 'NOT LIKE', '%3btn%')
                ->distinct()
                ->pluck('darulifta_name');
        } else {
            // $this->daruliftaNames = $this->darulifta;
            $this->daruliftaNames = DB::table('uploaded_files')
            ->select('darulifta_name')
            ->where('darulifta_name',$this->darulifta)
                
                ->distinct()
                ->pluck('darulifta_name');
        }
    }
    private function loadMailfolderDate()
    {
        if ($this->mailfolder === null) {
        $this->mailfolderDate = DB::table('uploaded_files')
        ->select('mail_folder_date')
        
        ->distinct()
        
        ->pluck('mail_folder_date');
    } else {
    $this->mailfolderDate = DB::table('uploaded_files')
    ->select('mail_folder_date')
    ->where('mail_folder_date',$this->mailfolder)
    ->distinct()
    
    ->pluck('mail_folder_date');
    }
}
public function sendMessage($iftaCode)
{
    
    // Save the message to the database
    Chat::create([
        'ifta_code' => $iftaCode,
        'user_name' => auth()->user()->name,
        'user_id' => auth()->user()->id,
        'message' => $this->message,
    ]);

    $this->message = '';

    // Refresh the main active chat model.
    

    // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
    // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
}    
public function render()
    {
        
        // $this->obtain = DB::table('uploaded_files')
        // ->where('selected', 0)
        // // Add any additional conditions here
        // ->select('mail_folder_date', DB::raw('SUM(total_score) as total_score_sum'))
        // ->groupBy('mail_folder_date')
        // ->havingRaw('COUNT(mail_folder_date) > 1')
        // ->get();
        $this->que_day_r = DB::table('questions')
        ->whereIn('question_branch', $this->daruliftaNames)
        ->get();
        $this->allfatawa = DB::table('uploaded_files')
    ->orderBy('id', 'asc') // Sorting by 'id' in ascending order
    ->get();
    $this->datefilter= DB::table('uploaded_files')
    ->selectRaw("DISTINCT YEAR(mail_folder_date) as year, MONTH(mail_folder_date) as month")
    ->orderBy('year', 'asc')
    ->orderBy('month', 'asc')
    ->get();

    $this->codebylower = DB::table('uploaded_files as uf1')
    ->select('uf1.*')
    ->join(DB::raw('(SELECT MIN(id) as id, file_code FROM uploaded_files GROUP BY file_code) as uf2'), function($join) {
        $join->on('uf1.id', '=', 'uf2.id');
    })
    ->orderBy('uf1.id', 'asc')
    ->get();
    
        $this->mahlenazar_null = DB::table('uploaded_files as u1')
        ->whereIn('u1.darulifta_name', $this->daruliftaNames)
        ->where('u1.checked_folder', 'Mahl-e-Nazar') // Specify the table alias u1
        
        ->leftJoin('uploaded_files as u2', function ($join) {
            $join->on('u1.file_code', '=', 'u2.file_code')
                ->where(function ($query) {
                    $query->where('u2.checked_folder', 'ok')
                    
                        ->orWhere('u2.checked_folder', 'Tahqiqi'); // Use OR instead of orwhere
                        
                });
        })
        ->whereNull('u2.file_code')
        
        ->select('u1.*')
        ->get();
        $this->remainingFatawa = $this->remainingFatawadata();
// dd($this->remainingFatawa);
        $messages = Chat::latest()->get();
        // dd([
        //     'mailfolderDate' => $this->mailfolderDate,
        //     'daruliftaNames' => $this->daruliftaNames,
        //     'remainingFatawa' => $this->remainingFatawa,
        //     'obtain' => $this->obtain,
        // ]);
        return view('livewire.remaining-fatawa', [
            'mailfolderDate' => $this->mailfolderDate,
            'daruliftaNames' => $this->daruliftaNames,
            'remainingFatawa' => $this->remainingFatawa,
            'obtain' => $this->obtain,
            'mahlenazar_null' => $this->mahlenazar_null,
            'que_day_r' => $this->que_day_r,
            'messages' => $messages,
            'selectedmufti' => $this->selectedmufti,
            'selectedTimeFrame' => $this->selectedTimeFrame,
            'allfatawa' => $this->allfatawa,
            'codebylower' => $this->codebylower,
            'datefilter' => $this->datefilter,
            
            // ... (other data to be passed to the view)
        ]
        
        )
        ->layout('layouts.app');
        
    }
    public function remainingFatawadata()
{
    $remainingFatawas = [];

    // Format the selectedMonths array to match database format "YYYY-MM"
    $formattedSelectedMonths = array_map(function ($date) {
        return DateTime::createFromFormat('Y-n', $date)->format('Y-m');
    }, $this->selectedMonths);
    // dd($this->selectedMonths);

    foreach ($this->mailfolderDate as $mailfolderDates) {
        $data = [];

        foreach ($this->daruliftaNames as $daruliftaName) {
            $query = DB::table('uploaded_files')
                ->where('darulifta_name', $daruliftaName)
                ->where('mail_folder_date', $mailfolderDates)
                ->where('selected', 0);

            if ($this->selectedmufti == 'mufti_ali_asghar') {
                $query->where('checker', 'mufti_ali_asghar');
            } elseif ($this->selectedmufti == 'sayed_masood') {
                $query->where('checker', 'sayed_masood');
            }

            if ($this->selectedTimeFrame == 'this_month') {
                $query->whereBetween(DB::raw('DATE(mail_folder_date)'), [
                    now()->startOfMonth(),
                    now()->endOfMonth()
                ]);
            } elseif ($this->selectedTimeFrame == 'other' && !empty($this->selectedMonths)) {
                $query->whereIn(DB::raw("DATE_FORMAT(mail_folder_date, '%Y-%m')"), $formattedSelectedMonths);
            }

            if ($query->exists()) {
                $remainingFatawas[$daruliftaName][$mailfolderDates] = $query;
            }
        }
    }

    return $remainingFatawas;
}

}
