<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\DailyPerformance;
use App\Models\User;
use App\Models\Department;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class PerformanceManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $selectedDate;
    public $selectedUser = 'all';
    public $selectedDepartment = 'all';
    public $selectedStatus = 'all';
    public $selectedRating = 'all';
    
    public $showViewModal = false;
    public $selectedPerformance = null;
    
    public $users = [];
    public $departments = [];
    public $statistics = [];

    public function mount()
    {
        $this->authorize('view-all-performance');
        $this->selectedDate = Carbon::today()->format('Y-m-d');
        $this->loadData();
        $this->loadStatistics();
    }

    public function render()
    {
        $performances = $this->getPerformances();
        
        return view('livewire.performance-management', [
            'performances' => $performances,
        ]);
    }

    public function loadData()
    {
        $user = auth()->user();
        
        if ($user->isNazim()) {
            // <PERSON><PERSON> can see all users
            $this->users = User::select('id', 'name', 'email')->orderBy('name')->get();
        } elseif ($user->isSuperior()) {
            // Superior can see their assistants and department members
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->departments->flatMap->users->pluck('id');
            $userIds = $assistantIds->merge($departmentUserIds)->unique();
            
            $this->users = User::whereIn('id', $userIds)
                ->select('id', 'name', 'email')
                ->orderBy('name')
                ->get();
        }
        
        $this->departments = Department::active()->select('id', 'name')->orderBy('name')->get();
    }

    public function loadStatistics()
    {
        $user = auth()->user();
        $query = DailyPerformance::query();
        
        // Apply user restrictions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->departments->flatMap->users->pluck('id');
                $userIds = $assistantIds->merge($departmentUserIds)->unique();
                $query->whereIn('user_id', $userIds);
            }
        }
        
        $this->statistics = [
            'total_reports' => $query->count(),
            'submitted_today' => $query->where('performance_date', Carbon::today())->where('is_submitted', true)->count(),
            'pending_today' => $query->where('performance_date', Carbon::today())->where('is_submitted', false)->count(),
            'avg_hours_worked' => round($query->where('is_submitted', true)->avg('hours_worked') ?? 0, 1),
            'avg_fatawa_processed' => round($query->where('is_submitted', true)->avg('fatawa_processed') ?? 0, 1),
            'excellent_ratings' => $query->where('overall_rating', 'excellent')->where('is_submitted', true)->count(),
        ];
    }

    public function getPerformances()
    {
        $user = auth()->user();
        $query = DailyPerformance::with(['user'])
            ->when($this->selectedDate, function ($q) {
                $q->where('performance_date', $this->selectedDate);
            })
            ->when($this->selectedUser !== 'all', function ($q) {
                $q->where('user_id', $this->selectedUser);
            })
            ->when($this->selectedStatus !== 'all', function ($q) {
                $q->where('is_submitted', $this->selectedStatus === 'submitted');
            })
            ->when($this->selectedRating !== 'all', function ($q) {
                $q->where('overall_rating', $this->selectedRating);
            })
            ->when($this->selectedDepartment !== 'all', function ($q) {
                $q->whereHas('user.departments', function ($dept) {
                    $dept->where('departments.id', $this->selectedDepartment);
                });
            });

        // Apply user restrictions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->departments->flatMap->users->pluck('id');
                $userIds = $assistantIds->merge($departmentUserIds)->unique();
                $query->whereIn('user_id', $userIds);
            }
        }

        return $query->orderBy('performance_date', 'desc')
                    ->orderBy('submitted_at', 'desc')
                    ->paginate(15);
    }

    public function openViewModal($performanceId)
    {
        $this->selectedPerformance = DailyPerformance::with(['user'])->findOrFail($performanceId);
        $this->authorize('viewPerformance', $this->selectedPerformance->user);
        $this->showViewModal = true;
    }

    public function closeModal()
    {
        $this->showViewModal = false;
        $this->selectedPerformance = null;
    }

    public function updatedSelectedDate()
    {
        $this->resetPage();
    }

    public function updatedSelectedUser()
    {
        $this->resetPage();
    }

    public function updatedSelectedDepartment()
    {
        $this->resetPage();
    }

    public function updatedSelectedStatus()
    {
        $this->resetPage();
    }

    public function updatedSelectedRating()
    {
        $this->resetPage();
    }

    public function exportReport()
    {
        $this->authorize('generate-reports');
        
        // This would generate a PDF or Excel report
        session()->flash('message', 'Report export functionality will be implemented.');
    }

    public function getRatingColor($rating)
    {
        return match($rating) {
            'poor' => 'danger',
            'fair' => 'warning',
            'good' => 'info',
            'excellent' => 'success',
            default => 'secondary'
        };
    }

    public function getStatusColor($isSubmitted)
    {
        return $isSubmitted ? 'success' : 'warning';
    }

    public function sendReminder($userId)
    {
        $this->authorize('manage-users');
        
        // This would send a reminder notification to the user
        $user = User::findOrFail($userId);
        session()->flash('message', "Reminder sent to {$user->name}.");
    }

    public function generateMissingReports()
    {
        $this->authorize('view-all-performance');
        
        $date = $this->selectedDate;
        $userIds = $this->users->pluck('id');
        
        $existingReports = DailyPerformance::where('performance_date', $date)
            ->whereIn('user_id', $userIds)
            ->pluck('user_id');
        
        $missingUserIds = $userIds->diff($existingReports);
        
        foreach ($missingUserIds as $userId) {
            DailyPerformance::create([
                'user_id' => $userId,
                'performance_date' => $date,
                'is_submitted' => false,
            ]);
        }
        
        session()->flash('message', "Created {$missingUserIds->count()} missing performance records.");
        $this->loadStatistics();
    }
}
