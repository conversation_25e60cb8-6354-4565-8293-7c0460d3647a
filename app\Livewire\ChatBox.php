<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Chat;

class ChatBox extends Component
{
    public $ifta_code;
    public $user_name;
    public $user_id;
    public $message;

    public function sendMessage()
    {
        // Save the message to the database
        Chat::create([
            'ifta_code' => $this->ifta_code,
            'user_name' => $this->user_name,
            'user_id' => $this->user_id,
            'message' => $this->message,
        ]);

        $this->message = '';

        // Refresh the main active chat model.


        // Broadcast the new message to others (you can implement broadcasting as mentioned in the previous response)
        // $this->emitTo('chat-box', 'messageReceived', ['message' => $this->message]);
    }

    public function render()
    {
        // Retrieve chat messages from the database
        $messages = Chat::latest()->get();
        return view('livewire.chat-box', ['messages' => $messages]);
    }
}
