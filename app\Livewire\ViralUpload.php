<?php
namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ViralUpload extends Component
{
    use WithFileUploads;

    public $fileCode;
    public $viral;
    public $id;
    public $title;  // Initialize title if it exists
    public $viralUpload;
    public $daruliftaNames;
    public $shobaViralDate;
    public $fileErrorMessage;
    public $uploadedFile;
    public $fileName;
    public $webLink;
    public $viralLink;  // Add viralLink property
    public $isEditingWebLink = false;
    public $isEditingViralLink = false;  // Add isEditingViralLink property
    public $isEditingTitle = false;  // Add isEditingViralLink property

    public function mount($viral, $id, $title, $fileName, $fileCode, $viralUpload, $webLink = null, $viralLink = null)  // Add viralLink parameter
    {
        $this->fileCode = $fileCode;
        $this->fileName = $fileName;
        $this->viralUpload = $viralUpload;
        $this->webLink = $webLink;
        $this->viralLink = $viralLink;  // Initialize viralLink if it exists
        $this->title = $title;  // Initialize title if it exists
        $this->id = $id;  // Initialize title if it exists
        $this->viral = $viral;  // Initialize title if it exists


        $fileRecord = DB::table('uploaded_files')
        ->where('checked_file_name', $this->fileName)
        ->where('checked_folder', 'ok')
        ->where('viral', '!=', 0)
        ->first();

    $this->shobaViralDate = $fileRecord ? $fileRecord->shoba_viral : null;
    $this->daruliftaNames = DB::table('daruliftas')
    ->pluck('darul_name')
    ->toArray();

    }
    public function updatedTitle()
    {
        $fileRecord = DB::table('uploaded_files')
        ->where('checked_file_name', $this->fileName)
        ->where('checked_folder', 'ok')
        ->where('viral', '!=', 0)
        ->first();
        DB::table('uploaded_files')
        ->where('id', $fileRecord->id)
            ->update(['title' => $this->title]);

        $this->dispatch('titleUpdated');
    }

    // Enable title editing mode
    public function enableTitleEditing()
    {
        $this->isEditingTitle = true;
    }

    // Save the title and exit editing mode
    public function saveTitle()
    {
        $this->updatedTitle();
        $this->isEditingTitle = false;
    }

    // Cancel title editing mode
    public function cancelTitleEditing()
    {
        $this->isEditingTitle = false;
    }
   public function updatedUploadedFile()
{
    // Get the original file name from the uploaded file
    $fileName = $this->uploadedFile->getClientOriginalName();

    // Check if the record exists with the specified conditions
    $fileRecord = DB::table('uploaded_files')
        ->where('checked_file_name', $fileName)
        ->where('checked_folder', 'ok')
        ->where('viral', '!=', 0)
        ->first();

    // If the record exists, update the viral_upload field
    if ($fileRecord) {
        $storageLink = $this->uploadedFile->storeAs('viral', $fileName, 'public');
        $currentDate = now()->timezone('Asia/Karachi')->toDateTimeString();

        // Update the viral_upload field for the matching record
        DB::table('uploaded_files')
            ->where('id', $fileRecord->id)
            ->update([
                'viral_upload' => $currentDate,
            ]);

        // Set the viralUpload property to the stored file path
        $this->viralUpload = $storageLink;

        // Dispatch the file uploaded event
        $this->dispatch('fileUploaded');
        $this->fileErrorMessage = null; // Clear error message if the upload succeeds
    } else {
        // If no matching record is found, set an error message
        $this->fileErrorMessage = 'You cannot change the file name.';
    }
}
public function sendToShobaViral()
{
    if ($this->viralUpload) {
        $currentDate = now()->timezone('Asia/Karachi')->toDateTimeString();

        $fileRecord = DB::table('uploaded_files')
            ->where('checked_file_name', $this->fileName)
            ->where('checked_folder', 'ok')
            ->where('viral', '!=', 0)
            ->first();

        DB::table('uploaded_files')
            ->where('id', $fileRecord->id)
            ->update([
                'shoba_viral' => $currentDate,
            ]);

        $this->shobaViralDate = $currentDate;
        $this->dispatch('shobaViralUpdated');
    }
}

public function cancelShobaViral()
{
    if ($this->viralUpload) {
        $fileRecord = DB::table('uploaded_files')
            ->where('checked_file_name', $this->fileName)
            ->where('checked_folder', 'ok')
            ->where('viral', '!=', 0)
            ->first();

        DB::table('uploaded_files')
            ->where('id', $fileRecord->id)
            ->update([
                'shoba_viral' => null,
            ]);

        $this->shobaViralDate = null;
        $this->dispatch('shobaViralUpdated');
    }
}
public function downloadFile()
{
    // Define the path to the file in the storage directory
    $filePath = 'public/viral/' . $this->fileName;

    // Check if the file exists
    if (Storage::exists($filePath)) {
        // Download the file from storage
        return Storage::download($filePath, $this->fileName);
    } else {
        // Handle the error if the file does not exist
        $this->fileErrorMessage = "The file does not exist.";
    }
}

public function updatedWebLink()
{
    $currentDatePakistan = Carbon::now('Asia/Karachi')->toDateString();

    DB::table('uploaded_files')
        ->where('file_code', $this->fileCode)
        ->update([
            'web_link' => $this->webLink,
            'web_date' => $currentDatePakistan
        ]);

    $this->dispatch('webLinkUpdated');
}

    // Handle viralLink updates
    public function updatedViralLink()
    {
        $currentDatePakistan = Carbon::now('Asia/Karachi')->toDateString();

        DB::table('uploaded_files')
            ->where('file_code', $this->fileCode)
            ->update([
                'viral_link' => $this->viralLink,
                'viral_date' => $currentDatePakistan
            ]);

        $this->dispatch('viralLinkUpdated');
    }
    public function enableWebLinkEditing()
    {
        $this->isEditingWebLink = true;
    }

    public function saveWebLink()
    {
        $this->updatedWebLink();
        $this->isEditingWebLink = false;
    }

    public function cancelWebLinkEditing()
    {
        $this->isEditingWebLink = false;
    }

    // Methods for viral link editing
    public function enableViralLinkEditing()
    {
        $this->isEditingViralLink = true;
    }

    public function saveViralLink()
    {
        $this->updatedViralLink();
        $this->isEditingViralLink = false;
    }

    public function cancelViralLinkEditing()
    {
        $this->isEditingViralLink = false;
    }

    public function deleteFile()
    {
        if ($this->viralUpload) {
            Storage::disk('public')->delete($this->viralUpload);

            DB::table('uploaded_files')
                ->where('viral_upload', $this->viralUpload)
                ->update(['viral_upload' => null]);

            $this->viralUpload = null;
            $this->dispatch('fileDeleted');
        }
    }
    public function toggleViral($id)
    {
        $file = DB::table('uploaded_files')->where('id', $id)->first();

        if ($file) {
            $currentUserId = Auth::id();
            // Toggle viral value between current user ID and 0
            $newViralValue = $file->viral == $currentUserId ? 0 : $currentUserId;

            DB::table('uploaded_files')
                ->where('id', $id)
                ->update(['viral' => $newViralValue]);

            // Optionally dispatch an event to notify the UI about the update
            $this->dispatch('viralUpdated');
        }
    }
    public function render()
    {
        return view('livewire.viral-upload');
    }
}
