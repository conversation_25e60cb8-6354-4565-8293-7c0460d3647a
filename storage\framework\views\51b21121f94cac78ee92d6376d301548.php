<div>
    <!-- Header with Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>Locked Account Management</h6>
                            <p class="text-sm mb-0">Manage and unlock restricted user accounts</p>
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <div class="row">
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Total Locked</p>
                                                <h5 class="font-weight-bolder mb-0"><?php echo e($stats['total_locked']); ?></h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-primary shadow text-center border-radius-md">
                                                <i class="fas fa-lock text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Performance</p>
                                                <h5 class="font-weight-bolder mb-0"><?php echo e($stats['performance_locked']); ?></h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-warning shadow text-center border-radius-md">
                                                <i class="fas fa-clipboard-check text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Fatawa Limit</p>
                                                <h5 class="font-weight-bolder mb-0"><?php echo e($stats['fatawa_locked']); ?></h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-danger shadow text-center border-radius-md">
                                                <i class="fas fa-exclamation-triangle text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-capitalize font-weight-bold">Manual Lock</p>
                                                <h5 class="font-weight-bolder mb-0"><?php echo e($stats['manual_locked']); ?></h5>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-dark shadow text-center border-radius-md">
                                                <i class="fas fa-user-lock text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <input type="text" class="form-control" placeholder="Search by name or email..." wire:model.debounce.300ms="search">
        </div>
        <div class="col-md-6">
            <select class="form-select" wire:model="filterType">
                <option value="">All Restriction Types</option>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $restrictionTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($type); ?>"><?php echo e($label); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
    </div>

    <!-- Locked Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">User</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Restrictions</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Locked Since</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Details</th>
                                    <th class="text-secondary opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $lockedUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($user->name); ?></h6>
                                                <p class="text-xs text-secondary mb-0"><?php echo e($user->email); ?></p>
                                                <div class="mt-1">
                                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <span class="badge badge-sm bg-gradient-info"><?php echo e($role->name); ?></span>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $user->activeRestrictions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $restriction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-1">
                                            <span class="badge badge-sm <?php echo e($this->getRestrictionBadgeClass($restriction->restriction_type)); ?>">
                                                <i class="<?php echo e($this->getRestrictionIcon($restriction->restriction_type)); ?> me-1"></i>
                                                <?php echo e($restrictionTypes[$restriction->restriction_type] ?? $restriction->restriction_type); ?>

                                            </span>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td>
                                        <?php
                                            $earliestRestriction = $user->activeRestrictions->sortBy('restricted_at')->first();
                                        ?>
                                        <!--[if BLOCK]><![endif]--><?php if($earliestRestriction): ?>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm"><?php echo e($earliestRestriction->restricted_at->format('M d, Y')); ?></h6>
                                            <p class="text-xs text-secondary mb-0"><?php echo e($earliestRestriction->restricted_at->diffForHumans()); ?></p>
                                        </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $user->activeRestrictions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $restriction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="mb-2">
                                            <p class="text-xs mb-0"><strong>Reason:</strong></p>
                                            <p class="text-xs text-secondary mb-1"><?php echo e(Str::limit($restriction->reason, 50)); ?></p>
                                            
                                            <!--[if BLOCK]><![endif]--><?php if($restriction->restriction_type === 'performance_not_submitted'): ?>
                                                <?php
                                                    $missedDays = $this->getMissedPerformanceDays($user);
                                                ?>
                                                <!--[if BLOCK]><![endif]--><?php if(!empty($missedDays)): ?>
                                                <p class="text-xs text-danger mb-0">
                                                    <i class="fas fa-calendar-times me-1"></i>
                                                    <?php echo e(count($missedDays)); ?> missed day(s)
                                                </p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td class="align-middle">
                                        <button class="btn btn-sm btn-outline-success" 
                                                wire:click="showUnlockModal(<?php echo e($user->id); ?>)">
                                            <i class="fas fa-unlock me-1"></i>
                                            Unlock Account
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-unlock-alt fa-3x text-success mb-3"></i>
                                            <h6 class="text-secondary">No locked accounts found</h6>
                                            <p class="text-sm text-secondary">All user accounts are currently active.</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!--[if BLOCK]><![endif]--><?php if($lockedUsers->hasPages()): ?>
                <div class="card-footer">
                    <?php echo e($lockedUsers->links()); ?>

                </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <!-- Unlock Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showUnlockModal && $selectedUser): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Unlock Account - <?php echo e($selectedUser->name); ?></h5>
                    <button type="button" class="btn-close" wire:click="hideUnlockModal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Current Restrictions:</strong>
                        <ul class="mb-0 mt-2">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $selectedUser->activeRestrictions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $restriction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($restrictionTypes[$restriction->restriction_type] ?? $restriction->restriction_type); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Reason for Unlocking *</label>
                        <textarea class="form-control <?php $__errorArgs = ['unlockReason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  wire:model="unlockReason" rows="4" 
                                  placeholder="Please provide a detailed reason for unlocking this account..."></textarea>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['unlockReason'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> Unlocking this account will remove all active restrictions and allow the user to access the system immediately.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="hideUnlockModal">Cancel</button>
                    <button type="button" class="btn btn-success" wire:click="unlockAccount">
                        <i class="fas fa-unlock me-1"></i>
                        Unlock Account
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/locked-account-management.blade.php ENDPATH**/ ?>