<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Locked - Fatawa Checking System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="page-header align-items-start min-vh-100"
     style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container my-auto">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7 col-sm-9">
                <div class="card shadow-lg border-0" style="backdrop-filter: blur(10px); background: rgba(255, 255, 255, 0.98);">
                    <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
                        <div class="bg-gradient-danger shadow-danger border-radius-lg py-4">
                            <div class="text-center">
                                <i class="fas fa-lock fa-3x text-white mb-3 shake-animation"></i>
                                <h4 class="font-weight-bolder text-white mt-1 mb-0">Account Locked</h4>
                                <p class="mb-0 text-white text-sm opacity-8">Performance submission required</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- User Info -->
                        <div class="text-center mb-4">
                            <div class="avatar avatar-xl mx-auto mb-3">
                                <div class="avatar-name bg-gradient-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                    <i class="fas fa-user fa-2x"></i>
                                </div>
                            </div>
                            <h5 class="fw-bold text-dark mb-1"><?php echo e($user->name); ?></h5>
                            <p class="text-muted mb-0"><?php echo e($user->email); ?></p>
                            <small class="text-secondary"><?php echo e($user->roles->pluck('name')->join(', ')); ?></small>
                        </div>

                        <?php if($reason === 'performance_not_submitted'): ?>
                        <!-- Lock Reason -->
                        <div class="alert alert-warning border-0 shadow-sm" role="alert">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="alert-heading mb-1">Missing Performance Reports</h6>
                                    <p class="mb-2">Your account has been temporarily locked due to missed daily performance submissions.</p>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-danger me-2"><?php echo e(count($missedDays)); ?></span>
                                        <small class="text-muted">missed working day(s)</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Missed Days List -->
                        <?php if(!empty($missedDays)): ?>
                        <div class="mb-4">
                            <div class="d-flex align-items-center justify-content-between mb-3">
                                <h6 class="fw-bold text-dark mb-0">Recent Missed Days</h6>
                                <?php if(count($missedDays) > 5): ?>
                                <small class="text-muted">Showing 5 of <?php echo e(count($missedDays)); ?></small>
                                <?php endif; ?>
                            </div>
                            <div class="list-group list-group-flush">
                                <?php $__currentLoopData = array_slice($missedDays, 0, 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $missedDay): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="list-group-item border-0 px-0 py-2">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-calendar-times text-danger pulse-animation"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <div class="fw-medium"><?php echo e(\Carbon\Carbon::parse($missedDay)->format('M d, Y')); ?></div>
                                            <small class="text-muted"><?php echo e(\Carbon\Carbon::parse($missedDay)->format('l')); ?></small>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <span class="badge bg-light text-dark"><?php echo e(\Carbon\Carbon::parse($missedDay)->diffForHumans()); ?></span>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php if(count($missedDays) > 5): ?>
                            <div class="text-center mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-ellipsis-h me-1"></i>
                                    and <?php echo e(count($missedDays) - 5); ?> more day(s)
                                </small>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2 mb-4">
                            <div class="alert alert-info border-0 shadow-sm" role="alert">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <div>
                                        <strong>Account Locked</strong><br>
                                        <small>Your account has been locked. Only an administrator can unlock it. Please contact your supervisor or admin for assistance.</small>
                                    </div>
                                </div>
                            </div>
                            <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Go to Dashboard
                            </a>
                        </div>

                        <!-- Help Section -->
                        <div class="bg-light rounded-3 p-4 mb-3">
                            <div class="text-center">
                                <div class="mb-3">
                                    <i class="fas fa-info-circle text-info fa-2x"></i>
                                </div>
                                <h6 class="fw-bold text-dark mb-2">Need Help?</h6>
                                <p class="small text-secondary mb-3">
                                    If you believe this is an error or need assistance, please contact your Nazim or Admin.
                                </p>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="border-end">
                                            <i class="fas fa-clock text-muted mb-1"></i>
                                            <div class="small text-muted">Locked</div>
                                            <div class="small fw-medium"><?php echo e(now()->format('M d, Y')); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <i class="fas fa-shield-alt text-muted mb-1"></i>
                                        <div class="small text-muted">Status</div>
                                        <div class="small fw-medium text-danger">Restricted</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Logout Option -->
                        <div class="text-center">
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="d-inline">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="btn btn-link text-secondary text-decoration-none">
                                    <i class="fas fa-sign-out-alt me-1"></i>
                                    Sign Out
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Enhanced animations */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

@keyframes pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
    100% { opacity: 1; transform: scale(1); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation classes */
.shake-animation {
    animation: shake 3s infinite;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Card animations */
.card {
    animation: fadeInUp 0.6s ease-out;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Button enhancements */
.btn {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* List group enhancements */
.list-group-item {
    transition: all 0.2s ease;
    border-radius: 8px !important;
    margin-bottom: 2px;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* Badge enhancements */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* Avatar styling */
.avatar-name {
    font-size: 1.2rem;
    font-weight: 600;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Auto-refresh every 60 seconds to check if account is unlocked
let refreshTimer = setTimeout(function() {
    window.location.reload();
}, 60000);

// Show loading state when clicking submit button
document.addEventListener('DOMContentLoaded', function() {
    const submitBtn = document.querySelector('a[href*="daily-performance"]');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            // Clear the refresh timer since user is taking action
            clearTimeout(refreshTimer);

            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Redirecting...';
            this.classList.add('disabled');

            // Re-enable after 3 seconds in case of issues
            setTimeout(() => {
                this.innerHTML = originalText;
                this.classList.remove('disabled');
            }, 3000);
        });
    }

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add entrance animation delay for list items
    const listItems = document.querySelectorAll('.list-group-item');
    listItems.forEach((item, index) => {
        item.style.animationDelay = `${index * 0.1}s`;
        item.classList.add('fadeInUp');
    });
});

// Add a visual indicator for auto-refresh
let countdownElement = null;
function showRefreshCountdown() {
    if (!countdownElement) {
        countdownElement = document.createElement('div');
        countdownElement.className = 'position-fixed bottom-0 end-0 m-3 p-2 bg-info text-white rounded shadow-sm';
        countdownElement.style.fontSize = '0.8rem';
        countdownElement.style.zIndex = '1050';
        document.body.appendChild(countdownElement);
    }

    let seconds = 60;
    const interval = setInterval(() => {
        seconds--;
        countdownElement.innerHTML = `<i class="fas fa-sync-alt me-1"></i>Auto-refresh in ${seconds}s`;

        if (seconds <= 0) {
            clearInterval(interval);
            countdownElement.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Refreshing...';
        }
    }, 1000);
}

// Start countdown after 30 seconds
setTimeout(showRefreshCountdown, 30000);
</script>
</body>
</html>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/account-locked.blade.php ENDPATH**/ ?>