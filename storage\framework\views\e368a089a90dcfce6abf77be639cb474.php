<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">My Performance Reports</h5>
                            <p class="text-sm mb-0">View your daily performance submission history</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <a href="<?php echo e(route('daily-performance.create')); ?>" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-plus"></i>&nbsp;&nbsp;Submit Today's Performance
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Reports -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Performance History</h6>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hours</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Self Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Superior Rating</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $performances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $performance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($performance->performance_date->format('M d, Y')); ?></h6>
                                                <p class="text-xs text-secondary mb-0"><?php echo e($performance->performance_date->format('l')); ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm"><?php echo e($performance->task->title ?? 'No Task'); ?></h6>
                                            <?php if($performance->department): ?>
                                                <p class="text-xs text-secondary mb-0"><?php echo e($performance->department->name); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            <?php echo e($performance->hours_worked ?? '-'); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <?php if($performance->overall_rating): ?>
                                            <span class="badge badge-sm bg-gradient-<?php echo e($performance->overall_rating === 'excellent' ? 'success' : ($performance->overall_rating === 'good' ? 'info' : ($performance->overall_rating === 'fair' ? 'warning' : 'danger'))); ?>">
                                                <?php echo e(ucfirst($performance->overall_rating)); ?>

                                            </span>
                                        <?php else: ?>
                                            <span class="text-secondary">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle text-center">
                                        <?php if($performance->superior_rating): ?>
                                            <span class="badge badge-sm bg-gradient-<?php echo e($performance->superior_rating === 'excellent' ? 'success' : ($performance->superior_rating === 'good' ? 'info' : ($performance->superior_rating === 'fair' ? 'warning' : 'danger'))); ?>">
                                                <?php echo e(ucfirst($performance->superior_rating)); ?>

                                            </span>
                                            <?php if($performance->superior_comments): ?>
                                                <br><small class="text-xs text-secondary">Has Comments</small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge badge-sm bg-gradient-secondary">Not Rated</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle text-center">
                                        <?php if($performance->is_submitted): ?>
                                            <span class="badge badge-sm bg-gradient-success">Submitted</span>
                                            <?php if($performance->submitted_at): ?>
                                                <br><small class="text-xs text-secondary"><?php echo e($performance->submitted_at->format('H:i')); ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge badge-sm bg-gradient-warning">Draft</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="align-middle text-center">
                                        <div class="d-flex justify-content-center gap-1">
                                            <button class="btn btn-sm btn-outline-info mb-0" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#viewModal<?php echo e($performance->id); ?>" 
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <?php if(!$performance->is_submitted): ?>
                                                <a href="<?php echo e(route('daily-performance.create')); ?>?edit=<?php echo e($performance->id); ?>" 
                                                   class="btn btn-sm btn-outline-primary mb-0" 
                                                   title="Edit Draft">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>

                                <!-- View Modal -->
                                <div class="modal fade" id="viewModal<?php echo e($performance->id); ?>" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-calendar-day me-2"></i>
                                                    Performance Report - <?php echo e($performance->performance_date->format('M d, Y')); ?>

                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Task:</strong> <?php echo e($performance->task->title ?? 'No Task'); ?></p>
                                                        <p class="mb-1"><strong>Department:</strong> <?php echo e($performance->department->name ?? 'No Department'); ?></p>
                                                        <p class="mb-1"><strong>Hours Worked:</strong> <?php echo e($performance->hours_worked ?? '-'); ?></p>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <p class="mb-1"><strong>Self Rating:</strong> 
                                                            <?php if($performance->overall_rating): ?>
                                                                <span class="badge bg-<?php echo e($performance->overall_rating === 'excellent' ? 'success' : ($performance->overall_rating === 'good' ? 'info' : ($performance->overall_rating === 'fair' ? 'warning' : 'danger'))); ?>">
                                                                    <?php echo e(ucfirst($performance->overall_rating)); ?>

                                                                </span>
                                                            <?php else: ?>
                                                                <span class="text-muted">No rating</span>
                                                            <?php endif; ?>
                                                        </p>
                                                        <p class="mb-1"><strong>Superior Rating:</strong> 
                                                            <?php if($performance->superior_rating): ?>
                                                                <span class="badge bg-<?php echo e($performance->superior_rating === 'excellent' ? 'success' : ($performance->superior_rating === 'good' ? 'info' : ($performance->superior_rating === 'fair' ? 'warning' : 'danger'))); ?>">
                                                                    <?php echo e(ucfirst($performance->superior_rating)); ?>

                                                                </span>
                                                            <?php else: ?>
                                                                <span class="text-muted">Not rated</span>
                                                            <?php endif; ?>
                                                        </p>
                                                        <p class="mb-1"><strong>Status:</strong> 
                                                            <span class="badge bg-<?php echo e($performance->is_submitted ? 'success' : 'warning'); ?>">
                                                                <?php echo e($performance->is_submitted ? 'Submitted' : 'Draft'); ?>

                                                            </span>
                                                        </p>
                                                    </div>
                                                </div>
                                                
                                                <?php if($performance->tasks_completed): ?>
                                                    <div class="mt-3">
                                                        <strong>Tasks Completed:</strong>
                                                        <p class="mb-0 mt-1"><?php echo e($performance->tasks_completed); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if($performance->challenges_faced): ?>
                                                    <div class="mt-3">
                                                        <strong>Challenges Faced:</strong>
                                                        <p class="mb-0 mt-1"><?php echo e($performance->challenges_faced); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if($performance->next_day_plan): ?>
                                                    <div class="mt-3">
                                                        <strong>Next Day Plan:</strong>
                                                        <p class="mb-0 mt-1"><?php echo e($performance->next_day_plan); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if($performance->additional_notes): ?>
                                                    <div class="mt-3">
                                                        <strong>Additional Notes:</strong>
                                                        <p class="mb-0 mt-1"><?php echo e($performance->additional_notes); ?></p>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <?php if($performance->superior_comments): ?>
                                                    <div class="mt-3">
                                                        <strong>Superior Comments:</strong>
                                                        <div class="alert alert-info mt-1">
                                                            <?php echo e($performance->superior_comments); ?>

                                                            <?php if($performance->ratedBy): ?>
                                                                <br><small class="text-muted">- <?php echo e($performance->ratedBy->name); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                                <?php if(!$performance->is_submitted): ?>
                                                    <a href="<?php echo e(route('daily-performance.create')); ?>?edit=<?php echo e($performance->id); ?>" 
                                                       class="btn btn-primary">
                                                        <i class="fas fa-edit me-2"></i>Edit Draft
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-clipboard-list text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h6 class="text-muted">No Performance Reports Found</h6>
                                            <p class="text-sm text-muted mb-3">You haven't submitted any performance reports yet.</p>
                                            <a href="<?php echo e(route('daily-performance.create')); ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus me-2"></i>Submit Your First Report
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if($performances->hasPages()): ?>
                        <div class="d-flex justify-content-center mt-3">
                            <?php echo e($performances->links()); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/daily-performance/my-reports.blade.php ENDPATH**/ ?>