<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="notice"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Appointment"></x-navbars.navs.auth>
        <!-- End Navbar -->

        <!-- Create Appointment Entry Form -->
        <style>
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
        </style>
        
        <div class="container">
            <h1 class="mb-4">Add Appointment</h1>
            <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js"></script>
            <script>
              tinymce.init({
                selector: '#myEditor',
                plugins: 'image',
                toolbar: 'undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | outdent indent | image',
                images_upload_url: 'your-upload-handler.php', // Replace with your server-side upload handler
                images_upload_credentials: true
              });
            </script>
          </head>
          <body>
          
          <div>
            <textarea id="myEditor"></textarea>
          </div>
            <form action="#" method="get">    
                <table id="emptbl">
                    <tr>
                        <th>Appointment Date</th>
                        <th>Appointment Day</th>
                        <th>Appointment Time</th>
                    </tr>
                    <tr>
                        <td id="col0"><input type="Date" name="date[]" value="" /></td>
                        <td id="col1">
                            <select name="day[]">
                                <option value="Monday">Monday</option>
                                <option value="Tuesday">Tuesday</option>
                                <option value="Wednesday">Wednesday</option>
                                <option value="Thursday">Thursday</option>
                                <option value="Friday">Friday</option>
                                <option value="Saturday">Saturday</option>
                                <option value="Sunday">Sunday</option>
                            </select>
                        </td>
                        <td id="col2"><input type="time" name="time[]" value="" /></td>
                    </tr>
                    <tr>
                        <th>Scheduler Name</th>
                        <th>Contact No.</th>
                        <th>Description</th>
                    </tr>
                    <tr>
                        <td id="col3"><input type="name" name="scheduler[]" value="" /></td>
                        <td id="col4"><input type="number" name="contact[]" value="" /></td>
                        <td id="col5"><textarea name="description[]" rows="4" cols="50"></textarea></td>
                    </tr>
                </table> 
                <div style="text-align: center; margin-top: 20px;">
                        <input type="submit" class="btn btn-success" value="Submit" />
                </div>
            </form> 
        </div>

        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
