<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\TalaqFatawa;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;




class TalaqRemainingView extends Component
{
    public $currentId; // The ID of the current record being viewed
    public $record;    // The current record
    public $muftiComment = ''; // Holds the new comment
    public $comments = [];     // Holds all comments for the current record



    public function mount($id = null)
    {
        $this->currentId = $id ?: TalaqFatawa::first()->id; // Default to the first record if no ID is passed
        $this->loadRecord();

        if ($this->record) {
            DB::table('talaq_fatawa_manage')
                ->where('talaq_fatawa_id', $this->currentId)
                ->update(['downloaded_by_admin' => now()]);
        }

    }
    public function loadRecord()
    {
        $this->record = TalaqFatawa::find($this->currentId);
        $this->comments = $this->record ? json_decode($this->record->comments, true) ?? [] : [];

    }
    public function updatedSelectedRecord()
    {
        if ($this->record) {
            DB::table('talaq_fatawa_manage')
                ->where('talaq_fatawa_id', $this->currentId)
                ->update(['downloaded_by_admin' => null]);
        }
    }
    public function markAsChecked($folderLabel, $recordId)
{
    // Fetch the corresponding record from talaq_fatawa_manage table for the specific record ID
    $manageRecord = DB::table('talaq_fatawa_manage')
        ->where('talaq_fatawa_id', $recordId)
        ->first();

    // Fetch the content from TalaqFatawa table using $recordId
    $talaqFatawa = TalaqFatawa::find($recordId);

    if ($manageRecord && $talaqFatawa) {
        // Insert a new record into talaq_fatawa and get the ID
        $talaqFatawaId = DB::table('talaq_fatawa')->insertGetId([
            'content' => $talaqFatawa->content, // Get content from TalaqFatawa
            'ifta_code' => $manageRecord->file_code,
            'rec_date' => $manageRecord->file_created_date,
            'assign_id' => $manageRecord->sender,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Update the corresponding record in talaq_fatawa_manage table
        DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', $recordId) // Ensure this is the unique ID for the row
            ->update([
                'selected' => 1, // Set 'selected' to 1
                'checked_file_name' => $manageRecord->file_name,
                'checked_folder' => $folderLabel, // 'ok' or 'mahl-e-nazar'
                'checked_date' => now(),
                'talaq_checked_id' => $talaqFatawaId, // Add the new talaq_fatawa ID
            ]);

        // Optionally reload data to refresh the UI
        $this->loadRecord();
    }
}
    public function addComment()
    {
        if (trim($this->muftiComment)) {
            // Parse the current content
            $content = $this->record->content;

            // Generate the styled comment with right alignment for Urdu
            $styledComment = "<p style=\"font-family: 'Jameel Noori Nastaleeq'; font-size: 16px; background-color: yellow; margin: 0; text-align: right;\">{$this->muftiComment}</p>";

            // Ensure the mufti-commit div exists
            if (strpos($content, '<div class="mufti-commit">') === false) {
                $content .= '<div class="mufti-commit"></div>';
            }

            // Append the new comment to the mufti-commit div
            $updatedContent = preg_replace_callback(
                '/<div class="mufti-commit">(.*?)<\/div>/s',
                function ($matches) use ($styledComment) {
                    return '<div class="mufti-commit">' . $matches[1] . $styledComment . '</div>';
                },
                $content
            );

            // Update the record in the database
            $this->record->update([
                'content' => $updatedContent,
            ]);

            // Add the comment to the array for live display
            $this->comments[] = $this->muftiComment;

            // Clear the muftiComment field
            $this->muftiComment = '';

            // Reload the record to reflect changes
            $this->loadRecord();
        }
    }
    public function next()
    {
        // Fetch the next record based on the criteria
        $nextRecordId = DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', '>', $this->currentId)
            ->where('selected', 0)
            ->orderBy('talaq_fatawa_id', 'asc')
            ->value('talaq_fatawa_id'); // Get only the ID

        if ($nextRecordId) {
            $this->currentId = $nextRecordId;
            $this->loadRecord();

            // Update downloaded_by_admin to current date and time
            if ($this->record) {
                DB::table('talaq_fatawa_manage')
                    ->where('talaq_fatawa_id', $this->currentId)
                    ->update(['downloaded_by_admin' => now()]);
            }
        }
    }

    public function previous()
    {
        // Fetch the previous record based on the criteria
        $previousRecordId = DB::table('talaq_fatawa_manage')
            ->where('talaq_fatawa_id', '<', $this->currentId)
            ->where('selected', 0)
            ->orderBy('talaq_fatawa_id', 'desc')
            ->value('talaq_fatawa_id'); // Get only the ID

        if ($previousRecordId) {
            $this->currentId = $previousRecordId;
            $this->loadRecord();

            // Update downloaded_by_admin to current date and time
            if ($this->record) {
                DB::table('talaq_fatawa_manage')
                    ->where('talaq_fatawa_id', $this->currentId)
                    ->update(['downloaded_by_admin' => now()]);
            }
        }
    }
    public function render()
    {
        return view('livewire.talaq-remaining-view')->layout('layouts.app');
    }
}
