<?php

namespace App\Http\Controllers;

Use Str;
Use Hash;
use Illuminate\Auth\Events\PasswordReset;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Password;

class SessionsController extends Controller
{
    public function create()
    {
        // Generate a random captcha string with numbers and alphabets
        $captcha = $this->generateRandomString(6); // Adjust the length as needed

        // Store the generated captcha in the session
        session(['captcha' => $captcha]);

        return view('sessions.create', [
            'captcha' => $captcha, // Pass the captcha string to the view
        ]);
    }

    public function store()
    {
        $attributes = request()->validate([
            'name' => 'required',
            'password' => 'required',
            'captcha' => 'required|string|in:' . session('captcha'), // Validate against the session value

        ]);

        if (!auth()->attempt(['name' => $attributes['name'], 'password' => $attributes['password']])) {
            throw ValidationException::withMessages([
                'name' => 'Your provided credentials could not be verified.'
            ]);
        }

        // Check if the authenticated user has the role 'Ma<PERSON><PERSON><PERSON>'
        if (auth()->user()->roles->contains('name', 'Mahlenazar')) {
            return redirect('/mahlenazar');
        }
        if (auth()->user()->roles->contains('name', 'Nazim_Viral')) {
            return redirect('/selected-viral');
        }
        if ((auth()->user()->roles->contains('name', 'Shoba_Viral') || auth()->user()->roles->contains('name', 'Shoba_Viral_Iec'))
            && request()->path() !== 'shoba-viral') {
            return redirect('/shoba-viral');
        }
        // if (auth()->user()->roles->contains('name', 'mujeeb')) {
        //     return redirect('/mujeeb_assign_page');
        // }

        session()->regenerate();

        return redirect('/dashboard');
    }

    public function show(){
        request()->validate([
            'email' => 'required|email',
        ]);

        $status = Password::sendResetLink(
            request()->only('email')
        );

        return $status === Password::RESET_LINK_SENT
                    ? back()->with(['status' => __($status)])
                    : back()->withErrors(['email' => __($status)]);

    }

    public function update(){

        request()->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:8|confirmed',
        ]);

        $status = Password::reset(
            request()->only('email', 'password', 'password_confirmation', 'token'),
            function ($user, $password) {
                $user->forceFill([
                    'password' => ($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );

        return $status === Password::PASSWORD_RESET
                    ? redirect()->route('login')->with('status', __($status))
                    : back()->withErrors(['email' => [__($status)]]);
    }

    public function destroy()
    {
        auth()->logout();

        return redirect('/sign-in');
    }
    private function generateRandomString($length = 6)
{
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}

}
