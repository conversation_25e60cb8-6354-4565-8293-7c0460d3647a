<div style="direction: rtl;">
<div class="mt-4">
    <h3 class="font-bold mb-2">Add New Tasurat</h3>
    <input type="text" wire:model="newOption" placeholder="Tasurat" class="border border-gray-300 p-1 mb-2" style="width: 100%; max-width: 400px;">
    <input type="text" wire:model="newTasuratNo" placeholder="Tasurat No" class="border border-gray-300 p-1 mb-2">
    <button wire:click="save" class="btn btn-success text-white px-3 py-1">Add</button>
</div>    <!-- Display Tasurat List -->
    <table class="table-auto w-full">
    <thead>
        <tr>
            <th>ID</th>
            <th>Option</th>
            <th>Tasurat No</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($tasurats as $tasurat)
            <tr>
                @if ($editId === $tasurat->id)
                    <!-- Edit Mode -->
                    <td>{{ $tasurat->id }}</td>
                    <td>
                        <input type="text" wire:model="option" class="form-control">
                    </td>
                    <td>
                        <input type="text" wire:model="tasurat_no" class="form-control">
                    </td>
                    <td>
                        <button wire:click="save" class="btn btn-success">Save</button>
                        <button wire:click="resetInput" class="btn btn-danger">Cancel</button>
                    </td>
                @else
                    <!-- View Mode -->
                    <td>{{ $tasurat->id }}</td>
                    <td style="text-align: right;">{{ $tasurat->option }}</td>
                    <td>{{ $tasurat->tasurat_no }}</td>
                    <td>
                        <button wire:click="edit({{ $tasurat->id }})" class="btn btn-primary">Edit</button>
                        <button wire:click="delete({{ $tasurat->id }})" class="btn btn-danger">Delete</button>
                    </td>
                @endif
            </tr>
        @endforeach
    </tbody>
</table>

   
</div>
