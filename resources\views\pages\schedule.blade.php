<x-layout bodyClass="g-sidenav-show  bg-gray-200">
    <x-navbars.sidebar activePage="schedule"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="schedule"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <link rel="stylesheet" href="//cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css">
            <!-- Link to create Fatawa entry -->
            <div class="container">
                
                <!-- Link to view Fatawa entries -->
                <a href="{{ route('sform') }}" class="btn btn-primary">Set Appointment</a>
                     <!-- You can add more content here as needed -->
                     <div class="container">
                        <h1>Schedule</h1>
                        <table class="table" id="myTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Day</th>
                                    <th>Time</th>
                                    <th>Event Name</th>
                                    <th>Location</th>
                                    <th>Description</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($schedules as $schedule)
                                <tr>
                                    <td>{{ $schedule->schedule_date }}</td>
                                    <td>{{ $schedule->schedule_day }}</td>
                                    <td>{{ $schedule->schedule_time }}</td>
                                    <td>{{ $schedule->event_name }}</td>
                                    <td>{{ $schedule->location }}</td>
                                    <td>{{ $schedule->description }}</td>
                                    <td>
                                        <a href="{{ route('schedules.edit', ['id' => $schedule->id]) }}" class="btn btn-sm btn-warning">Edit</a>
                                        <form action="{{ route('schedules.destroy', ['id' => $schedule->id]) }}" method="POST" style="display: inline;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this schedule?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
            </div>
            <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
            <script src="//cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
            <script>
                $(document).ready(function () {
                  $('#myTable').DataTable();
            
                });
              </script>
                    </div>
            <x-footers.auth></x-footers.auth>
        </div>
    </main>
    <x-plugins></x-plugins>

</x-layout>
