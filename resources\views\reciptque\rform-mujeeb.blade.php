<x-layout bodyClass="g-sidenav-show bg-gray-200">


    <!-- Include Bootstrap Timepicker JS -->
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-timepicker/0.5.2/js/bootstrap-timepicker.min.js"></script> --}}

    <x-navbars.sidebar activePage="reciption"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Reciption Page"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <!-- Include jQuery library -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.min.js"></script>


        <!-- Create Appointment Entry Form -->
        <style>

          .white-box{
            font-family: Jameel Noori Nastaleeq;

          }

            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }

            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }

            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }

            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }

            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }

                #emptbl th {
                    font-weight: bold;
                }

                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }

                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
        </style>
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                // Get the input element by its ID
                var dateInput = document.getElementById("rec_date");

                // Create a new date object with the current date
                var currentDate = new Date();

                // Format the current date as "YYYY-MM-DD"
                var formattedDate = currentDate.toISOString().split('T')[0];

                // Set the formatted date as the input's value
                dateInput.value = formattedDate;
            });
        </script>
        <div class="white-box">
            <h1 class="text-center">Darulifta Ahlesunnat Reciption </h1>
            <div class="row">
                <div class="col-md-12">
                    <form action="{{ route('reciptque.store') }}" method="POST" enctype="multipart/form-data" class="form-horizontal answer-form" role="form" id="action">

                        {{-- {{ url(current_controller('l') . '-transaction/' . $id) }} --}}
                        @csrf



                        <div class="row">

                            <!-- Phone -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="date" class="col-md-12">Date<i class="req">*</i></label>
                                <div class="col-md-12">

                                    <input type="date" name="rec_date" id="rec_date" tabindex="1" class="form-control" required>


                                    @error('date')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="form-group col-md-4 pull-right">
                                <label for="phone" class="col-md-12">Question Daily/Email<i class="req">*</i></label>
                                <div class="col-md-12">

                                    <select name="question_type" id="question_type" tabindex="1" class="form-control searchSelect2" required>
                                    <option value="Daily">Daily</option>
                                    <option value="Email">Email</option>
                                    </select>
                                    @error('phone')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="form-group col-md-4 pull-right">
                                <label for="phone" class="col-md-12">Ifta Code<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <input type="text" name="ifta_code" id="ifta_code" tabindex="1" class="form-control" value="{{ old('ifta_code', $newIftaCode) }}" required>
                                    @error('ifta_code')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>

                            </div>
                            <div class="form-group col-md-4 pull-right">
                                <label for="phone" class="col-md-12">Phon No:</label>
                                <div class="col-md-12">
                                    <input type="tel" name="phone" id="number_phone" tabindex="1" class="form-control" placeholder="Enter Phone No" minlength="10" maxlength="15" pattern="\d{10,15}" value="">
                                    @error('phone')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <!-- Email -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="email" class="col-md-12">email</label>
                                <div class="col-md-12">
                                    <input type="email" name="email" id="qs_email" tabindex="3" class="form-control" placeholder="Enter Email" maxlength="100" value="">
                                    @error('email')
                                    <div class="alert-danger error-message"></div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Requester Title -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="title" class="col-md-12">Sayel<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <input type="text" name="sayel" id="qs_title" tabindex="2" class="form-control" placeholder="Sayel" pattern="[A-Z][a-z. -\u0600-\u06FF]{3,99}" value="" required minlength="4" maxlength="100">
                                    @error('name')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>



                            <!-- Address -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="address" class="col-md-12">Address</label>
                                <div class="col-md-12">
                                    <input type="text" name="address" id="qs_address" tabindex="4" class="form-control" placeholder="address" maxlength="150" pattern="[A-Za-z0-9\s:\/#,.\u0600-\u06FF]{1,150}" value="">
                                    @error('address')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            {{-- @endif --}}

                            <!-- Issues -->
                           <!-- Issues Dropdown -->
                           <div class="form-group col-md-4 pull-right">
                            <label for="issue_category_id" class="col-md-12">Muzo<i class="req">*</i></label>
                            <div class="col-md-12">
                                <select id="issue" name="issue" tabindex="5" class="form-control searchSelect2">
                                    <option value="">Select Muzo</option>
                                    @foreach($issues as $issue)
                                        <option value="{{ $issue->id }}">{{ $issue->name }}</option>
                                    @endforeach
                                </select>
                                @error('issue_category_id')
                                <div class="alert-danger error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <!-- Sub Issues Dropdown -->
                        <div class="form-group col-md-4 pull-right">
                            <label for="issue_subcategory_id" class="col-md-12">Zeli Muzo<i class="req">*</i></label>
                            <div class="col-md-12">
                                <select id="sub_issue" name="sub_issue" tabindex="6" class="form-control searchSelect2">
                                    <option value="">Select a Zeli-Muzo</option>
                                </select>
                                @error('issue_subcategory_id')
                                <div class="alert-danger error-message">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                            <!-- Title -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="question_title" class="col-md-12">Question Title<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <input type="text" name="question_title" tabindex="7" class="form-control" placeholder="title"  maxlength="150" pattern="[A-Za-z0-9\s:\/#,.\u0600-\u06FF]{1,150}" value="">
                                    @error('question_title')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Expected Date -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="expected_date" class="col-md-12">Expected Date<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <input type="date" name="expected_date" tabindex="8" class="form-control" id="expected_date" placeholder="dd-mm-yyyy" value="">
                                    @error('expected_date')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            {{-- @if (count($branches) > 1) --}}
                            <!-- User Branches -->
                            <div class="form-group col-md-4 pull-right">
                                <label for="question_branch" class="col-md-12">Branch<i class="req">*</i></label>
                                <div class="col-md-12">
                                    <select name="question_branch" tabindex="9" class="form-control">
                                        @php
                                                    $darulifta = (count(Auth::user()->roles) > 1) ? $branches : $rolebranches;
                                                @endphp


                                        @foreach($darulifta as $branch)
                                        <option value="{{ $branch->darul_name }}">{{ $branch->darul_name }}</option>
                                        @endforeach
                                    </select>
                                    @error('question_branch')
                                    <div class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            {{-- @endif --}}
                        </div>

                        <div class="row">
                            <!-- Rest of your form fields as needed -->
                        </div>

                        <div class="row">
                            <!-- Question Text Area -->
                            <div class="col-md-12">
                                <h2>Question<i style="font-size: 25px;" class="req">*</i></h2>
                                @error('file')
                                <div for="file" class="alert-danger error-message font-Nastalleq">{{ $message }}</div>
                                @enderror
                                <div class="form-group">
                                    <textarea name="question" tabindex="10" class="summernote form-control font-Nastalleq" id="summernote" rows="4" placeholder="Enter Question Here"></textarea>
                                    @error('question')
                                    <div for="question" class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row" id="template-row">
                            <!-- File -->
                            <!-- HTML for file upload can be added here -->
                        </div>

                        <div class="row" id="actions">
                            <!-- User Assigning -->
                            <div class="col-md-6">
                                <label for="assign_id" class="col-md-12">Assign Question To Mujeeb</label>
                                <div class="col-md-6 pull-right">
                                    <select name="assign_id" tabindex="12" class="form-control">
                                        @php
                                                    $mujeeb = (count(Auth::user()->roles) > 1) ? $user : $rolemujeeb;
                                                @endphp
                                                <option value="" selected>Select Mujeeb For Send Fatwa</option>
                                        @foreach($mujeeb as $assignUser)

                                        <option value="{{ $assignUser->mujeeb_name }}">{{ $assignUser->mujeeb_name }}</option>
                                        @endforeach
                                    </select>
                                    @error('assign_to')
                                    <div for="assign_to" class="alert-danger error-message">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <input type="hidden" id="uploaded-files" name="uploaded">
                            <div class="col-md-6">
                                <div class="col-md-12">
                                    <!-- The file input button can be added here -->
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="question_text" class="text">
                        <div class="clearfix"></div>
                        <div class="row">
                            <div class="form-group">
                                <div class="col-md-12">
                                    <button id="btn_submit" class="btn btn-info btn_submit" type="submit">submit</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>


        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

    $(document).ready(function () {
        $('#issue').on('change', function () {
            var issueId = $(this).val();
            if (issueId) {
                $.ajax({
                    url: '{{ route("get-sub-issues") }}',
                    type: 'GET',
                    data: {issue_id: issueId},
                    success: function (data) {
                        $('#sub_issue').empty();
                        $('#sub_issue').append('<option value="">Select a Zali-Muzo</option>');
                        $.each(data, function (key, value) {
                            $('#sub_issue').append('<option value="' + value.id + '">' + value.name + '</option>');
                        });
                    }
                });
            } else {
                $('#sub_issue').empty();
                $('#sub_issue').append('<option value="">Select a Zali-Muzo</option>');
            }
        });
    });
    $(document).ready(function () {
    // Add an event listener for the phone input
    $('#number_phone').on('blur', function () {
        let phone = $(this).val();
        if (phone) {
            // Send an AJAX request to check if the phone exists
            $.ajax({
                url: '{{ route("checkPhoneExist") }}',
                type: 'GET',
                data: { phone: phone },
                success: function (data) {
                    if (data) {
                        // Populate the "sayel" and "address" fields
                        if (data.sayel) {
                            $('#qs_title').val(data.sayel);
                        }
                        if (data.address) {
                            $('#qs_address').val(data.address);
                        }
                    }
                }
            });
        }
    });

    // Add an event listener for the email input
    $('#qs_email').on('blur', function () {
        let email = $(this).val();
        if (email) {
            // Send an AJAX request to check if the email exists
            $.ajax({
                url: '{{ route("checkEmailExist") }}',
                type: 'GET',
                data: { email: email },
                success: function (data) {
                    if (data) {
                        // Populate the "sayel" and "address" fields
                        if (data.sayel) {
                            $('#qs_title').val(data.sayel);
                        }
                        if (data.address) {
                            $('#qs_address').val(data.address);
                        }
                    }
                }
            });
        }
    });
});
$(document).ready(function () {
    // Add an event listener for the question_type select
    $('#question_type').on('change', function () {
        if ($(this).val() === 'Email') {
            // If Email is selected, disable the phone input
            $('#number_phone').prop('disabled', true);
        } else {
            // If Daily is selected, enable the phone input
            $('#number_phone').prop('disabled', false);
        }
    });
});
$(document).ready(function () {
    // Function to calculate and set expected_date
    function setExpectedDate() {
        const recDate = new Date($('#rec_date').val());
        const expectedDate = new Date(recDate);
        expectedDate.setDate(recDate.getDate() + 3);
        const formattedExpectedDate = expectedDate.toISOString().split('T')[0];
        $('#expected_date').val(formattedExpectedDate);
    }

    // Initialize rec_date with the current date
    const currentDate = new Date();
    const formattedCurrentDate = currentDate.toISOString().split('T')[0];
    $('#rec_date').val(formattedCurrentDate);

    // Call the function to set expected_date on initial load
    setExpectedDate();

    // Add event listeners for the rec_date input
    $('#rec_date').on('change', setExpectedDate);

    // Add an event listener for manually adjusting expected_date
    $('#expected_date').on('change', function () {
        // You can add validation or further handling here if needed
    });
});
</script>


        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
