<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\User;
use App\Models\SupervisorAssistant;
use App\Services\RoleManagementService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class SupervisorAssistantMapping extends Component
{
    use WithPagination, AuthorizesRequests;

    public $showAssignModal = false;
    public $showViewModal = false;
    public $showCreateSuperiorModal = false;
    
    public $selectedSupervisor = null;
    public $selectedAssistants = [];
    public $availableAssistants = [];
    public $eligibleSuperiors = [];
    public $newSuperiorId = '';
    public $newSuperiorAssistants = [];
    
    public $search = '';
    public $filterDepartment = 'all';
    public $departments = [];
    
    public $mappings = [];
    public $statistics = [];

    protected $roleManagementService;

    public function boot(RoleManagementService $roleManagementService)
    {
        $this->roleManagementService = $roleManagementService;
    }

    public function mount()
    {
        $this->authorize('assign-supervisors');
        $this->loadData();
        $this->loadStatistics();
    }

    public function render()
    {
        $filteredMappings = collect($this->mappings)
            ->when($this->search, function ($collection) {
                return $collection->filter(function ($mapping) {
                    return stripos($mapping['supervisor']->name, $this->search) !== false ||
                           $mapping['assistants']->contains(function ($assistant) {
                               return stripos($assistant->name, $this->search) !== false;
                           });
                });
            })
            ->when($this->filterDepartment !== 'all', function ($collection) {
                return $collection->filter(function ($mapping) {
                    return $mapping['supervisor']->departments->contains('id', $this->filterDepartment);
                });
            });

        return view('livewire.supervisor-assistant-mapping', [
            'mappings' => $filteredMappings,
        ]);
    }

    public function loadData()
    {
        // Load supervisor-assistant mappings
        $this->mappings = SupervisorAssistant::with(['supervisor.departments', 'assistant.departments'])
            ->where('is_active', true)
            ->get()
            ->groupBy('supervisor_id')
            ->map(function ($group) {
                return [
                    'supervisor' => $group->first()->supervisor,
                    'assistants' => $group->pluck('assistant'),
                    'assigned_at' => $group->first()->assigned_at,
                    'assigned_by' => $group->first()->assignedBy,
                ];
            })
            ->values()
            ->toArray();

        // Load available data for modals
        $this->availableAssistants = $this->roleManagementService->getEligibleAssistants();
        $this->eligibleSuperiors = $this->roleManagementService->getEligibleSuperiors();
        
        // Load departments
        $this->departments = \App\Models\Department::active()->select('id', 'name')->orderBy('name')->get();
    }

    public function loadStatistics()
    {
        $totalSuperiors = User::whereHas('roles', function ($query) {
            $query->where('name', 'Superior');
        })->count();

        $activeMappings = SupervisorAssistant::where('is_active', true)->count();
        $totalAssistants = SupervisorAssistant::where('is_active', true)->distinct('assistant_id')->count();
        $unassignedAssistants = $this->availableAssistants->count();

        $this->statistics = [
            'total_superiors' => $totalSuperiors,
            'active_mappings' => $activeMappings,
            'total_assistants' => $totalAssistants,
            'unassigned_assistants' => $unassignedAssistants,
            'avg_assistants_per_superior' => $totalSuperiors > 0 ? round($totalAssistants / $totalSuperiors, 1) : 0,
        ];
    }

    public function openAssignModal($supervisorId)
    {
        $this->selectedSupervisor = User::findOrFail($supervisorId);
        $this->authorize('assignAssistants', $this->selectedSupervisor);
        
        // Get current assistants
        $currentAssistants = SupervisorAssistant::where('supervisor_id', $supervisorId)
            ->where('is_active', true)
            ->pluck('assistant_id')
            ->toArray();
        
        $this->selectedAssistants = $currentAssistants;
        $this->showAssignModal = true;
    }

    public function openViewModal($supervisorId)
    {
        $this->selectedSupervisor = User::with(['assistants', 'departments'])->findOrFail($supervisorId);
        $this->showViewModal = true;
    }

    public function openCreateSuperiorModal()
    {
        $this->authorize('assign-supervisors');
        $this->resetCreateForm();
        $this->showCreateSuperiorModal = true;
    }

    public function assignAssistants()
    {
        $this->authorize('assignAssistants', $this->selectedSupervisor);
        
        $this->roleManagementService->assignAssistants(
            $this->selectedSupervisor,
            $this->selectedAssistants,
            auth()->user()
        );

        $this->loadData();
        $this->loadStatistics();
        $this->showAssignModal = false;
        
        session()->flash('message', 'Assistants assigned successfully.');
    }

    public function createSuperior()
    {
        $this->authorize('assign-supervisors');
        
        $this->validate([
            'newSuperiorId' => 'required|exists:users,id',
            'newSuperiorAssistants' => 'array',
            'newSuperiorAssistants.*' => 'exists:users,id',
        ]);

        $user = User::findOrFail($this->newSuperiorId);
        
        $this->roleManagementService->assignSuperiorRole(
            $user,
            $this->newSuperiorAssistants,
            auth()->user()
        );

        $this->loadData();
        $this->loadStatistics();
        $this->showCreateSuperiorModal = false;
        
        session()->flash('message', "Superior role assigned to {$user->name} successfully.");
    }

    public function removeSuperior($supervisorId)
    {
        $supervisor = User::findOrFail($supervisorId);
        $this->authorize('assignRoles', $supervisor);
        
        $this->roleManagementService->removeSuperiorRole($supervisor);

        $this->loadData();
        $this->loadStatistics();
        
        session()->flash('message', "Superior role removed from {$supervisor->name}.");
    }

    public function removeAssistant($supervisorId, $assistantId)
    {
        $supervisor = User::findOrFail($supervisorId);
        $this->authorize('assignAssistants', $supervisor);
        
        SupervisorAssistant::where('supervisor_id', $supervisorId)
            ->where('assistant_id', $assistantId)
            ->where('is_active', true)
            ->update(['is_active' => false]);

        $this->loadData();
        $this->loadStatistics();
        
        $assistant = User::findOrFail($assistantId);
        session()->flash('message', "Removed {$assistant->name} from {$supervisor->name}'s team.");
    }

    public function closeModals()
    {
        $this->showAssignModal = false;
        $this->showViewModal = false;
        $this->showCreateSuperiorModal = false;
        $this->selectedSupervisor = null;
        $this->resetCreateForm();
    }

    private function resetCreateForm()
    {
        $this->newSuperiorId = '';
        $this->newSuperiorAssistants = [];
        $this->resetValidation();
    }

    public function updatedSearch()
    {
        // No pagination reset needed since we're using collection filtering
    }

    public function updatedFilterDepartment()
    {
        // No pagination reset needed since we're using collection filtering
    }

    public function refreshData()
    {
        $this->loadData();
        $this->loadStatistics();
        session()->flash('message', 'Data refreshed successfully.');
    }

    public function exportMappings()
    {
        $this->authorize('generate-reports');
        
        // This would generate a PDF or Excel report
        session()->flash('message', 'Mapping export functionality will be implemented.');
    }

    public function getDepartmentBadgeColor($departmentName)
    {
        $colors = [
            'Research' => 'primary',
            'Documentation' => 'info',
            'Quality Control' => 'success',
            'Translation' => 'warning',
            'Digital Media' => 'secondary',
            'Administration' => 'dark',
        ];

        return $colors[$departmentName] ?? 'secondary';
    }
}
