<div>
    @if(!$webLink)
        <!-- Show input for web link if webLink is not available -->
        <input type="text" wire:model.lazy="webLink" placeholder="Insert web link">
        <div wire:loading wire:target="webLink">Saving web link...</div>
    @else
        <!-- Show view and edit buttons for web link -->
        <a href="{{ $webLink }}" target="_blank" class="btn btn-success btn-sm">View on web</a>
        <button wire:click="enableWebLinkEditing" class="btn btn-secondary btn-sm">Edit</button>

        @if($isEditingWebLink)
            <!-- Show input for editing web link -->
            <input type="text" wire:model.lazy="webLink" placeholder="Edit web link">
            <button wire:click="saveWebLink" class="btn btn-success btn-sm">Save</button>
            <button wire:click="cancelWebLinkEditing" class="btn btn-secondary btn-sm">Cancel</button>
        @endif
    @endif

    <!-- Display the web_date if it exists -->
    @if($webDate)
        <br>
        <span>Upload On Web: {{ $webDate }}</span>
    @elseif($WebDays)
        <!-- Display days not uploaded on web if web_date is empty -->
        <br>
        <span style="color: {{ $WebDays > 7 ? 'red' : 'black' }};">
            {{ $WebDays }} days not uploaded on web
        </span>
    @endif
</div>
