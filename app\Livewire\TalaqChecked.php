<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Models\ViralChat;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use DateTime;

class TalaqChecked extends Component
{
    use WithPagination;

    public $filter;
    public $search;
    public $daruliftaNames;
    public $darulifta;
    public $daruliftalist;
    public $orderByDaruliftaName = false; // Toggle button state


    protected $queryString = [
        'filter' => ['except' => ''],
        'search' => ['except' => ''],
    ];

    public function mount($filter = null, $darulifta = null)
    {
        $this->filter = $filter;
        $this->darulifta = $darulifta;
        $this->loadDaruliftaNames();


    }
    private function loadDaruliftaNames()
    {
        $user = Auth::user();
        $userRoles = $user->roles;
        $roleNames = $userRoles->pluck('name')->toArray();
        $firstRoleName = reset($roleNames);
        if ($this->darulifta === null) {
            if (count(Auth::user()->roles) > 1) {
                $this->daruliftaNames = DB::table('talaq_fatawa_manage')
                    ->join('daruliftas', 'talaq_fatawa_manage.darulifta_name', '=', 'daruliftas.darul_name')
                    ->select('talaq_fatawa_manage.darulifta_name')
                    ->distinct()
                    ->orderBy('daruliftas.id')
                    ->pluck('talaq_fatawa_manage.darulifta_name');


            } else {
                $this->daruliftaNames = DB::table('talaq_fatawa_manage')
    ->join('daruliftas', 'talaq_fatawa_manage.darulifta_name', '=', 'daruliftas.darul_name')
    ->select('talaq_fatawa_manage.darulifta_name')
    ->distinct()
    ->where('darulifta_name', $firstRoleName)
    ->orderBy('daruliftas.id')
    ->pluck('talaq_fatawa_manage.darulifta_name');


            }
        } else {
            $this->daruliftaNames = DB::table('talaq_fatawa_manage')
            ->join('daruliftas', 'talaq_fatawa_manage.darulifta_name', '=', 'daruliftas.darul_name')
            ->select('talaq_fatawa_manage.darulifta_name')
            ->distinct()
            ->where('darulifta_name', $this->darulifta)
            ->orderBy('daruliftas.id')
            ->pluck('talaq_fatawa_manage.darulifta_name');


        }

        $this->daruliftalist = DB::table('talaq_fatawa_manage')
        ->join('daruliftas', 'talaq_fatawa_manage.darulifta_name', '=', 'daruliftas.darul_name')
        ->select('talaq_fatawa_manage.darulifta_name')
        ->distinct()
        ->orderBy('daruliftas.id')
        ->pluck('talaq_fatawa_manage.darulifta_name');

    }

    public function deleteRecord($talaqFatawaId)
{
    // Start a transaction to ensure data integrity
    DB::transaction(function () use ($talaqFatawaId) {
        // Update the corresponding record in talaq_fatawa_manage table
        DB::table('talaq_fatawa_manage')
            ->where('talaq_checked_id', $talaqFatawaId)
            ->update([
                'selected' => 0, // Reset selected column
                'checked_folder' => null, // Clear checked folder
                'checked_date' => null, // Clear checked date
                'checked_file_name' => null, // Clear checked file name
                'checked_grade' => null, // Clear checked grade
                'checked_tasurat' => null, // Clear checked tasurat
                'checked_instructions' => null, // Clear checked instructions
                'talaq_checked_id' => null, // Clear talaq_checked_id
            ]);

        // Delete the record from talaq_fatawa table
        DB::table('talaq_fatawa')->where('id', $talaqFatawaId)->delete();
    });

    // Provide feedback to the user
    session()->flash('message', 'Record updated successfully.');

    // Optionally, refresh the component data
    $this->resetPage();
}
    public function updatedSearch()
    {
        $this->resetPage();
    }

        public function updatingFilter()
        {
            $this->resetPage();
        }

 public function toggleOrderBy()
    {
        $this->orderByDaruliftaName = !$this->orderByDaruliftaName;
        $this->resetPage(); // Reset pagination when toggling
    }
        public function render()
        {
             // Get the user roles
    $userRoles = auth()->user()->roles;
    $roleNames = $userRoles->pluck('name')->toArray();

    // Initialize the query with a subquery to get the latest IDs for each file_code
    $latestIds = DB::table('talaq_fatawa_manage')
        ->select('file_code', DB::raw('MAX(id) as latest_id'))
        ->groupBy('file_code');

    // Main query joining with the latest IDs
    $query = DB::table('talaq_fatawa_manage')
        ->join('talaq_fatawa', 'talaq_fatawa_manage.talaq_checked_id', '=', 'talaq_fatawa.id')
        ->joinSub($latestIds, 'latest', function($join) {
            $join->on('talaq_fatawa_manage.id', '=', 'latest.latest_id');
        })
        ->where('talaq_fatawa_manage.selected', 1)
        ->select(
            'talaq_fatawa_manage.*',
            'talaq_fatawa.content',
            'talaq_fatawa.ifta_code'
        );

// Apply conditions based on roles
if (in_array('mujeeb', $roleNames)) {
    $userName = auth()->user()->name;
    $query->where('sender', $userName);
} else {
    $query->whereIn('darulifta_name', $this->daruliftaNames);
}

// Apply search conditions
if ($this->search) {
    $query->where(function ($subQuery) {
        $subQuery->where('sender', 'like', '%' . $this->search . '%')
                 ->orWhere('file_code', 'like', '%' . $this->search . '%')
                 ->orWhere('category', 'like', '%' . $this->search . '%')
                 ->orWhere('darulifta_name', 'like', '%' . $this->search . '%');
    });
}

// Apply ordering
if ($this->orderByDaruliftaName) {
    $query->join('daruliftas', 'talaq_fatawa_manage.darulifta_name', '=', 'daruliftas.darul_name')
          ->orderBy('daruliftas.id');
} else {
    $query->orderBy('checked_date', 'desc');
}

// Paginate results
$question_b = $query->paginate(50);
            // Pass data to the view
            return view('livewire.talaq-checked', [
                'question_b' => $question_b,
                'daruliftalist' => $this->daruliftalist,
            ])->layout('layouts.app');
        }

}
