<div>
    <!-- Debug Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session('debug')): ?>
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <strong>Debug:</strong> <?php echo e(session('debug')); ?>

        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Livewire Debug Info -->
    <div class="alert alert-secondary">
        <strong>Livewire Debug:</strong>
        showAddForm = <?php echo e($showAddForm ? 'true' : 'false'); ?> |
        User: <?php echo e(auth()->user()->name ?? 'Not authenticated'); ?> |
        Component ID: <?php echo e($this->getId()); ?>

    </div>

    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                Performance Holiday Management
                            </h6>
                            <p class="text-sm mb-0 text-secondary">Manage holidays when performance submission is not required</p>
                        </div>
                        <button wire:click="openAddForm"
                                class="btn bg-gradient-primary btn-sm"
                                wire:loading.attr="disabled"
                                wire:target="openAddForm">
                            <span wire:loading.remove wire:target="openAddForm">
                                <i class="fas fa-plus me-1"></i> Add Holiday
                            </span>
                            <span wire:loading wire:target="openAddForm">
                                <i class="fas fa-spinner fa-spin me-1"></i> Loading...
                            </span>
                        </button>

                        <!-- Test Button -->
                        <button wire:click="$set('showAddForm', true)" class="btn btn-warning btn-sm ms-2">
                            Test Modal
                        </button>
                    </div>
                </div>
                <div class="card-body pt-3">
                    <!-- Filters -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <select wire:model.live="filterType" class="form-select">
                                    <option value="">All Types</option>
                                    <option value="religious">Religious</option>
                                    <option value="national">National</option>
                                    <option value="custom">Custom</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <select wire:model.live="filterYear" class="form-select">
                                    <option value="">All Years</option>
                                    <!--[if BLOCK]><![endif]--><?php for($year = date('Y'); $year >= date('Y') - 2; $year--): ?>
                                        <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                    <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-end">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Holidays exclude performance requirements
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Form Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showAddForm): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo e($editingId ? 'Edit Holiday' : 'Add New Holiday'); ?></h5>
                    <button type="button" class="btn-close" wire:click="hideAddForm"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="save">
                        <div class="mb-3">
                            <label class="form-label">Holiday Date *</label>
                            <input type="date" class="form-control <?php $__errorArgs = ['holiday_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   wire:model="holiday_date">
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['holiday_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Holiday Name *</label>
                            <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   wire:model="name" placeholder="e.g., Eid ul-Fitr">
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Type *</label>
                            <select class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" wire:model="type">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </select>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      wire:model="description" rows="3" 
                                      placeholder="Optional description about this holiday"></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="hideAddForm">Cancel</button>
                    <button type="button" class="btn btn-primary" wire:click="save">
                        <?php echo e($editingId ? 'Update Holiday' : 'Add Holiday'); ?>

                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-md-6">
            <select class="form-select" wire:model="filterType">
                <option value="">All Types</option>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($value); ?>"><?php echo e($label); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
        <div class="col-md-6">
            <select class="form-select" wire:model="filterYear">
                <?php for($year = date('Y') - 1; $year <= date('Y') + 2; $year++): ?>
                    <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
    </div>

    <!-- Holidays Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Date</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Holiday</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Type</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Created By</th>
                                    <th class="text-secondary opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm"><?php echo e($holiday->holiday_date->format('M d, Y')); ?></h6>
                                                <p class="text-xs text-secondary mb-0"><?php echo e($holiday->holiday_date->format('l')); ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm"><?php echo e($holiday->name); ?></h6>
                                            <!--[if BLOCK]><![endif]--><?php if($holiday->description): ?>
                                                <p class="text-xs text-secondary mb-0"><?php echo e(Str::limit($holiday->description, 50)); ?></p>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-sm 
                                            <?php if($holiday->type === 'religious'): ?> bg-gradient-success
                                            <?php elseif($holiday->type === 'national'): ?> bg-gradient-info
                                            <?php else: ?> bg-gradient-secondary
                                            <?php endif; ?>">
                                            <?php echo e($types[$holiday->type]); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-sm <?php echo e($holiday->is_active ? 'bg-gradient-success' : 'bg-gradient-secondary'); ?>">
                                            <?php echo e($holiday->is_active ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column justify-content-center">
                                            <h6 class="mb-0 text-sm"><?php echo e($holiday->createdBy->name); ?></h6>
                                            <p class="text-xs text-secondary mb-0"><?php echo e($holiday->created_at->format('M d, Y')); ?></p>
                                        </div>
                                    </td>
                                    <td class="align-middle">
                                        <div class="dropdown">
                                            <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                                                <i class="fa fa-ellipsis-v text-xs"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" wire:click="edit(<?php echo e($holiday->id); ?>)">Edit</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="toggleStatus(<?php echo e($holiday->id); ?>)">
                                                    <?php echo e($holiday->is_active ? 'Deactivate' : 'Activate'); ?>

                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" 
                                                       wire:click="delete(<?php echo e($holiday->id); ?>)"
                                                       onclick="return confirm('Are you sure you want to delete this holiday?')">Delete</a></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-calendar-times fa-3x text-secondary mb-3"></i>
                                            <h6 class="text-secondary">No holidays found</h6>
                                            <p class="text-sm text-secondary">Add holidays when performance submission is not required.</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!--[if BLOCK]><![endif]--><?php if($holidays->hasPages()): ?>
                <div class="card-footer">
                    <?php echo e($holidays->links()); ?>

                </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    </div>

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <?php if(session()->has('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/performance-holiday-management.blade.php ENDPATH**/ ?>