<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserController;
use App\Models\{
	User,
	Role,
	};
	use App\Http\Controllers\DashboardController;
	// use App\Livewire\DashBoard;
	use App\Http\Controllers\ProfileController;
    use App\Http\Controllers\DownloadController;
    use App\Http\Controllers\TalaqSidebarController;
    use App\Http\Controllers\TalaqFatawaController;
	use App\Http\Controllers\RegisterController;
	use App\Http\Controllers\SessionsController;
	use App\Http\Controllers\CheckFatawaController;
	use App\Http\Controllers\CmailController;
	use App\Models\Appointment;
	use App\Http\Controllers\SidebarController;
	use App\Http\Controllers\ScheduleController;
	use App\Http\Controllers\AppointmentController;
	use App\Http\Controllers\DaruliftaController;
	use App\Http\Controllers\MujeebController;
	use App\Http\Controllers\CheckerController;
	use App\Http\Controllers\TaskController;
	use App\Http\Controllers\FileController;
	use App\Http\Controllers\YearMonthController;
	use App\Http\Controllers\DataController;
	use App\Http\Controllers\ReciptqueController;
	use App\Http\Controllers\DeliverController;
	use App\Http\Controllers\MahlenazarController;
	use App\Http\Controllers\MahlenazarFatawaController;
	use App\Http\Controllers\MutakhassisController;
	use App\Http\Controllers\ViralFatawaController;
	use App\Livewire\OkFatawa;
	// use App\Livewire\ChatBox;
	use App\Livewire\RemainingFatawa;
	use App\Livewire\SendingFatawa;
	use App\Livewire\SentFatawa;
    use App\Livewire\ShobaViral;
	use App\Livewire\ViralFatawa;
    use App\Livewire\SelectViral;
    use App\Livewire\SelectedViral;
    use App\Livewire\TalaqRemaining;
    use App\Livewire\TalaqChecked;
	use App\Livewire\FatwaDetail;
	use App\Livewire\ViralFatwaDetail;
	use App\Livewire\ReciptionFatawa;
	use App\Livewire\RecivedFatawa;
	use App\Livewire\SentChecking;
    use App\Livewire\ManageRecords;
    use App\Livewire\TalaqRemainingView;
    use App\Livewire\TalaqCheckedView;



	use App\Http\Middlewate\XSS;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
// Route::group(['prefix' => 'checkf'], function () {
//     Route::get('create', [CheckFatawaController::class, 'create'])->name('checkf.create');
//     Route::post('store', [CheckFatawaController::class, 'store'])->name('checkf.store');
//     Route::get('inde', [CheckFatawaController::class, 'inde'])->name('checkf.inde');
// });

Route::get('/', function () {

	// $user = User::with('roles')->first();
	// $user->roles()->detach(1);
	// $user->roles()->attach([1,2]);
	// $user = User::with('roles')->first();
	// dd($uesr->toArrary());

    return view('welcome');
});

Route::post('sign-out', [SessionsController::class, 'destroy'])
    ->withoutMiddleware(['auth', 'roles'])
    ->name('custom-logout');



Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
    'check.performance', // Add performance check to all authenticated routes
])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');


});

Route::group(['middleware'=>['XSS']],function(){

// Route::get('/dash-board', DashBoard::class);


// Route::get('/check', [CmailController::class, 'index'])->name('cmail.index');

// Process form submissions

// Route::get('/cform', [CmailController::class, 'store'])->name('cform.store');
// Route::get('/check', [CmailController::class, 'CheckPage']);

// Route::controller(CmailController::class)->group(function(){
// 	Route::get('/test', 'showCheckPage');
// 	Route::get('/utest', 'editcheck');
// 	Route::get('/check/{id}', 'deletecheck')->name('delete.check');
// 	Route::get('/check', 'CheckPage')->name('check.page');
// 	Route::get('/ccheck', 'CheckPage')->name('ccheck.page');
// 	// Route::post('/check', 'store')->name('cmail.store');
// 	// Route::post('/cmail', 'store')->name('cmail.store');
// 	// Route::post('/ucform/{id}', 'UcMail');
// 	Route::get('/ucform/{id}', 'UcMail')->name('ucmail');
// 	Route::post('/ucform/{id}', 'ScMail')->name('scmail');
// 	Route::post('/update-selected/{id}', 'updateSelected')->name('update.selected');
// 	Route::get('/get-mujeebs/{selectedDarul}', 'getMujeebs')->name('get.mujeebs');
// 	Route::get('/cform', 'create')->name('cform');
// 	Route::post('/cmail/store', 'store')->name('cmail.store');
// 	Route::get('/cmail','index');


// 	// Define a route to handle the AJAX request to get Mujeeb options


// 	// Route::get('/tests', 'create');
// 	}
// );

// Route::get('/check', [CmailController::class, 'showCheckPage'])->name('check');
// Route::post('/check', [CmailController::class, 'store'])->name('cmail.store');
Route::get('/tests',  [CmailController::class, 'create']);
Route::get('/', function () {return redirect('sign-in');})->middleware('guest');

Route::get('sign-up', [RegisterController::class, 'create'])->middleware('guest')->name('custom-register');
Route::post('sign-up', [RegisterController::class, 'store'])->middleware('guest');
Route::get('sign-in', [SessionsController::class, 'create'])->middleware(['guest', 'throttle:30,1'])->name('login');
Route::post('sign-in', [SessionsController::class, 'store'])->middleware(['guest', 'throttle:30,1']);
Route::post('verify', [SessionsController::class, 'show'])->middleware('guest');
Route::post('reset-password', [SessionsController::class, 'update'])->middleware('guest')->name('password.update');
Route::get('verify', function () {
	return view('sessions.password.verify');
})->middleware('guest')->name('verify');
Route::get('/reset-password/{token}', function ($token) {
	return view('sessions.password.reset', ['token' => $token]);
})->middleware('guest')->name('password.reset');

Route::get('profile', [ProfileController::class, 'create'])->middleware('auth')->name('profile');
Route::post('user-profile', [ProfileController::class, 'update'])->middleware('auth');
Route::group(['middleware' => ['auth', 'check.performance']], function () {





// Define a route to apply filters
// Route::get('/apply-filters', [YearMonthController::class, 'applyFilters'])->name('yearMonth.applyFilters');




// -----------

// Route::get('/direct-check', function () {
//     return view('pages.check');
// })->name('direct.check');
// Route to display the check page using a controller method



// Route to handle form submissions and process data
// Route::get('sending3', function () {
// 	return view('files.sending3');
// })->name('aform');
Route::group(['middleware'=>['auth','roles:Admin,Noorulirfan,Faizan-e-Ajmair,Markaz-ul-Iqtisaad,Gulzar-e-Taiba,mujeeb,Superior','check.performance']],function(){

	// Task Management Routes (Superior can access limited functionality)
	Route::get('/my-tasks', [\App\Http\Controllers\WorkflowTaskController::class, 'myTasks'])->name('my-tasks');

	// Daily Performance Routes (All authenticated users) - No performance check needed
	Route::get('/daily-performance/create', [\App\Http\Controllers\DailyPerformanceController::class, 'create'])->name('daily-performance.create');
	Route::post('/daily-performance', [\App\Http\Controllers\DailyPerformanceController::class, 'store'])->name('daily-performance.store');
	Route::get('/my-performance', [\App\Http\Controllers\DailyPerformanceController::class, 'myReports'])->name('my-performance');
	Route::get('/check-todays-submission', [\App\Http\Controllers\DailyPerformanceController::class, 'checkTodaysSubmission'])->name('check-todays-submission');

	// Mahl-e-Nazar Limit Check Routes (All authenticated users) - No performance check needed for status checks
	Route::get('/check-submission-eligibility', [\App\Http\Controllers\MahlENazarLimitController::class, 'checkSubmissionEligibility'])->name('check-submission-eligibility');
	Route::get('/mahl-e-nazar-widget', [\App\Http\Controllers\MahlENazarLimitController::class, 'getDashboardWidget'])->name('mahl-e-nazar-widget');

	// Team Information Routes (All authenticated users)
	Route::get('/users/{supervisor}/team', [\App\Http\Controllers\SupervisorAssistantController::class, 'getSupervisorTeam'])->name('users.team');
	Route::get('/users/{assistant}/supervisor', [\App\Http\Controllers\SupervisorAssistantController::class, 'getAssistantSupervisor'])->name('users.supervisor');

    Route::post('/download-word', [DownloadController::class, 'downloadWord'])->name('download-word');

    Route::get('/mahlenazar-fatawa/{darulifta?}/{fatwa_no?}', \App\Livewire\MahlenazarFatawa::class)->name('mahlenazar-fatawa');
    Route::get('/ok-fatawa/{darulifta?}/{mujeebn?}', OkFatawa::class)->name('ok-fatawa');
	// Route::get('/chat-box', ChatBox::class)->name('chat-box');
	Route::get('/remaining-fatawa/{darulifta?}/{mailfolder?}', function($darulifta = null, $mailfolder = null) {
	    return view('pages.remaining-fatawa', compact('darulifta', 'mailfolder'));
	})->name('remaining-fatawa');
	Route::get('/sending-fatawa/{darulifta?}/{mailfolder?}', SendingFatawa::class)->name('sending-fatawa')->middleware('check.mahl.limit');
	Route::get('/sent-fatawa/{darulifta?}/{mailfolder?}', function($darulifta = null, $mailfolder = null) {
	    return view('pages.sent-fatawa', compact('darulifta', 'mailfolder'));
	})->name('sent-fatawa');
    Route::get('/select-viral/{darulifta?}', SelectViral::class)->name('select-viral');
    Route::get('/selected-viral/{darulifta?}', SelectedViral::class)->name('selected-viral');
	Route::get('/viral-fatawa/{darulifta?}/{mailfolder?}', ViralFatawa::class)->name('viral-fatawa');
    Route::get('/talaq-remaining/{darulifta?}', TalaqRemaining::class)->name('talaq-remaining');
    Route::get('/talaq-checked/{darulifta?}', TalaqChecked::class)->name('talaq-checked');
	Route::get('/fatawadata', [SentFatawa::class, 'downloadFatawadataPDF'])->name('fatawa-data-pdf');
	Route::get('/fatwa-detail/{fatwa?}', FatwaDetail::class)->name('fatwa-detail');
	Route::get('/viral-fatwa-detail/{fatwa?}', ViralFatwaDetail::class)->name('viral-fatwa-detail');
	Route::get('/reciption-fatawa/{darulifta?}/{mailfolder?}', function($darulifta = null, $mailfolder = null) {
	    return view('pages.reciption-fatawa', compact('darulifta', 'mailfolder'));
	})->name('reciption-fatawa');
	Route::get('/recived-fatawa/{darulifta?}/{mailfolder?}', function($darulifta = null, $mailfolder = null) {
	    return view('pages.recived-fatawa', compact('darulifta', 'mailfolder'));
	})->name('recived-fatawa');
	Route::get('/sent-for-checking/{darulifta?}/{mailfolder?}', SentChecking::class)->name('sent-for-checking')->middleware('check.mahl.limit');
	Route::get('/dashboard', [DashboardController::class, 'index'])->middleware('auth')->name('dashboard');
Route::get('/dashboard/{role?}/{fatwa_no?}', [DashboardController::class, 'index'])->middleware('auth')->name('dashboard');
Route::get('create/{checker?}/{transfer_by?}', function ($checker = null ,$trasfer_by = null) {
    return view('files.create');
})->name('create')->middleware('check.mahl.limit');
	Route::controller(CmailController::class)->group(function(){
		Route::get('/test', 'showCheckPage');
		Route::get('/utest', 'editcheck');
		Route::get('/check/{id}', 'deletecheck')->name('delete.check');
		Route::get('/check', 'CheckPage')->name('check.page');
		Route::get('/ccheck', 'CheckPage')->name('ccheck.page');
		// Route::post('/check', 'store')->name('cmail.store');
		// Route::post('/cmail', 'store')->name('cmail.store');
		// Route::post('/ucform/{id}', 'UcMail');
		Route::get('/ucform/{id}', 'UcMail')->name('ucmail');
		Route::post('/ucform/{id}', 'ScMail')->name('scmail');
		Route::post('/update-selected/{id}', 'updateSelected')->name('update.selected');
		Route::get('/get-mujeebs/{selectedDarul}', 'getMujeebs')->name('get.mujeebs');
		Route::get('/cform', 'create')->name('cform')->middleware('check.mahl.limit');
		Route::post('/cmail/store', 'store')->name('cmail.store');
		Route::get('/cmail','index');
        Route::get('/manage-records', ManageRecords::class)->name('manage.records');

        Route::get('/talaq-fatawa', [TalaqFatawaController::class, 'index']);
        Route::post('/talaq-fatawa/save-header', [TalaqFatawaController::class, 'saveHeader']);
        Route::post('/talaq-fatawa/save-parent', [TalaqFatawaController::class, 'saveParent']);
        Route::post('/talaq-fatawa/save-child', [TalaqFatawaController::class, 'saveChild']);
        Route::delete('/talaq-fatawa/delete-header/{id}', [TalaqFatawaController::class, 'deleteHeader']);
        Route::delete('/talaq-fatawa/delete-parent/{id}', [TalaqFatawaController::class, 'deleteParent']);
        Route::delete('/talaq-fatawa/delete-child/{id}', [TalaqFatawaController::class, 'deleteChild']);
        // Route::post('/talaq-fatawa/update-header-order', [TalaqFatawaController::class, 'updateHeaderOrder']);
        Route::post('/talaq-fatawa/update-serial', [TalaqFatawaController::class, 'updateSerialNumber']);
        Route::post('/talaq-fatawa/update-parent-serial', [TalaqFatawaController::class, 'updateParentSerial']);
        Route::post('/talaq-fatawa/update-child-serial', [TalaqFatawaController::class, 'updateChildSerial']);
        Route::get('/editor', [TalaqSidebarController::class, 'index'])->name('editor');
        Route::get('/fetch-data', [TalaqSidebarController::class, 'fetchData'])->name('fetch.data');
        Route::get('/talaq-fatawa-manage', [TalaqSidebarController::class, 'index']);
        Route::get('/questions/{id}', [TalaqSidebarController::class, 'fetchQuestionData'])->name('questions.fetch');
        Route::post('/save-fatawa', [TalaqSidebarController::class, 'saveFatawa'])->name('saveFatawa');
        Route::get('/talaq-remaining-view/{id?}', TalaqRemainingView::class)->name('talaq-remaining-view');
        Route::get('/talaq-checked-view/{id?}', TalaqCheckedView::class)->name('talaq-checked-view');
        Route::get('/talaq-fatwa-edit/{id}', [TalaqFatawaController::class, 'view'])->name('talaq-fatwa-edit');
        Route::post('/mark-as-checked/{id}', [TalaqFatawaController::class, 'markAsChecked'])->name('mark-as-checked');




        // Route::post('/talaq-fatawa/update-serial', [TalaqFatawaController::class, 'updateSerial']);



		// Define a route to handle the AJAX request to get Mujeeb options


		// Route::get('/tests', 'create');
		}
	);

	Route::post('/transfer-mufti/{id}', [MutakhassisController::class, 'transferMufti']);
	Route::post('/transfer-mujeeb/{id}', [MutakhassisController::class, 'transferMujeeb']);
	Route::get('/mutakhassis', [MutakhassisController::class, 'showData'])->name('mutakhassis');
	Route::get('/transfer_by_mufti', [MutakhassisController::class, 'byMufti'])->name('transfer_by_mufti');
	Route::get('/mahlenazar', [MahlenazarController::class, 'index'])->name('mahlenazar');
	Route::get('/mahlenazar/{role?}/{fatwa_no?}', [MahlenazarController::class, 'index'])->name('mahlenazar');
	Route::get('/mahlenazarfatawa/{role?}/{fatwa_no?}', [MahlenazarFatawaController::class, 'index'])->name('mahlenazarfatawa');
	Route::get('/sidebar', [MahlenazarController::class, 'mnSidebare'])->name('sidebar');
	// Route::get('/dashboard', [MahlenazarController::class, 'mnSidebaredash']);
	Route::get('/question', [ReciptqueController::class, 'index'])->name('question.index');
	Route::get('/m_question', [ReciptqueController::class, 'mujeebindex'])->name('m_question.index');
    Route::get('/mujeeb_assign_page', [ReciptqueController::class, 'mujeebassign'])->name('mujeeb_assign_page');
    Route::get('/questions/{id}', [ReciptqueController::class, 'getQuestion'])->name('questions.get');


	// Route::get('/create', [ReciptqueController::class, 'selectFatwa'])->name('selectFatwa.index');
	Route::get('/rform', [ReciptqueController::class, 'create'])->name('reciptque.index')->middleware('check.mahl.limit');
    Route::get('/rform-mujeeb', [ReciptqueController::class, 'recMujeeb'])->name('rform-mujeeb')->middleware('check.mahl.limit');
	Route::get('/get-sub-issues', [ReciptqueController::class, 'getSubIssues'])->name('get-sub-issues');
	Route::any('/reciptque/store', [ReciptqueController::class, 'store'])->name('reciptque.store');
	Route::get('/reciptque/edit/{id}', [ReciptqueController::class, 'edit'])->name('reciptque.edit');
	Route::put('/reciptque/update/{id}', [ReciptqueController::class, 'update'])->name('reciptque.update');
	Route::delete('/reciptque/destroy/{id}', [ReciptqueController::class, 'destroy'])->name('reciptque.destroy');
	Route::get('/issues',  [ReciptqueController::class, 'listIssues'])->name('list.issues');
	Route::get('/editissues',  [ReciptqueController::class, 'editIssues'])->name('edit.issues');
	Route::get('/checkPhoneExist', [ReciptqueController::class, 'checkPhoneExist'])->name('checkPhoneExist');
	Route::get('/checkEmailExist', [ReciptqueController::class, 'checkEmailExist'])->name('checkEmailExist');
	Route::get('/slip', [ReciptqueController::class, 'store'])->name('slip');
	Route::post('/update-assign-id/{id}', [ReciptqueController::class, 'updateAssignId']);
	Route::get('/getQuestionData', [ReciptqueController::class, 'getQuestionData']);
	Route::get('/not_deliver', [DeliverController::class, 'index'])->name('not_deliver');
Route::get('/deliver', [DeliverController::class, 'indexDel'])->name('deliver');
Route::get('/delivers/create', [DeliverController::class, 'create']);
Route::post('/delivers', [DeliverController::class, 'store']);
Route::post('/update-deliver/{id}',[DeliverController::class, 'updateDeliver'])->name('update.deliver');
Route::post('/update-deliver-date/{id}', [DeliverController::class, 'updateDeliverDate']);
Route::post('/update-not-deliver-reason/{id}', [DeliverController::class, 'updateNotDeliverReason']);

Route::get('/storagelist', [FileController::class,'storFile'])->name('storagelist');
Route::get('/rename-folders', [FileController::class, 'renameFolders']);
Route::get('/files/create', [FileController::class,'create'])->name('files.create')->middleware('check.mahl.limit');
Route::post('/files',  [FileController::class,'store'])->name('files.store')->middleware('check.mahl.limit');
Route::post('/upload', [FileController::class,'upload'])->name('upload')->middleware('check.mahl.limit');
Route::post('/upload-update', [FileController::class, 'uploadUpdate'])->name('uploadUpdate')->middleware('check.mahl.limit');
Route::post('/update-fatwa', [FileController::class, 'updateFatwa'])->name('updateFatwa')->middleware('check.mahl.limit');
Route::get('/sidebar', [SidebarController::class, 'index'])->name('sidebar.index');

Route::post('/update', [FileController::class,'update'])->name('update');
Route::delete('/file/delete', [FileController::class, 'deleteFile'])->name('file.delete');

Route::delete('/Checkfolder/delete', [FileController::class, 'deleteCheckFolder'])->name('checkfolder.delete');
Route::delete('/Checkfile/delete', [FileController::class, 'deleteCheckFile'])->name('checkfile.delete');
Route::delete('/folder/delete', [FileController::class, 'deleteFolder'])->name('folder.delete');
Route::get('/year-month',[YearMonthController::Class,'index'] )->name('recived');
Route::get('/noorulirfan/{checker?}',[YearMonthController::Class,'indexNor'] )->name('recivedNor');
Route::get('/checkednor',[YearMonthController::Class,'checkedNor'] )->name('checkedNor');
Route::get('/checkedfaj',[YearMonthController::Class,'checkedFaj'] )->name('checkedFaj');
Route::get('/checkedgul',[YearMonthController::Class,'checkedGul'] )->name('checkedGul');
Route::get('/checkedIec',[YearMonthController::Class,'checkedIec'] )->name('checkedIec');
Route::get('/faizaneajmair',[YearMonthController::Class,'indexFaj'] )->name('recivedFaj');
Route::get('/gulzahretaiba',[YearMonthController::Class,'indexGul'] )->name('recivedGul');
Route::get('/iqtisad',[YearMonthController::Class,'indexIec'] )->name('recivedIec');

// Route::get('summary', function () {
// 	return view('files.summary');
// })->name('summary');
// Route::get('/summary',[YearMonthController::Class,'displaySelectedData'] )->name('selected-data');
// Route::get('/sending',[YearMonthController::Class,'indexSe'] )->name('yearMonth.index');
Route::get('/year-month/download/{date}/{filename}', [YearMonthController::Class,'download'])->name('download');
Route::get('/year-month/downloadCheck/{date}/{folder}/{filename}', [YearMonthController::Class,'downloadCheck'])->name('downloadCheck');
Route::get('/year-month/viewCheck/{date}/{folder}/{filename}', [YearMonthController::class, 'viewCheck'])
    ->name('viewCheck');
Route::get('/year-month/viewRemain/{date}/{filename}', [YearMonthController::class, 'viewRemain'])
    ->name('viewRemain');
Route::get('/year-month/download-all/{date}', [YearMonthController::class, 'downloadAll'])->name('downloadAll');
Route::get('/download-folder/{mailfolderDates}/{daruliftaName}', [YearMonthController::class, 'downloadFolder'])->name('downloadFolder');
Route::get('/download-file/{mailfolderDates}/{daruliftaName}/{checker?}', [YearMonthController::class, 'downloadFile'])->name('downloadFile');
Route::delete('/delete-folder/{mailfolderDates}/{daruliftaName}', [YearMonthController::class, 'deleteFolder'])->name('deleteFolder');
Route::delete('/delete-checked-folder/{mailfolderDates}/{daruliftaName}/{checker?}', [YearMonthController::class, 'deleteCheckedFolder'])->name('deleteCheckedFolder');
Route::delete('/delete-file/{mailfolderDates}/{daruliftaName}/{checker?}/{transferby?}', [YearMonthController::class, 'deleteFile'])->name('deleteFile');
Route::delete('/delete-checked-file/{mailfolderDates}/{daruliftaName}/{checker?}', [YearMonthController::class, 'deleteCheckedFile'])->name('deleteCheckedFile');
Route::get('/year-month/download-file/{date}/{filename}/{id}', [YearMonthController::Class,'downloadFile'])->name('downloadFile');
// Route::get('/shoba-viral', function () {
//     return view('path.to.main-view');
// })->name('shoba-viral');


// Route::get('/year-month', [YearMonthController::class, 'index'])->name('yearMonth.index');
// Route::get('/get-dates-by-darulifta-name', [YearMonthController::class, 'getDatesByDaruliftaName'])->name('get-dates-by-darulifta-name');




// Route::get('/sending', [DataController::class,'index'])->name('sending');
Route::get('/fetch-data', [DataController::class,'fetchData'])->name('fetch-data');
Route::get('/fetch-mail-folder-dates', [DataController::class, 'fetchMailFolderDates'])->name('fetch-mail-folder-dates');
Route::get('/get-mail-folder-dates/{selectedDarul}', [DataController::class, 'getMailFolderDates'])->name('get.mail-folder-dates');
Route::get('/get-unique-values', [DataController::class, 'getUniqueValues'])->name('get.unique-values');
// Route::get('/sending1', [DataController::class, 'create']);
// Route::get('/get-data', [DataController::class, 'getData1']);
Route::get('/get-folder/{selectedDarul}', [DataController::class, 'getfolder'])->name('get.folder');


	Route::get('/slip1', function () {
		return view('reciptque.slip1');
	})->name('slip1');


});
Route::group(['middleware'=>['auth','roles:Admin']],function(){
	// Test route for debugging department teams
	Route::get('/test-department-teams', function () {
		$service = app(\App\Services\DepartmentTeamManagementService::class);
		$departments = \App\Models\Department::all();
		$result = [];

		foreach ($departments as $dept) {
			$result[$dept->name] = $service->getDepartmentTeamStructure($dept);
		}

		return response()->json($result);
	});

	Route::resource('users',UserController::class);

	// Department Management Routes
	Route::resource('departments', \App\Http\Controllers\DepartmentController::class);
	Route::get('/departments/{department}/assign-users', [\App\Http\Controllers\DepartmentController::class, 'assignUsers'])->name('departments.assign-users');
	Route::post('/departments/{department}/assign-users', [\App\Http\Controllers\DepartmentController::class, 'storeUserAssignments'])->name('departments.store-assignments');
	Route::delete('/departments/{department}/users/{user}', [\App\Http\Controllers\DepartmentController::class, 'removeUser'])->name('departments.remove-user');
	Route::get('/departments-statistics', [\App\Http\Controllers\DepartmentController::class, 'statistics'])->name('departments.statistics');

	// Role Management Routes
	Route::get('/role-management', function() {
		return view('admin.role-management');
	})->name('role-management');

	// Task Management Routes (Admin can access all)
	Route::resource('workflow-tasks', \App\Http\Controllers\WorkflowTaskController::class);
	Route::post('/workflow-tasks/{task}/complete', [\App\Http\Controllers\WorkflowTaskController::class, 'complete'])->name('workflow-tasks.complete');
	Route::patch('/workflow-tasks/{task}/status', [\App\Http\Controllers\WorkflowTaskController::class, 'changeStatus'])->name('workflow-tasks.change-status');
	Route::get('/workflow-tasks-statistics', [\App\Http\Controllers\WorkflowTaskController::class, 'statistics'])->name('workflow-tasks.statistics');
	Route::get('/users/{user}/tasks', [\App\Http\Controllers\WorkflowTaskController::class, 'userTasks'])->name('users.tasks');

	// Performance Management Routes (Admin access)
	Route::get('/performance-management', [\App\Http\Controllers\DailyPerformanceController::class, 'index'])->name('performance-management');
	Route::get('/daily-performance/{dailyPerformance}', [\App\Http\Controllers\DailyPerformanceController::class, 'show'])->name('daily-performance.show');
	Route::get('/daily-performance/{dailyPerformance}/edit', [\App\Http\Controllers\DailyPerformanceController::class, 'edit'])->name('daily-performance.edit');
	Route::put('/daily-performance/{dailyPerformance}', [\App\Http\Controllers\DailyPerformanceController::class, 'update'])->name('daily-performance.update');
	Route::get('/performance-statistics', [\App\Http\Controllers\DailyPerformanceController::class, 'statistics'])->name('performance-statistics');
	Route::get('/users/{user}/performance', [\App\Http\Controllers\DailyPerformanceController::class, 'userHistory'])->name('users.performance');
	Route::post('/send-performance-reminders', [\App\Http\Controllers\DailyPerformanceController::class, 'sendReminders'])->name('send-performance-reminders');

	// Superior Performance Review Routes (Superior and Admin access)
	Route::get('/performance-review', \App\Livewire\SuperiorPerformanceReview::class)->name('performance-review');

	// Mahl-e-Nazar Limit Management Routes (Admin access)
	Route::get('/mahl-e-nazar-limits', [\App\Http\Controllers\MahlENazarLimitController::class, 'index'])->name('mahl-e-nazar-limits');
	Route::get('/mahl-e-nazar-statistics', [\App\Http\Controllers\MahlENazarLimitController::class, 'statistics'])->name('mahl-e-nazar-statistics');
	Route::post('/update-mahl-e-nazar-restrictions', [\App\Http\Controllers\MahlENazarLimitController::class, 'updateRestrictions'])->name('update-mahl-e-nazar-restrictions');
	Route::post('/users/{user}/toggle-mahl-e-nazar-restriction', [\App\Http\Controllers\MahlENazarLimitController::class, 'toggleUserRestriction'])->name('users.toggle-mahl-e-nazar-restriction');

	// Performance Holiday Management Routes (Admin/Nazim access)
	Route::get('/performance-holidays', function() {
		return view('pages.performance-holidays');
	})->name('performance-holidays');

	// Locked Account Management Routes (Admin/Nazim access)
	Route::get('/locked-accounts', function() {
		return view('pages.locked-accounts');
	})->name('locked-accounts');

	// Supervisor-Assistant Mapping Routes (Admin access)
	Route::get('/supervisor-assistant-mapping', [\App\Http\Controllers\SupervisorAssistantController::class, 'index'])->name('supervisor-assistant-mapping');
	Route::get('/supervisor-assistant-mappings', [\App\Http\Controllers\SupervisorAssistantController::class, 'getMappings'])->name('supervisor-assistant-mappings');
	Route::post('/users/{supervisor}/assign-assistants', [\App\Http\Controllers\SupervisorAssistantController::class, 'assignAssistants'])->name('users.assign-assistants');
	Route::post('/create-superior', [\App\Http\Controllers\SupervisorAssistantController::class, 'createSuperior'])->name('create-superior');
	Route::delete('/users/{supervisor}/remove-superior', [\App\Http\Controllers\SupervisorAssistantController::class, 'removeSuperior'])->name('users.remove-superior');
	Route::delete('/users/{supervisor}/assistants/{assistant}', [\App\Http\Controllers\SupervisorAssistantController::class, 'removeAssistant'])->name('users.remove-assistant');
	Route::get('/available-assistants', [\App\Http\Controllers\SupervisorAssistantController::class, 'getAvailableAssistants'])->name('available-assistants');
	Route::get('/eligible-superiors', [\App\Http\Controllers\SupervisorAssistantController::class, 'getEligibleSuperiors'])->name('eligible-superiors');
	Route::get('/supervisor-assistant-statistics', [\App\Http\Controllers\SupervisorAssistantController::class, 'getStatistics'])->name('supervisor-assistant-statistics');
	Route::get('/export-supervisor-mappings', [\App\Http\Controllers\SupervisorAssistantController::class, 'exportMappings'])->name('export-supervisor-mappings');

	// Route::get('/summary',[YearMonthController::Class,'generateSummaryReport'] )->name('summary');
	Route::get('/sending', [DataController::class, 'getData'])->name('sending')->middleware('check.mahl.limit');
	Route::get('/sending1/{selectedDarul?}/{selectedFolder?}/{transfer_by?}', [DataController::class, 'storeSelectedValues'])->name('store.selected.values')->middleware('check.mahl.limit');
	Route::get('/sending2', [DataController::class, 'createSending2'])->name('sending2');
	Route::get('/udarulifta/{id}', [DaruliftaController::class, 'UpDarul'])->name('update.darul');
	Route::put('/update-darulifta/{id}', [DaruliftaController::class, 'UpdateDarul'])->name('update.darul.process');
	Route::delete('/darulifta/{id}', [DaruliftaController::class, 'deleteDarul'])->name('darulifta.delete');
    Route::get('/talaq-fatwa-edit/{id}', [TalaqFatawaController::class, 'view'])->name('talaq-fatwa-edit');
    Route::post('/mark-as-checked/{id}', [TalaqFatawaController::class, 'markAsChecked'])->name('mark-as-checked');

// Route::get('/darulifta', [DaruliftaController::class, 'index'])->name('darulita.page');
// Route for displaying the form
Route::get('/mujeeb', [MujeebController::class, 'index'])->name('mujeeb');
Route::post('/mujeeb', [MujeebController::class, 'store'])->name('mujeeb.store');
Route::get('/edit_mujeeb/{id}', [MujeebController::class, 'edit'])->name('mujeeb.edit');
Route::put('/edit_mujeeb/{id}', [MujeebController::class, 'update'])->name('mujeeb.update');
// Route::put('/mujeeb/update/{id}', [MujeebController::class, 'update'])->name('mujeeb.update');
Route::delete('/mujeeb/delete/{id}', [MujeebController::class, 'deleteMujeeb'])->name('mujeeb.delete');
Route::get('/checker', [CheckerController::class, 'index'])->name('checker');
Route::post('/checker', [CheckerController::class, 'store'])->name('checker.store');
Route::get('/edit_checker/{id}', [CheckerController::class, 'edit'])->name('checker.edit');
Route::put('/edit_checker/{id}', [CheckerController::class, 'update'])->name('checker.update');
// Route::put('/mujeeb/update/{id}', [MujeebController::class, 'update'])->name('mujeeb.update');
Route::delete('/checker/delete/{id}', [CheckerController::class, 'deleteChecker'])->name('checker.delete');
Route::any('/darulifta', [DaruliftaController::class, 'AddDarul'])->name('darulifta.add');
Route::get('/darulifta', [DaruliftaController::class, 'ShowDarulifta'])->name('darulifta');



});

// Test route to demonstrate performance lock system
Route::get('/test-performance-system', function() {
	return view('test-performance-system');
})->name('test-performance-system');

Route::group(['middleware'=>['auth','roles:Manager']],function(){
	Route::get('billing', function () {
		return view('pages.billing');
	})->name('billing');
	Route::get('tables', function () {
		return view('pages.tables');
	})->name('tables');
	// Route::get('cform', function () {
	// 	return view('entryform.cform');
	// })->name('cform');
	// Route::get('mujeeb', function () {
	// 	return view('pages.mujeeb');
	// })->name('mujeeb');
	// Route::get('appointment', function () {
	// 	return view('pages.appointment');
	// })->name('appointment');
	// Route::get('darulifta', function () {
	// 	return view('pages.darulifta');
	// })->name('darulifta');
	Route::get('aform', function () {
		return view('entryform.aform');
	})->name('aform');
	Route::get('sform', function () {
		return view('entryform.sform');
	})->name('sform');
	Route::get('tform', function () {
		return view('entryform.tform');
	})->name('tform');
	// 	Route::get('create', function () {
	// 	return view('files.create');
	// })->name('create');
	Route::get('inde', function () {
		return view('checkf.inde');
	})->name('inde');
	Route::get('recfatwa', function () {
		return view('pages.recfatwa');
	})->name('recfatwa');
	// Route::get('schedule', function () {
	// 	return view('pages.schedule');
	// })->name('schedule');
	Route::get('task', function () {
		return view('pages.task');
	})->name('task');
	Route::get('notice', function () {
		return view('pages.notice');
	})->name('notice');
	// Route::get('check', function () {
	// 	return view('pages.check');
	// })->name('check');
	Route::get('otheri', function () {
		return view('pages.otheri');
	})->name('otheri');
	Route::get('rtl', function () {
		return view('pages.rtl');
	})->name('rtl');
	Route::get('virtual-reality', function () {
		return view('pages.virtual-reality');
	})->name('virtual-reality');
	Route::get('notifications', function () {
		return view('pages.notifications');
	})->name('notifications');
	Route::get('static-sign-in', function () {
		return view('pages.static-sign-in');
	})->name('static-sign-in');
	Route::get('static-sign-up', function () {
		return view('pages.static-sign-up');
	})->name('static-sign-up');
	Route::get('user-management', function () {
		return view('pages.laravel-examples.user-management');
	})->name('user-management');
	Route::get('user-profile', function () {
		return view('pages.laravel-examples.user-profile');
	})->name('user-profile');
	// Route::get('/task', [TaskController::class, 'index'])->name('task');
	Route::get('/task', [TaskController::class, 'create'])->name('tasks.create');
	Route::post('/task', [TaskController::class, 'store'])->name('tasks.store');
	Route::get('/task/{id}/edit', [TaskController::class, 'edit'])->name('tasks.edit');
	Route::delete('/task/{id}', [TaskController::class, 'destroy'])->name('tasks.destroy');




Route::controller(ScheduleController::class)->group(function(){
	Route::post('/schedule','ApptStore')->name('schedules.store');
	Route::get('/schedule', 'index')->name('schedule');
	Route::get('/schedule/{id}/edit', 'edit')->name('schedules.edit');
	Route::put('/schedule/{id}', 'update')->name('schedules.update');
	Route::DELETE('/schedules/{id}', 'destroy')->name('schedules.destroy');

}
);

Route::controller(AppointmentController::class)->group(function(){
	Route::post('/appointments','ApptStore')->name('appointments.store');
	Route::get('/appointment', 'index')->name('appointment');
	Route::get('/appointment/{id}/edit', 'edit')->name('appointments.edit');
	Route::put('/appointment/{id}', 'update')->name('appointments.update');
	Route::DELETE('/appointment/{id}', 'destroy')->name('appointments.destroy');

}
);



});
Route::group(['middleware'=>['auth','roles:Mahlenazar,Admin,Noorulirfan,Faizan-e-Ajmair,Markaz-ul-Iqtisaad,Gulzar-e-Taiba','check.performance']],function(){
	Route::get('/mahlenazar', [MahlenazarController::class, 'index'])->name('mahlenazar');
	Route::get('/mahlenazar/{role?}/{fatwa_no?}', [MahlenazarController::class, 'index'])->name('mahlenazar');
	Route::get('/mahlenazarfatawa/{role?}/{fatwa_no?}', [MahlenazarFatawaController::class, 'index'])->name('mahlenazarfatawa');
	Route::get('/year-month/downloadCheck/{date}/{folder}/{filename}', [YearMonthController::Class,'downloadCheck'])->name('downloadCheck');
Route::get('/year-month/viewCheck/{date}/{folder}/{filename}', [YearMonthController::class, 'viewCheck'])
    ->name('viewCheck');
	Route::get('/viewPdf', [YearMonthController::class, 'viewPdf'])->name('viewPdf');
});
Route::group(['middleware'=>['auth','roles:Nazim_Viral,Admin,Noorulirfan,Faizan-e-Ajmair,Markaz-ul-Iqtisaad,Gulzar-e-Taiba','check.performance']],function(){

    Route::get('/viral-fatawa/{darulifta?}/{mailfolder?}', ViralFatawa::class)->name('viral-fatawa');
	Route::get('/year-month/downloadCheck/{date}/{folder}/{filename}', [YearMonthController::Class,'downloadCheck'])->name('downloadCheck');
	Route::get('/year-month/viewRemain/{date}/{filename}', [YearMonthController::class, 'viewRemain'])
    ->name('viewRemain');
	Route::get('/year-month/viewCheck/{date}/{folder}/{filename}', [YearMonthController::class, 'viewCheck'])
    ->name('viewCheck');
    Route::get('/select-viral/{darulifta?}', SelectViral::class)->name('select-viral');
    Route::get('/selected-viral/{darulifta?}', SelectedViral::class)->name('selected-viral');


});
Route::group(['middleware'=>['auth','roles:Shoba_Viral,Shoba_Viral_Iec,Admin,Nazim_Viral']],function(){
    Route::get('/shoba-viral/{filter?}', ShobaViral::class)->name('shoba-viral');


});
Route::group(['middleware'=>['auth','roles:Admin,Noorulirfan,Faizan-e-Ajmair,Markaz-ul-Iqtisaad,Gulzar-e-Taiba,mujeeb,Nazim_Viral','check.performance']],function(){
    // Talaq related routes
    Route::get('/talaq-remaining/{darulifta?}', TalaqRemaining::class)->name('talaq-remaining');
    Route::get('/talaq-checked/{darulifta?}', TalaqChecked::class)->name('talaq-checked');
    Route::get('/talaq-remaining-view/{id?}', TalaqRemainingView::class)->name('talaq-remaining-view');
    Route::get('/talaq-checked-view/{id?}', TalaqCheckedView::class)->name('talaq-checked-view');
	Route::get('/talaq-resend-mn/{id}', [TalaqFatawaController::class, 'resendMn'])->name('talaq-resend-mn');
    Route::get('/talaq-fatawa', [TalaqFatawaController::class, 'index']);
    Route::get('/editor', [TalaqSidebarController::class, 'index'])->name('editor');
    Route::get('/fetch-data', [TalaqSidebarController::class, 'fetchData'])->name('fetch.data');
    Route::get('/talaq-fatawa-manage', [TalaqSidebarController::class, 'index']);
    Route::get('/questions/{id}', [TalaqSidebarController::class, 'fetchQuestionData'])->name('questions.fetch');
    Route::post('/save-mn-fatawa/{id}', [TalaqSidebarController::class, 'saveMnFatawa'])->name('saveMnFatawa');

    Route::post('/save-fatawa', [TalaqSidebarController::class, 'saveFatawa'])->name('saveFatawa');

    // Mujeeb related routes
    Route::get('/m_question', [ReciptqueController::class, 'mujeebindex'])->name('m_question.index');
    Route::get('/mujeeb_assign_page', [ReciptqueController::class, 'mujeebassign'])->name('mujeeb_assign_page');
    Route::get('/rform-mujeeb', [ReciptqueController::class, 'recMujeeb'])->name('rform-mujeeb');

    // Additional talaq routes for managing fatawa
    Route::post('/talaq-fatawa/save-header', [TalaqFatawaController::class, 'saveHeader']);
    Route::post('/talaq-fatawa/save-parent', [TalaqFatawaController::class, 'saveParent']);
    Route::post('/talaq-fatawa/save-child', [TalaqFatawaController::class, 'saveChild']);
    Route::delete('/talaq-fatawa/delete-header/{id}', [TalaqFatawaController::class, 'deleteHeader']);
    Route::delete('/talaq-fatawa/delete-parent/{id}', [TalaqFatawaController::class, 'deleteParent']);
    Route::delete('/talaq-fatawa/delete-child/{id}', [TalaqFatawaController::class, 'deleteChild']);
    Route::post('/talaq-fatawa/update-serial', [TalaqFatawaController::class, 'updateSerialNumber']);
    Route::post('/talaq-fatawa/update-parent-serial', [TalaqFatawaController::class, 'updateParentSerial']);
    Route::post('/talaq-fatawa/update-child-serial', [TalaqFatawaController::class, 'updateChildSerial']);

    // Download related routes
    Route::post('/download-word', [DownloadController::class, 'downloadWord'])->name('download-word');
    Route::get('/year-month/download/{date}/{filename}', [YearMonthController::Class,'download'])->name('download');
    Route::get('/year-month/downloadCheck/{date}/{folder}/{filename}', [YearMonthController::Class,'downloadCheck'])->name('downloadCheck');
    Route::get('/year-month/viewCheck/{date}/{folder}/{filename}', [YearMonthController::class, 'viewCheck'])->name('viewCheck');
    Route::get('/year-month/viewRemain/{date}/{filename}', [YearMonthController::class, 'viewRemain'])->name('viewRemain');
    Route::get('/year-month/download-all/{date}', [YearMonthController::class, 'downloadAll'])->name('downloadAll');
    Route::get('/download-folder/{mailfolderDates}/{daruliftaName}', [YearMonthController::class, 'downloadFolder'])->name('downloadFolder');
    Route::get('/download-file/{mailfolderDates}/{daruliftaName}/{checker?}', [YearMonthController::class, 'downloadFile'])->name('downloadFile');
    Route::get('/year-month/download-file/{date}/{filename}/{id}', [YearMonthController::Class,'downloadFile'])->name('downloadFile');
    Route::get('/viewPdf', [YearMonthController::class, 'viewPdf'])->name('viewPdf');
});
});
});

