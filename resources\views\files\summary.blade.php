<x-layout bodyClass="g-sidenav-show bg-gray-200">
    <x-navbars.sidebar activePage="summary"></x-navbars.sidebar>
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <!-- Navbar -->
        <x-navbars.navs.auth titlePage="Recived Fatawa"></x-navbars.navs.auth>
        <!-- End Navbar -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

        <!-- Create Appointment Entry Form -->
        <style>
                        .year {
                background-color: #7971ec; /* Year section color */
            }

            .month {
                background-color: #96c47d; /* Month section color */
            }

            .date {
                background-color: #c8d053; /* Date section color */
            }

            .file {
                background-color: #c6d2f4; /* Date section color */
            }
            .download-button {
                display: inline-block;
                padding: 10px 20px; /* Adjust the padding as needed */
                background-color: #007bff; /* Button background color */
                color: #fff; /* Text color */
                text-decoration: none;
                border: none;
                border-radius: 5px; /* Rounded corners */
                cursor: pointer;
            }

            .download-button:hover {
                background-color: #0056b3; /* Change color on hover */
            }
            #emptbl th,
            #emptbl td {
                padding: 8px;
                border: 1px solid #ccc;
                text-align: center;
            }
            
            #emptbl {
                width: 100%;
                border-collapse: collapse;
            }
            
            /* Define alternate row colors */
            #emptbl tr:nth-child(odd) {
                background-color: #f2f2f2;
            }
            
            #emptbl tr:nth-child(even) {
                background-color: #ffffff;
            }
            
            /* Responsive adjustments */
            @media (max-width: 768px) {
                #emptbl th, #emptbl td {
                    display: block;
                    text-align: left;
                    width: 100%;
                }
                
                #emptbl th {
                    font-weight: bold;
                }
                
                #emptbl td:before {
                    content: attr(data-label);
                    float: left;
                    font-weight: normal;
                }
                
                #col5 textarea {
                    width: 100%;
                    box-sizing: border-box;
                }
            }
            @media print {
    /* Hide elements not meant for printing */
    body {
        background-color: white;
    }
    .container {
        display: none;
    }
    .card {
        margin: 0; /* Remove card margin */
    }
    /* Add any other styles for printing as needed */
}
    /* Add any other styles for printing as needed */

        </style>
        <a href="{{ route('summary') }}" class="btn btn-primary" style="background-color: rgb(115, 150, 110);">Summary</a>
        <a href="{{ route('recivedNor') }}" class="btn btn-success" style="background-color: lightgray;">Recived Fatawa</a>
        <a href="{{ route('sending') }}" class="btn btn-success" style="background-color: lightgray;">Sending Fatwaw</a>
        <a href="{{ route('checkedNor') }}" class="btn btn-success" style="background-color: lightgray;">Checked Fatawa</a>
        
        {{-- <button id="print-button">Print</button> --}}
        <div class="container">
            <div class="card mb-4"> <!-- Added "mb-4" for margin at the bottom -->
                <div class="card-body">
                    <h2 class="card-title">Recived Fataw Folder For Checking</h2>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Darulifta</th>
                                <th>Folder For Checking</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($summaryReport as $item)
                                <tr>
                                    <td>{{ $item['Darulifta'] }}</td>
                                    <td>{{ $item['Mail Recived'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">Recived Fatawa From Darulifta at {{ $latestMailFolderDate }}</h2>
        
                    @if (empty($dataByDaruliftaName))
                        <p>No data found for the selected criteria.</p>
                    @else
                        @foreach ($dataByDaruliftaName as $daruliftaName => $categoryData)
                            <h3>{{ $daruliftaName }}</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>File Code</th>
                                            <th>Sender</th>
                                            <th>FType</th>
                                            <th>Category</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($categoryData as $data)
                                            <tr>
                                                <td>{{ $data->file_code }}</td>
                                                <td>{{ $data->sender }}</td>
                                                <td>{{ $data->ftype }}</td>
                                                <td>{{ $data->category }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    
        {{-- <table class="table table-bordered" id="yearMonthTable">
            <tbody>
                @foreach ($data as $year => $months)
                <tr class="year">
                    <td>
                        <a href="#" class="toggle-row" data-target=".year-{{ $year }}">
                            <span class="toggle-arrow">▼</span> {{ $year }}
                        </a>
                    </td>
                </tr>
                @foreach ($months as $month => $dates)
                <tr class="month year-{{ $year }}">
                    <td>
                        <a href="#" class="toggle-row" data-target=".month-{{ $year }}-{{ $month }}">
                            <span class="toggle-arrow">▼</span> {{ $month }}
                        </a>
                    </td>
                </tr>
                @foreach ($dates as $date => $files)
                <tr class="date year-{{ $year }} month-{{ $year }}-{{ $month }}">
                    <td>
                        <a href="#" class="toggle-row" data-target=".date-{{ $year }}-{{ $month }}-{{ $date }}">
                            <span class="toggle-arrow">▼</span> {{ $date }}
                        </a>
                        <a href="{{ route('downloadAll', ['date' => $date]) }}" class="download-button">
                            <span class="button-text">Download Mail Folder</span>
                        </a>
                    </td>
                </tr>
                @foreach ($files as $file)
                <tr class="file date-{{ $year }}-{{ $month }}-{{ $date }}">
                    <td>
                        <a href="{{ route('download', ['date' => $date, 'filename' => $file['file_name']]) }}">
                            {{ $file['file_name'] }}
                        </a>
                        | {{ $file['file_code'] }}
                        | {{ $file['sender'] }}
                    </td>
                    
                </tr>
                @endforeach
                @endforeach
                @endforeach
                @endforeach
            </tbody>
        </table>
        <script>
            jQuery(document).ready(function () {
                // Initially hide month, date, and file sections
                $('#yearMonthTable tbody tr.month, #yearMonthTable tbody tr.date, #yearMonthTable tbody tr.file').hide();
        
                // Add click event handler for toggling rows
                $('.toggle-row').click(function () {
                    var target = $(this).data('target');
                    $(target).toggle();
                    $(this)
                        .find('.toggle-arrow')
                        .text(function (_, text) {
                            return text === '▼' ? '▲' : '▼';
                        });
                });
        
                // Show month sections initially when clicking on a year
                $('.year a.toggle-row').click();
            });
        </script> --}}
        

        {{-- <script>
            jQuery(document).ready(function () {
                // Initially hide month, date, and file sections
                $('#yearMonthTable tbody tr.month, #yearMonthTable tbody tr.date, #yearMonthTable tbody tr.file').hide();
        
                // Show year sections
                
                $('#yearMonthTable tbody tr.year').show();
        
                // Add click event handler for toggling rows
                $('.toggle-row').click(function () {
                    var target = $(this).data('target');
                    $(target).toggle();
                    $(this)
                        .find('.toggle-arrow')
                        .text(function (_, text) {
                            return text === '▼' ? '▲' : '▼';
                        });
                });
            });
        </script> --}}
        
        
        <script>
document.getElementById('print-button').addEventListener('click', function () {
    // Show the container elements for printing
    const containers = document.querySelectorAll('.container');
    containers.forEach(function (container) {
        container.style.display = 'block';
    });

    window.print(); // Trigger the browser's print dialog

    // Hide the container elements after printing is complete
    containers.forEach(function (container) {
        container.style.display = 'none';
    });
});
        </script>
        <x-footers.auth></x-footers.auth>
    </main>
    <x-plugins></x-plugins>
</x-layout>
