{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "barryvdh/laravel-dompdf": "^2.1", "dompdf/dompdf": "2.0.4", "guzzlehttp/guzzle": "^7.2", "jenssegers/agent": "*", "laravel-frontend-presets/material-dashboard": "^2.0", "laravel/framework": "^10.10", "laravel/jetstream": "^3.3", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "laravel/ui": "^4.2", "livewire/livewire": "^3.3", "livewire/volt": "^1.0", "phpoffice/phpword": "^1.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.26", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "roave/security-advisories": "dev-master", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "PhpOffice\\PhpWord\\": "vendor/phpoffice/phpword/src/PhpWord/", "Barryvdh\\DomPDF\\": "vendor/barryvdh/laravel-dompdf/src/"}, "files": ["app/Helpers/custom_helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}