<!-- Filter Section -->
<div class="filter-section">
    <h5 class="mb-3">
        <i class="fas fa-filter me-2"></i>
        Filters & Search
    </h5>

    <form method="GET" action="<?php echo e(route('remaining-fatawa', [
        'darulifta' => request()->route('darulifta'),
        'mailfolder' => request()->route('mailfolder')
    ])); ?>">
        <!-- Hidden inputs to preserve display options -->
        <input type="hidden" name="showDetail" value="<?php echo e(request('showDetail', '0')); ?>">
        <input type="hidden" name="showQue" value="<?php echo e(request('showQue', '0')); ?>">
        <input type="hidden" name="showChat" value="<?php echo e(request('showChat', '0')); ?>">

        <div class="filter-grid">
            <!-- Mujeeb Filter -->
            <div class="form-group-modern">
                <label for="mujeebframe">
                    <i class="fas fa-user me-1"></i>
                    Select Mujeeb
                </label>
                <select class="form-control-modern" id="mujeebframe" name="selectedmujeeb">
                    <option value="all" <?php echo e(request('selectedmujeeb') === 'all' ? 'selected' : ''); ?>>All Mujeeb</option>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $mujeebs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sender): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($sender); ?>" <?php echo e(request('selectedmujeeb') === $sender ? 'selected' : ''); ?>>
                            <?php echo e($sender); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>

            <!-- Mufti Filter -->
            <div class="form-group-modern">
                <label for="muftiframe">
                    <i class="fas fa-user-tie me-1"></i>
                    Select Mufti
                </label>
                <select class="form-control-modern" id="muftiframe" name="selectedmufti">
                    <!--[if BLOCK]><![endif]--><?php if($munsab == 'Musaddiq' || is_null($munsab)): ?>
                        <option value="all" <?php echo e(request('selectedmufti') === 'all' || !request('selectedmufti') ? 'selected' : ''); ?>>All</option>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $checkerlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item->folder_id); ?>" <?php echo e(request('selectedmufti') == $item->folder_id ? 'selected' : ''); ?>>
                                <?php echo e($item->checker_name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php else: ?>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $checkerlist; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <!--[if BLOCK]><![endif]--><?php if($item->checker_name == $userName): ?>
                                <option value="<?php echo e($item->folder_id); ?>" <?php echo e(request('selectedmufti') == $item->folder_id ? 'selected' : ''); ?>>
                                    <?php echo e($item->checker_name); ?>

                                </option>
                                <option value="transfer_checked" <?php echo e(request('selectedmufti') === 'transfer_checked' ? 'selected' : ''); ?>>
                                    Transfer Checked
                                </option>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>

            <!-- Time Frame Filter -->
            <div class="form-group-modern">
                <label for="timeframe">
                    <i class="fas fa-calendar me-1"></i>
                    Time Frame
                </label>
                <select class="form-control-modern" id="timeframe" name="selectedTimeFrame">
                    <option value="all" <?php echo e(request('selectedTimeFrame') === 'all' ? 'selected' : ''); ?>>All</option>
                    <option value="this_month" <?php echo e(request('selectedTimeFrame') === 'this_month' ? 'selected' : ''); ?>>This Month</option>
                    <option value="other" <?php echo e(request('selectedTimeFrame') === 'other' ? 'selected' : ''); ?>>Other</option>
                    <!--[if BLOCK]><![endif]--><?php if(request('selectedTimeFrame') === 'custom'): ?>
                        <option value="custom" selected>Selected Date</option>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>

            <!-- Start Date -->
            <div class="form-group-modern">
                <label for="start_date">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Start Date
                </label>
                <input type="date" class="form-control-modern" id="start_date" name="startDate" value="<?php echo e(request('startDate')); ?>">
            </div>

            <!-- End Date -->
            <div class="form-group-modern">
                <label for="end_date">
                    <i class="fas fa-calendar-check me-1"></i>
                    End Date
                </label>
                <input type="date" class="form-control-modern" id="end_date" name="endDate" value="<?php echo e(request('endDate')); ?>">
            </div>

            <!-- Apply Button -->
            <div class="form-group-modern">
                <button type="submit" class="btn-modern btn-primary-modern w-100">
                    <i class="fas fa-search me-2"></i>
                    Apply Filters
                </button>
            </div>
        </div>

        <!-- Month Selector for "Other" timeframe -->
        <?php if(request('selectedTimeFrame') === 'other'): ?>
            <div class="mt-4">
                <h6 class="mb-3">Select Months:</h6>
                <div class="row">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $datefilter; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $monthName = DateTime::createFromFormat('!m', $data->month)->format('F');
                        ?>
                        <div class="col-md-3 col-sm-6 mb-2">
                            <div class="form-check">
                                <input type="checkbox" name="selectedMonths[]" id="month-<?php echo e($data->year); ?>-<?php echo e($data->month); ?>"
                                       value="<?php echo e($data->year); ?>-<?php echo e($data->month); ?>" class="form-check-input"
                                       <?php
                                           $selectedMonths = request('selectedMonths', []);
                                           if (is_string($selectedMonths)) {
                                               $selectedMonths = explode(',', $selectedMonths);
                                           }
                                           $isChecked = in_array("$data->year-$data->month", $selectedMonths);
                                       ?>
                                       <?php echo e($isChecked ? 'checked' : ''); ?>>
                                <label for="month-<?php echo e($data->year); ?>-<?php echo e($data->month); ?>" class="form-check-label">
                                    <?php echo e($monthName); ?> <?php echo e($data->year); ?>

                                </label>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                <!-- Hidden inputs for month filter to preserve display options -->
                <input type="hidden" name="showDetail" value="<?php echo e(request('showDetail', '0')); ?>">
                <input type="hidden" name="showQue" value="<?php echo e(request('showQue', '0')); ?>">
                <input type="hidden" name="showChat" value="<?php echo e(request('showChat', '0')); ?>">
                <input type="hidden" name="selectedmujeeb" value="<?php echo e(request('selectedmujeeb', 'all')); ?>">
                <input type="hidden" name="selectedmufti" value="<?php echo e(request('selectedmufti', 'all')); ?>">
                <input type="hidden" name="selectedTimeFrame" value="<?php echo e(request('selectedTimeFrame', 'other')); ?>">
                <input type="hidden" name="startDate" value="<?php echo e(request('startDate')); ?>">
                <input type="hidden" name="endDate" value="<?php echo e(request('endDate')); ?>">

                <button type="submit" class="btn-modern btn-primary-modern mt-3">
                    <i class="fas fa-filter me-2"></i>
                    Filter By Date
                </button>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </form>
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-filters.blade.php ENDPATH**/ ?>