<div>
<style>
    /* --- CSS Variables for Easy Theming (within this component) --- */
    :root { /* Using :root might affect globally if not scoped, but often works fine in component styles */
        --sidebar-primary-color: #007bff; /* Blue */
        --sidebar-primary-light: #e0f2ff;
        --sidebar-secondary-color: #6c757d; /* Gray */
        --sidebar-bg-color: #f8f9fa; /* Light Gray Background */
        --sidebar-filter-bg: #ffffff;
        --sidebar-card-bg: #ffffff;
        --sidebar-text-color: #343a40;
        --sidebar-text-muted: #6c757d;
        --sidebar-border-color: #dee2e6;
        --sidebar-highlight-bg: #ffeb3b; /* Yellow */
        --sidebar-font-urdu: '<PERSON><PERSON>', 'Noto Nastaliq Urdu', Arial, sans-serif; /* Added fallback */
        --sidebar-border-radius: 8px;
        --sidebar-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        --sidebar-box-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.12);
    }

    /* --- <PERSON> (Scoped to this component's container if possible, otherwise apply cautiously) --- */
    /* Avoid styling 'body' here unless absolutely necessary and scoped */
    .talaq-sidebar-component { /* Add a wrapper class to the main div if needed for scoping */
        font-family: var(--sidebar-font-urdu);
        direction: rtl;
        text-align: right;
        color: var(--sidebar-text-color);
        line-height: 1.7;
        padding: 15px; /* Padding for the component itself */
        box-sizing: border-box;
    }

    /* --- Search Box --- */
    .talaq-sidebar-component .search-box {
        margin: 0 auto 20px auto; /* Centered, bottom margin */
        width: 100%; /* Full width within component */
        max-width: 400px;
        position: relative;
    }

    .talaq-sidebar-component .search-input {
        width: 100%;
        padding: 10px 15px;
        border: 1px solid var(--sidebar-border-color);
        border-radius: var(--sidebar-border-radius);
        font-size: 1rem;
        font-family: var(--sidebar-font-urdu);
        color: var(--sidebar-text-color);
        background-color: var(--sidebar-filter-bg);
        outline: none;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
        box-sizing: border-box;
    }

    .talaq-sidebar-component .search-input:focus {
        border-color: var(--sidebar-primary-color);
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    }

    .talaq-sidebar-component .search-input::placeholder {
        font-family: var(--sidebar-font-urdu);
        color: var(--sidebar-text-muted);
        opacity: 0.8;
    }

    /* --- Filters Styling (previously .sid-menu) --- */
    .talaq-sidebar-component .filters-container { /* Renamed from .sid-menu */
        background-color: var(--sidebar-filter-bg);
        border: 1px solid var(--sidebar-border-color);
        border-radius: var(--sidebar-border-radius);
        padding: 15px;
        box-shadow: var(--sidebar-box-shadow);
        width: 100%; /* Take full width of parent */
        margin: 0 auto 20px auto;
        box-sizing: border-box;
    }

    .talaq-sidebar-component .filters-container ul {
        list-style-type: none;
        padding: 0;
        margin: 0;
    }

    .talaq-sidebar-component .filters-container > ul > li > label { /* Main Category Label */
        font-size: 1.15rem;
        font-weight: bold;
        color: var(--sidebar-primary-color);
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--sidebar-border-color);
        display: block; /* Make it block */
    }

    /* --- Filter Item & Custom Checkbox --- */
     .talaq-sidebar-component .filter-item {
        margin-bottom: 8px;
        padding: 3px 0;
    }

    .talaq-sidebar-component .custom-checkbox {
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .talaq-sidebar-component .custom-checkbox input[type="checkbox"] {
        opacity: 0;
        position: absolute;
        width: 0;
        height: 0;
    }

    .talaq-sidebar-component .custom-checkbox label {
        font-size: 1rem; /* Main item label size */
        font-weight: normal; /* Remove bold default */
        color: var(--sidebar-text-color);
        padding-right: 30px; /* Space for custom checkbox (RTL) */
        position: relative;
        cursor: pointer;
        user-select: none;
        transition: color 0.2s ease;
        flex-grow: 1;
        line-height: 1.5;
        margin-bottom: 0; /* Override default label margin */
    }

    /* Custom checkbox box */
    .talaq-sidebar-component .custom-checkbox label::before {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 18px;
        height: 18px;
        border: 2px solid var(--sidebar-border-color);
        border-radius: 4px;
        background-color: #fff;
        transition: background-color 0.2s ease, border-color 0.2s ease;
    }

    /* Checked state box */
    .talaq-sidebar-component .custom-checkbox input[type="checkbox"]:checked + label::before {
        background-color: var(--sidebar-primary-color);
        border-color: var(--sidebar-primary-color);
    }

    /* Checkmark */
    .talaq-sidebar-component .custom-checkbox label::after {
        content: "";
        position: absolute;
        right: 6px;
        top: 48%;
        transform: translateY(-50%) rotate(45deg) scale(0);
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transition: transform 0.2s ease;
    }

    /* Show checkmark when checked */
    .talaq-sidebar-component .custom-checkbox input[type="checkbox"]:checked + label::after {
        transform: translateY(-50%) rotate(45deg) scale(1);
    }

     /* Checked label style */
    .talaq-sidebar-component .custom-checkbox input[type="checkbox"]:checked + label {
        color: var(--sidebar-primary-color);
        font-weight: bold; /* Make bold when checked */
    }

    /* Focus style */
    .talaq-sidebar-component .custom-checkbox input[type="checkbox"]:focus + label::before {
         box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    }

    /* Hover effect */
    .talaq-sidebar-component .custom-checkbox:hover label::before {
        border-color: var(--sidebar-primary-color);
    }
    .talaq-sidebar-component .custom-checkbox:hover label {
        color: var(--sidebar-primary-color);
    }


    /* --- Sub-Items Styling (previously .parent-box) --- */
    .talaq-sidebar-component .sub-items { /* Renamed from .parent-box */
        margin-top: 8px;
        padding: 10px 15px 5px 0; /* Adjust padding */
        padding-right: 25px; /* Indentation for sub-items (RTL) */
        border-right: 2px solid var(--sidebar-primary-light); /* Visual indicator */
        margin-right: 9px; /* Align with checkbox center */
        background-color: #fdfdfd; /* Very light background for distinction */
        border-radius: 0 5px 5px 0; /* Rounded corners */
    }

    .talaq-sidebar-component .sub-items > label { /* Sub-item group label */
        font-size: 0.9rem;
        font-weight: bold;
        color: var(--sidebar-text-muted);
        margin-bottom: 8px;
        display: block;
    }

    .talaq-sidebar-component .sub-items ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .talaq-sidebar-component .sub-items li {
        margin-bottom: 5px;
    }

    /* Styling for labels within sub-items */
    .talaq-sidebar-component .sub-items .custom-checkbox label {
        font-size: 0.95rem;
        color: var(--sidebar-text-muted);
        font-weight: normal; /* Ensure normal weight initially */
    }

    .talaq-sidebar-component .sub-items .custom-checkbox input[type="checkbox"]:checked + label {
        color: var(--sidebar-primary-color);
        font-weight: bold; /* Bold when checked */
    }


    /* --- Cards Container --- */
    .talaq-sidebar-component #cards-container {
        margin-top: 20px;
        padding: 15px;
        background-color: var(--sidebar-bg-color); /* Use main bg or slightly different */
        border: 1px solid var(--sidebar-border-color);
        border-radius: var(--sidebar-border-radius);
        box-shadow: var(--sidebar-box-shadow);
        /* Remove flex properties causing centering issues if cards should stack */
        /* display: flex;
        flex-direction: column;
        align-items: center; */
        gap: 15px; /* Space between cards if needed (though margin-bottom on card handles it) */
    }

    .talaq-sidebar-component #cards-container > .cards-title { /* Changed label to a class */
        font-size: 1.2rem;
        font-weight: bold;
        color: var(--sidebar-secondary-color);
        margin: 0 0 15px 0; /* Adjusted margin */
        padding-bottom: 10px;
        border-bottom: 1px solid var(--sidebar-border-color);
        display: block;
        text-align: center;
    }

    /* --- Individual Card Styling --- */
    .talaq-sidebar-component .card {
        border: 1px solid var(--sidebar-border-color);
        border-radius: var(--sidebar-border-radius);
        background-color: var(--sidebar-card-bg);
        color: var(--sidebar-text-color);
        padding: 15px;
        box-shadow: var(--sidebar-box-shadow);
        margin-bottom: 15px; /* Space between cards */
        direction: rtl;
        text-align: right;
        transition: box-shadow 0.3s ease, transform 0.3s ease;
        display: flex;
        flex-direction: column;
        width: 100%; /* *** IMPORTANT: Allow card to fill container *** */
        max-width: 100%; /* Ensure it doesn't overflow its container */
        box-sizing: border-box; /* Include padding in width */
        overflow-wrap: break-word; /* Break long words/strings */
        word-wrap: break-word; /* Legacy support */
        word-break: break-word; /* Ensure breaks */
    }
     .talaq-sidebar-component .card:last-child {
        margin-bottom: 0;
    }

    .talaq-sidebar-component .card:hover {
        box-shadow: var(--sidebar-box-shadow-hover);
        transform: translateY(-3px);
    }

    .talaq-sidebar-component .card-content {
        margin-bottom: 10px;
        flex-grow: 1;
    }

    .talaq-sidebar-component .card p {
        margin: 0 0 8px 0;
        font-size: 1rem;
        font-family: var(--sidebar-font-urdu);
        line-height: 1.8;
        direction: rtl;
        text-align: right;
        unicode-bidi: embed;
        /* Word wrap properties moved to .card for better overall control */
        white-space: normal; /* Ensure text wraps */
        text-indent: 0;
    }
    .talaq-sidebar-component .card p:last-child {
        margin-bottom: 0;
    }

    /* Keep your essential rule */
    .talaq-sidebar-component p[style*="text-align:left;"][style*="argin-right"] {
        margin-right: 0 !important;
    }

    /* Fix for Gaps in Specific Inline Spans */
    .talaq-sidebar-component .card p span {
        display: inline;
        line-height: inherit; /* Inherit from p */
    }

    /* Highlight Search Matches */
    .talaq-sidebar-component .highlight {
        background-color: var(--sidebar-highlight-bg);
        font-weight: bold;
        padding: 1px 3px;
        border-radius: 3px;
        color: #333; /* Darker text on yellow */
        direction: rtl;
        display: inline;
        line-height: inherit;
    }

    .talaq-sidebar-component .card-actions {
        text-align: left; /* Align button to the start (left in RTL) */
        margin-top: 10px;
    }

    /* --- Button Styling --- */
    .talaq-sidebar-component .copy-btn {
        background-color: var(--sidebar-primary-color);
        color: #ffffff;
        font-size: 0.9rem;
        font-family: var(--sidebar-font-urdu);
        border: none;
        border-radius: 5px;
        padding: 6px 14px;
        cursor: pointer;
        transition: background-color 0.3s ease, box-shadow 0.3s ease;
        font-weight: normal; /* Regular weight */
    }

    .talaq-sidebar-component .copy-btn:hover {
        background-color: #0056b3; /* Darker blue */
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    }

    .talaq-sidebar-component .copy-btn:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);
    }

    /* --- Responsive Design Adjustments within component --- */
    @media (max-width: 576px) {
         .talaq-sidebar-component .search-input {
            padding: 8px 12px;
            font-size: 0.9rem;
        }
        .talaq-sidebar-component .filters-container {
             padding: 10px;
        }
        .talaq-sidebar-component .custom-checkbox label {
            font-size: 0.9rem;
            padding-right: 26px;
        }
        .talaq-sidebar-component .custom-checkbox label::before {
            width: 16px;
            height: 16px;
        }
         .talaq-sidebar-component .custom-checkbox label::after {
            right: 5px;
            width: 4px;
            height: 9px;
        }
        .talaq-sidebar-component .sub-items {
            padding-right: 20px;
            margin-right: 8px;
        }
        .talaq-sidebar-component #cards-container {
             padding: 10px;
        }
         .talaq-sidebar-component .card {
             padding: 10px;
        }
        .talaq-sidebar-component .card p {
            font-size: 0.95rem;
        }
        .talaq-sidebar-component .copy-btn {
            font-size: 0.85rem;
            padding: 5px 10px;
        }
    }

</style>

<!-- Add a wrapper div with a class for potential CSS scoping -->
<div class="talaq-sidebar-component">

    <!-- Search Box -->
    <div class="search-box">
        <input type="text" wire:model.live="searchQuery" placeholder="  تلاش کریں۔۔۔۔  " class="search-input">
    </div>

    <!-- Filters -->
    <div class="filters-container"> <!-- Renamed class -->
        <ul>
            <li>
                <label>مرکزی عنوان</label> <!-- Main Category Label -->
                @foreach ($headers as $header)
                    <!-- Header Filter Item -->
                    <div class="filter-item">
                        <div class="custom-checkbox">
                            <input type="checkbox" wire:model.live="headerChecks.{{ $header->id }}" id="header-{{ $header->id }}">
                            <label for="header-{{ $header->id }}">{{ $header->name }}</label>
                        </div>
                    </div>

                    <!-- Sub-items related to this header -->
                    @php
                        $relatedParents = $parents->where('header_id', $header->id);
                    @endphp
                    @if ($relatedParents->isNotEmpty())
                        <div class="sub-items"> <!-- Renamed class -->
                            <label>ذیلی عنوان</label> <!-- Sub-item group label -->
                            <ul>
                                @foreach ($relatedParents as $parent)
                                    <li>
                                        <!-- Parent Filter Item -->
                                        <div class="filter-item">
                                            <div class="custom-checkbox">
                                                <input type="checkbox" wire:model.live="parentChecks.{{ $parent->id }}" id="parent-{{ $parent->id }}">
                                                <label for="parent-{{ $parent->id }}">{{ $parent->name }}</label>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                @endforeach
            </li>
        </ul>
    </div>

    <!-- Cards -->
    @if ($children->isNotEmpty())
        <div id="cards-container">
            <label class="cards-title">جزئیات</label> <!-- Added class -->
            @foreach ($children as $child)
                <div class="card" data-name="{{ $child->name }}">
                    <div class="card-content">
                        <p>
                            {!! str_replace(
                                $searchQuery,
                                '<span class="highlight">' . $searchQuery . '</span>',
                                $child->name
                            ) !!}
                        </p>
                    </div>
                    <div class="card-actions">
                         {{-- Changed text to Insert and added wire:click --}}
                        <button class="copy-btn" wire:click="$dispatch('insert-text', { text: '{{ addslashes(preg_replace('/\s+/', ' ', strip_tags($child->name))) }}' })">
                            Insert
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    {{-- Add Script to handle the insert event --}}
    @push('scripts')
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('insert-text', (event) => {
                // Assuming you have a Summernote instance with id 'editor'
                // Adjust selector if your editor ID is different
                var editor = $('#editor'); // Use jQuery selector if using Summernote with Bootstrap 4/5
                if (editor.length && typeof editor.summernote === 'function') {
                   // Insert text at the current cursor position
                   editor.summernote('insertText', event.text);
                } else {
                    // Fallback for plain textarea or other editors might need different handling
                    const textarea = document.getElementById('editor');
                    if (textarea) {
                        const start = textarea.selectionStart;
                        const end = textarea.selectionEnd;
                        textarea.value = textarea.value.substring(0, start) + event.text + textarea.value.substring(end);
                        textarea.selectionStart = textarea.selectionEnd = start + event.text.length;
                        textarea.focus(); // Keep focus on the editor
                    }
                }
            });
        });
    </script>
    @endpush

</div> {{-- End of talaq-sidebar-component wrapper --}}

</div>