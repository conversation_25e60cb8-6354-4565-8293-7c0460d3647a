
<div>
    <!-- Legacy Loading Overlay (for custom JS only) -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="spinner"></div>
    </div>

    <!-- Livewire Full Page Loading Overlay - Only for Apply Filters -->
    <div wire:loading wire:target="applyFilters"
         class="full-page-loading"
         style="display: none !important; opacity: 0 !important; visibility: hidden !important;">
        <div class="loading-content">
            <div class="spinner-border spinner-border-lg text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>

            <!-- Filter Loading Message -->
            <div wire:loading wire:target="applyFilters">
                <h5 class="mb-2 text-dark">
                    <i class="fas fa-filter fa-spin me-2 text-primary"></i>
                    Applying Filters
                </h5>
                <p class="mb-0 text-muted">Please wait while we apply your filters and refresh the data...</p>
            </div>
        </div>
    </div>

    <!-- Main Content with Sidebar Layout -->
    <main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
        <x-navbars.navs.auth titlePage="Sending Fatawa"></x-navbars.navs.auth>

        <!-- Container for all content -->
        <div class="container-fluid py-4">
            <!-- Page Header -->
            <div class="modern-card mb-4">
                <div class="modern-card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-paper-plane me-2"></i>
                        Sending Fatawa Management
                    </h4>
                    <p class="mb-0 opacity-75">Manage and track sending fatawa across all Daruliftaas</p>
                </div>
            </div>

            <!-- Loading indicator for initial data load -->
            <div wire:loading.delay wire:target="loadData" class="modern-card mb-4">
                <div class="modern-card-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5 class="mb-2">Loading Sending Fatawa Data...</h5>
                    <p class="text-muted">Please wait while we fetch the latest data</p>
                </div>
            </div>

            <!-- Filter Section -->
            @include('livewire.partials.sending-fatawa-filters')

            <!-- Statistics Section -->
            @include('livewire.partials.sending-fatawa-stats')

            <!-- Summary Table Section -->
            @include('livewire.partials.sending-fatawa-summary')

            <!-- Include Styles -->
            @include('livewire.partials.sending-fatawa-styles')

            <!-- Include Scripts -->
            @include('livewire.partials.sending-fatawa-scripts')
        </div>
    </main>

    <!-- Sidebar and Layout -->
    <div wire:ignore>
        <x-layout bodyClass="g-sidenav-show bg-gray-200">
            <x-navbars.sidebar activePage="{{ request()->route('darulifta') ?? 'sending-fatawa' }}"></x-navbars.sidebar>
        </x-layout>
        <x-footers.auth></x-footers.auth>
    </div>
    <x-plugins></x-plugins>
</div>


