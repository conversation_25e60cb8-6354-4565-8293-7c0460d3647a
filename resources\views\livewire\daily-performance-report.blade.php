<div>
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Daily Performance Report</h5>
                            <p class="text-sm mb-0">
                                Submit your daily performance for {{ Carbon\Carbon::parse($performance_date)->format('F d, Y') }}
                                @if($isSubmitted)
                                    <span class="badge bg-gradient-success ms-2">Submitted</span>
                                @else
                                    <span class="badge bg-gradient-warning ms-2">Pending</span>
                                @endif
                            </p>

                            <!-- User Department and Role Info -->
                            <div class="mt-2">
                                @if($userDepartments->count() > 0)
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>
                                        <strong>Department(s):</strong>
                                        @foreach($userDepartments as $department)
                                            <span class="badge bg-light text-dark me-1">{{ $department->name }}</span>
                                        @endforeach
                                    </small>
                                @endif

                                @if(auth()->user()->isMujeeb() || auth()->user()->isSuperior())
                                    <small class="text-muted ms-3">
                                        <i class="fas fa-user-tag me-1"></i>
                                        <strong>Role:</strong>
                                        <span class="badge bg-primary">
                                            @if(auth()->user()->isSuperior())
                                                Superior
                                            @elseif(auth()->user()->isMujeeb())
                                                Mujeeb
                                            @endif
                                        </span>
                                    </small>
                                @endif
                            </div>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                @if($todaysTasks->count() > 0)
                                    <button wire:click="autoFillTasksCompleted" class="btn btn-outline-info btn-sm mb-0 me-2">
                                        <i class="fas fa-magic"></i>&nbsp;&nbsp;Auto-fill Tasks
                                    </button>
                                @endif
                                @if(!$isSubmitted)
                                    <button wire:click="saveAsDraft" class="btn btn-outline-secondary btn-sm mb-0 me-2">
                                        <i class="fas fa-save"></i>&nbsp;&nbsp;Save Draft
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    @if(!$hasAssignedTasks)
                        <!-- No Tasks Message -->
                        <div class="alert alert-info text-center" role="alert">
                            <div class="icon-container mb-3">
                                <i class="fas fa-tasks text-info" style="font-size: 3rem;"></i>
                            </div>
                            <h4 class="alert-heading">No Tasks Assigned</h4>
                            <p class="mb-3">
                                You currently have no active tasks assigned to you. Daily performance submission is only required for users with assigned tasks.
                            </p>
                            <hr>
                            <p class="mb-0">
                                Please contact your Superior or Admin if you believe this is an error, or if you need tasks to be assigned to you.
                            </p>
                        </div>
                    @else
                        <form wire:submit.prevent="submitReport">
                            <div class="row">
                                <!-- Performance Date -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Performance Date *</label>
                                        <input type="date" wire:model.live="performance_date"
                                               class="form-control @error('performance_date') is-invalid @enderror"
                                               {{ !$this->canEdit() ? 'disabled' : '' }}>
                                        @error('performance_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Task Selection -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Select Task *</label>
                                        <select wire:model.live="selected_task_id"
                                                class="form-select @error('selected_task_id') is-invalid @enderror"
                                                {{ !$this->canEdit() ? 'disabled' : '' }}>
                                            <option value="">Choose a task...</option>
                                            @foreach($assignedTasks as $task)
                                                <option value="{{ $task->id }}">
                                                    {{ $task->title }}
                                                    @if($task->department)
                                                        ({{ $task->department->name }})
                                                    @endif
                                                    - Due: {{ $task->due_date ? $task->due_date->format('M d') : 'No due date' }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('selected_task_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Department Display -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Department</label>
                                        <div class="form-control bg-light">
                                            @if($selectedTaskDepartment)
                                                <span class="badge bg-primary">{{ $selectedTaskDepartment->name }}</span>
                                            @else
                                                <span class="text-muted">Select a task first</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Second Row: Rating, Hours, Summary -->
                            <div class="row">
                                <!-- Overall Rating (Optional for Mujeeb) -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">
                                            Self Rating
                                            @if(auth()->user()->isMujeeb())
                                                <small class="text-muted">(Optional)</small>
                                            @endif
                                        </label>
                                        <select wire:model="overall_rating"
                                                class="form-select @error('overall_rating') is-invalid @enderror"
                                                {{ !$this->canEdit() ? 'disabled' : '' }}>
                                            <option value="">No Rating</option>
                                            <option value="poor">Poor</option>
                                            <option value="fair">Fair</option>
                                            <option value="good">Good</option>
                                            <option value="excellent">Excellent</option>
                                        </select>
                                        @error('overall_rating') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Hours Worked -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Hours Worked *</label>
                                        <input type="number" step="0.5" min="0" max="24" wire:model="hours_worked"
                                               class="form-control @error('hours_worked') is-invalid @enderror"
                                               placeholder="8.0"
                                               {{ !$this->canEdit() ? 'disabled' : '' }}>
                                        @error('hours_worked') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                    </div>
                                </div>

                                <!-- Performance Summary -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Performance Summary</label>
                                        <div class="d-flex gap-2">
                                            <span class="badge bg-gradient-{{ $this->getRatingColor($overall_rating) }}">
                                                {{ ucfirst($overall_rating ?: 'Not Rated') }}
                                            </span>
                                            <span class="badge bg-gradient-info">
                                                {{ $hours_worked ?: '0' }} hrs
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>



                        <!-- Tasks Completed -->
                        <div class="form-group">
                            <label class="form-label">Tasks Completed *</label>
                            <textarea wire:model="tasks_completed"
                                      class="form-control @error('tasks_completed') is-invalid @enderror"
                                      rows="4"
                                      placeholder="Describe the tasks you completed today..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('tasks_completed') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Challenges Faced -->
                        <div class="form-group">
                            <label class="form-label">Challenges Faced</label>
                            <textarea wire:model="challenges_faced"
                                      class="form-control @error('challenges_faced') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Describe any challenges or obstacles you faced..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('challenges_faced') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Next Day Plan -->
                        <div class="form-group">
                            <label class="form-label">Next Day Plan</label>
                            <textarea wire:model="next_day_plan"
                                      class="form-control @error('next_day_plan') is-invalid @enderror"
                                      rows="3"
                                      placeholder="Outline your plan for tomorrow..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('next_day_plan') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea wire:model="additional_notes"
                                      class="form-control @error('additional_notes') is-invalid @enderror"
                                      rows="2"
                                      placeholder="Any additional notes or comments..."
                                      {{ !$this->canEdit() ? 'disabled' : '' }}></textarea>
                            @error('additional_notes') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Submit Button -->
                        @if($this->canEdit() && !$isSubmitted)
                        <div class="form-group text-end">
                            <button type="submit" class="btn bg-gradient-primary">
                                <i class="fas fa-paper-plane"></i>&nbsp;&nbsp;Submit Performance Report
                            </button>
                        </div>
                        @endif
                    </form>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Tasks Card -->
    @if($todaysTasks->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Today's Tasks ({{ $completedTasksCount }}/{{ $todaysTasks->count() }} completed)</h6>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($todaysTasks as $task)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $this->getTaskStatusColor($task->status) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($task->status !== 'completed' && $this->canEdit())
                                            <button wire:click="markTaskCompleted({{ $task->id }})" 
                                                    class="btn btn-sm btn-outline-success mb-0">
                                                <i class="fas fa-check"></i> Complete
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- All Assigned Tasks Card -->
    @if($allUserTasks->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">All Assigned Tasks ({{ $allUserTasks->where('status', 'completed')->count() }}/{{ $allUserTasks->count() }} completed)</h6>
                    <p class="text-sm mb-0">Tasks assigned to you across all departments</p>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Department</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($allUserTasks as $task)
                                <tr class="{{ $task->isOverdue() ? 'table-warning' : '' }}">
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                                @if($task->assignedBy)
                                                    <p class="text-xs text-info mb-0">Assigned by: {{ $task->assignedBy->name }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($task->department)
                                            <span class="badge badge-sm bg-gradient-info">{{ $task->department->name }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="text-xs {{ $task->isOverdue() ? 'text-danger fw-bold' : 'text-secondary' }}">
                                            {{ $task->due_date ? $task->due_date->format('M d, Y') : '-' }}
                                            @if($task->isOverdue())
                                                <br><small class="text-danger">(Overdue)</small>
                                            @endif
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $this->getTaskStatusColor($task->status) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($task->status !== 'completed' && $this->canEdit())
                                            <button wire:click="markTaskCompleted({{ $task->id }})"
                                                    class="btn btn-sm btn-outline-success mb-0">
                                                <i class="fas fa-check"></i> Complete
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif
</div>
</div>
