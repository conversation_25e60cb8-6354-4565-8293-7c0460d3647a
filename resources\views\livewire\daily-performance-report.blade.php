<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Daily Performance Report</h5>
                            <p class="text-sm mb-0">
                                Submit your daily performance for {{ Carbon\Carbon::parse($performance_date)->format('F d, Y') }}
                                @if($isSubmitted)
                                    <span class="badge bg-gradient-success ms-2">Submitted</span>
                                @else
                                    <span class="badge bg-gradient-warning ms-2">Pending</span>
                                @endif
                            </p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                @if($todaysTasks->count() > 0)
                                    <button wire:click="autoFillTasksCompleted" class="btn btn-outline-info btn-sm mb-0 me-2">
                                        <i class="fas fa-magic"></i>&nbsp;&nbsp;Auto-fill Tasks
                                    </button>
                                @endif
                                @if(!$isSubmitted)
                                    <button wire:click="saveAsDraft" class="btn btn-outline-secondary btn-sm mb-0 me-2">
                                        <i class="fas fa-save"></i>&nbsp;&nbsp;Save Draft
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <form wire:submit.prevent="submitReport">
                        <div class="row">
                            <!-- Performance Date -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Performance Date *</label>
                                    <input type="date" wire:model.live="performance_date" 
                                           class="form-control @error('performance_date') is-invalid @enderror"
                                           {{ !$canEdit() ? 'disabled' : '' }}>
                                    @error('performance_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>

                            <!-- Overall Rating -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Overall Rating *</label>
                                    <select wire:model="overall_rating" 
                                            class="form-select @error('overall_rating') is-invalid @enderror"
                                            {{ !$canEdit() ? 'disabled' : '' }}>
                                        <option value="poor">Poor</option>
                                        <option value="fair">Fair</option>
                                        <option value="good">Good</option>
                                        <option value="excellent">Excellent</option>
                                    </select>
                                    @error('overall_rating') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>

                            <!-- Hours Worked -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Hours Worked *</label>
                                    <input type="number" step="0.5" min="0" max="24" wire:model="hours_worked" 
                                           class="form-control @error('hours_worked') is-invalid @enderror"
                                           placeholder="8.0"
                                           {{ !$canEdit() ? 'disabled' : '' }}>
                                    @error('hours_worked') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>

                            <!-- Performance Summary -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Performance Summary</label>
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-gradient-{{ $getRatingColor($overall_rating) }}">
                                            {{ ucfirst($overall_rating) }}
                                        </span>
                                        <span class="badge bg-gradient-info">
                                            {{ $hours_worked ?: '0' }} hrs
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Fatawa Processed -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Fatawa Processed *</label>
                                    <input type="number" min="0" wire:model="fatawa_processed" 
                                           class="form-control @error('fatawa_processed') is-invalid @enderror"
                                           placeholder="0"
                                           {{ !$canEdit() ? 'disabled' : '' }}>
                                    @error('fatawa_processed') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>

                            <!-- Questions Answered -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Questions Answered *</label>
                                    <input type="number" min="0" wire:model="questions_answered" 
                                           class="form-control @error('questions_answered') is-invalid @enderror"
                                           placeholder="0"
                                           {{ !$canEdit() ? 'disabled' : '' }}>
                                    @error('questions_answered') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Tasks Completed -->
                        <div class="form-group">
                            <label class="form-label">Tasks Completed *</label>
                            <textarea wire:model="tasks_completed" 
                                      class="form-control @error('tasks_completed') is-invalid @enderror" 
                                      rows="4" 
                                      placeholder="Describe the tasks you completed today..."
                                      {{ !$canEdit() ? 'disabled' : '' }}></textarea>
                            @error('tasks_completed') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Challenges Faced -->
                        <div class="form-group">
                            <label class="form-label">Challenges Faced</label>
                            <textarea wire:model="challenges_faced" 
                                      class="form-control @error('challenges_faced') is-invalid @enderror" 
                                      rows="3" 
                                      placeholder="Describe any challenges or obstacles you faced..."
                                      {{ !$canEdit() ? 'disabled' : '' }}></textarea>
                            @error('challenges_faced') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Next Day Plan -->
                        <div class="form-group">
                            <label class="form-label">Next Day Plan</label>
                            <textarea wire:model="next_day_plan" 
                                      class="form-control @error('next_day_plan') is-invalid @enderror" 
                                      rows="3" 
                                      placeholder="Outline your plan for tomorrow..."
                                      {{ !$canEdit() ? 'disabled' : '' }}></textarea>
                            @error('next_day_plan') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Additional Notes -->
                        <div class="form-group">
                            <label class="form-label">Additional Notes</label>
                            <textarea wire:model="additional_notes" 
                                      class="form-control @error('additional_notes') is-invalid @enderror" 
                                      rows="2" 
                                      placeholder="Any additional notes or comments..."
                                      {{ !$canEdit() ? 'disabled' : '' }}></textarea>
                            @error('additional_notes') <div class="invalid-feedback">{{ $message }}</div> @enderror
                        </div>

                        <!-- Submit Button -->
                        @if($canEdit() && !$isSubmitted)
                        <div class="form-group text-end">
                            <button type="submit" class="btn bg-gradient-primary">
                                <i class="fas fa-paper-plane"></i>&nbsp;&nbsp;Submit Performance Report
                            </button>
                        </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Tasks Card -->
    @if($todaysTasks->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <h6 class="mb-0">Today's Tasks ({{ $completedTasksCount }}/{{ $todaysTasks->count() }} completed)</h6>
                </div>
                <div class="card-body pt-0">
                    <div class="table-responsive">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($todaysTasks as $task)
                                <tr>
                                    <td>
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-sm">{{ $task->title }}</h6>
                                                @if($task->description)
                                                    <p class="text-xs text-secondary mb-0">{{ Str::limit($task->description, 50) }}</p>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $task->priority === 3 ? 'danger' : ($task->priority === 2 ? 'warning' : 'success') }}">
                                            {{ $task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low') }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        <span class="badge badge-sm bg-gradient-{{ $getTaskStatusColor($task->status) }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </td>
                                    <td class="align-middle text-center">
                                        @if($task->status !== 'completed' && $canEdit())
                                            <button wire:click="markTaskCompleted({{ $task->id }})" 
                                                    class="btn btn-sm btn-outline-success mb-0">
                                                <i class="fas fa-check"></i> Complete
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif
</div>
