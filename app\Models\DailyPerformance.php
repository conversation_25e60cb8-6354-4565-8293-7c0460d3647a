<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class DailyPerformance extends Model
{
    use HasFactory;

    protected $table = 'daily_performance';

    protected $fillable = [
        'user_id',
        'task_id',
        'department_id',
        'performance_date',
        'tasks_completed',
        'challenges_faced',
        'next_day_plan',
        'hours_worked',
        'overall_rating',
        'additional_notes',
        'is_submitted',
        'submitted_at',
        'superior_comments',
        'superior_rating',
        'rated_by',
        'rated_at',
    ];

    protected $casts = [
        'performance_date' => 'date',
        'hours_worked' => 'decimal:2',
        'is_submitted' => 'boolean',
        'submitted_at' => 'datetime',
        'rated_at' => 'datetime',
    ];

    /**
     * Get the user this performance record belongs to.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the task this performance is for.
     */
    public function task()
    {
        return $this->belongsTo(Task::class);
    }

    /**
     * Get the department this performance belongs to.
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Get the superior who rated this performance.
     */
    public function ratedBy()
    {
        return $this->belongsTo(User::class, 'rated_by');
    }

    /**
     * Scope to get submitted performance records.
     */
    public function scopeSubmitted($query)
    {
        return $query->where('is_submitted', true);
    }

    /**
     * Scope to get pending performance records.
     */
    public function scopePending($query)
    {
        return $query->where('is_submitted', false);
    }

    /**
     * Scope to get performance records for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('performance_date', $date);
    }

    /**
     * Scope to get performance records for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Submit the performance record.
     */
    public function submit()
    {
        $this->update([
            'is_submitted' => true,
            'submitted_at' => Carbon::now(),
        ]);
    }

    /**
     * Check if performance is submitted for today.
     */
    public static function isSubmittedForToday($userId)
    {
        return static::where('user_id', $userId)
                    ->where('performance_date', Carbon::today())
                    ->where('is_submitted', true)
                    ->exists();
    }

    /**
     * Get or create today's performance record for a user.
     */
    public static function getTodaysRecord($userId)
    {
        return static::firstOrCreate(
            [
                'user_id' => $userId,
                'performance_date' => Carbon::today(),
            ],
            [
                'is_submitted' => false,
            ]
        );
    }
}
