<?php if (isset($component)) { $__componentOriginal23a33f287873b564aaf305a1526eada4 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal23a33f287873b564aaf305a1526eada4 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.layout','data' => ['bodyClass' => 'g-sidenav-show bg-gray-50']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['bodyClass' => 'g-sidenav-show bg-gray-50']); ?>
    <?php if (isset($component)) { $__componentOriginaleeb4de73933bf6c97cffe74e5846276e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.sidebar','data' => ['activePage' => ''.e(request()->route('role') ?? 'dashboard').'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['activePage' => ''.e(request()->route('role') ?? 'dashboard').'']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $attributes = $__attributesOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__attributesOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e)): ?>
<?php $component = $__componentOriginaleeb4de73933bf6c97cffe74e5846276e; ?>
<?php unset($__componentOriginaleeb4de73933bf6c97cffe74e5846276e); ?>
<?php endif; ?>

<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg">
    <!-- Navbar -->
    <?php if (isset($component)) { $__componentOriginal778d3beb0063990dd56df93abee65235 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal778d3beb0063990dd56df93abee65235 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.navbars.navs.auth','data' => ['titlePage' => 'Dashboard']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('navbars.navs.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['titlePage' => 'Dashboard']); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $attributes = $__attributesOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__attributesOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal778d3beb0063990dd56df93abee65235)): ?>
<?php $component = $__componentOriginal778d3beb0063990dd56df93abee65235; ?>
<?php unset($__componentOriginal778d3beb0063990dd56df93abee65235); ?>
<?php endif; ?>
    <!-- End Navbar -->

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://rawgit.com/eKoopmans/html2pdf/master/dist/html2pdf.bundle.js"></script>


    <style>
        /* Modern Dashboard Styles */
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #6366f1;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --light-bg: #f8fafc;
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --border-radius: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Typography */
        .urdu-font {
            font-family: 'Jameel Noori Nastaleeq', serif;
            font-size: 18px;
            line-height: 1.6;
        }

        /* Viral Card Component */
        .select-viral-card {
            position: relative;
            cursor: pointer;
        }

        .select-viral-card .card {
            padding: 16px 20px;
            transition: var(--transition);
            border-radius: var(--border-radius);
            border: 1px solid #e5e7eb;
            background: white;
        }

        .select-viral-card .card:hover {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: var(--card-shadow-hover);
            transform: translateY(-2px);
        }

        .select-viral-card .tooltip-text {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            opacity: 0;
            transition: var(--transition);
            pointer-events: none;
            white-space: nowrap;
            z-index: 1000;
        }

        .select-viral-card:hover .tooltip-text {
            opacity: 1;
        }

        /* Status Colors */
        .status-red { background-color: #fef2f2; color: #dc2626; }
        .status-blue { background-color: #eff6ff; color: #2563eb; }
        .status-green { background-color: #f0fdf4; color: #16a34a; }
        .status-yellow { background-color: #fffbeb; color: #d97706; }

        /* Date Status */
        .not-assigned { color: var(--danger-color); font-weight: 500; }
        .future-date {
            color: var(--danger-color) !important;
            border: 2px solid var(--danger-color);
            border-radius: 6px;
            padding: 4px 8px;
        }
        .past-date {
            border: 2px solid var(--success-color);
            border-radius: 6px;
            padding: 4px 8px;
        }

        /* Table Styles */
        .modern-table {
            font-size: 16px;
            border-collapse: separate;
            border-spacing: 0;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--card-shadow);
        }

        .modern-table th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            font-weight: 600;
            color: #374151;
            padding: 16px;
            border-bottom: 2px solid #e5e7eb;
        }

        .modern-table td {
            padding: 14px 16px;
            border-bottom: 1px solid #f3f4f6;
            transition: var(--transition);
        }

        .modern-table tbody tr:hover {
            background-color: #f8fafc;
        }
        /* Modern Dashboard Cards */
        .dashboard-card {
            height: 100%;
            transition: var(--transition);
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            background: white;
            overflow: hidden;
            position: relative;
        }

        .dashboard-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--card-shadow-hover);
            border-color: var(--primary-color);
        }

        .dashboard-card .card-header {
            border-bottom: 1px solid #f1f5f9;
            padding: 30px 20px 20px 20px;
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            position: relative;
            min-height: auto;
            margin-top: 30px;
            text-align: center;
        }

        .dashboard-card .card-body {
            padding: 20px;
        }

        .dashboard-card .card-footer {
            background: #f8fafc;
            border-top: 1px solid #f1f5f9;
            padding: 12px 20px;
            color: #6b7280;
            font-size: 14px;
            margin-top: auto;
        }

        /* Stat Cards */
        .dashboard-stat-card {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 16px;
            transition: var(--transition);
            margin-bottom: 12px;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .dashboard-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: var(--transition);
        }

        .dashboard-stat-card:hover::before {
            opacity: 1;
        }

        .dashboard-stat-card h5, .dashboard-stat-card h6 {
            margin: 0;
            font-weight: 600;
            color: #374151;
            font-size: 16px;
        }

        .dashboard-stat-card:hover {
            background: white;
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: var(--card-shadow);
        }

        /* Section Spacing */
        .dashboard-section {
            margin-bottom: 40px;
        }

        /* Card Icons */
        .card-icon {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-radius: 12px;
            transition: var(--transition);
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
            width: 56px;
            height: 56px;
        }

        .dashboard-card:hover .card-icon {
            transform: translateX(-50%) scale(1.05);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
        }

        /* Modern Buttons */
        .btn-dashboard {
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 600;
            text-transform: none;
            font-size: 14px;
            transition: var(--transition);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn-dashboard:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }

        /* Gradient Backgrounds */
        .bg-gradient-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .bg-gradient-secondary { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .bg-gradient-success { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .bg-gradient-info { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .bg-gradient-warning { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }

        /* Avatar Styles */
        .avatar {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .avatar.avatar-sm {
            width: 32px;
            height: 32px;
        }

        /* Badge Improvements */
        .badge {
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        /* Loading Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .dashboard-card {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Remove extra gaps and improve layout */
        .dashboard-card .card-body {
            padding: 20px;
        }

        .dashboard-card .dashboard-stat-card:last-child {
            margin-bottom: 0;
        }

        /* Ensure proper spacing between stat cards */
        .dashboard-stat-card + .dashboard-stat-card {
            margin-top: 12px;
        }

        /* Remove extra spacing from folder date sections */
        .alert + .alert {
            margin-top: 0;
        }

        /* Compact table styling */
        .table-responsive {
            margin-bottom: 1rem !important;
        }

        .table-responsive:last-child {
            margin-bottom: 0 !important;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .dashboard-stat-card {
                min-height: 50px;
                padding: 12px;
            }

            .dashboard-card .card-header {
                padding: 20px 20px 15px 20px;
                text-align: center;
            }

            .card-icon {
                position: relative !important;
                top: 0 !important;
                left: 0 !important;
                margin: 0 auto 15px auto;
                display: block;
            }

            .urdu-font {
                font-size: 16px;
            }
        }

        /* Hover Effects */
        .table tbody tr:hover {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            transform: scale(1.01);
        }

        /* Custom Scrollbar */
        .table-responsive::-webkit-scrollbar {
            height: 8px;
        }

        .table-responsive::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }
    </style>
</style>
<?php
$userRoles = Auth::user()->roles->pluck('name')->toArray();
?>

    <!-- Main Dashboard Container -->
    <div class="container-fluid py-4">
        <!-- Dashboard Notifications -->
        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('dashboard-notifications', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-209244196-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

        <!-- Dashboard Stats Row -->
        <div class="row g-4 mb-4">
            <!-- Remaining Status Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card" onclick="showBigCard(1)" data-card-name="Remaining Status">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-info shadow-info text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">pending_actions</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Remaining Status</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('remaining-fatawa')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Remaining Folder
                                    <span class="badge bg-primary ms-2">
                                        <?php echo e($remainfolderDate->count() ?? 0); ?>

                                    </span>
                                </h6>
                            </div>

                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Remaining Fatawa
                                    <span class="badge bg-warning ms-2">
                                        <?php echo e($unselectedFiles->count() ?? 0); ?>

                                    </span>
                                </h6>
                            </div>
                        </a>

                        <!-- Talaq Remaining Section -->
                        <div class="dashboard-stat-card">
                            <a href="<?php echo e(route('talaq-remaining')); ?>" target="_blank"
                               class="btn bg-gradient-primary btn-dashboard w-100">
                                <i class="material-icons me-2">assignment</i>
                                Talaq Remaining
                                <span class="badge bg-white text-primary ms-2"><?php echo e($unselectedTalaq); ?></span>
                            </a>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-success me-2">trending_up</i>
                            <span class="text-sm">Total Till Today</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Received/Sent Fatawa Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card" onclick="showBigCard(2)" data-card-name="Fatawa Status">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-secondary shadow-secondary text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">inbox</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">
                                <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                                    Received Fatawa Status
                                <?php else: ?>
                                    Sent Fatawa Status
                                <?php endif; ?>
                            </h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                            <a href="<?php echo e(route('recived-fatawa')); ?>" class="text-decoration-none">
                        <?php else: ?>
                            <a href="<?php echo e(route('sent-for-checking')); ?>" class="text-decoration-none">
                        <?php endif; ?>
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">
                                    <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                                        Received Folder
                                    <?php else: ?>
                                        Sent Folder
                                    <?php endif; ?>
                                    <span class="badge bg-info ms-2">
                                        <?php echo e($recivedfolderDate->count() ?? 0); ?>

                                    </span>
                                </h6>
                            </div>

                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">
                                    <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                                        Received Fatawa
                                    <?php else: ?>
                                        Sent Fatawa
                                    <?php endif; ?>
                                    <span class="badge bg-secondary ms-2">
                                        <?php echo e($currentmonthrecived ?? 0); ?>

                                    </span>
                                </h6>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-info me-2">calendar_month</i>
                            <span class="text-sm">This Month</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Mahl-e-Nazar Fatawa Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card" onclick="showBigCard(3)" data-card-name="Mahl-e-Nazar Status">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-warning shadow-warning text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">help_outline</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Mahl-e-Nazar Fatawa</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('mahlenazar-fatawa')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card" style="min-height: 80px;">
                                <div class="text-center">
                                    <h6 class="urdu-font mb-2">Total Fatawa</h6>
                                    <span class="badge bg-warning text-dark fs-6">
                                        <?php echo e($netmahlenazar ?? 0); ?>

                                    </span>
                                </div>
                            </div>
                        </a>

                        <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                            <div class="dashboard-stat-card">
                                <a href="<?php echo e(route('transferred-fatawa')); ?>"
                                   class="btn bg-gradient-info btn-dashboard w-100">
                                    <i class="material-icons me-2">swap_horiz</i>
                                    Manage Transfers
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-warning me-2">analytics</i>
                            <span class="text-sm">Total Count</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Sending Fatawa Mail Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card" onclick="showBigCard(4)" data-card-name="Mail Operations">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-success shadow-success text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">mail_outline</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Mail Operations</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="dashboard-stat-card">
                            <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                                <a href="<?php echo e(route('sending-fatawa')); ?>" class="btn bg-gradient-primary btn-dashboard w-100">
                                    <i class="material-icons me-2">send</i>
                                    Send Mail to Mujeeb
                                </a>
                            <?php else: ?>
                                <a href="<?php echo e(route('create', ['checker' => 'mufti_ali_asghar'])); ?>" class="btn bg-gradient-primary btn-dashboard w-100">
                                    <i class="material-icons me-2">send</i>
                                    Send Mail to Mufti Sahib
                                </a>
                            <?php endif; ?>
                        </div>

                        <?php if(in_array('Admin', Auth::user()->roles->pluck('name')->toArray())): ?>
                            <div class="dashboard-stat-card">
                                <a href="<?php echo e(route('mutakhassis')); ?>" class="btn bg-gradient-secondary btn-dashboard w-100">
                                    <i class="material-icons me-2">transform</i>
                                    Transfer Fatawa
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-success me-2">schedule</i>
                            <span class="text-sm">Daily Operations</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Management Systems Row (Admin/Superior Only) -->
        <?php if(in_array('Admin', $userRoles) || in_array('Superior', $userRoles)): ?>
        <div class="row g-4 dashboard-section">
            <!-- Task Management Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-info shadow-info text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">task</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Task Management</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('workflow-tasks.index')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Active Tasks
                                    <span class="badge bg-info ms-2" id="active-tasks-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Pending Tasks
                                    <span class="badge bg-warning ms-2" id="pending-tasks-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-info me-2">assignment</i>
                            <span class="text-sm">Team Tasks</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Management Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-success shadow-success text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">analytics</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Performance Reports</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('performance-management')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Today's Reports
                                    <span class="badge bg-success ms-2" id="todays-reports-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Pending Reports
                                    <span class="badge bg-warning ms-2" id="pending-reports-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-success me-2">trending_up</i>
                            <span class="text-sm">Team Performance</span>
                        </div>
                    </div>
                </div>
            </div>

            <?php if(in_array('Admin', $userRoles)): ?>
            <!-- Mahl-e-Nazar Limits Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-warning shadow-warning text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">shield</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Limit Control</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('mahl-e-nazar-limits')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">At Limit
                                    <span class="badge bg-warning ms-2" id="at-limit-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Restricted
                                    <span class="badge bg-danger ms-2" id="restricted-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-warning me-2">security</i>
                            <span class="text-sm">12 Fatawa Limit</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Team Management Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-secondary shadow-secondary text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">group</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Team Management</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('supervisor-assistant-mapping')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Active Superiors
                                    <span class="badge bg-secondary ms-2" id="superiors-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Team Members
                                    <span class="badge bg-info ms-2" id="assistants-count">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-secondary me-2">people</i>
                            <span class="text-sm">Superior-Muawin</span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <!-- Personal Dashboard Section (All Users) -->
        <div class="row g-4 dashboard-section">
            <!-- My Performance Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-primary shadow-primary text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">assignment_turned_in</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">My Performance</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('daily-performance.create')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Today's Status
                                    <span class="badge bg-primary ms-2" id="my-performance-status">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">My Limit Status
                                    <span class="badge bg-info ms-2" id="my-limit-status">
                                        <i class="fas fa-spinner fa-spin"></i>
                                    </span>
                                </h6>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-primary me-2">person</i>
                            <span class="text-sm">Personal Dashboard</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reception Status Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card" onclick="showBigCard(5)" data-card-name="Reception Status">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-info shadow-info text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">meeting_room</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Reception Status</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('reciption-fatawa')); ?>" class="text-decoration-none">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Total</small>
                                            <h6 class="mb-0 text-primary"><?php echo e($totalQuestionsCount ?? 0); ?></h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Sent</small>
                                            <h6 class="mb-0 text-success"><?php echo e($sendToMufti ?? 0); ?></h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Monthly</small>
                                            <h6 class="mb-0 text-info"><?php echo e($monthlyQuestionsCount ?? 0); ?></h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">M. Sent</small>
                                            <h6 class="mb-0 text-warning"><?php echo e($monthlysendToMufti ?? 0); ?></h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-primary me-2">info</i>
                            <span class="text-sm">Excluding OK Status</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Checked Fatawa Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-warning shadow-warning text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">done_all</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Checked Fatawa</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('sent-fatawa')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Checked Folder
                                    <span class="badge bg-warning text-dark ms-2">
                                        <?php echo e($checkedfolderDate->count() ?? 0); ?>

                                    </span>
                                </h6>
                            </div>

                            <div class="dashboard-stat-card">
                                <h6 class="urdu-font">Checked Fatawa
                                    <span class="badge bg-success ms-2">
                                        <?php echo e($currentmonthchecked ?? 0); ?>

                                    </span>
                                </h6>
                            </div>
                        </a>

                        <div class="dashboard-stat-card">
                            <a href="<?php echo e(route('talaq-checked')); ?>" target="_blank"
                               class="btn bg-gradient-success btn-dashboard w-100">
                                <i class="material-icons me-2">verified</i>
                                Talaq Checked
                                <span class="badge bg-white text-success ms-2"><?php echo e($currentmonthTalaqchecked); ?></span>
                            </a>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-warning me-2">calendar_month</i>
                            <span class="text-sm">Monthly Statistics</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Today Checked Fatawa Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-success shadow-success text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">today</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Today's Progress</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('sent-fatawa')); ?>" class="text-decoration-none">
                            <div class="dashboard-stat-card" style="min-height: 120px;">
                                <div class="text-center">
                                    <div class="mb-2">
                                        <i class="material-icons text-success" style="font-size: 2rem;">check_circle</i>
                                    </div>
                                    <h6 class="urdu-font mb-2">Today Checked</h6>
                                    <span class="badge bg-success fs-5">
                                        <?php echo e($todayChecked ?? 0); ?>

                                    </span>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-success me-2">schedule</i>
                            <span class="text-sm">Daily Progress</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Viral Fatawa Status Card -->
            <div class="col-xl-3 col-lg-6 col-md-6 col-sm-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="icon icon-lg icon-shape bg-gradient-info shadow-info text-center border-radius-xl card-icon">
                            <i class="material-icons opacity-10">trending_up</i>
                        </div>
                        <div class="text-center mb-3">
                            <h6 class="fw-bold text-dark mb-0">Viral Fatawa Status</h6>
                        </div>
                    </div>
                    <div class="card-body">
                        <a href="<?php echo e(route('selected-viral')); ?>" class="text-decoration-none">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Selected</small>
                                            <h6 class="mb-0 text-info"><?php echo e($totalselectedviral ?? 0); ?></h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Sent</small>
                                            <h6 class="mb-0 text-primary"><?php echo e($totalsentsviral ?? 0); ?></h6>
                                            <?php if($totalselectedviral > 0 && $totalsentsviral > 0): ?>
                                                <small class="text-warning">Bal: <?php echo e($totalselectedviral - $totalsentsviral); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Viral</small>
                                            <h6 class="mb-0 text-success"><?php echo e($totalviral ?? 0); ?></h6>
                                            <?php if($totalsentsviral > 0 && $totalviral > 0): ?>
                                                <small class="text-warning">Bal: <?php echo e($totalsentsviral - $totalviral); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="dashboard-stat-card">
                                        <div class="text-center">
                                            <small class="text-muted">Uploaded</small>
                                            <h6 class="mb-0 text-warning"><?php echo e($totalwupload ?? 0); ?></h6>
                                            <?php if($totalsentsviral > 0 && $totalwupload > 0): ?>
                                                <small class="text-info">Bal: <?php echo e($totalsentsviral - $totalwupload); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex align-items-center">
                            <i class="material-icons text-info me-2">analytics</i>
                            <span class="text-sm">Total Statistics</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            <!-- <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                <div class="card">
                    <div class="card-header p-3 pt-2">
                        <div
                            class="icon icon-lg icon-shape bg-gradient-info shadow-info text-center border-radius-xl mt-n5 position-absolute">
                            <i class="material-icons opacity-10">weekend</i>
                        </div>
                        <div class="pt-1">

                            <div class=" mb-0 text-capitalize mb-0 text-center"><h6>Zahr-e-Thaqiq</h6></div>

                                <div class="card custom-card mt-3" onclick="showBigCard(7)" style="height: 112px;" data-card-name="Pending Status">
                                <p class=" mb-0 text-capitalize mb-0"><h6>Zahr-e-Thaqiq(
                                <?php if($totalnotsent > 0): ?>
                                    <?php echo e($totalnotsent); ?>

                                    <?php endif; ?>)</h6></p>
                                </div>

                        </div>
                    </div>
                    <hr class="dark horizontal my-0">
                    <div class="card-footer p-3">
                        <p class="mb-0"><span class="text-success  font-weight-bolder"></span>
                            Total Till Today</p>
                    </div>
                </div>
            </div>
            <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                <div class="card">
                    <div class="card-header p-3 pt-2">
                        <div
                            class="icon icon-lg icon-shape bg-gradient-info shadow-info text-center border-radius-xl mt-n5 position-absolute">
                            <i class="material-icons opacity-10">weekend</i>
                        </div>
                        <div class="pt-1">

                            <div class=" mb-0 text-capitalize mb-0 text-center"><h6>Viral List</h6></div>

                                <div class="card custom-card mt-3" onclick="showBigCard(7)" style="height: 112px;" data-card-name="Pending Status">
                                <p class=" mb-0 text-capitalize mb-0"><h6>Viral List(
                                <?php if($totalnotsent > 0): ?>
                                    <?php echo e($totalnotsent); ?>

                                    <?php endif; ?>)</h6></p>
                                </div>

                        </div>
                    </div>
                    <hr class="dark horizontal my-0">
                    <div class="card-footer p-3">
                        <p class="mb-0"><span class="text-success  font-weight-bolder"></span>
                            Total Till Today</p>
                    </div>
                </div>
            </div> -->


            <?php
// Get the user roles
$userRoles = auth()->user()->roles ?? [];

// Get the current route name
$currentRoute = Route::currentRouteName();

// Concatenate $currentRoute and $role
$currentUrl = $currentRoute . '/' . $role;


?>

        <?php if(count($userRoles) > 1 && $currentUrl == 'dashboard/'): ?>
        <!-- Summary Report Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <div class="icon icon-shape bg-gradient-primary shadow-primary text-center border-radius-xl me-3">
                                <i class="material-icons opacity-10">folder_open</i>
                            </div>
                            <div>
                                <h5 class="mb-0 text-dark fw-bold">Received Fatawa Folders</h5>
                                <p class="text-sm text-muted mb-0">Pending for checking and review</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table modern-table urdu-font">
                                <thead>
                                    <tr>
                                        <th class="text-secondary fw-bold">
                                            <i class="material-icons me-2">business</i>Darulifta
                                        </th>
                                        <th class="text-secondary fw-bold text-center">
                                            <i class="material-icons me-2">pending</i>Folders Pending Check
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $summaryReport; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="fw-medium text-dark">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-sm bg-gradient-secondary rounded-circle me-3">
                                                        <span class="text-white text-xs fw-bold">
                                                            <?php echo e(substr($item['Darulifta'], 0, 2)); ?>

                                                        </span>
                                                    </div>
                                                    <?php echo e($item['Darulifta']); ?>

                                                </div>
                                            </td>
                                            <td>
                                                <div class="d-flex justify-content-center flex-wrap gap-2">
                                                    <?php if(!empty($item['Mail Recived'])): ?>
                                                        <?php $__currentLoopData = explode('  |  ', $item['Mail Recived']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $date): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php if(!empty(trim($date))): ?>
                                                                <span class="badge bg-gradient-info text-white rounded-pill px-3 py-2">
                                                                    <i class="material-icons me-1" style="font-size: 14px;">event</i>
                                                                    <?php echo e(trim($date)); ?>

                                                                </span>
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php else: ?>
                                                        <span class="badge bg-light text-secondary border rounded-pill px-3 py-2">
                                                            <i class="material-icons me-1" style="font-size: 14px;">info</i>
                                                            No pending dates
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Folder Information -->
        <div class="row mt-4">
            <div class="col-12">
                <?php
                $gradientColors = [
                    'bg-gradient-primary',
                    'bg-gradient-info',
                    'bg-gradient-secondary',
                    'bg-gradient-success'
                ];
                $iconColors = [
                    'text-primary',
                    'text-info',
                    'text-secondary',
                    'text-success'
                ];
                $colorIndex = 0;
                ?>

                <?php $__currentLoopData = $mailfolderDates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mailfolderDate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <!-- Received Folder Date Header -->
                <div class="alert alert-primary border-0 mb-3" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <h5 class="text-white mb-0 fw-bold">
                        <i class="material-icons me-2" style="vertical-align: middle;">folder</i>
                        Received Folder Date: <?php echo e($mailfolderDate); ?>

                    </h5>
                </div>

                <?php if(empty($dataByDaruliftaName)): ?>
                    <div class="alert alert-info border-0 mb-4">
                        <div class="d-flex align-items-center">
                            <i class="material-icons me-3">info</i>
                            <span>No data found for the selected criteria.</span>
                        </div>
                    </div>
                <?php else: ?>
                    <?php $__currentLoopData = $daruliftaNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $daruliftaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if(isset($dataByDaruliftaName[$daruliftaName][$mailfolderDate])): ?>
                            <!-- Darulifta Name Header -->
                            <div class="bg-light p-3 rounded mb-3">
                                <h6 class="text-dark fw-bold mb-0"><?php echo e($daruliftaName); ?></h6>
                            </div>

                            <!-- Data Table -->
                            <div class="table-responsive mb-4">
                                <table class="table table-striped urdu-font">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="text-center">#</th>
                                            <th>File Code</th>
                                            <th>Sender</th>
                                            <th>Fatwa Type</th>
                                            <th>Sending Time & Date</th>
                                            <th>Category</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $serialNumber = 1; ?>
                                        <?php $__currentLoopData = $dataByDaruliftaName[$daruliftaName][$mailfolderDate]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="text-center"><?php echo e($serialNumber++); ?></td>
                                                <td>
                                                    <span class="fw-medium text-dark"><?php echo e($item->file_code); ?></span>
                                                    <?php if($item->source === 'talaq'): ?>
                                                        <span class="badge bg-warning text-dark ms-2">Talaq</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-secondary"><?php echo e($item->sender); ?></td>
                                                <td>
                                                    <span class="badge bg-info text-white"><?php echo e($item->ftype); ?></span>
                                                </td>
                                                <td class="text-secondary"><?php echo e($item->mail_recived_date); ?></td>
                                                <td>
                                                    <span class="badge bg-secondary text-white"><?php echo e($item->category); ?></span>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <!-- No Data Message -->
                            <div class="alert alert-light border mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="material-icons me-3 text-muted">info</i>
                                    <span class="text-muted">No data available for <?php echo e($daruliftaName); ?></span>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if (isset($component)) { $__componentOriginalf30276552b63aa6c9559a1667ce359f9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf30276552b63aa6c9559a1667ce359f9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.footers.auth','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('footers.auth'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $attributes = $__attributesOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__attributesOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf30276552b63aa6c9559a1667ce359f9)): ?>
<?php $component = $__componentOriginalf30276552b63aa6c9559a1667ce359f9; ?>
<?php unset($__componentOriginalf30276552b63aa6c9559a1667ce359f9); ?>
<?php endif; ?>
    </div>
</main>
<?php if (isset($component)) { $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.plugins','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('plugins'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?> <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $attributes = $__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__attributesOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69)): ?>
<?php $component = $__componentOriginal68b5b6b5278ad5d9ad739656255d7f69; ?>
<?php unset($__componentOriginal68b5b6b5278ad5d9ad739656255d7f69); ?>
<?php endif; ?>
</div>
<?php
// Get the user's roles
$userRoles = auth()->user()->roles ?? [];

// Extract only the role names
$roleNames = $userRoles->pluck('name')->toArray();

// Set the default role to null
$defaultRole = null;

// Check if the user has less than two roles
if (count($roleNames) < 2) {
    // Use the first role as the default role
    $defaultRole = $roleNames[0] ?? null;
}
?>

<script>
/**
 * Dashboard JavaScript Functions
 * Modern and optimized code for dashboard functionality
 */

// Global Variables
const userRoles = <?php echo json_encode($roleNames, 15, 512) ?>;
const defaultRole = <?php echo json_encode($defaultRole, 15, 512) ?>;

// Dashboard Initialization
$(document).ready(function() {
    console.log('Dashboard initialized with role:', defaultRole);

    // Initialize filters
    initializeFilters();

    // Initialize date handling
    initializeDateHandling();

    // Add smooth scrolling to cards
    addSmoothScrolling();

    // Load dashboard widgets
    loadDashboardWidgets();
});

// Load Dashboard Widgets
function loadDashboardWidgets() {
    // Load task statistics
    loadTaskStatistics();

    // Load performance statistics
    loadPerformanceStatistics();

    // Load Mahl-e-Nazar statistics
    loadMahlENazarStatistics();

    // Load team statistics
    loadTeamStatistics();

    // Load personal dashboard data
    loadPersonalDashboard();
}

// Task Statistics
function loadTaskStatistics() {
    if ($('#active-tasks-count').length) {
        $.get('/workflow-tasks-statistics')
            .done(function(data) {
                $('#active-tasks-count').html(data.active_tasks || 0);
                $('#pending-tasks-count').html(data.pending_tasks || 0);
            })
            .fail(function() {
                $('#active-tasks-count').html('--');
                $('#pending-tasks-count').html('--');
            });
    }
}

// Performance Statistics
function loadPerformanceStatistics() {
    if ($('#todays-reports-count').length) {
        $.get('/performance-statistics')
            .done(function(data) {
                $('#todays-reports-count').html(data.submitted_today || 0);
                $('#pending-reports-count').html(data.pending_today || 0);
            })
            .fail(function() {
                $('#todays-reports-count').html('--');
                $('#pending-reports-count').html('--');
            });
    }
}

// Mahl-e-Nazar Statistics
function loadMahlENazarStatistics() {
    if ($('#at-limit-count').length) {
        $.get('/mahl-e-nazar-statistics')
            .done(function(data) {
                $('#at-limit-count').html(data.users_at_limit || 0);
                $('#restricted-count').html(data.users_with_restrictions || 0);
            })
            .fail(function() {
                $('#at-limit-count').html('--');
                $('#restricted-count').html('--');
            });
    }
}

// Team Statistics
function loadTeamStatistics() {
    if ($('#superiors-count').length) {
        $.get('/supervisor-assistant-statistics')
            .done(function(data) {
                $('#superiors-count').html(data.total_superiors || 0);
                $('#assistants-count').html(data.total_assistants || 0);
            })
            .fail(function() {
                $('#superiors-count').html('--');
                $('#assistants-count').html('--');
            });
    }
}

// Personal Dashboard
function loadPersonalDashboard() {
    if ($('#my-performance-status').length) {
        // Check today's performance submission
        $.get('/check-todays-submission')
            .done(function(data) {
                if (data.has_submitted) {
                    $('#my-performance-status').removeClass('bg-warning').addClass('bg-success').html('Submitted');
                } else {
                    $('#my-performance-status').removeClass('bg-success').addClass('bg-warning').html('Pending');
                }
            })
            .fail(function() {
                $('#my-performance-status').html('--');
            });
    }

    if ($('#my-limit-status').length) {
        // Check Mahl-e-Nazar limit status
        $.get('/check-submission-eligibility')
            .done(function(data) {
                const remaining = data.remaining || 0;
                const limit = data.limit || 12;

                if (remaining <= 0) {
                    $('#my-limit-status').removeClass('bg-info bg-warning').addClass('bg-danger').html('At Limit');
                } else if (remaining <= 2) {
                    $('#my-limit-status').removeClass('bg-info bg-danger').addClass('bg-warning').html(`${remaining} Left`);
                } else {
                    $('#my-limit-status').removeClass('bg-warning bg-danger').addClass('bg-info').html(`${remaining}/${limit}`);
                }
            })
            .fail(function() {
                $('#my-limit-status').html('--');
            });
    }
}

// Filter Functions
function initializeFilters() {
    // Apply filters on page load
    applyFilters();

    // Bind change events
    $("#timeframe1, #mujeebInput1, #status, #mujeebInput").change(function() {
        applyFilters();
    });
}

function applyFilters() {
    const selectedTimeframe = $("#timeframe1").val();
    const selectedMujeeb = $("#mujeebInput1").val();
    const currentMonth = "<?php echo e(\Carbon\Carbon::now()->format('Y-m')); ?>";

    // Filter data rows
    $(".data-row-r").each(function() {
        const recDate = $(this).data("rec-date-r");
        const shouldShow = selectedTimeframe === "all" ||
                          (selectedTimeframe === "this_month" && recDate.includes(currentMonth));
        $(this).toggle(shouldShow);
    });

    // Filter mujeeb columns
    $(".mujeeb-class-r").each(function() {
        const mujeebText = $(this).text().toLowerCase();
        const shouldShow = !selectedMujeeb || mujeebText.includes(selectedMujeeb.toLowerCase());
        $(this).closest("tr").toggle(shouldShow);
    });
}

// Card Display Functions
function showBigCard(cardNumber) {
    // Remove active class from all cards
    $('.nav-link').removeClass('active');

    // Add active class to clicked card
    $(`[data-card-number="${cardNumber}"]`).addClass('active');

    // Get card name and update sidebar
    const cardName = $(`[data-card-name][onclick="showBigCard(${cardNumber})"]`).data('card-name');
    $('#clickedCardName').text(cardName);

    // Hide all big cards and show selected one
    for (let i = 1; i <= 9; i++) {
        const card = document.getElementById(`big-card-${i}`);
        if (card) card.style.display = i === cardNumber ? 'block' : 'none';
    }
}
// Toggle Header Functions
$(document).on('click', '.toggle-header', function() {
    const toggleElement = $(this).next('.toggle-content');
    const arrowElement = $(this).find('span.toggle-arrow');

    // Toggle visibility with smooth animation
    toggleElement.slideToggle(300);

    // Update arrow symbol
    setTimeout(() => {
        arrowElement.text(toggleElement.is(':visible') ? '▲' : '▼');
    }, 150);

    // Handle child toggles
    if ($(this).hasClass('toggle-child')) {
        $(this).nextUntil('.toggle-child').slideToggle(300);
    }
});
// Date Handling Functions
function initializeDateHandling() {
    const today = new Date();
    const currentDate = today.toISOString().split('T')[0];
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 2);
    const firstDayFormatted = firstDay.toISOString().split('T')[0];

    // Set default date values if elements exist
    const endDateEl = document.getElementById('endDate');
    const startDateEl = document.getElementById('startDate');

    if (endDateEl) {
        endDateEl.value = currentDate;
        endDateEl.addEventListener('input', handleFiltering);
    }

    if (startDateEl) {
        startDateEl.value = firstDayFormatted;
        startDateEl.addEventListener('input', handleFiltering);
    }

    // Initial filtering
    handleFiltering();
}

// Main Filtering Function
function handleFiltering() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');

    if (!startDateEl || !endDateEl) return;

    const startDate = startDateEl.value;
    const endDate = endDateEl.value;

    // Filter cards by date range
    filterCardsByDate(startDate, endDate);

    // Filter table rows by status and mujeeb
    filterTableRows();
}

function filterCardsByDate(startDate, endDate) {
    const cards = document.querySelectorAll('.card.z-index-2.mb-3');

    cards.forEach(card => {
        const dataRow = card.querySelector('.data-row');
        if (!dataRow) return;

        const cardDate = dataRow.getAttribute('data-rec-date');
        const shouldShow = isDateInRange(cardDate, startDate, endDate);
        card.style.display = shouldShow ? '' : 'none';
    });
}

function isDateInRange(cardDate, startDate, endDate) {
    if (!startDate && !endDate) return true;
    if (!startDate) return cardDate <= endDate;
    if (!endDate) return cardDate >= startDate;
    return startDate <= cardDate && cardDate <= endDate;
}

function filterTableRows() {
    const selectedStatus = $("#status").val();
    const selectedMujeeb = $("#mujeebInput").val()?.toLowerCase() || '';

    // Filter status columns
    $(".status-column").each(function() {
        const statusCell = $(this);
        const row = statusCell.closest("tr");
        const mujeebCell = row.find(".mujeeb-column");

        const statusText = statusCell.text().trim().toLowerCase();
        const mujeebText = mujeebCell.text().toLowerCase();

        const statusMatch = selectedStatus !== "exclude_ok" || statusText !== "ok";
        const mujeebMatch = !selectedMujeeb || mujeebText.includes(selectedMujeeb);

        row.toggle(statusMatch && mujeebMatch);
    });

    // Filter mujeeb columns across different tables
    for (let i = 0; i <= 7; i++) {
        $(`.table${i > 0 ? i : ''} .mujeeb-column${i > 0 ? i : '0'}`).each(function() {
            const mujeebCell = $(this);
            const mujeebText = mujeebCell.text().toLowerCase();
            const shouldShow = !selectedMujeeb || mujeebText.includes(selectedMujeeb);
            mujeebCell.closest("tr").toggle(shouldShow);
        });
    }
}
// Navigation Functions
function redirectToDashboard() {
    const fatwaNoInput = document.getElementById('fatwa_noInput');
    if (!fatwaNoInput) return;

    const fatwaNo = fatwaNoInput.value;
    const role = '<?php echo e($role ?? ""); ?>';
    const roleParam = defaultRole ? encodeURIComponent(defaultRole) : role;

    const url = fatwaNo ?
        `/dashboard/${roleParam}/${encodeURIComponent(fatwaNo)}` :
        '/dashboard';

    window.location.href = url;
}

// Utility Functions
function addSmoothScrolling() {
    // Add smooth scrolling to dashboard cards
    $('.dashboard-card').on('click', function() {
        $(this).addClass('clicked');
        setTimeout(() => $(this).removeClass('clicked'), 200);
    });
}

// Performance optimization - debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimized filtering with debounce
const debouncedFiltering = debounce(handleFiltering, 300);

// Initialize event listeners with debounced filtering
$("#status, #mujeebInput, #startDate, #endDate").on('change input', debouncedFiltering);

// Add loading states for better UX
function showLoadingState() {
    $('.dashboard-card').addClass('loading');
}

function hideLoadingState() {
    $('.dashboard-card').removeClass('loading');
}

// Add CSS class for loading state
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .dashboard-card.loading {
            opacity: 0.7;
            pointer-events: none;
        }
        .dashboard-card.clicked {
            transform: scale(0.98);
        }
    `)
    .appendTo('head');

console.log('Dashboard JavaScript loaded successfully');
</script>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $attributes = $__attributesOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__attributesOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal23a33f287873b564aaf305a1526eada4)): ?>
<?php $component = $__componentOriginal23a33f287873b564aaf305a1526eada4; ?>
<?php unset($__componentOriginal23a33f287873b564aaf305a1526eada4); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/dashboard/index.blade.php ENDPATH**/ ?>