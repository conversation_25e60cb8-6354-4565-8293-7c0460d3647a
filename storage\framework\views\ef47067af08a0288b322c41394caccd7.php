<?php
    $totalCounts = 0;
    $overallFolderCount = 0;

    // Calculate statistics from remainingFatawa data
    if (isset($remainingFatawa)) {
        foreach ($remainingFatawa as $daruliftaName => $folders) {
            foreach ($folders as $folderId => $files) {
                // Handle both arrays and collections
                $fileCount = is_array($files) ? count($files) : $files->count();
                $totalCounts += $fileCount;
                $overallFolderCount++;
            }
        }
    }

    // Calculate statistics from sendingFatawa data if selectedmufti is 'all'
    if ($selectedmufti == 'all' && isset($sendingFatawa)) {
        $totalCounts = 0;
        $overallFolderCount = 0;

        foreach ($sendingFatawa as $daruliftaName => $checkers) {
            foreach ($checkers as $checker => $dates) {
                foreach ($dates as $date => $files) {
                    // Handle both arrays and collections
                    $fileCount = is_array($files) ? count($files) : $files->count();
                    $totalCounts += $fileCount;
                    $overallFolderCount++;
                }
            }
        }
    }
?>

<!-- Summary Statistics -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value" id="totalFatawaCount"><?php echo e($totalCounts); ?></div>
        <div class="stat-label">
            <i class="fas fa-file-alt me-1"></i>
            Total Remaining Fatawa
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="totalFolderCount"><?php echo e($overallFolderCount); ?></div>
        <div class="stat-label">
            <i class="fas fa-folder me-1"></i>
            Total Folders
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?php echo e(count($daruliftaNames)); ?></div>
        <div class="stat-label">
            <i class="fas fa-building me-1"></i>
            Active Daruliftaas
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-value"><?php echo e(count($mujeebs)); ?></div>
        <div class="stat-label">
            <i class="fas fa-users me-1"></i>
            Active Mujeebs
        </div>
    </div>
</div>
<?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/partials/remaining-fatawa-stats.blade.php ENDPATH**/ ?>