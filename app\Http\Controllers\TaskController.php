<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
class TaskController extends Controller
{
    public function index()
    {
        $tasks = DB::table('tasks')->get();

        return view('tasks.index', compact('tasks'));
    }

    public function create()
    {
        return view('pages.task');
    }

    public function store(Request $request)
    {
        $sdate = $request->input('start_date');
        $edate = $request->input('end_date');
        $status = $request->input('status');
        $priority = $request->input('priority');
        $summary = $request->input('summary');
        
    
        // Assuming you have a 'tasks' table
        foreach ($sdate as $key => $date) {
            $selectedstatus = $status[$key];
            $selectedpriority = $priority[$key];


            DB::table('tasks')->insert([
                'start_date' => $date,
                'end_date' => $edate[$key],
                'status' => $selectedstatus,
                'priority' => $selectedpriority,
                'summary' => $summary[$key],
                
            ]);
        }
    
        return redirect()->back()->with('success', 'Tasks added successfully.');
    }
    public function edit($id)
{
    $task = DB::table('tasks')->find($id);

    if (!$task) {
        return redirect()->route('tasks.index')->with('error', 'Task not found.');
    }

    return view('pages.edit-task', compact('task'));
}
public function update(Request $request, $id)
{
    $data = $request->validate([
        'start_date' => 'required|date',
        'end_date' => 'required|date',
        'status' => 'required|integer',
        'priority' => 'required|integer',
        'summary' => 'required|string',
    ]);

    DB::table('tasks')->where('id', $id)->update($data);

    return redirect()->route('tasks.index')->with('success', 'Task updated successfully.');
}
public function destroy($id)
{
    DB::table('tasks')->where('id', $id)->delete();

    return redirect()->route('tasks.index')->with('success', 'Task deleted successfully.');
}
}
