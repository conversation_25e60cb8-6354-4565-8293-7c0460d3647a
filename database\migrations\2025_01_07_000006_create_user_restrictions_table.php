<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_restrictions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('restriction_type', ['performance_not_submitted', 'fatawa_limit_exceeded', 'manual_lock']);
            $table->text('reason')->nullable();
            $table->boolean('is_active')->default(true);
            $table->foreignId('restricted_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('restricted_at')->useCurrent();
            $table->foreignId('lifted_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('lifted_at')->nullable();
            $table->text('lift_reason')->nullable();
            $table->timestamps();
            
            // Indexes for efficient queries
            $table->index(['user_id', 'is_active']);
            $table->index(['restriction_type', 'is_active']);
            $table->index(['restricted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_restrictions');
    }
};
