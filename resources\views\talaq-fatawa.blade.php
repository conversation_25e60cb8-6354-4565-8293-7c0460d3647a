<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلاق فتاوی مینجمنٹ</title>
    <style>
        .modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #f0e6e6;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}
body {
            direction: rtl;
            text-align: right;
            font-family: 'Jameel Noori Nastaleeq', serif;
        }
.card.dragging {
    opacity: 0.5;
    border: 2px dashed #007bff;
}
.save-button {
    margin-top: 20px;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 5px;
}

.save-button:hover {
    background-color: #0056b3;
}

 .records-grid {
    display: grid;
    grid-template-columns: repeat(3, minmax(300px, 1fr)); /* Fixed to 3 columns with min width 300px */
    gap: 20px; /* Spacing between cards */
    margin-top: 20px;
    width: 100%; /* Ensure it takes the container's width */
    max-width: 100%; /* Prevent overflow */
}
    /* Card Styling */
    .card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        background: #fff;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* Fixed Size for Child Cards */
    .main-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Child card details adjustments */
.child-content {
    position: relative;
    margin: 0; /* Removes margins */
    padding: 0; /* Removes paddings */
    white-space: normal; /* Collapses extra spaces */
}

.child-content {
    position: relative;
    margin: 0; /* Removes margins */
    padding: 0; /* Removes paddings */
    white-space: normal; /* Collapses extra spaces */
    /* display: flex; */
    /* flex-direction: column; */
    /* gap: 5px; */
}
.child-card {
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    padding: 10px;
    margin-bottom: 10px; /* Add spacing between child cards */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: block; /* Ensure it doesn't inherit grid layout */
    direction: rtl; /* Maintain RTL alignment */
    text-align: right;
    height: 200px;
    overflow: hidden;
    transition: height 0.3s ease; /* Smooth height transition */
}
.child-card .main-content {
    display: block; /* Disable flex/grid for child cards */
}
.child-card.expanded {
    height: auto;
}

.extra-details {
    display: none;
    margin-top: 10px;
    color: #555;
    font-family: 'Jameel Noori Nastaleeq', serif; /* Urdu font for details */
}

.child-card.expanded .extra-details {
    display: block;
}

/* Button adjustments */
.toggle-details {
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.toggle-details:hover {
    background-color: #0056b3;
}

    /* Button Styling */
    button {
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 5px 10px;
        cursor: pointer;
    }

    button:hover {
        background-color: #0056b3;
    }
        @font-face {
            font-family: 'Jameel Noori Nastaleeq';
            src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');
        }
        @font-face {
            font-family: 'Naskh Unicode';
            src: url('/storage/fonts/UrduNaskhUnicode.ttf');
        }
        @font-face {
            font-family: 'Al_Mushaf';
            src: url('/storage/fonts/Al_Mushaf.ttf');
        }
    </style>
<!-- <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"> -->
<!-- jQuery and Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <!-- FontAwesome CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <!-- <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script> -->

</head>
<body>
<div class="container">
<h1>طلاق فتاوی مینجمنٹ</h1>

    <!-- Header Input -->
    <h2>مرکزی عنوان</h2>
    <form action="{{ url('/talaq-fatawa/save-header') }}" method="POST">
        @csrf
        <input type="hidden" name="headerId" value="{{ old('headerId') }}">
        <input type="text" name="headerName" placeholder="مرکزی عنوان" value="{{ old('headerName') }}">
        <input type="number" name="headerSerialNumber" placeholder="سریل نمبر" value="{{ old('headerSerialNumber') }}">
        <button type="submit">محفوظ کریں</button>
    </form>

    <!-- Parent Input -->
    <h2>موضوع</h2>
    <form action="{{ url('/talaq-fatawa/save-parent') }}" method="POST">
        @csrf
        <input type="hidden" name="parentId" value="{{ old('parentId') }}">
        <input type="text" name="parentName" placeholder="موضوع" value="{{ old('parentName') }}">
        <input type="number" name="parentSerialNumber" placeholder="سریل نمبر" value="{{ old('parentSerialNumber') }}">
        <select name="parentHeaderId">
            <option value="">مرکزی عنوان منتخب کریں</option>
            @foreach($headers as $header)
                <option value="{{ $header->id }}">{{ $header->name }}</option>
            @endforeach
        </select>
        <button type="submit">محفوظ کریں</button>
    </form>

    <!-- Child Input -->
    <h2>جزئیات</h2>
<form action="{{ url('/talaq-fatawa/save-child') }}" method="POST">
    @csrf
    <input type="hidden" name="childId" value="{{ old('childId') }}">

    <textarea name="childName" id="editor" placeholder="جزئیات">{{ old('childName') }}</textarea>

    <input type="number" name="childSerialNumber" placeholder="سریل نمبر" value="{{ old('childSerialNumber') }}">
    <select name="childParentId">
        <option value="">موضوع منتخب کریں</option>
        @foreach($parents as $parent)
            <option value="{{ $parent->id }}">{{ $parent->name }}</option>
        @endforeach
    </select>
    <button type="submit">محفوظ کریں</button>
</form>

    <!-- Records Display -->
    <h2>Records</h2>
    <div class="records-grid" id="headers-container">
    @foreach($headers as $header)
        <div class="card" data-id="{{ $header->id }}" data-serial="{{ $header->serial_number }}">
            <div>
                <strong>{{ $header->name }}</strong> ({{ $header->serial_number }})
            </div>
            <div class="card-buttons">
                <button class="move-button" data-direction="up">↑</button>
                <button class="move-button" data-direction="down">↓</button>
                <button class="move-button" data-direction="left">←</button>
                <button class="move-button" data-direction="right">→</button>
            </div>
            <form action="{{ url('/talaq-fatawa/delete-header', $header->id) }}" method="POST" style="display: inline-block;">
                @csrf
                @method('DELETE')
                <button type="submit">Delete</button>
            </form>
            <button type="button" class="edit-header" data-id="{{ $header->id }}" data-name="{{ $header->name }}" data-serial="{{ $header->serial_number }}">Edit</button>
          <!-- Parents -->
@foreach($header->parents as $parent)
<div class="card parent-card" data-id="{{ $parent->id }}" data-serial="{{ $parent->serial_number }}" data-header-id="{{ $header->id }}">
<div>
            <strong>{{ $parent->name }}</strong> ({{ $parent->serial_number }})
        </div>
        <div class="card-buttons">
                    <button class="move-button-parent" data-direction="up">↑</button>
                    <button class="move-button-parent" data-direction="down">↓</button>
                </div>

        <form action="{{ url('/talaq-fatawa/delete-parent', $parent->id) }}" method="POST" style="display: inline-block;">
            @csrf
            @method('DELETE')
            <button type="submit">Delete</button>
        </form>
        <button
    type="button"
    class="edit-parent"
    data-id="{{ $parent->id }}"
    data-name="{{ $parent->name }}"
    data-serial="{{ $parent->serial_number }}"
    data-header-id="{{ $header->id }}"
>
    Edit
</button>

                    <!-- Children -->
                    @foreach($parent->children as $child)
                    <div class="card child-card" data-id="{{ $child->id }}" data-serial="{{ $child->serial_number }}" data-parent-id="{{ $parent->id }}">
        <div class="child-content">
        <div class="card-buttons">
                            <button class="move-button-child" data-direction="up">↑</button>
                            <button class="move-button-child" data-direction="down">↓</button>
                        </div>
             <p>Serial Number: {{ $child->serial_number }}</p>
            <button type="button" class="toggle-details">⇓</button>
            <div class="main-content">

            <p>{!! preg_replace('/\s+/', ' ', $child->name) !!}</p>

            </div>
            <div class="extra-details">
                <p>{{ $child->details ?? 'No additional details available.' }}</p>
            </div>
        </div>
        <form action="{{ url('/talaq-fatawa/delete-child', $child->id) }}" method="POST" style="display: inline-block;">
            @csrf
            @method('DELETE')
            <button type="submit">Delete</button>
        </form>
        <button
    type="button"
    class="edit-child"
    data-id="{{ $child->id }}"
    data-name="{{ $child->name }}"
    data-serial="{{ $child->serial_number }}"
    data-parent-id="{{ $child->parent_id }}">
    Edit
</button>
    </div>
@endforeach
                </div>
            @endforeach
        </div>
    @endforeach
</div>
<!-- Header Edit Modal -->
<div id="editHeaderModal" class="modal" style="display: none;">
<button id="closeHederModal" type="button">Close</button>

    <form action="{{ url('/talaq-fatawa/save-header') }}" method="POST">
        @csrf
        <input type="hidden" name="headerId" id="editHeaderId">
        <label>مرکزی عنوان</label>
        <input type="text" name="headerName" id="editHeaderName" placeholder="مرکزی عنوان">
        <label>سریل نمبر</label>
        <input type="number" name="headerSerialNumber" id="editHeaderSerialNumber" placeholder="سریل نمبر">
        <button type="submit">محفوظ کریں</button>
    </form>
</div>

<!-- Parent Edit Modal -->
<div id="editParentModal" class="modal" style="display: none;">
<button id="closeParentModal" type="button">Close</button>

    <form action="{{ url('/talaq-fatawa/save-parent') }}" method="POST">
        @csrf
        <input type="hidden" name="parentId" id="editParentId">
        <label>موضوع</label>
        <input type="text" name="parentName" id="editParentName" placeholder="موضوع">
        <label>سریل نمبر</label>
        <input type="number" name="parentSerialNumber" id="editParentSerialNumber" placeholder="سریل نمبر">
        <label>مرکزی عنوان</label>
        <select name="parentHeaderId" id="editParentHeaderId">
            <option value="">مرکزی عنوان منتخب کریں</option>
            @foreach($headers as $header)
                <option value="{{ $header->id }}">{{ $header->name }}</option>
            @endforeach
        </select>
        <button type="submit">محفوظ کریں</button>
    </form>
</div>

<!-- Child Edit Modal -->
<div id="editChildModal" class="modal" style="display: none;">
<button id="closeModal" type="button">Close</button>
    <form action="{{ url('/talaq-fatawa/save-child') }}" method="POST">
        @csrf
        <input type="hidden" name="childId" id="editChildId">

        <label>جزئیات</label>
        <textarea name="childName" id="editChildNameEditor" placeholder="جزئیات"></textarea>

        <label>سریل نمبر</label>
        <input type="number" name="childSerialNumber" id="editChildSerialNumber" placeholder="سریل نمبر">

        <select name="childParentId" id="editChildParentId">
    <option value="">موضوع منتخب کریں</option>
    @foreach($parents as $parent)
        <option value="{{ $parent->id }}">{{ $parent->name }}</option>
    @endforeach
</select>

        <button type="submit">محفوظ کریں</button>
    </form>
</div>
<!-- Include Required Libraries -->

<script src="https://code.jquery.com/jquery-3.4.1.slim.min.js" integrity="sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n" crossorigin="anonymous"></script>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.9.0/dist/summernote-lite.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/summernote@0.9.0/dist/summernote-lite.min.js"></script>

<script>
  $(document).ready(function () {
    $('#editor').summernote({
      height: 200, // Set the editor height
      toolbar: [
        ['style', ['style']],
        ['font', ['bold', 'italic', 'underline', 'clear', 'fontname', 'fontsize']],
        ['fontstyle', ['fontsize', 'fontfamily']],
        ['color', ['color']],
        ['para', ['ul', 'ol', 'paragraph']],
        ['insert', ['link', 'picture', 'video']],
        ['view', ['fullscreen', 'codeview', 'help']],
        ['custom', ['removeWhitespace', 'increaseGap', 'decreaseGap']]      ],
        buttons: {
        removeWhitespace: function () {
          return $.summernote.ui.button({
            contents: '<i class="fas fa-eraser"></i> Remove Whitespace',
            tooltip: 'Remove extra whitespaces',
            click: function () {
              let content = $('#editor').summernote('code');
              // Remove excessive whitespaces
              content = content.replace(/\s+/g, ' ').trim(); // Replace multiple spaces with a single space
              $('#editor').summernote('code', content);
            }
          }).render();
        },
        increaseGap: function () {
          return $.summernote.ui.button({
            contents: '<i class="fas fa-plus"></i> Increase Gap',
            tooltip: 'Increase paragraph gap',
            click: function () {
              let content = $('#editor').summernote('code');
              // Add margin between paragraphs
              content = content.replace(/<p>/g, '<p style="margin-bottom: 20px;">');
              $('#editor').summernote('code', content);
            }
          }).render();
        },
        decreaseGap: function () {
          return $.summernote.ui.button({
            contents: '<i class="fas fa-minus"></i> Decrease Gap',
            tooltip: 'Decrease paragraph gap',
            click: function () {
              let content = $('#editor').summernote('code');
              // Reduce margin between paragraphs
              content = content.replace(/<p style="margin-bottom: (\d+)px;">/g, function (match, p1) {
                let newMargin = Math.max(0, parseInt(p1) - 5); // Decrease gap by 5px, minimum is 0
                return `<p style="margin-bottom: ${newMargin}px;">`;
              });
              $('#editor').summernote('code', content);
            }
          }).render();
        }
      },
      fontNames: [
        'Jameel Noori Nastaleeq',
        'Naskh Unicode',
        'Al Mushaf',
        'Arial',
        'Times New Roman',
        'Courier New'
      ],
      fontNamesIgnoreCheck: [
        'Jameel Noori Nastaleeq',
        'Naskh Unicode',
        'Al Mushaf'
      ],
      callbacks: {
        onInit: function () {
          $('head').append(`
           <style>
                        @font-face {
                            font-family: 'Jameel Noori Nastaleeq';
                            src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');
                        }
                        @font-face {
                            font-family: 'Naskh Unicode';
                            src: url('/storage/fonts/UrduNaskhUnicode.ttf');
                        }
                        @font-face {
                            font-family: 'Al Mushaf';
                            src: url('/storage/fonts/Al_Mushaf.ttf');
                        }

                    </style>
          `);
// Set initial font and alignment after editor is initialized.
$('#editor').summernote('fontName', 'Jameel Noori Nastaleeq');
                $('#editor').summernote('justifyRight');
        }
      }
    });
  });
</script>
<!--
<script src="https://cdn.tiny.cloud/1/y44puvyz852ods9tg2k2e76t7aqbwvhk5x922totn19kjaa7/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>

<script>
  tinymce.init({
    selector: '#editor',
    plugins: 'anchor autolink charmap codesample emoticons image link lists media searchreplace table visualblocks wordcount',
    toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | link image media table | align lineheight | numlist bullist indent outdent | emoticons charmap | removeformat',
    font_family_formats:
      "Jameel Noori Nastaleeq=Jameel Noori Nastaleeq, serif;" +
      "Naskh Unicode=Naskh Unicode, serif;" +
      "Al Mushaf=Al_Mushaf, serif;" +
      "Arial=Arial, sans-serif;" +
      "Times New Roman=Times New Roman, serif;" +
      "Courier New=Courier New, monospace;",
    content_style: `
        @font-face {
            font-family: 'Jameel Noori Nastaleeq';
            src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');
        }
        @font-face {
            font-family: 'Naskh Unicode';
            src: url('/storage/fonts/UrduNaskhUnicode.ttf');
        }
        @font-face {
            font-family: 'Al Mushaf';
            src: url('/storage/fonts/Al_Mushaf.ttf');
        }
       body {
                font-family: Jameel Noori Nastaleeq, serif;
                direction: rtl;
                text-align: right;
            }
    `,

  });
</script> -->

    <script>
  document.addEventListener('DOMContentLoaded', () => {
        const toggleButtons = document.querySelectorAll('.toggle-details');

        toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
                const childCard = button.closest('.child-card');
                childCard.classList.toggle('expanded');
                button.textContent = childCard.classList.contains('expanded') ? '⇑' : '⇓';
            });
        });
    });
</script>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const container = document.getElementById('headers-container');

    document.querySelectorAll('.move-button').forEach(button => {
        button.addEventListener('click', async (e) => {
            const direction = e.target.getAttribute('data-direction');
            const card = e.target.closest('.card');
            const cardId = card.dataset.id;
            const cardSerial = parseInt(card.dataset.serial);


            // Calculate new position based on direction
            let newSerial = cardSerial;
            if (direction === 'up') newSerial -= 3;
            else if (direction === 'down') newSerial += 3;
            else if (direction === 'left') newSerial -= 1;
            else if (direction === 'right') newSerial += 1;

            // Prevent invalid positions
            if (newSerial < 1) return;

            try {
                const response = await fetch("{{ url('/talaq-fatawa/update-serial') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    },
                    body: JSON.stringify({
                        id: cardId,
                        new_serial: newSerial,
                    }),
                });

                if (response.ok) {
                    location.reload(); // Reload to reflect changes
                } else {
                    alert('Failed to move the card. Try again.');
                }
            } catch (error) {
                console.error('Error:', error);
            }
        });
    });
});
document.addEventListener('DOMContentLoaded', () => {
    // Handle Parent Movement
    document.querySelectorAll('.move-button-parent').forEach(button => {
        button.addEventListener('click', async (e) => {
            const direction = e.target.getAttribute('data-direction');
            const card = e.target.closest('.parent-card');
            const cardId = card.dataset.id;
            const cardSerial = parseInt(card.dataset.serial);
            const headerId = card.dataset.headerId;

            let newSerial = cardSerial + (direction === 'up' ? -1 : 1);
            if (newSerial < 1) return;

            await updateSerial('/talaq-fatawa/update-parent-serial', cardId, newSerial, { header_id: headerId });
        });
    });

    // Handle Child Movement
    document.querySelectorAll('.move-button-child').forEach(button => {
        button.addEventListener('click', async (e) => {
            const direction = e.target.getAttribute('data-direction');
            const card = e.target.closest('.child-card');
            const cardId = card.dataset.id;
            const cardSerial = parseInt(card.dataset.serial);
            const parentId = card.dataset.parentId;

            let newSerial = cardSerial + (direction === 'up' ? -1 : 1);
            if (newSerial < 1) return;

            await updateSerial('/talaq-fatawa/update-child-serial', cardId, newSerial, { parent_id: parentId });
        });
    });

    // Helper function for updating serial numbers
    async function updateSerial(url, id, newSerial, extraData) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                },
                body: JSON.stringify({
                    id,
                    new_serial: newSerial,
                    ...extraData,
                }),
            });

            if (response.ok) {
                location.reload();
            } else {
                alert('Failed to move the record. Try again.');
            }
        } catch (error) {
            console.error('Error:', error);
        }
    }
});
document.addEventListener('DOMContentLoaded', function () {
    // Header Edit
    document.querySelectorAll('.edit-header').forEach(button => {
        button.addEventListener('click', function () {
            const headerId = this.dataset.id;
            const headerName = this.dataset.name;
            const headerSerialNumber = this.dataset.serial;

            document.getElementById('editHeaderId').value = headerId;
            document.getElementById('editHeaderName').value = headerName;
            document.getElementById('editHeaderSerialNumber').value = headerSerialNumber;

            document.getElementById('editHeaderModal').style.display = 'block';
        });
    });
    document.querySelectorAll('.edit-parent').forEach(button => {
    button.addEventListener('click', function () {
        const parentId = this.dataset.id;
        const parentName = this.dataset.name;
        const parentSerialNumber = this.dataset.serial;

        // Retrieve parent header ID if available
        const parentHeaderId = this.dataset.headerId;

        // Set the parent ID, name, and serial number
        document.getElementById('editParentId').value = parentId;
        document.getElementById('editParentName').value = parentName;
        document.getElementById('editParentSerialNumber').value = parentSerialNumber;

        // Set the selected option in the header dropdown
        const headerDropdown = document.getElementById('editParentHeaderId');
        Array.from(headerDropdown.options).forEach(option => {
            option.selected = option.value === parentHeaderId; // Compare as strings
        });

        // Show the modal
        document.getElementById('editParentModal').style.display = 'block';
    });
});
    document.getElementById('closeHederModal').addEventListener('click', function () {
    document.getElementById('editHeaderModal').style.display = 'none';
});
document.getElementById('closeParentModal').addEventListener('click', function () {
    document.getElementById('editParentModal').style.display = 'none';
});

    // Add similar logic for Parent and Child edit buttons
});
</script>
<script>
    $(document).ready(function () {
    // Initialize Summernote for the editChildNameEditor
    $('#editChildNameEditor').summernote({
        height: 200, // Set the editor height
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear', 'fontname', 'fontsize']],
            ['fontstyle', ['fontsize', 'fontfamily']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['insert', ['link', 'picture', 'video']],
            ['view', ['fullscreen', 'codeview', 'help']],
            ['custom', ['removeWhitespace', 'increaseGap', 'decreaseGap']]
        ],
        buttons: {
            removeWhitespace: function () {
                return $.summernote.ui.button({
                    contents: '<i class="fas fa-eraser"></i> Remove Whitespace',
                    tooltip: 'Remove extra whitespaces',
                    click: function () {
                        let content = $('#editChildNameEditor').summernote('code');
                        content = content.replace(/\s+/g, ' ').trim();
                        $('#editChildNameEditor').summernote('code', content);
                    }
                }).render();
            },
            increaseGap: function () {
                return $.summernote.ui.button({
                    contents: '<i class="fas fa-plus"></i> Increase Gap',
                    tooltip: 'Increase paragraph gap',
                    click: function () {
                        let content = $('#editChildNameEditor').summernote('code');
                        content = content.replace(/<p>/g, '<p style="margin-bottom: 20px;">');
                        $('#editChildNameEditor').summernote('code', content);
                    }
                }).render();
            },
            decreaseGap: function () {
                return $.summernote.ui.button({
                    contents: '<i class="fas fa-minus"></i> Decrease Gap',
                    tooltip: 'Decrease paragraph gap',
                    click: function () {
                        let content = $('#editChildNameEditor').summernote('code');
                        content = content.replace(/<p style="margin-bottom: (\d+)px;">/g, function (match, p1) {
                            let newMargin = Math.max(0, parseInt(p1) - 5);
                            return `<p style="margin-bottom: ${newMargin}px;">`;
                        });
                        $('#editChildNameEditor').summernote('code', content);
                    }
                }).render();
            }
        },
        fontNames: [
            'Jameel Noori Nastaleeq',
            'Naskh Unicode',
            'Al Mushaf',
            'Arial',
            'Times New Roman',
            'Courier New'
        ],
        fontNamesIgnoreCheck: [
            'Jameel Noori Nastaleeq',
            'Naskh Unicode',
            'Al Mushaf'
        ],
        callbacks: {
            onInit: function () {
                $('head').append(`
                    <style>
                        @font-face {
                            font-family: 'Jameel Noori Nastaleeq';
                            src: url('/storage/fonts/JameelNooriNastaleeqNew.ttf');
                        }
                        @font-face {
                            font-family: 'Naskh Unicode';
                            src: url('/storage/fonts/UrduNaskhUnicode.ttf');
                        }
                        @font-face {
                            font-family: 'Al Mushaf';
                            src: url('/storage/fonts/Al_Mushaf.ttf');
                        }
                    </style>
                `);
                $('#editChildNameEditor').summernote('fontName', 'Jameel Noori Nastaleeq');
                $('#editChildNameEditor').summernote('justifyRight');
            }
        }
    });

    document.querySelectorAll('.edit-child').forEach(button => {
    button.addEventListener('click', function () {
        const childId = this.dataset.id;
        const childName = this.dataset.name;
        const childSerialNumber = this.dataset.serial;
        const childParentId = this.dataset.parentId; // Get parent ID from dataset

        // Set the child ID
        document.getElementById('editChildId').value = childId;

        // Set the child name using Summernote editor
        $('#editChildNameEditor').summernote('code', childName);

        // Set the serial number
        document.getElementById('editChildSerialNumber').value = childSerialNumber;

        // Set the selected option in the parent dropdown
        const parentDropdown = document.getElementById('editChildParentId');
        Array.from(parentDropdown.options).forEach(option => {
            option.selected = option.value === childParentId; // Compare as strings
        });

        // Show the modal
        document.getElementById('editChildModal').style.display = 'block';
    });
});

// Close modal functionality
document.getElementById('closeModal').addEventListener('click', function () {
    document.getElementById('editChildModal').style.display = 'none';
});
console.log('Child Parent ID:', childParentId);
Array.from(parentDropdown.options).forEach(option => {
    console.log('Option Value:', option.value, 'Is Selected:', option.value === childParentId);
});
});

    </script>
</body>
</html>
